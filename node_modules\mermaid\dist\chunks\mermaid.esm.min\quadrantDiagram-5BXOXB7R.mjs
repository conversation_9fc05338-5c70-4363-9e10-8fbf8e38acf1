import{N as re,Q as se,R as oe,S as le,T as de,U as ue,V as ce,W as vt,Y as xt,c as lt,ha as bt,j as ie,l as L,oa as Dt,y as ne}from"./chunk-F632ZYSZ.mjs";import{a as r}from"./chunk-GTKDMUJJ.mjs";var Et=function(){var t=r(function(X,o,l,x){for(l=l||{},x=X.length;x--;l[X[x]]=o);return l},"o"),n=[1,3],f=[1,4],u=[1,5],c=[1,6],g=[1,7],y=[1,4,5,10,12,13,14,18,25,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],S=[1,4,5,10,12,13,14,18,25,28,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],i=[55,56,57],A=[2,36],h=[1,37],T=[1,36],m=[1,38],b=[1,35],q=[1,43],p=[1,41],K=[1,14],dt=[1,23],ft=[1,18],pt=[1,19],gt=[1,20],ut=[1,21],kt=[1,22],ct=[1,24],a=[1,25],Bt=[1,26],wt=[1,27],It=[1,28],Ot=[1,29],W=[1,32],N=[1,33],P=[1,34],_=[1,39],F=[1,40],Q=[1,42],C=[1,44],R=[1,62],H=[1,61],v=[4,5,8,10,12,13,14,18,44,47,49,55,56,57,63,64,65,66,67],Wt=[1,65],Nt=[1,66],Rt=[1,67],Ht=[1,68],Ut=[1,69],jt=[1,70],Xt=[1,71],Mt=[1,72],Yt=[1,73],Gt=[1,74],Kt=[1,75],Zt=[1,76],B=[4,5,6,7,8,9,10,11,12,13,14,15,18],Z=[1,90],J=[1,91],$=[1,92],tt=[1,99],et=[1,93],at=[1,96],it=[1,94],nt=[1,95],rt=[1,97],st=[1,98],St=[1,102],Jt=[10,55,56,57],I=[4,5,6,8,10,11,13,17,18,19,20,55,56,57],At={trace:r(function(){},"trace"),yy:{},symbols_:{error:2,idStringToken:3,ALPHA:4,NUM:5,NODE_STRING:6,DOWN:7,MINUS:8,DEFAULT:9,COMMA:10,COLON:11,AMP:12,BRKT:13,MULT:14,UNICODE_TEXT:15,styleComponent:16,UNIT:17,SPACE:18,STYLE:19,PCT:20,idString:21,style:22,stylesOpt:23,classDefStatement:24,CLASSDEF:25,start:26,eol:27,QUADRANT:28,document:29,line:30,statement:31,axisDetails:32,quadrantDetails:33,points:34,title:35,title_value:36,acc_title:37,acc_title_value:38,acc_descr:39,acc_descr_value:40,acc_descr_multiline_value:41,section:42,text:43,point_start:44,point_x:45,point_y:46,class_name:47,"X-AXIS":48,"AXIS-TEXT-DELIMITER":49,"Y-AXIS":50,QUADRANT_1:51,QUADRANT_2:52,QUADRANT_3:53,QUADRANT_4:54,NEWLINE:55,SEMI:56,EOF:57,alphaNumToken:58,textNoTagsToken:59,STR:60,MD_STR:61,alphaNum:62,PUNCTUATION:63,PLUS:64,EQUALS:65,DOT:66,UNDERSCORE:67,$accept:0,$end:1},terminals_:{2:"error",4:"ALPHA",5:"NUM",6:"NODE_STRING",7:"DOWN",8:"MINUS",9:"DEFAULT",10:"COMMA",11:"COLON",12:"AMP",13:"BRKT",14:"MULT",15:"UNICODE_TEXT",17:"UNIT",18:"SPACE",19:"STYLE",20:"PCT",25:"CLASSDEF",28:"QUADRANT",35:"title",36:"title_value",37:"acc_title",38:"acc_title_value",39:"acc_descr",40:"acc_descr_value",41:"acc_descr_multiline_value",42:"section",44:"point_start",45:"point_x",46:"point_y",47:"class_name",48:"X-AXIS",49:"AXIS-TEXT-DELIMITER",50:"Y-AXIS",51:"QUADRANT_1",52:"QUADRANT_2",53:"QUADRANT_3",54:"QUADRANT_4",55:"NEWLINE",56:"SEMI",57:"EOF",60:"STR",61:"MD_STR",63:"PUNCTUATION",64:"PLUS",65:"EQUALS",66:"DOT",67:"UNDERSCORE"},productions_:[0,[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[21,1],[21,2],[22,1],[22,2],[23,1],[23,3],[24,5],[26,2],[26,2],[26,2],[29,0],[29,2],[30,2],[31,0],[31,1],[31,2],[31,1],[31,1],[31,1],[31,2],[31,2],[31,2],[31,1],[31,1],[34,4],[34,5],[34,5],[34,6],[32,4],[32,3],[32,2],[32,4],[32,3],[32,2],[33,2],[33,2],[33,2],[33,2],[27,1],[27,1],[27,1],[43,1],[43,2],[43,1],[43,1],[62,1],[62,2],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[59,1],[59,1],[59,1]],performAction:r(function(o,l,x,d,k,e,ht){var s=e.length-1;switch(k){case 23:this.$=e[s];break;case 24:this.$=e[s-1]+""+e[s];break;case 26:this.$=e[s-1]+e[s];break;case 27:this.$=[e[s].trim()];break;case 28:e[s-2].push(e[s].trim()),this.$=e[s-2];break;case 29:this.$=e[s-4],d.addClass(e[s-2],e[s]);break;case 37:this.$=[];break;case 42:this.$=e[s].trim(),d.setDiagramTitle(this.$);break;case 43:this.$=e[s].trim(),d.setAccTitle(this.$);break;case 44:case 45:this.$=e[s].trim(),d.setAccDescription(this.$);break;case 46:d.addSection(e[s].substr(8)),this.$=e[s].substr(8);break;case 47:d.addPoint(e[s-3],"",e[s-1],e[s],[]);break;case 48:d.addPoint(e[s-4],e[s-3],e[s-1],e[s],[]);break;case 49:d.addPoint(e[s-4],"",e[s-2],e[s-1],e[s]);break;case 50:d.addPoint(e[s-5],e[s-4],e[s-2],e[s-1],e[s]);break;case 51:d.setXAxisLeftText(e[s-2]),d.setXAxisRightText(e[s]);break;case 52:e[s-1].text+=" \u27F6 ",d.setXAxisLeftText(e[s-1]);break;case 53:d.setXAxisLeftText(e[s]);break;case 54:d.setYAxisBottomText(e[s-2]),d.setYAxisTopText(e[s]);break;case 55:e[s-1].text+=" \u27F6 ",d.setYAxisBottomText(e[s-1]);break;case 56:d.setYAxisBottomText(e[s]);break;case 57:d.setQuadrant1Text(e[s]);break;case 58:d.setQuadrant2Text(e[s]);break;case 59:d.setQuadrant3Text(e[s]);break;case 60:d.setQuadrant4Text(e[s]);break;case 64:this.$={text:e[s],type:"text"};break;case 65:this.$={text:e[s-1].text+""+e[s],type:e[s-1].type};break;case 66:this.$={text:e[s],type:"text"};break;case 67:this.$={text:e[s],type:"markdown"};break;case 68:this.$=e[s];break;case 69:this.$=e[s-1]+""+e[s];break}},"anonymous"),table:[{18:n,26:1,27:2,28:f,55:u,56:c,57:g},{1:[3]},{18:n,26:8,27:2,28:f,55:u,56:c,57:g},{18:n,26:9,27:2,28:f,55:u,56:c,57:g},t(y,[2,33],{29:10}),t(S,[2,61]),t(S,[2,62]),t(S,[2,63]),{1:[2,30]},{1:[2,31]},t(i,A,{30:11,31:12,24:13,32:15,33:16,34:17,43:30,58:31,1:[2,32],4:h,5:T,10:m,12:b,13:q,14:p,18:K,25:dt,35:ft,37:pt,39:gt,41:ut,42:kt,48:ct,50:a,51:Bt,52:wt,53:It,54:Ot,60:W,61:N,63:P,64:_,65:F,66:Q,67:C}),t(y,[2,34]),{27:45,55:u,56:c,57:g},t(i,[2,37]),t(i,A,{24:13,32:15,33:16,34:17,43:30,58:31,31:46,4:h,5:T,10:m,12:b,13:q,14:p,18:K,25:dt,35:ft,37:pt,39:gt,41:ut,42:kt,48:ct,50:a,51:Bt,52:wt,53:It,54:Ot,60:W,61:N,63:P,64:_,65:F,66:Q,67:C}),t(i,[2,39]),t(i,[2,40]),t(i,[2,41]),{36:[1,47]},{38:[1,48]},{40:[1,49]},t(i,[2,45]),t(i,[2,46]),{18:[1,50]},{4:h,5:T,10:m,12:b,13:q,14:p,43:51,58:31,60:W,61:N,63:P,64:_,65:F,66:Q,67:C},{4:h,5:T,10:m,12:b,13:q,14:p,43:52,58:31,60:W,61:N,63:P,64:_,65:F,66:Q,67:C},{4:h,5:T,10:m,12:b,13:q,14:p,43:53,58:31,60:W,61:N,63:P,64:_,65:F,66:Q,67:C},{4:h,5:T,10:m,12:b,13:q,14:p,43:54,58:31,60:W,61:N,63:P,64:_,65:F,66:Q,67:C},{4:h,5:T,10:m,12:b,13:q,14:p,43:55,58:31,60:W,61:N,63:P,64:_,65:F,66:Q,67:C},{4:h,5:T,10:m,12:b,13:q,14:p,43:56,58:31,60:W,61:N,63:P,64:_,65:F,66:Q,67:C},{4:h,5:T,8:R,10:m,12:b,13:q,14:p,18:H,44:[1,57],47:[1,58],58:60,59:59,63:P,64:_,65:F,66:Q,67:C},t(v,[2,64]),t(v,[2,66]),t(v,[2,67]),t(v,[2,70]),t(v,[2,71]),t(v,[2,72]),t(v,[2,73]),t(v,[2,74]),t(v,[2,75]),t(v,[2,76]),t(v,[2,77]),t(v,[2,78]),t(v,[2,79]),t(v,[2,80]),t(y,[2,35]),t(i,[2,38]),t(i,[2,42]),t(i,[2,43]),t(i,[2,44]),{3:64,4:Wt,5:Nt,6:Rt,7:Ht,8:Ut,9:jt,10:Xt,11:Mt,12:Yt,13:Gt,14:Kt,15:Zt,21:63},t(i,[2,53],{59:59,58:60,4:h,5:T,8:R,10:m,12:b,13:q,14:p,18:H,49:[1,77],63:P,64:_,65:F,66:Q,67:C}),t(i,[2,56],{59:59,58:60,4:h,5:T,8:R,10:m,12:b,13:q,14:p,18:H,49:[1,78],63:P,64:_,65:F,66:Q,67:C}),t(i,[2,57],{59:59,58:60,4:h,5:T,8:R,10:m,12:b,13:q,14:p,18:H,63:P,64:_,65:F,66:Q,67:C}),t(i,[2,58],{59:59,58:60,4:h,5:T,8:R,10:m,12:b,13:q,14:p,18:H,63:P,64:_,65:F,66:Q,67:C}),t(i,[2,59],{59:59,58:60,4:h,5:T,8:R,10:m,12:b,13:q,14:p,18:H,63:P,64:_,65:F,66:Q,67:C}),t(i,[2,60],{59:59,58:60,4:h,5:T,8:R,10:m,12:b,13:q,14:p,18:H,63:P,64:_,65:F,66:Q,67:C}),{45:[1,79]},{44:[1,80]},t(v,[2,65]),t(v,[2,81]),t(v,[2,82]),t(v,[2,83]),{3:82,4:Wt,5:Nt,6:Rt,7:Ht,8:Ut,9:jt,10:Xt,11:Mt,12:Yt,13:Gt,14:Kt,15:Zt,18:[1,81]},t(B,[2,23]),t(B,[2,1]),t(B,[2,2]),t(B,[2,3]),t(B,[2,4]),t(B,[2,5]),t(B,[2,6]),t(B,[2,7]),t(B,[2,8]),t(B,[2,9]),t(B,[2,10]),t(B,[2,11]),t(B,[2,12]),t(i,[2,52],{58:31,43:83,4:h,5:T,10:m,12:b,13:q,14:p,60:W,61:N,63:P,64:_,65:F,66:Q,67:C}),t(i,[2,55],{58:31,43:84,4:h,5:T,10:m,12:b,13:q,14:p,60:W,61:N,63:P,64:_,65:F,66:Q,67:C}),{46:[1,85]},{45:[1,86]},{4:Z,5:J,6:$,8:tt,11:et,13:at,16:89,17:it,18:nt,19:rt,20:st,22:88,23:87},t(B,[2,24]),t(i,[2,51],{59:59,58:60,4:h,5:T,8:R,10:m,12:b,13:q,14:p,18:H,63:P,64:_,65:F,66:Q,67:C}),t(i,[2,54],{59:59,58:60,4:h,5:T,8:R,10:m,12:b,13:q,14:p,18:H,63:P,64:_,65:F,66:Q,67:C}),t(i,[2,47],{22:88,16:89,23:100,4:Z,5:J,6:$,8:tt,11:et,13:at,17:it,18:nt,19:rt,20:st}),{46:[1,101]},t(i,[2,29],{10:St}),t(Jt,[2,27],{16:103,4:Z,5:J,6:$,8:tt,11:et,13:at,17:it,18:nt,19:rt,20:st}),t(I,[2,25]),t(I,[2,13]),t(I,[2,14]),t(I,[2,15]),t(I,[2,16]),t(I,[2,17]),t(I,[2,18]),t(I,[2,19]),t(I,[2,20]),t(I,[2,21]),t(I,[2,22]),t(i,[2,49],{10:St}),t(i,[2,48],{22:88,16:89,23:104,4:Z,5:J,6:$,8:tt,11:et,13:at,17:it,18:nt,19:rt,20:st}),{4:Z,5:J,6:$,8:tt,11:et,13:at,16:89,17:it,18:nt,19:rt,20:st,22:105},t(I,[2,26]),t(i,[2,50],{10:St}),t(Jt,[2,28],{16:103,4:Z,5:J,6:$,8:tt,11:et,13:at,17:it,18:nt,19:rt,20:st})],defaultActions:{8:[2,30],9:[2,31]},parseError:r(function(o,l){if(l.recoverable)this.trace(o);else{var x=new Error(o);throw x.hash=l,x}},"parseError"),parse:r(function(o){var l=this,x=[0],d=[],k=[null],e=[],ht=this.table,s="",yt=0,$t=0,te=0,Te=2,ee=1,me=e.slice.call(arguments,1),D=Object.create(this.lexer),M={yy:{}};for(var _t in this.yy)Object.prototype.hasOwnProperty.call(this.yy,_t)&&(M.yy[_t]=this.yy[_t]);D.setInput(o,M.yy),M.yy.lexer=D,M.yy.parser=this,typeof D.yylloc>"u"&&(D.yylloc={});var Ft=D.yylloc;e.push(Ft);var be=D.options&&D.options.ranges;typeof M.yy.parseError=="function"?this.parseError=M.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Ie(w){x.length=x.length-2*w,k.length=k.length-w,e.length=e.length-w}r(Ie,"popStack");function qe(){var w;return w=d.pop()||D.lex()||ee,typeof w!="number"&&(w instanceof Array&&(d=w,w=d.pop()),w=l.symbols_[w]||w),w}r(qe,"lex");for(var z,Qt,Y,O,Oe,Ct,ot={},Tt,U,ae,mt;;){if(Y=x[x.length-1],this.defaultActions[Y]?O=this.defaultActions[Y]:((z===null||typeof z>"u")&&(z=qe()),O=ht[Y]&&ht[Y][z]),typeof O>"u"||!O.length||!O[0]){var Lt="";mt=[];for(Tt in ht[Y])this.terminals_[Tt]&&Tt>Te&&mt.push("'"+this.terminals_[Tt]+"'");D.showPosition?Lt="Parse error on line "+(yt+1)+`:
`+D.showPosition()+`
Expecting `+mt.join(", ")+", got '"+(this.terminals_[z]||z)+"'":Lt="Parse error on line "+(yt+1)+": Unexpected "+(z==ee?"end of input":"'"+(this.terminals_[z]||z)+"'"),this.parseError(Lt,{text:D.match,token:this.terminals_[z]||z,line:D.yylineno,loc:Ft,expected:mt})}if(O[0]instanceof Array&&O.length>1)throw new Error("Parse Error: multiple actions possible at state: "+Y+", token: "+z);switch(O[0]){case 1:x.push(z),k.push(D.yytext),e.push(D.yylloc),x.push(O[1]),z=null,Qt?(z=Qt,Qt=null):($t=D.yyleng,s=D.yytext,yt=D.yylineno,Ft=D.yylloc,te>0&&te--);break;case 2:if(U=this.productions_[O[1]][1],ot.$=k[k.length-U],ot._$={first_line:e[e.length-(U||1)].first_line,last_line:e[e.length-1].last_line,first_column:e[e.length-(U||1)].first_column,last_column:e[e.length-1].last_column},be&&(ot._$.range=[e[e.length-(U||1)].range[0],e[e.length-1].range[1]]),Ct=this.performAction.apply(ot,[s,$t,yt,M.yy,O[1],k,e].concat(me)),typeof Ct<"u")return Ct;U&&(x=x.slice(0,-1*U*2),k=k.slice(0,-1*U),e=e.slice(0,-1*U)),x.push(this.productions_[O[1]][0]),k.push(ot.$),e.push(ot._$),ae=ht[x[x.length-2]][x[x.length-1]],x.push(ae);break;case 3:return!0}}return!0},"parse")},ye=function(){var X={EOF:1,parseError:r(function(l,x){if(this.yy.parser)this.yy.parser.parseError(l,x);else throw new Error(l)},"parseError"),setInput:r(function(o,l){return this.yy=l||this.yy||{},this._input=o,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:r(function(){var o=this._input[0];this.yytext+=o,this.yyleng++,this.offset++,this.match+=o,this.matched+=o;var l=o.match(/(?:\r\n?|\n).*/g);return l?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),o},"input"),unput:r(function(o){var l=o.length,x=o.split(/(?:\r\n?|\n)/g);this._input=o+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-l),this.offset-=l;var d=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),x.length-1&&(this.yylineno-=x.length-1);var k=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:x?(x.length===d.length?this.yylloc.first_column:0)+d[d.length-x.length].length-x[0].length:this.yylloc.first_column-l},this.options.ranges&&(this.yylloc.range=[k[0],k[0]+this.yyleng-l]),this.yyleng=this.yytext.length,this},"unput"),more:r(function(){return this._more=!0,this},"more"),reject:r(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:r(function(o){this.unput(this.match.slice(o))},"less"),pastInput:r(function(){var o=this.matched.substr(0,this.matched.length-this.match.length);return(o.length>20?"...":"")+o.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:r(function(){var o=this.match;return o.length<20&&(o+=this._input.substr(0,20-o.length)),(o.substr(0,20)+(o.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:r(function(){var o=this.pastInput(),l=new Array(o.length+1).join("-");return o+this.upcomingInput()+`
`+l+"^"},"showPosition"),test_match:r(function(o,l){var x,d,k;if(this.options.backtrack_lexer&&(k={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(k.yylloc.range=this.yylloc.range.slice(0))),d=o[0].match(/(?:\r\n?|\n).*/g),d&&(this.yylineno+=d.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:d?d[d.length-1].length-d[d.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+o[0].length},this.yytext+=o[0],this.match+=o[0],this.matches=o,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(o[0].length),this.matched+=o[0],x=this.performAction.call(this,this.yy,this,l,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),x)return x;if(this._backtrack){for(var e in k)this[e]=k[e];return!1}return!1},"test_match"),next:r(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var o,l,x,d;this._more||(this.yytext="",this.match="");for(var k=this._currentRules(),e=0;e<k.length;e++)if(x=this._input.match(this.rules[k[e]]),x&&(!l||x[0].length>l[0].length)){if(l=x,d=e,this.options.backtrack_lexer){if(o=this.test_match(x,k[e]),o!==!1)return o;if(this._backtrack){l=!1;continue}else return!1}else if(!this.options.flex)break}return l?(o=this.test_match(l,k[d]),o!==!1?o:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:r(function(){var l=this.next();return l||this.lex()},"lex"),begin:r(function(l){this.conditionStack.push(l)},"begin"),popState:r(function(){var l=this.conditionStack.length-1;return l>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:r(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:r(function(l){return l=this.conditionStack.length-1-Math.abs(l||0),l>=0?this.conditionStack[l]:"INITIAL"},"topState"),pushState:r(function(l){this.begin(l)},"pushState"),stateStackSize:r(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:r(function(l,x,d,k){var e=k;switch(d){case 0:break;case 1:break;case 2:return 55;case 3:break;case 4:return this.begin("title"),35;break;case 5:return this.popState(),"title_value";break;case 6:return this.begin("acc_title"),37;break;case 7:return this.popState(),"acc_title_value";break;case 8:return this.begin("acc_descr"),39;break;case 9:return this.popState(),"acc_descr_value";break;case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:return 48;case 14:return 50;case 15:return 49;case 16:return 51;case 17:return 52;case 18:return 53;case 19:return 54;case 20:return 25;case 21:this.begin("md_string");break;case 22:return"MD_STR";case 23:this.popState();break;case 24:this.begin("string");break;case 25:this.popState();break;case 26:return"STR";case 27:this.begin("class_name");break;case 28:return this.popState(),47;break;case 29:return this.begin("point_start"),44;break;case 30:return this.begin("point_x"),45;break;case 31:this.popState();break;case 32:this.popState(),this.begin("point_y");break;case 33:return this.popState(),46;break;case 34:return 28;case 35:return 4;case 36:return 11;case 37:return 64;case 38:return 10;case 39:return 65;case 40:return 65;case 41:return 14;case 42:return 13;case 43:return 67;case 44:return 66;case 45:return 12;case 46:return 8;case 47:return 5;case 48:return 18;case 49:return 56;case 50:return 63;case 51:return 57}},"anonymous"),rules:[/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n\r]+)/i,/^(?:%%[^\n]*)/i,/^(?:title\b)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?: *x-axis *)/i,/^(?: *y-axis *)/i,/^(?: *--+> *)/i,/^(?: *quadrant-1 *)/i,/^(?: *quadrant-2 *)/i,/^(?: *quadrant-3 *)/i,/^(?: *quadrant-4 *)/i,/^(?:classDef\b)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?::::)/i,/^(?:^\w+)/i,/^(?:\s*:\s*\[\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?:\s*\] *)/i,/^(?:\s*,\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?: *quadrantChart *)/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:=)/i,/^(?:\*)/i,/^(?:#)/i,/^(?:[\_])/i,/^(?:\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\s)/i,/^(?:;)/i,/^(?:[!"#$%&'*+,-.`?\\_/])/i,/^(?:$)/i],conditions:{class_name:{rules:[28],inclusive:!1},point_y:{rules:[33],inclusive:!1},point_x:{rules:[32],inclusive:!1},point_start:{rules:[30,31],inclusive:!1},acc_descr_multiline:{rules:[11,12],inclusive:!1},acc_descr:{rules:[9],inclusive:!1},acc_title:{rules:[7],inclusive:!1},title:{rules:[5],inclusive:!1},md_string:{rules:[22,23],inclusive:!1},string:{rules:[25,26],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,6,8,10,13,14,15,16,17,18,19,20,21,24,27,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],inclusive:!0}}};return X}();At.lexer=ye;function Pt(){this.yy={}}return r(Pt,"Parser"),Pt.prototype=At,At.Parser=Pt,new Pt}();Et.parser=Et;var he=Et;var V=ie(),qt=class{constructor(){this.classes=new Map;this.config=this.getDefaultConfig(),this.themeConfig=this.getDefaultThemeConfig(),this.data=this.getDefaultData()}static{r(this,"QuadrantBuilder")}getDefaultData(){return{titleText:"",quadrant1Text:"",quadrant2Text:"",quadrant3Text:"",quadrant4Text:"",xAxisLeftText:"",xAxisRightText:"",yAxisBottomText:"",yAxisTopText:"",points:[]}}getDefaultConfig(){return{showXAxis:!0,showYAxis:!0,showTitle:!0,chartHeight:L.quadrantChart?.chartWidth||500,chartWidth:L.quadrantChart?.chartHeight||500,titlePadding:L.quadrantChart?.titlePadding||10,titleFontSize:L.quadrantChart?.titleFontSize||20,quadrantPadding:L.quadrantChart?.quadrantPadding||5,xAxisLabelPadding:L.quadrantChart?.xAxisLabelPadding||5,yAxisLabelPadding:L.quadrantChart?.yAxisLabelPadding||5,xAxisLabelFontSize:L.quadrantChart?.xAxisLabelFontSize||16,yAxisLabelFontSize:L.quadrantChart?.yAxisLabelFontSize||16,quadrantLabelFontSize:L.quadrantChart?.quadrantLabelFontSize||16,quadrantTextTopPadding:L.quadrantChart?.quadrantTextTopPadding||5,pointTextPadding:L.quadrantChart?.pointTextPadding||5,pointLabelFontSize:L.quadrantChart?.pointLabelFontSize||12,pointRadius:L.quadrantChart?.pointRadius||5,xAxisPosition:L.quadrantChart?.xAxisPosition||"top",yAxisPosition:L.quadrantChart?.yAxisPosition||"left",quadrantInternalBorderStrokeWidth:L.quadrantChart?.quadrantInternalBorderStrokeWidth||1,quadrantExternalBorderStrokeWidth:L.quadrantChart?.quadrantExternalBorderStrokeWidth||2}}getDefaultThemeConfig(){return{quadrant1Fill:V.quadrant1Fill,quadrant2Fill:V.quadrant2Fill,quadrant3Fill:V.quadrant3Fill,quadrant4Fill:V.quadrant4Fill,quadrant1TextFill:V.quadrant1TextFill,quadrant2TextFill:V.quadrant2TextFill,quadrant3TextFill:V.quadrant3TextFill,quadrant4TextFill:V.quadrant4TextFill,quadrantPointFill:V.quadrantPointFill,quadrantPointTextFill:V.quadrantPointTextFill,quadrantXAxisTextFill:V.quadrantXAxisTextFill,quadrantYAxisTextFill:V.quadrantYAxisTextFill,quadrantTitleFill:V.quadrantTitleFill,quadrantInternalBorderStrokeFill:V.quadrantInternalBorderStrokeFill,quadrantExternalBorderStrokeFill:V.quadrantExternalBorderStrokeFill}}clear(){this.config=this.getDefaultConfig(),this.themeConfig=this.getDefaultThemeConfig(),this.data=this.getDefaultData(),this.classes=new Map,lt.info("clear called")}setData(n){this.data={...this.data,...n}}addPoints(n){this.data.points=[...n,...this.data.points]}addClass(n,f){this.classes.set(n,f)}setConfig(n){lt.trace("setConfig called with: ",n),this.config={...this.config,...n}}setThemeConfig(n){lt.trace("setThemeConfig called with: ",n),this.themeConfig={...this.themeConfig,...n}}calculateSpace(n,f,u,c){let g=this.config.xAxisLabelPadding*2+this.config.xAxisLabelFontSize,y={top:n==="top"&&f?g:0,bottom:n==="bottom"&&f?g:0},S=this.config.yAxisLabelPadding*2+this.config.yAxisLabelFontSize,i={left:this.config.yAxisPosition==="left"&&u?S:0,right:this.config.yAxisPosition==="right"&&u?S:0},A=this.config.titleFontSize+this.config.titlePadding*2,h={top:c?A:0},T=this.config.quadrantPadding+i.left,m=this.config.quadrantPadding+y.top+h.top,b=this.config.chartWidth-this.config.quadrantPadding*2-i.left-i.right,q=this.config.chartHeight-this.config.quadrantPadding*2-y.top-y.bottom-h.top,p=b/2,K=q/2;return{xAxisSpace:y,yAxisSpace:i,titleSpace:h,quadrantSpace:{quadrantLeft:T,quadrantTop:m,quadrantWidth:b,quadrantHalfWidth:p,quadrantHeight:q,quadrantHalfHeight:K}}}getAxisLabels(n,f,u,c){let{quadrantSpace:g,titleSpace:y}=c,{quadrantHalfHeight:S,quadrantHeight:i,quadrantLeft:A,quadrantHalfWidth:h,quadrantTop:T,quadrantWidth:m}=g,b=!!this.data.xAxisRightText,q=!!this.data.yAxisTopText,p=[];return this.data.xAxisLeftText&&f&&p.push({text:this.data.xAxisLeftText,fill:this.themeConfig.quadrantXAxisTextFill,x:A+(b?h/2:0),y:n==="top"?this.config.xAxisLabelPadding+y.top:this.config.xAxisLabelPadding+T+i+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:b?"center":"left",horizontalPos:"top",rotation:0}),this.data.xAxisRightText&&f&&p.push({text:this.data.xAxisRightText,fill:this.themeConfig.quadrantXAxisTextFill,x:A+h+(b?h/2:0),y:n==="top"?this.config.xAxisLabelPadding+y.top:this.config.xAxisLabelPadding+T+i+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:b?"center":"left",horizontalPos:"top",rotation:0}),this.data.yAxisBottomText&&u&&p.push({text:this.data.yAxisBottomText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+A+m+this.config.quadrantPadding,y:T+i-(q?S/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:q?"center":"left",horizontalPos:"top",rotation:-90}),this.data.yAxisTopText&&u&&p.push({text:this.data.yAxisTopText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+A+m+this.config.quadrantPadding,y:T+S-(q?S/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:q?"center":"left",horizontalPos:"top",rotation:-90}),p}getQuadrants(n){let{quadrantSpace:f}=n,{quadrantHalfHeight:u,quadrantLeft:c,quadrantHalfWidth:g,quadrantTop:y}=f,S=[{text:{text:this.data.quadrant1Text,fill:this.themeConfig.quadrant1TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:c+g,y,width:g,height:u,fill:this.themeConfig.quadrant1Fill},{text:{text:this.data.quadrant2Text,fill:this.themeConfig.quadrant2TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:c,y,width:g,height:u,fill:this.themeConfig.quadrant2Fill},{text:{text:this.data.quadrant3Text,fill:this.themeConfig.quadrant3TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:c,y:y+u,width:g,height:u,fill:this.themeConfig.quadrant3Fill},{text:{text:this.data.quadrant4Text,fill:this.themeConfig.quadrant4TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:c+g,y:y+u,width:g,height:u,fill:this.themeConfig.quadrant4Fill}];for(let i of S)i.text.x=i.x+i.width/2,this.data.points.length===0?(i.text.y=i.y+i.height/2,i.text.horizontalPos="middle"):(i.text.y=i.y+this.config.quadrantTextTopPadding,i.text.horizontalPos="top");return S}getQuadrantPoints(n){let{quadrantSpace:f}=n,{quadrantHeight:u,quadrantLeft:c,quadrantTop:g,quadrantWidth:y}=f,S=Dt().domain([0,1]).range([c,y+c]),i=Dt().domain([0,1]).range([u+g,g]);return this.data.points.map(h=>{let T=this.classes.get(h.className);return T&&(h={...T,...h}),{x:S(h.x),y:i(h.y),fill:h.color??this.themeConfig.quadrantPointFill,radius:h.radius??this.config.pointRadius,text:{text:h.text,fill:this.themeConfig.quadrantPointTextFill,x:S(h.x),y:i(h.y)+this.config.pointTextPadding,verticalPos:"center",horizontalPos:"top",fontSize:this.config.pointLabelFontSize,rotation:0},strokeColor:h.strokeColor??this.themeConfig.quadrantPointFill,strokeWidth:h.strokeWidth??"0px"}})}getBorders(n){let f=this.config.quadrantExternalBorderStrokeWidth/2,{quadrantSpace:u}=n,{quadrantHalfHeight:c,quadrantHeight:g,quadrantLeft:y,quadrantHalfWidth:S,quadrantTop:i,quadrantWidth:A}=u;return[{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:y-f,y1:i,x2:y+A+f,y2:i},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:y+A,y1:i+f,x2:y+A,y2:i+g-f},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:y-f,y1:i+g,x2:y+A+f,y2:i+g},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:y,y1:i+f,x2:y,y2:i+g-f},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:y+S,y1:i+f,x2:y+S,y2:i+g-f},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:y+f,y1:i+c,x2:y+A-f,y2:i+c}]}getTitle(n){if(n)return{text:this.data.titleText,fill:this.themeConfig.quadrantTitleFill,fontSize:this.config.titleFontSize,horizontalPos:"top",verticalPos:"center",rotation:0,y:this.config.titlePadding,x:this.config.chartWidth/2}}build(){let n=this.config.showXAxis&&!!(this.data.xAxisLeftText||this.data.xAxisRightText),f=this.config.showYAxis&&!!(this.data.yAxisTopText||this.data.yAxisBottomText),u=this.config.showTitle&&!!this.data.titleText,c=this.data.points.length>0?"bottom":this.config.xAxisPosition,g=this.calculateSpace(c,n,f,u);return{points:this.getQuadrantPoints(g),quadrants:this.getQuadrants(g),axisLabels:this.getAxisLabels(c,n,f,g),borderLines:this.getBorders(g),title:this.getTitle(u)}}};var G=class extends Error{static{r(this,"InvalidStyleError")}constructor(n,f,u){super(`value for ${n} ${f} is invalid, please use a valid ${u}`),this.name="InvalidStyleError"}};function zt(t){return!/^#?([\dA-Fa-f]{6}|[\dA-Fa-f]{3})$/.test(t)}r(zt,"validateHexCode");function xe(t){return!/^\d+$/.test(t)}r(xe,"validateNumber");function fe(t){return!/^\d+px$/.test(t)}r(fe,"validateSizeInPixels");var ke=xt();function j(t){return ne(t.trim(),ke)}r(j,"textSanitizer");var E=new qt;function Se(t){E.setData({quadrant1Text:j(t.text)})}r(Se,"setQuadrant1Text");function Ae(t){E.setData({quadrant2Text:j(t.text)})}r(Ae,"setQuadrant2Text");function Pe(t){E.setData({quadrant3Text:j(t.text)})}r(Pe,"setQuadrant3Text");function _e(t){E.setData({quadrant4Text:j(t.text)})}r(_e,"setQuadrant4Text");function Fe(t){E.setData({xAxisLeftText:j(t.text)})}r(Fe,"setXAxisLeftText");function Qe(t){E.setData({xAxisRightText:j(t.text)})}r(Qe,"setXAxisRightText");function Ce(t){E.setData({yAxisTopText:j(t.text)})}r(Ce,"setYAxisTopText");function Le(t){E.setData({yAxisBottomText:j(t.text)})}r(Le,"setYAxisBottomText");function Vt(t){let n={};for(let f of t){let[u,c]=f.trim().split(/\s*:\s*/);if(u==="radius"){if(xe(c))throw new G(u,c,"number");n.radius=parseInt(c)}else if(u==="color"){if(zt(c))throw new G(u,c,"hex code");n.color=c}else if(u==="stroke-color"){if(zt(c))throw new G(u,c,"hex code");n.strokeColor=c}else if(u==="stroke-width"){if(fe(c))throw new G(u,c,"number of pixels (eg. 10px)");n.strokeWidth=c}else throw new Error(`style named ${u} is not supported.`)}return n}r(Vt,"parseStyles");function ve(t,n,f,u,c){let g=Vt(c);E.addPoints([{x:f,y:u,text:j(t.text),className:n,...g}])}r(ve,"addPoint");function De(t,n){E.addClass(t,Vt(n))}r(De,"addClass");function Ee(t){E.setConfig({chartWidth:t})}r(Ee,"setWidth");function ze(t){E.setConfig({chartHeight:t})}r(ze,"setHeight");function Ve(){let t=xt(),{themeVariables:n,quadrantChart:f}=t;return f&&E.setConfig(f),E.setThemeConfig({quadrant1Fill:n.quadrant1Fill,quadrant2Fill:n.quadrant2Fill,quadrant3Fill:n.quadrant3Fill,quadrant4Fill:n.quadrant4Fill,quadrant1TextFill:n.quadrant1TextFill,quadrant2TextFill:n.quadrant2TextFill,quadrant3TextFill:n.quadrant3TextFill,quadrant4TextFill:n.quadrant4TextFill,quadrantPointFill:n.quadrantPointFill,quadrantPointTextFill:n.quadrantPointTextFill,quadrantXAxisTextFill:n.quadrantXAxisTextFill,quadrantYAxisTextFill:n.quadrantYAxisTextFill,quadrantExternalBorderStrokeFill:n.quadrantExternalBorderStrokeFill,quadrantInternalBorderStrokeFill:n.quadrantInternalBorderStrokeFill,quadrantTitleFill:n.quadrantTitleFill}),E.setData({titleText:vt()}),E.build()}r(Ve,"getQuadrantData");var Be=r(function(){E.clear(),se()},"clear"),pe={setWidth:Ee,setHeight:ze,setQuadrant1Text:Se,setQuadrant2Text:Ae,setQuadrant3Text:Pe,setQuadrant4Text:_e,setXAxisLeftText:Fe,setXAxisRightText:Qe,setYAxisTopText:Ce,setYAxisBottomText:Le,parseStyles:Vt,addPoint:ve,addClass:De,getQuadrantData:Ve,clear:Be,setAccTitle:oe,getAccTitle:le,setDiagramTitle:ce,getDiagramTitle:vt,getAccDescription:ue,setAccDescription:de};var we=r((t,n,f,u)=>{function c(a){return a==="top"?"hanging":"middle"}r(c,"getDominantBaseLine");function g(a){return a==="left"?"start":"middle"}r(g,"getTextAnchor");function y(a){return`translate(${a.x}, ${a.y}) rotate(${a.rotation||0})`}r(y,"getTransformation");let S=xt();lt.debug(`Rendering quadrant chart
`+t);let i=S.securityLevel,A;i==="sandbox"&&(A=bt("#i"+n));let T=(i==="sandbox"?bt(A.nodes()[0].contentDocument.body):bt("body")).select(`[id="${n}"]`),m=T.append("g").attr("class","main"),b=S.quadrantChart?.chartWidth??500,q=S.quadrantChart?.chartHeight??500;re(T,q,b,S.quadrantChart?.useMaxWidth??!0),T.attr("viewBox","0 0 "+b+" "+q),u.db.setHeight(q),u.db.setWidth(b);let p=u.db.getQuadrantData(),K=m.append("g").attr("class","quadrants"),dt=m.append("g").attr("class","border"),ft=m.append("g").attr("class","data-points"),pt=m.append("g").attr("class","labels"),gt=m.append("g").attr("class","title");p.title&&gt.append("text").attr("x",0).attr("y",0).attr("fill",p.title.fill).attr("font-size",p.title.fontSize).attr("dominant-baseline",c(p.title.horizontalPos)).attr("text-anchor",g(p.title.verticalPos)).attr("transform",y(p.title)).text(p.title.text),p.borderLines&&dt.selectAll("line").data(p.borderLines).enter().append("line").attr("x1",a=>a.x1).attr("y1",a=>a.y1).attr("x2",a=>a.x2).attr("y2",a=>a.y2).style("stroke",a=>a.strokeFill).style("stroke-width",a=>a.strokeWidth);let ut=K.selectAll("g.quadrant").data(p.quadrants).enter().append("g").attr("class","quadrant");ut.append("rect").attr("x",a=>a.x).attr("y",a=>a.y).attr("width",a=>a.width).attr("height",a=>a.height).attr("fill",a=>a.fill),ut.append("text").attr("x",0).attr("y",0).attr("fill",a=>a.text.fill).attr("font-size",a=>a.text.fontSize).attr("dominant-baseline",a=>c(a.text.horizontalPos)).attr("text-anchor",a=>g(a.text.verticalPos)).attr("transform",a=>y(a.text)).text(a=>a.text.text),pt.selectAll("g.label").data(p.axisLabels).enter().append("g").attr("class","label").append("text").attr("x",0).attr("y",0).text(a=>a.text).attr("fill",a=>a.fill).attr("font-size",a=>a.fontSize).attr("dominant-baseline",a=>c(a.horizontalPos)).attr("text-anchor",a=>g(a.verticalPos)).attr("transform",a=>y(a));let ct=ft.selectAll("g.data-point").data(p.points).enter().append("g").attr("class","data-point");ct.append("circle").attr("cx",a=>a.x).attr("cy",a=>a.y).attr("r",a=>a.radius).attr("fill",a=>a.fill).attr("stroke",a=>a.strokeColor).attr("stroke-width",a=>a.strokeWidth),ct.append("text").attr("x",0).attr("y",0).text(a=>a.text.text).attr("fill",a=>a.text.fill).attr("font-size",a=>a.text.fontSize).attr("dominant-baseline",a=>c(a.text.horizontalPos)).attr("text-anchor",a=>g(a.text.verticalPos)).attr("transform",a=>y(a.text))},"draw"),ge={draw:we};var ha={parser:he,db:pe,renderer:ge,styles:r(()=>"","styles")};export{ha as diagram};
