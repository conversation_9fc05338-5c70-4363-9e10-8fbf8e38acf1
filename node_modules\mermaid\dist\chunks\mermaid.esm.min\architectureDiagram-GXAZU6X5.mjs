import{a as Ne}from"./chunk-MBJCTAW2.mjs";import{a as _e}from"./chunk-4KE642ED.mjs";import{a as je}from"./chunk-RPR3SYFS.mjs";import"./chunk-P6UA7CIO.mjs";import"./chunk-4AZJR7FE.mjs";import"./chunk-XAVRVNBM.mjs";import"./chunk-JHXWDPGM.mjs";import"./chunk-V4WPH7A7.mjs";import{a as tr}from"./chunk-U7M5BGKE.mjs";import{a as He,b as We,c as ue,g as de}from"./chunk-IXVBHSNP.mjs";import{l as Ke}from"./chunk-3R3PQ5PD.mjs";import"./chunk-TI4EEUUG.mjs";import{O as Ve,Q as Be,R as ze,S as $e,T as ke,U as Ze,V as Je,W as qe,Y as ge,c as fe,ha as Qe,l as Ye,t as Ue,y as Xe}from"./chunk-F632ZYSZ.mjs";import"./chunk-WSUO5DN6.mjs";import"./chunk-JCWWVGLQ.mjs";import"./chunk-L6MQJ2ZU.mjs";import"./chunk-5ZJXQJOJ.mjs";import"./chunk-YPUTD6PB.mjs";import"./chunk-6BY5RJGC.mjs";import{a as L,b as Ae,e as yr}from"./chunk-GTKDMUJJ.mjs";var xe=Ae((se,we)=>{"use strict";L(function(C,T){typeof se=="object"&&typeof we=="object"?we.exports=T():typeof define=="function"&&define.amd?define([],T):typeof se=="object"?se.layoutBase=T():C.layoutBase=T()},"webpackUniversalModuleDefinition")(se,function(){return function(D){var C={};function T(g){if(C[g])return C[g].exports;var o=C[g]={i:g,l:!1,exports:{}};return D[g].call(o.exports,o,o.exports,T),o.l=!0,o.exports}return L(T,"__webpack_require__"),T.m=D,T.c=C,T.i=function(g){return g},T.d=function(g,o,n){T.o(g,o)||Object.defineProperty(g,o,{configurable:!1,enumerable:!0,get:n})},T.n=function(g){var o=g&&g.__esModule?L(function(){return g.default},"getDefault"):L(function(){return g},"getModuleExports");return T.d(o,"a",o),o},T.o=function(g,o){return Object.prototype.hasOwnProperty.call(g,o)},T.p="",T(T.s=28)}([function(D,C,T){"use strict";function g(){}L(g,"LayoutConstants"),g.QUALITY=1,g.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,g.DEFAULT_INCREMENTAL=!1,g.DEFAULT_ANIMATION_ON_LAYOUT=!0,g.DEFAULT_ANIMATION_DURING_LAYOUT=!1,g.DEFAULT_ANIMATION_PERIOD=50,g.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,g.DEFAULT_GRAPH_MARGIN=15,g.NODE_DIMENSIONS_INCLUDE_LABELS=!1,g.SIMPLE_NODE_SIZE=40,g.SIMPLE_NODE_HALF_SIZE=g.SIMPLE_NODE_SIZE/2,g.EMPTY_COMPOUND_NODE_SIZE=40,g.MIN_EDGE_LENGTH=1,g.WORLD_BOUNDARY=1e6,g.INITIAL_WORLD_BOUNDARY=g.WORLD_BOUNDARY/1e3,g.WORLD_CENTER_X=1200,g.WORLD_CENTER_Y=900,D.exports=g},function(D,C,T){"use strict";var g=T(2),o=T(8),n=T(9);function r(c,t,u){g.call(this,u),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=u,this.bendpoints=[],this.source=c,this.target=t}L(r,"LEdge"),r.prototype=Object.create(g.prototype);for(var a in g)r[a]=g[a];r.prototype.getSource=function(){return this.source},r.prototype.getTarget=function(){return this.target},r.prototype.isInterGraph=function(){return this.isInterGraph},r.prototype.getLength=function(){return this.length},r.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},r.prototype.getBendpoints=function(){return this.bendpoints},r.prototype.getLca=function(){return this.lca},r.prototype.getSourceInLca=function(){return this.sourceInLca},r.prototype.getTargetInLca=function(){return this.targetInLca},r.prototype.getOtherEnd=function(c){if(this.source===c)return this.target;if(this.target===c)return this.source;throw"Node is not incident with this edge"},r.prototype.getOtherEndInGraph=function(c,t){for(var u=this.getOtherEnd(c),i=t.getGraphManager().getRoot();;){if(u.getOwner()==t)return u;if(u.getOwner()==i)break;u=u.getOwner().getParent()}return null},r.prototype.updateLength=function(){var c=new Array(4);this.isOverlapingSourceAndTarget=o.getIntersection(this.target.getRect(),this.source.getRect(),c),this.isOverlapingSourceAndTarget||(this.lengthX=c[0]-c[2],this.lengthY=c[1]-c[3],Math.abs(this.lengthX)<1&&(this.lengthX=n.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=n.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},r.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=n.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=n.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},D.exports=r},function(D,C,T){"use strict";function g(o){this.vGraphObject=o}L(g,"LGraphObject"),D.exports=g},function(D,C,T){"use strict";var g=T(2),o=T(10),n=T(13),r=T(0),a=T(16),c=T(5);function t(i,e,l,f){l==null&&f==null&&(f=e),g.call(this,f),i.graphManager!=null&&(i=i.graphManager),this.estimatedSize=o.MIN_VALUE,this.inclusionTreeDepth=o.MAX_VALUE,this.vGraphObject=f,this.edges=[],this.graphManager=i,l!=null&&e!=null?this.rect=new n(e.x,e.y,l.width,l.height):this.rect=new n}L(t,"LNode"),t.prototype=Object.create(g.prototype);for(var u in g)t[u]=g[u];t.prototype.getEdges=function(){return this.edges},t.prototype.getChild=function(){return this.child},t.prototype.getOwner=function(){return this.owner},t.prototype.getWidth=function(){return this.rect.width},t.prototype.setWidth=function(i){this.rect.width=i},t.prototype.getHeight=function(){return this.rect.height},t.prototype.setHeight=function(i){this.rect.height=i},t.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},t.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},t.prototype.getCenter=function(){return new c(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},t.prototype.getLocation=function(){return new c(this.rect.x,this.rect.y)},t.prototype.getRect=function(){return this.rect},t.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},t.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},t.prototype.setRect=function(i,e){this.rect.x=i.x,this.rect.y=i.y,this.rect.width=e.width,this.rect.height=e.height},t.prototype.setCenter=function(i,e){this.rect.x=i-this.rect.width/2,this.rect.y=e-this.rect.height/2},t.prototype.setLocation=function(i,e){this.rect.x=i,this.rect.y=e},t.prototype.moveBy=function(i,e){this.rect.x+=i,this.rect.y+=e},t.prototype.getEdgeListToNode=function(i){var e=[],l,f=this;return f.edges.forEach(function(h){if(h.target==i){if(h.source!=f)throw"Incorrect edge source!";e.push(h)}}),e},t.prototype.getEdgesBetween=function(i){var e=[],l,f=this;return f.edges.forEach(function(h){if(!(h.source==f||h.target==f))throw"Incorrect edge source and/or target";(h.target==i||h.source==i)&&e.push(h)}),e},t.prototype.getNeighborsList=function(){var i=new Set,e=this;return e.edges.forEach(function(l){if(l.source==e)i.add(l.target);else{if(l.target!=e)throw"Incorrect incidency!";i.add(l.source)}}),i},t.prototype.withChildren=function(){var i=new Set,e,l;if(i.add(this),this.child!=null)for(var f=this.child.getNodes(),h=0;h<f.length;h++)e=f[h],l=e.withChildren(),l.forEach(function(A){i.add(A)});return i},t.prototype.getNoOfChildren=function(){var i=0,e;if(this.child==null)i=1;else for(var l=this.child.getNodes(),f=0;f<l.length;f++)e=l[f],i+=e.getNoOfChildren();return i==0&&(i=1),i},t.prototype.getEstimatedSize=function(){if(this.estimatedSize==o.MIN_VALUE)throw"assert failed";return this.estimatedSize},t.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},t.prototype.scatter=function(){var i,e,l=-r.INITIAL_WORLD_BOUNDARY,f=r.INITIAL_WORLD_BOUNDARY;i=r.WORLD_CENTER_X+a.nextDouble()*(f-l)+l;var h=-r.INITIAL_WORLD_BOUNDARY,A=r.INITIAL_WORLD_BOUNDARY;e=r.WORLD_CENTER_Y+a.nextDouble()*(A-h)+h,this.rect.x=i,this.rect.y=e},t.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var i=this.getChild();if(i.updateBounds(!0),this.rect.x=i.getLeft(),this.rect.y=i.getTop(),this.setWidth(i.getRight()-i.getLeft()),this.setHeight(i.getBottom()-i.getTop()),r.NODE_DIMENSIONS_INCLUDE_LABELS){var e=i.getRight()-i.getLeft(),l=i.getBottom()-i.getTop();this.labelWidth&&(this.labelPosHorizontal=="left"?(this.rect.x-=this.labelWidth,this.setWidth(e+this.labelWidth)):this.labelPosHorizontal=="center"&&this.labelWidth>e?(this.rect.x-=(this.labelWidth-e)/2,this.setWidth(this.labelWidth)):this.labelPosHorizontal=="right"&&this.setWidth(e+this.labelWidth)),this.labelHeight&&(this.labelPosVertical=="top"?(this.rect.y-=this.labelHeight,this.setHeight(l+this.labelHeight)):this.labelPosVertical=="center"&&this.labelHeight>l?(this.rect.y-=(this.labelHeight-l)/2,this.setHeight(this.labelHeight)):this.labelPosVertical=="bottom"&&this.setHeight(l+this.labelHeight))}}},t.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==o.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},t.prototype.transform=function(i){var e=this.rect.x;e>r.WORLD_BOUNDARY?e=r.WORLD_BOUNDARY:e<-r.WORLD_BOUNDARY&&(e=-r.WORLD_BOUNDARY);var l=this.rect.y;l>r.WORLD_BOUNDARY?l=r.WORLD_BOUNDARY:l<-r.WORLD_BOUNDARY&&(l=-r.WORLD_BOUNDARY);var f=new c(e,l),h=i.inverseTransformPoint(f);this.setLocation(h.x,h.y)},t.prototype.getLeft=function(){return this.rect.x},t.prototype.getRight=function(){return this.rect.x+this.rect.width},t.prototype.getTop=function(){return this.rect.y},t.prototype.getBottom=function(){return this.rect.y+this.rect.height},t.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},D.exports=t},function(D,C,T){"use strict";var g=T(0);function o(){}L(o,"FDLayoutConstants");for(var n in g)o[n]=g[n];o.MAX_ITERATIONS=2500,o.DEFAULT_EDGE_LENGTH=50,o.DEFAULT_SPRING_STRENGTH=.45,o.DEFAULT_REPULSION_STRENGTH=4500,o.DEFAULT_GRAVITY_STRENGTH=.4,o.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,o.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,o.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,o.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,o.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,o.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,o.COOLING_ADAPTATION_FACTOR=.33,o.ADAPTATION_LOWER_NODE_LIMIT=1e3,o.ADAPTATION_UPPER_NODE_LIMIT=5e3,o.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,o.MAX_NODE_DISPLACEMENT=o.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,o.MIN_REPULSION_DIST=o.DEFAULT_EDGE_LENGTH/10,o.CONVERGENCE_CHECK_PERIOD=100,o.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,o.MIN_EDGE_LENGTH=1,o.GRID_CALCULATION_CHECK_PERIOD=10,D.exports=o},function(D,C,T){"use strict";function g(o,n){o==null&&n==null?(this.x=0,this.y=0):(this.x=o,this.y=n)}L(g,"PointD"),g.prototype.getX=function(){return this.x},g.prototype.getY=function(){return this.y},g.prototype.setX=function(o){this.x=o},g.prototype.setY=function(o){this.y=o},g.prototype.getDifference=function(o){return new DimensionD(this.x-o.x,this.y-o.y)},g.prototype.getCopy=function(){return new g(this.x,this.y)},g.prototype.translate=function(o){return this.x+=o.width,this.y+=o.height,this},D.exports=g},function(D,C,T){"use strict";var g=T(2),o=T(10),n=T(0),r=T(7),a=T(3),c=T(1),t=T(13),u=T(12),i=T(11);function e(f,h,A){g.call(this,A),this.estimatedSize=o.MIN_VALUE,this.margin=n.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=f,h!=null&&h instanceof r?this.graphManager=h:h!=null&&h instanceof Layout&&(this.graphManager=h.graphManager)}L(e,"LGraph"),e.prototype=Object.create(g.prototype);for(var l in g)e[l]=g[l];e.prototype.getNodes=function(){return this.nodes},e.prototype.getEdges=function(){return this.edges},e.prototype.getGraphManager=function(){return this.graphManager},e.prototype.getParent=function(){return this.parent},e.prototype.getLeft=function(){return this.left},e.prototype.getRight=function(){return this.right},e.prototype.getTop=function(){return this.top},e.prototype.getBottom=function(){return this.bottom},e.prototype.isConnected=function(){return this.isConnected},e.prototype.add=function(f,h,A){if(h==null&&A==null){var v=f;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(v)>-1)throw"Node already in graph!";return v.owner=this,this.getNodes().push(v),v}else{var y=f;if(!(this.getNodes().indexOf(h)>-1&&this.getNodes().indexOf(A)>-1))throw"Source or target not in graph!";if(!(h.owner==A.owner&&h.owner==this))throw"Both owners must be this graph!";return h.owner!=A.owner?null:(y.source=h,y.target=A,y.isInterGraph=!1,this.getEdges().push(y),h.edges.push(y),A!=h&&A.edges.push(y),y)}},e.prototype.remove=function(f){var h=f;if(f instanceof a){if(h==null)throw"Node is null!";if(!(h.owner!=null&&h.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var A=h.edges.slice(),v,y=A.length,N=0;N<y;N++)v=A[N],v.isInterGraph?this.graphManager.remove(v):v.source.owner.remove(v);var S=this.nodes.indexOf(h);if(S==-1)throw"Node not in owner node list!";this.nodes.splice(S,1)}else if(f instanceof c){var v=f;if(v==null)throw"Edge is null!";if(!(v.source!=null&&v.target!=null))throw"Source and/or target is null!";if(!(v.source.owner!=null&&v.target.owner!=null&&v.source.owner==this&&v.target.owner==this))throw"Source and/or target owner is invalid!";var M=v.source.edges.indexOf(v),b=v.target.edges.indexOf(v);if(!(M>-1&&b>-1))throw"Source and/or target doesn't know this edge!";v.source.edges.splice(M,1),v.target!=v.source&&v.target.edges.splice(b,1);var S=v.source.owner.getEdges().indexOf(v);if(S==-1)throw"Not in owner's edge list!";v.source.owner.getEdges().splice(S,1)}},e.prototype.updateLeftTop=function(){for(var f=o.MAX_VALUE,h=o.MAX_VALUE,A,v,y,N=this.getNodes(),S=N.length,M=0;M<S;M++){var b=N[M];A=b.getTop(),v=b.getLeft(),f>A&&(f=A),h>v&&(h=v)}return f==o.MAX_VALUE?null:(N[0].getParent().paddingLeft!=null?y=N[0].getParent().paddingLeft:y=this.margin,this.left=h-y,this.top=f-y,new u(this.left,this.top))},e.prototype.updateBounds=function(f){for(var h=o.MAX_VALUE,A=-o.MAX_VALUE,v=o.MAX_VALUE,y=-o.MAX_VALUE,N,S,M,b,$,X=this.nodes,et=X.length,R=0;R<et;R++){var J=X[R];f&&J.child!=null&&J.updateBounds(),N=J.getLeft(),S=J.getRight(),M=J.getTop(),b=J.getBottom(),h>N&&(h=N),A<S&&(A=S),v>M&&(v=M),y<b&&(y=b)}var s=new t(h,v,A-h,y-v);h==o.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),X[0].getParent().paddingLeft!=null?$=X[0].getParent().paddingLeft:$=this.margin,this.left=s.x-$,this.right=s.x+s.width+$,this.top=s.y-$,this.bottom=s.y+s.height+$},e.calculateBounds=function(f){for(var h=o.MAX_VALUE,A=-o.MAX_VALUE,v=o.MAX_VALUE,y=-o.MAX_VALUE,N,S,M,b,$=f.length,X=0;X<$;X++){var et=f[X];N=et.getLeft(),S=et.getRight(),M=et.getTop(),b=et.getBottom(),h>N&&(h=N),A<S&&(A=S),v>M&&(v=M),y<b&&(y=b)}var R=new t(h,v,A-h,y-v);return R},e.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},e.prototype.getEstimatedSize=function(){if(this.estimatedSize==o.MIN_VALUE)throw"assert failed";return this.estimatedSize},e.prototype.calcEstimatedSize=function(){for(var f=0,h=this.nodes,A=h.length,v=0;v<A;v++){var y=h[v];f+=y.calcEstimatedSize()}return f==0?this.estimatedSize=n.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=f/Math.sqrt(this.nodes.length),this.estimatedSize},e.prototype.updateConnected=function(){var f=this;if(this.nodes.length==0){this.isConnected=!0;return}var h=new i,A=new Set,v=this.nodes[0],y,N,S=v.withChildren();for(S.forEach(function(R){h.push(R),A.add(R)});h.length!==0;){v=h.shift(),y=v.getEdges();for(var M=y.length,b=0;b<M;b++){var $=y[b];if(N=$.getOtherEndInGraph(v,this),N!=null&&!A.has(N)){var X=N.withChildren();X.forEach(function(R){h.push(R),A.add(R)})}}}if(this.isConnected=!1,A.size>=this.nodes.length){var et=0;A.forEach(function(R){R.owner==f&&et++}),et==this.nodes.length&&(this.isConnected=!0)}},D.exports=e},function(D,C,T){"use strict";var g,o=T(1);function n(r){g=T(6),this.layout=r,this.graphs=[],this.edges=[]}L(n,"LGraphManager"),n.prototype.addRoot=function(){var r=this.layout.newGraph(),a=this.layout.newNode(null),c=this.add(r,a);return this.setRootGraph(c),this.rootGraph},n.prototype.add=function(r,a,c,t,u){if(c==null&&t==null&&u==null){if(r==null)throw"Graph is null!";if(a==null)throw"Parent node is null!";if(this.graphs.indexOf(r)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(r),r.parent!=null)throw"Already has a parent!";if(a.child!=null)throw"Already has a child!";return r.parent=a,a.child=r,r}else{u=c,t=a,c=r;var i=t.getOwner(),e=u.getOwner();if(!(i!=null&&i.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(e!=null&&e.getGraphManager()==this))throw"Target not in this graph mgr!";if(i==e)return c.isInterGraph=!1,i.add(c,t,u);if(c.isInterGraph=!0,c.source=t,c.target=u,this.edges.indexOf(c)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(c),!(c.source!=null&&c.target!=null))throw"Edge source and/or target is null!";if(!(c.source.edges.indexOf(c)==-1&&c.target.edges.indexOf(c)==-1))throw"Edge already in source and/or target incidency list!";return c.source.edges.push(c),c.target.edges.push(c),c}},n.prototype.remove=function(r){if(r instanceof g){var a=r;if(a.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(a==this.rootGraph||a.parent!=null&&a.parent.graphManager==this))throw"Invalid parent node!";var c=[];c=c.concat(a.getEdges());for(var t,u=c.length,i=0;i<u;i++)t=c[i],a.remove(t);var e=[];e=e.concat(a.getNodes());var l;u=e.length;for(var i=0;i<u;i++)l=e[i],a.remove(l);a==this.rootGraph&&this.setRootGraph(null);var f=this.graphs.indexOf(a);this.graphs.splice(f,1),a.parent=null}else if(r instanceof o){if(t=r,t==null)throw"Edge is null!";if(!t.isInterGraph)throw"Not an inter-graph edge!";if(!(t.source!=null&&t.target!=null))throw"Source and/or target is null!";if(!(t.source.edges.indexOf(t)!=-1&&t.target.edges.indexOf(t)!=-1))throw"Source and/or target doesn't know this edge!";var f=t.source.edges.indexOf(t);if(t.source.edges.splice(f,1),f=t.target.edges.indexOf(t),t.target.edges.splice(f,1),!(t.source.owner!=null&&t.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(t.source.owner.getGraphManager().edges.indexOf(t)==-1)throw"Not in owner graph manager's edge list!";var f=t.source.owner.getGraphManager().edges.indexOf(t);t.source.owner.getGraphManager().edges.splice(f,1)}},n.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},n.prototype.getGraphs=function(){return this.graphs},n.prototype.getAllNodes=function(){if(this.allNodes==null){for(var r=[],a=this.getGraphs(),c=a.length,t=0;t<c;t++)r=r.concat(a[t].getNodes());this.allNodes=r}return this.allNodes},n.prototype.resetAllNodes=function(){this.allNodes=null},n.prototype.resetAllEdges=function(){this.allEdges=null},n.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},n.prototype.getAllEdges=function(){if(this.allEdges==null){for(var r=[],a=this.getGraphs(),c=a.length,t=0;t<a.length;t++)r=r.concat(a[t].getEdges());r=r.concat(this.edges),this.allEdges=r}return this.allEdges},n.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},n.prototype.setAllNodesToApplyGravitation=function(r){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=r},n.prototype.getRoot=function(){return this.rootGraph},n.prototype.setRootGraph=function(r){if(r.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=r,r.parent==null&&(r.parent=this.layout.newNode("Root node"))},n.prototype.getLayout=function(){return this.layout},n.prototype.isOneAncestorOfOther=function(r,a){if(!(r!=null&&a!=null))throw"assert failed";if(r==a)return!0;var c=r.getOwner(),t;do{if(t=c.getParent(),t==null)break;if(t==a)return!0;if(c=t.getOwner(),c==null)break}while(!0);c=a.getOwner();do{if(t=c.getParent(),t==null)break;if(t==r)return!0;if(c=t.getOwner(),c==null)break}while(!0);return!1},n.prototype.calcLowestCommonAncestors=function(){for(var r,a,c,t,u,i=this.getAllEdges(),e=i.length,l=0;l<e;l++){if(r=i[l],a=r.source,c=r.target,r.lca=null,r.sourceInLca=a,r.targetInLca=c,a==c){r.lca=a.getOwner();continue}for(t=a.getOwner();r.lca==null;){for(r.targetInLca=c,u=c.getOwner();r.lca==null;){if(u==t){r.lca=u;break}if(u==this.rootGraph)break;if(r.lca!=null)throw"assert failed";r.targetInLca=u.getParent(),u=r.targetInLca.getOwner()}if(t==this.rootGraph)break;r.lca==null&&(r.sourceInLca=t.getParent(),t=r.sourceInLca.getOwner())}if(r.lca==null)throw"assert failed"}},n.prototype.calcLowestCommonAncestor=function(r,a){if(r==a)return r.getOwner();var c=r.getOwner();do{if(c==null)break;var t=a.getOwner();do{if(t==null)break;if(t==c)return t;t=t.getParent().getOwner()}while(!0);c=c.getParent().getOwner()}while(!0);return c},n.prototype.calcInclusionTreeDepths=function(r,a){r==null&&a==null&&(r=this.rootGraph,a=1);for(var c,t=r.getNodes(),u=t.length,i=0;i<u;i++)c=t[i],c.inclusionTreeDepth=a,c.child!=null&&this.calcInclusionTreeDepths(c.child,a+1)},n.prototype.includesInvalidEdge=function(){for(var r,a=[],c=this.edges.length,t=0;t<c;t++)r=this.edges[t],this.isOneAncestorOfOther(r.source,r.target)&&a.push(r);for(var t=0;t<a.length;t++)this.remove(a[t]);return!1},D.exports=n},function(D,C,T){"use strict";var g=T(12);function o(){}L(o,"IGeometry"),o.calcSeparationAmount=function(n,r,a,c){if(!n.intersects(r))throw"assert failed";var t=new Array(2);this.decideDirectionsForOverlappingNodes(n,r,t),a[0]=Math.min(n.getRight(),r.getRight())-Math.max(n.x,r.x),a[1]=Math.min(n.getBottom(),r.getBottom())-Math.max(n.y,r.y),n.getX()<=r.getX()&&n.getRight()>=r.getRight()?a[0]+=Math.min(r.getX()-n.getX(),n.getRight()-r.getRight()):r.getX()<=n.getX()&&r.getRight()>=n.getRight()&&(a[0]+=Math.min(n.getX()-r.getX(),r.getRight()-n.getRight())),n.getY()<=r.getY()&&n.getBottom()>=r.getBottom()?a[1]+=Math.min(r.getY()-n.getY(),n.getBottom()-r.getBottom()):r.getY()<=n.getY()&&r.getBottom()>=n.getBottom()&&(a[1]+=Math.min(n.getY()-r.getY(),r.getBottom()-n.getBottom()));var u=Math.abs((r.getCenterY()-n.getCenterY())/(r.getCenterX()-n.getCenterX()));r.getCenterY()===n.getCenterY()&&r.getCenterX()===n.getCenterX()&&(u=1);var i=u*a[0],e=a[1]/u;a[0]<e?e=a[0]:i=a[1],a[0]=-1*t[0]*(e/2+c),a[1]=-1*t[1]*(i/2+c)},o.decideDirectionsForOverlappingNodes=function(n,r,a){n.getCenterX()<r.getCenterX()?a[0]=-1:a[0]=1,n.getCenterY()<r.getCenterY()?a[1]=-1:a[1]=1},o.getIntersection2=function(n,r,a){var c=n.getCenterX(),t=n.getCenterY(),u=r.getCenterX(),i=r.getCenterY();if(n.intersects(r))return a[0]=c,a[1]=t,a[2]=u,a[3]=i,!0;var e=n.getX(),l=n.getY(),f=n.getRight(),h=n.getX(),A=n.getBottom(),v=n.getRight(),y=n.getWidthHalf(),N=n.getHeightHalf(),S=r.getX(),M=r.getY(),b=r.getRight(),$=r.getX(),X=r.getBottom(),et=r.getRight(),R=r.getWidthHalf(),J=r.getHeightHalf(),s=!1,m=!1;if(c===u){if(t>i)return a[0]=c,a[1]=l,a[2]=u,a[3]=X,!1;if(t<i)return a[0]=c,a[1]=A,a[2]=u,a[3]=M,!1}else if(t===i){if(c>u)return a[0]=e,a[1]=t,a[2]=b,a[3]=i,!1;if(c<u)return a[0]=f,a[1]=t,a[2]=S,a[3]=i,!1}else{var p=n.height/n.width,E=r.height/r.width,d=(i-t)/(u-c),O=void 0,x=void 0,G=void 0,F=void 0,I=void 0,Z=void 0;if(-p===d?c>u?(a[0]=h,a[1]=A,s=!0):(a[0]=f,a[1]=l,s=!0):p===d&&(c>u?(a[0]=e,a[1]=l,s=!0):(a[0]=v,a[1]=A,s=!0)),-E===d?u>c?(a[2]=$,a[3]=X,m=!0):(a[2]=b,a[3]=M,m=!0):E===d&&(u>c?(a[2]=S,a[3]=M,m=!0):(a[2]=et,a[3]=X,m=!0)),s&&m)return!1;if(c>u?t>i?(O=this.getCardinalDirection(p,d,4),x=this.getCardinalDirection(E,d,2)):(O=this.getCardinalDirection(-p,d,3),x=this.getCardinalDirection(-E,d,1)):t>i?(O=this.getCardinalDirection(-p,d,1),x=this.getCardinalDirection(-E,d,3)):(O=this.getCardinalDirection(p,d,2),x=this.getCardinalDirection(E,d,4)),!s)switch(O){case 1:F=l,G=c+-N/d,a[0]=G,a[1]=F;break;case 2:G=v,F=t+y*d,a[0]=G,a[1]=F;break;case 3:F=A,G=c+N/d,a[0]=G,a[1]=F;break;case 4:G=h,F=t+-y*d,a[0]=G,a[1]=F;break}if(!m)switch(x){case 1:Z=M,I=u+-J/d,a[2]=I,a[3]=Z;break;case 2:I=et,Z=i+R*d,a[2]=I,a[3]=Z;break;case 3:Z=X,I=u+J/d,a[2]=I,a[3]=Z;break;case 4:I=$,Z=i+-R*d,a[2]=I,a[3]=Z;break}}return!1},o.getCardinalDirection=function(n,r,a){return n>r?a:1+a%4},o.getIntersection=function(n,r,a,c){if(c==null)return this.getIntersection2(n,r,a);var t=n.x,u=n.y,i=r.x,e=r.y,l=a.x,f=a.y,h=c.x,A=c.y,v=void 0,y=void 0,N=void 0,S=void 0,M=void 0,b=void 0,$=void 0,X=void 0,et=void 0;return N=e-u,M=t-i,$=i*u-t*e,S=A-f,b=l-h,X=h*f-l*A,et=N*b-S*M,et===0?null:(v=(M*X-b*$)/et,y=(S*$-N*X)/et,new g(v,y))},o.angleOfVector=function(n,r,a,c){var t=void 0;return n!==a?(t=Math.atan((c-r)/(a-n)),a<n?t+=Math.PI:c<r&&(t+=this.TWO_PI)):c<r?t=this.ONE_AND_HALF_PI:t=this.HALF_PI,t},o.doIntersect=function(n,r,a,c){var t=n.x,u=n.y,i=r.x,e=r.y,l=a.x,f=a.y,h=c.x,A=c.y,v=(i-t)*(A-f)-(h-l)*(e-u);if(v===0)return!1;var y=((A-f)*(h-t)+(l-h)*(A-u))/v,N=((u-e)*(h-t)+(i-t)*(A-u))/v;return 0<y&&y<1&&0<N&&N<1},o.findCircleLineIntersections=function(n,r,a,c,t,u,i){var e=(a-n)*(a-n)+(c-r)*(c-r),l=2*((n-t)*(a-n)+(r-u)*(c-r)),f=(n-t)*(n-t)+(r-u)*(r-u)-i*i,h=l*l-4*e*f;if(h>=0){var A=(-l+Math.sqrt(l*l-4*e*f))/(2*e),v=(-l-Math.sqrt(l*l-4*e*f))/(2*e),y=null;return A>=0&&A<=1?[A]:v>=0&&v<=1?[v]:y}else return null},o.HALF_PI=.5*Math.PI,o.ONE_AND_HALF_PI=1.5*Math.PI,o.TWO_PI=2*Math.PI,o.THREE_PI=3*Math.PI,D.exports=o},function(D,C,T){"use strict";function g(){}L(g,"IMath"),g.sign=function(o){return o>0?1:o<0?-1:0},g.floor=function(o){return o<0?Math.ceil(o):Math.floor(o)},g.ceil=function(o){return o<0?Math.floor(o):Math.ceil(o)},D.exports=g},function(D,C,T){"use strict";function g(){}L(g,"Integer"),g.MAX_VALUE=2147483647,g.MIN_VALUE=-2147483648,D.exports=g},function(D,C,T){"use strict";var g=function(){function t(u,i){for(var e=0;e<i.length;e++){var l=i[e];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(u,l.key,l)}}return L(t,"defineProperties"),function(u,i,e){return i&&t(u.prototype,i),e&&t(u,e),u}}();function o(t,u){if(!(t instanceof u))throw new TypeError("Cannot call a class as a function")}L(o,"_classCallCheck");var n=L(function(u){return{value:u,next:null,prev:null}},"nodeFrom"),r=L(function(u,i,e,l){return u!==null?u.next=i:l.head=i,e!==null?e.prev=i:l.tail=i,i.prev=u,i.next=e,l.length++,i},"add"),a=L(function(u,i){var e=u.prev,l=u.next;return e!==null?e.next=l:i.head=l,l!==null?l.prev=e:i.tail=e,u.prev=u.next=null,i.length--,u},"_remove"),c=function(){function t(u){var i=this;o(this,t),this.length=0,this.head=null,this.tail=null,u?.forEach(function(e){return i.push(e)})}return L(t,"LinkedList"),g(t,[{key:"size",value:L(function(){return this.length},"size")},{key:"insertBefore",value:L(function(i,e){return r(e.prev,n(i),e,this)},"insertBefore")},{key:"insertAfter",value:L(function(i,e){return r(e,n(i),e.next,this)},"insertAfter")},{key:"insertNodeBefore",value:L(function(i,e){return r(e.prev,i,e,this)},"insertNodeBefore")},{key:"insertNodeAfter",value:L(function(i,e){return r(e,i,e.next,this)},"insertNodeAfter")},{key:"push",value:L(function(i){return r(this.tail,n(i),null,this)},"push")},{key:"unshift",value:L(function(i){return r(null,n(i),this.head,this)},"unshift")},{key:"remove",value:L(function(i){return a(i,this)},"remove")},{key:"pop",value:L(function(){return a(this.tail,this).value},"pop")},{key:"popNode",value:L(function(){return a(this.tail,this)},"popNode")},{key:"shift",value:L(function(){return a(this.head,this).value},"shift")},{key:"shiftNode",value:L(function(){return a(this.head,this)},"shiftNode")},{key:"get_object_at",value:L(function(i){if(i<=this.length()){for(var e=1,l=this.head;e<i;)l=l.next,e++;return l.value}},"get_object_at")},{key:"set_object_at",value:L(function(i,e){if(i<=this.length()){for(var l=1,f=this.head;l<i;)f=f.next,l++;f.value=e}},"set_object_at")}]),t}();D.exports=c},function(D,C,T){"use strict";function g(o,n,r){this.x=null,this.y=null,o==null&&n==null&&r==null?(this.x=0,this.y=0):typeof o=="number"&&typeof n=="number"&&r==null?(this.x=o,this.y=n):o.constructor.name=="Point"&&n==null&&r==null&&(r=o,this.x=r.x,this.y=r.y)}L(g,"Point"),g.prototype.getX=function(){return this.x},g.prototype.getY=function(){return this.y},g.prototype.getLocation=function(){return new g(this.x,this.y)},g.prototype.setLocation=function(o,n,r){o.constructor.name=="Point"&&n==null&&r==null?(r=o,this.setLocation(r.x,r.y)):typeof o=="number"&&typeof n=="number"&&r==null&&(parseInt(o)==o&&parseInt(n)==n?this.move(o,n):(this.x=Math.floor(o+.5),this.y=Math.floor(n+.5)))},g.prototype.move=function(o,n){this.x=o,this.y=n},g.prototype.translate=function(o,n){this.x+=o,this.y+=n},g.prototype.equals=function(o){if(o.constructor.name=="Point"){var n=o;return this.x==n.x&&this.y==n.y}return this==o},g.prototype.toString=function(){return new g().constructor.name+"[x="+this.x+",y="+this.y+"]"},D.exports=g},function(D,C,T){"use strict";function g(o,n,r,a){this.x=0,this.y=0,this.width=0,this.height=0,o!=null&&n!=null&&r!=null&&a!=null&&(this.x=o,this.y=n,this.width=r,this.height=a)}L(g,"RectangleD"),g.prototype.getX=function(){return this.x},g.prototype.setX=function(o){this.x=o},g.prototype.getY=function(){return this.y},g.prototype.setY=function(o){this.y=o},g.prototype.getWidth=function(){return this.width},g.prototype.setWidth=function(o){this.width=o},g.prototype.getHeight=function(){return this.height},g.prototype.setHeight=function(o){this.height=o},g.prototype.getRight=function(){return this.x+this.width},g.prototype.getBottom=function(){return this.y+this.height},g.prototype.intersects=function(o){return!(this.getRight()<o.x||this.getBottom()<o.y||o.getRight()<this.x||o.getBottom()<this.y)},g.prototype.getCenterX=function(){return this.x+this.width/2},g.prototype.getMinX=function(){return this.getX()},g.prototype.getMaxX=function(){return this.getX()+this.width},g.prototype.getCenterY=function(){return this.y+this.height/2},g.prototype.getMinY=function(){return this.getY()},g.prototype.getMaxY=function(){return this.getY()+this.height},g.prototype.getWidthHalf=function(){return this.width/2},g.prototype.getHeightHalf=function(){return this.height/2},D.exports=g},function(D,C,T){"use strict";var g=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n};function o(){}L(o,"UniqueIDGeneretor"),o.lastID=0,o.createID=function(n){return o.isPrimitive(n)?n:(n.uniqueID!=null||(n.uniqueID=o.getString(),o.lastID++),n.uniqueID)},o.getString=function(n){return n==null&&(n=o.lastID),"Object#"+n},o.isPrimitive=function(n){var r=typeof n>"u"?"undefined":g(n);return n==null||r!="object"&&r!="function"},D.exports=o},function(D,C,T){"use strict";function g(l){if(Array.isArray(l)){for(var f=0,h=Array(l.length);f<l.length;f++)h[f]=l[f];return h}else return Array.from(l)}L(g,"_toConsumableArray");var o=T(0),n=T(7),r=T(3),a=T(1),c=T(6),t=T(5),u=T(17),i=T(29);function e(l){i.call(this),this.layoutQuality=o.QUALITY,this.createBendsAsNeeded=o.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=o.DEFAULT_INCREMENTAL,this.animationOnLayout=o.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=o.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=o.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=o.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new n(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,l!=null&&(this.isRemoteUse=l)}L(e,"Layout"),e.RANDOM_SEED=1,e.prototype=Object.create(i.prototype),e.prototype.getGraphManager=function(){return this.graphManager},e.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},e.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},e.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},e.prototype.newGraphManager=function(){var l=new n(this);return this.graphManager=l,l},e.prototype.newGraph=function(l){return new c(null,this.graphManager,l)},e.prototype.newNode=function(l){return new r(this.graphManager,l)},e.prototype.newEdge=function(l){return new a(null,null,l)},e.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},e.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var l;return this.checkLayoutSuccess()?l=!1:l=this.layout(),o.ANIMATE==="during"?!1:(l&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,l)},e.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},e.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var l,f=this.graphManager.getAllEdges(),h=0;h<f.length;h++)l=f[h];for(var A,v=this.graphManager.getRoot().getNodes(),h=0;h<v.length;h++)A=v[h];this.update(this.graphManager.getRoot())}},e.prototype.update=function(l){if(l==null)this.update2();else if(l instanceof r){var f=l;if(f.getChild()!=null)for(var h=f.getChild().getNodes(),A=0;A<h.length;A++)update(h[A]);if(f.vGraphObject!=null){var v=f.vGraphObject;v.update(f)}}else if(l instanceof a){var y=l;if(y.vGraphObject!=null){var N=y.vGraphObject;N.update(y)}}else if(l instanceof c){var S=l;if(S.vGraphObject!=null){var M=S.vGraphObject;M.update(S)}}},e.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=o.QUALITY,this.animationDuringLayout=o.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=o.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=o.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=o.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=o.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=o.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},e.prototype.transform=function(l){if(l==null)this.transform(new t(0,0));else{var f=new u,h=this.graphManager.getRoot().updateLeftTop();if(h!=null){f.setWorldOrgX(l.x),f.setWorldOrgY(l.y),f.setDeviceOrgX(h.x),f.setDeviceOrgY(h.y);for(var A=this.getAllNodes(),v,y=0;y<A.length;y++)v=A[y],v.transform(f)}}},e.prototype.positionNodesRandomly=function(l){if(l==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var f,h,A=l.getNodes(),v=0;v<A.length;v++)f=A[v],h=f.getChild(),h==null||h.getNodes().length==0?f.scatter():(this.positionNodesRandomly(h),f.updateBounds())},e.prototype.getFlatForest=function(){for(var l=[],f=!0,h=this.graphManager.getRoot().getNodes(),A=!0,v=0;v<h.length;v++)h[v].getChild()!=null&&(A=!1);if(!A)return l;var y=new Set,N=[],S=new Map,M=[];for(M=M.concat(h);M.length>0&&f;){for(N.push(M[0]);N.length>0&&f;){var b=N[0];N.splice(0,1),y.add(b);for(var $=b.getEdges(),v=0;v<$.length;v++){var X=$[v].getOtherEnd(b);if(S.get(b)!=X)if(!y.has(X))N.push(X),S.set(X,b);else{f=!1;break}}}if(!f)l=[];else{var et=[].concat(g(y));l.push(et);for(var v=0;v<et.length;v++){var R=et[v],J=M.indexOf(R);J>-1&&M.splice(J,1)}y=new Set,S=new Map}}return l},e.prototype.createDummyNodesForBendpoints=function(l){for(var f=[],h=l.source,A=this.graphManager.calcLowestCommonAncestor(l.source,l.target),v=0;v<l.bendpoints.length;v++){var y=this.newNode(null);y.setRect(new Point(0,0),new Dimension(1,1)),A.add(y);var N=this.newEdge(null);this.graphManager.add(N,h,y),f.add(y),h=y}var N=this.newEdge(null);return this.graphManager.add(N,h,l.target),this.edgeToDummyNodes.set(l,f),l.isInterGraph()?this.graphManager.remove(l):A.remove(l),f},e.prototype.createBendpointsFromDummyNodes=function(){var l=[];l=l.concat(this.graphManager.getAllEdges()),l=[].concat(g(this.edgeToDummyNodes.keys())).concat(l);for(var f=0;f<l.length;f++){var h=l[f];if(h.bendpoints.length>0){for(var A=this.edgeToDummyNodes.get(h),v=0;v<A.length;v++){var y=A[v],N=new t(y.getCenterX(),y.getCenterY()),S=h.bendpoints.get(v);S.x=N.x,S.y=N.y,y.getOwner().remove(y)}this.graphManager.add(h,h.source,h.target)}}},e.transform=function(l,f,h,A){if(h!=null&&A!=null){var v=f;if(l<=50){var y=f/h;v-=(f-y)/50*(50-l)}else{var N=f*A;v+=(N-f)/50*(l-50)}return v}else{var S,M;return l<=50?(S=9*f/500,M=f/10):(S=9*f/50,M=-8*f),S*l+M}},e.findCenterOfTree=function(l){var f=[];f=f.concat(l);var h=[],A=new Map,v=!1,y=null;(f.length==1||f.length==2)&&(v=!0,y=f[0]);for(var N=0;N<f.length;N++){var S=f[N],M=S.getNeighborsList().size;A.set(S,S.getNeighborsList().size),M==1&&h.push(S)}var b=[];for(b=b.concat(h);!v;){var $=[];$=$.concat(b),b=[];for(var N=0;N<f.length;N++){var S=f[N],X=f.indexOf(S);X>=0&&f.splice(X,1);var et=S.getNeighborsList();et.forEach(function(s){if(h.indexOf(s)<0){var m=A.get(s),p=m-1;p==1&&b.push(s),A.set(s,p)}})}h=h.concat(b),(f.length==1||f.length==2)&&(v=!0,y=f[0])}return y},e.prototype.setGraphManager=function(l){this.graphManager=l},D.exports=e},function(D,C,T){"use strict";function g(){}L(g,"RandomSeed"),g.seed=1,g.x=0,g.nextDouble=function(){return g.x=Math.sin(g.seed++)*1e4,g.x-Math.floor(g.x)},D.exports=g},function(D,C,T){"use strict";var g=T(5);function o(n,r){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}L(o,"Transform"),o.prototype.getWorldOrgX=function(){return this.lworldOrgX},o.prototype.setWorldOrgX=function(n){this.lworldOrgX=n},o.prototype.getWorldOrgY=function(){return this.lworldOrgY},o.prototype.setWorldOrgY=function(n){this.lworldOrgY=n},o.prototype.getWorldExtX=function(){return this.lworldExtX},o.prototype.setWorldExtX=function(n){this.lworldExtX=n},o.prototype.getWorldExtY=function(){return this.lworldExtY},o.prototype.setWorldExtY=function(n){this.lworldExtY=n},o.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},o.prototype.setDeviceOrgX=function(n){this.ldeviceOrgX=n},o.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},o.prototype.setDeviceOrgY=function(n){this.ldeviceOrgY=n},o.prototype.getDeviceExtX=function(){return this.ldeviceExtX},o.prototype.setDeviceExtX=function(n){this.ldeviceExtX=n},o.prototype.getDeviceExtY=function(){return this.ldeviceExtY},o.prototype.setDeviceExtY=function(n){this.ldeviceExtY=n},o.prototype.transformX=function(n){var r=0,a=this.lworldExtX;return a!=0&&(r=this.ldeviceOrgX+(n-this.lworldOrgX)*this.ldeviceExtX/a),r},o.prototype.transformY=function(n){var r=0,a=this.lworldExtY;return a!=0&&(r=this.ldeviceOrgY+(n-this.lworldOrgY)*this.ldeviceExtY/a),r},o.prototype.inverseTransformX=function(n){var r=0,a=this.ldeviceExtX;return a!=0&&(r=this.lworldOrgX+(n-this.ldeviceOrgX)*this.lworldExtX/a),r},o.prototype.inverseTransformY=function(n){var r=0,a=this.ldeviceExtY;return a!=0&&(r=this.lworldOrgY+(n-this.ldeviceOrgY)*this.lworldExtY/a),r},o.prototype.inverseTransformPoint=function(n){var r=new g(this.inverseTransformX(n.x),this.inverseTransformY(n.y));return r},D.exports=o},function(D,C,T){"use strict";function g(i){if(Array.isArray(i)){for(var e=0,l=Array(i.length);e<i.length;e++)l[e]=i[e];return l}else return Array.from(i)}L(g,"_toConsumableArray");var o=T(15),n=T(4),r=T(0),a=T(8),c=T(9);function t(){o.call(this),this.useSmartIdealEdgeLengthCalculation=n.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=n.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=n.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=n.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=n.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*n.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=n.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=n.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=n.MAX_ITERATIONS}L(t,"FDLayout"),t.prototype=Object.create(o.prototype);for(var u in o)t[u]=o[u];t.prototype.initParameters=function(){o.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=n.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},t.prototype.calcIdealEdgeLengths=function(){for(var i,e,l,f,h,A,v,y=this.getGraphManager().getAllEdges(),N=0;N<y.length;N++)i=y[N],e=i.idealLength,i.isInterGraph&&(f=i.getSource(),h=i.getTarget(),A=i.getSourceInLca().getEstimatedSize(),v=i.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(i.idealLength+=A+v-2*r.SIMPLE_NODE_SIZE),l=i.getLca().getInclusionTreeDepth(),i.idealLength+=e*n.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(f.getInclusionTreeDepth()+h.getInclusionTreeDepth()-2*l))},t.prototype.initSpringEmbedder=function(){var i=this.getAllNodes().length;this.incremental?(i>n.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*n.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(i-n.ADAPTATION_LOWER_NODE_LIMIT)/(n.ADAPTATION_UPPER_NODE_LIMIT-n.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-n.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=n.MAX_NODE_DISPLACEMENT_INCREMENTAL):(i>n.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(n.COOLING_ADAPTATION_FACTOR,1-(i-n.ADAPTATION_LOWER_NODE_LIMIT)/(n.ADAPTATION_UPPER_NODE_LIMIT-n.ADAPTATION_LOWER_NODE_LIMIT)*(1-n.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=n.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.displacementThresholdPerNode=3*n.DEFAULT_EDGE_LENGTH/100,this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},t.prototype.calcSpringForces=function(){for(var i=this.getAllEdges(),e,l=0;l<i.length;l++)e=i[l],this.calcSpringForce(e,e.idealLength)},t.prototype.calcRepulsionForces=function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,l,f,h,A,v=this.getAllNodes(),y;if(this.useFRGridVariant)for(this.totalIterations%n.GRID_CALCULATION_CHECK_PERIOD==1&&i&&this.updateGrid(),y=new Set,l=0;l<v.length;l++)h=v[l],this.calculateRepulsionForceOfANode(h,y,i,e),y.add(h);else for(l=0;l<v.length;l++)for(h=v[l],f=l+1;f<v.length;f++)A=v[f],h.getOwner()==A.getOwner()&&this.calcRepulsionForce(h,A)},t.prototype.calcGravitationalForces=function(){for(var i,e=this.getAllNodesToApplyGravitation(),l=0;l<e.length;l++)i=e[l],this.calcGravitationalForce(i)},t.prototype.moveNodes=function(){for(var i=this.getAllNodes(),e,l=0;l<i.length;l++)e=i[l],e.move()},t.prototype.calcSpringForce=function(i,e){var l=i.getSource(),f=i.getTarget(),h,A,v,y;if(this.uniformLeafNodeSizes&&l.getChild()==null&&f.getChild()==null)i.updateLengthSimple();else if(i.updateLength(),i.isOverlapingSourceAndTarget)return;h=i.getLength(),h!=0&&(A=i.edgeElasticity*(h-e),v=A*(i.lengthX/h),y=A*(i.lengthY/h),l.springForceX+=v,l.springForceY+=y,f.springForceX-=v,f.springForceY-=y)},t.prototype.calcRepulsionForce=function(i,e){var l=i.getRect(),f=e.getRect(),h=new Array(2),A=new Array(4),v,y,N,S,M,b,$;if(l.intersects(f)){a.calcSeparationAmount(l,f,h,n.DEFAULT_EDGE_LENGTH/2),b=2*h[0],$=2*h[1];var X=i.noOfChildren*e.noOfChildren/(i.noOfChildren+e.noOfChildren);i.repulsionForceX-=X*b,i.repulsionForceY-=X*$,e.repulsionForceX+=X*b,e.repulsionForceY+=X*$}else this.uniformLeafNodeSizes&&i.getChild()==null&&e.getChild()==null?(v=f.getCenterX()-l.getCenterX(),y=f.getCenterY()-l.getCenterY()):(a.getIntersection(l,f,A),v=A[2]-A[0],y=A[3]-A[1]),Math.abs(v)<n.MIN_REPULSION_DIST&&(v=c.sign(v)*n.MIN_REPULSION_DIST),Math.abs(y)<n.MIN_REPULSION_DIST&&(y=c.sign(y)*n.MIN_REPULSION_DIST),N=v*v+y*y,S=Math.sqrt(N),M=(i.nodeRepulsion/2+e.nodeRepulsion/2)*i.noOfChildren*e.noOfChildren/N,b=M*v/S,$=M*y/S,i.repulsionForceX-=b,i.repulsionForceY-=$,e.repulsionForceX+=b,e.repulsionForceY+=$},t.prototype.calcGravitationalForce=function(i){var e,l,f,h,A,v,y,N;e=i.getOwner(),l=(e.getRight()+e.getLeft())/2,f=(e.getTop()+e.getBottom())/2,h=i.getCenterX()-l,A=i.getCenterY()-f,v=Math.abs(h)+i.getWidth()/2,y=Math.abs(A)+i.getHeight()/2,i.getOwner()==this.graphManager.getRoot()?(N=e.getEstimatedSize()*this.gravityRangeFactor,(v>N||y>N)&&(i.gravitationForceX=-this.gravityConstant*h,i.gravitationForceY=-this.gravityConstant*A)):(N=e.getEstimatedSize()*this.compoundGravityRangeFactor,(v>N||y>N)&&(i.gravitationForceX=-this.gravityConstant*h*this.compoundGravityConstant,i.gravitationForceY=-this.gravityConstant*A*this.compoundGravityConstant))},t.prototype.isConverged=function(){var i,e=!1;return this.totalIterations>this.maxIterations/3&&(e=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),i=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,i||e},t.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},t.prototype.calcNoOfChildrenForAllNodes=function(){for(var i,e=this.graphManager.getAllNodes(),l=0;l<e.length;l++)i=e[l],i.noOfChildren=i.getNoOfChildren()},t.prototype.calcGrid=function(i){var e=0,l=0;e=parseInt(Math.ceil((i.getRight()-i.getLeft())/this.repulsionRange)),l=parseInt(Math.ceil((i.getBottom()-i.getTop())/this.repulsionRange));for(var f=new Array(e),h=0;h<e;h++)f[h]=new Array(l);for(var h=0;h<e;h++)for(var A=0;A<l;A++)f[h][A]=new Array;return f},t.prototype.addNodeToGrid=function(i,e,l){var f=0,h=0,A=0,v=0;f=parseInt(Math.floor((i.getRect().x-e)/this.repulsionRange)),h=parseInt(Math.floor((i.getRect().width+i.getRect().x-e)/this.repulsionRange)),A=parseInt(Math.floor((i.getRect().y-l)/this.repulsionRange)),v=parseInt(Math.floor((i.getRect().height+i.getRect().y-l)/this.repulsionRange));for(var y=f;y<=h;y++)for(var N=A;N<=v;N++)this.grid[y][N].push(i),i.setGridCoordinates(f,h,A,v)},t.prototype.updateGrid=function(){var i,e,l=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),i=0;i<l.length;i++)e=l[i],this.addNodeToGrid(e,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},t.prototype.calculateRepulsionForceOfANode=function(i,e,l,f){if(this.totalIterations%n.GRID_CALCULATION_CHECK_PERIOD==1&&l||f){var h=new Set;i.surrounding=new Array;for(var A,v=this.grid,y=i.startX-1;y<i.finishX+2;y++)for(var N=i.startY-1;N<i.finishY+2;N++)if(!(y<0||N<0||y>=v.length||N>=v[0].length)){for(var S=0;S<v[y][N].length;S++)if(A=v[y][N][S],!(i.getOwner()!=A.getOwner()||i==A)&&!e.has(A)&&!h.has(A)){var M=Math.abs(i.getCenterX()-A.getCenterX())-(i.getWidth()/2+A.getWidth()/2),b=Math.abs(i.getCenterY()-A.getCenterY())-(i.getHeight()/2+A.getHeight()/2);M<=this.repulsionRange&&b<=this.repulsionRange&&h.add(A)}}i.surrounding=[].concat(g(h))}for(y=0;y<i.surrounding.length;y++)this.calcRepulsionForce(i,i.surrounding[y])},t.prototype.calcRepulsionRange=function(){return 0},D.exports=t},function(D,C,T){"use strict";var g=T(1),o=T(4);function n(a,c,t){g.call(this,a,c,t),this.idealLength=o.DEFAULT_EDGE_LENGTH,this.edgeElasticity=o.DEFAULT_SPRING_STRENGTH}L(n,"FDLayoutEdge"),n.prototype=Object.create(g.prototype);for(var r in g)n[r]=g[r];D.exports=n},function(D,C,T){"use strict";var g=T(3),o=T(4);function n(a,c,t,u){g.call(this,a,c,t,u),this.nodeRepulsion=o.DEFAULT_REPULSION_STRENGTH,this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}L(n,"FDLayoutNode"),n.prototype=Object.create(g.prototype);for(var r in g)n[r]=g[r];n.prototype.setGridCoordinates=function(a,c,t,u){this.startX=a,this.finishX=c,this.startY=t,this.finishY=u},D.exports=n},function(D,C,T){"use strict";function g(o,n){this.width=0,this.height=0,o!==null&&n!==null&&(this.height=n,this.width=o)}L(g,"DimensionD"),g.prototype.getWidth=function(){return this.width},g.prototype.setWidth=function(o){this.width=o},g.prototype.getHeight=function(){return this.height},g.prototype.setHeight=function(o){this.height=o},D.exports=g},function(D,C,T){"use strict";var g=T(14);function o(){this.map={},this.keys=[]}L(o,"HashMap"),o.prototype.put=function(n,r){var a=g.createID(n);this.contains(a)||(this.map[a]=r,this.keys.push(n))},o.prototype.contains=function(n){var r=g.createID(n);return this.map[n]!=null},o.prototype.get=function(n){var r=g.createID(n);return this.map[r]},o.prototype.keySet=function(){return this.keys},D.exports=o},function(D,C,T){"use strict";var g=T(14);function o(){this.set={}}L(o,"HashSet"),o.prototype.add=function(n){var r=g.createID(n);this.contains(r)||(this.set[r]=n)},o.prototype.remove=function(n){delete this.set[g.createID(n)]},o.prototype.clear=function(){this.set={}},o.prototype.contains=function(n){return this.set[g.createID(n)]==n},o.prototype.isEmpty=function(){return this.size()===0},o.prototype.size=function(){return Object.keys(this.set).length},o.prototype.addAllTo=function(n){for(var r=Object.keys(this.set),a=r.length,c=0;c<a;c++)n.push(this.set[r[c]])},o.prototype.size=function(){return Object.keys(this.set).length},o.prototype.addAll=function(n){for(var r=n.length,a=0;a<r;a++){var c=n[a];this.add(c)}},D.exports=o},function(D,C,T){"use strict";function g(){}L(g,"Matrix"),g.multMat=function(o,n){for(var r=[],a=0;a<o.length;a++){r[a]=[];for(var c=0;c<n[0].length;c++){r[a][c]=0;for(var t=0;t<o[0].length;t++)r[a][c]+=o[a][t]*n[t][c]}}return r},g.transpose=function(o){for(var n=[],r=0;r<o[0].length;r++){n[r]=[];for(var a=0;a<o.length;a++)n[r][a]=o[a][r]}return n},g.multCons=function(o,n){for(var r=[],a=0;a<o.length;a++)r[a]=o[a]*n;return r},g.minusOp=function(o,n){for(var r=[],a=0;a<o.length;a++)r[a]=o[a]-n[a];return r},g.dotProduct=function(o,n){for(var r=0,a=0;a<o.length;a++)r+=o[a]*n[a];return r},g.mag=function(o){return Math.sqrt(this.dotProduct(o,o))},g.normalize=function(o){for(var n=[],r=this.mag(o),a=0;a<o.length;a++)n[a]=o[a]/r;return n},g.multGamma=function(o){for(var n=[],r=0,a=0;a<o.length;a++)r+=o[a];r*=-1/o.length;for(var c=0;c<o.length;c++)n[c]=r+o[c];return n},g.multL=function(o,n,r){for(var a=[],c=[],t=[],u=0;u<n[0].length;u++){for(var i=0,e=0;e<n.length;e++)i+=-.5*n[e][u]*o[e];c[u]=i}for(var l=0;l<r.length;l++){for(var f=0,h=0;h<r.length;h++)f+=r[l][h]*c[h];t[l]=f}for(var A=0;A<n.length;A++){for(var v=0,y=0;y<n[0].length;y++)v+=n[A][y]*t[y];a[A]=v}return a},D.exports=g},function(D,C,T){"use strict";var g=function(){function a(c,t){for(var u=0;u<t.length;u++){var i=t[u];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(c,i.key,i)}}return L(a,"defineProperties"),function(c,t,u){return t&&a(c.prototype,t),u&&a(c,u),c}}();function o(a,c){if(!(a instanceof c))throw new TypeError("Cannot call a class as a function")}L(o,"_classCallCheck");var n=T(11),r=function(){function a(c,t){o(this,a),(t!==null||t!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var u=void 0;c instanceof n?u=c.size():u=c.length,this._quicksort(c,0,u-1)}return L(a,"Quicksort"),g(a,[{key:"_quicksort",value:L(function(t,u,i){if(u<i){var e=this._partition(t,u,i);this._quicksort(t,u,e),this._quicksort(t,e+1,i)}},"_quicksort")},{key:"_partition",value:L(function(t,u,i){for(var e=this._get(t,u),l=u,f=i;;){for(;this.compareFunction(e,this._get(t,f));)f--;for(;this.compareFunction(this._get(t,l),e);)l++;if(l<f)this._swap(t,l,f),l++,f--;else return f}},"_partition")},{key:"_get",value:L(function(t,u){return t instanceof n?t.get_object_at(u):t[u]},"_get")},{key:"_set",value:L(function(t,u,i){t instanceof n?t.set_object_at(u,i):t[u]=i},"_set")},{key:"_swap",value:L(function(t,u,i){var e=this._get(t,u);this._set(t,u,this._get(t,i)),this._set(t,i,e)},"_swap")},{key:"_defaultCompareFunction",value:L(function(t,u){return u>t},"_defaultCompareFunction")}]),a}();D.exports=r},function(D,C,T){"use strict";function g(){}L(g,"SVD"),g.svd=function(o){this.U=null,this.V=null,this.s=null,this.m=0,this.n=0,this.m=o.length,this.n=o[0].length;var n=Math.min(this.m,this.n);this.s=function(xt){for(var At=[];xt-- >0;)At.push(0);return At}(Math.min(this.m+1,this.n)),this.U=function(xt){var At=L(function $t(It){if(It.length==0)return 0;for(var Xt=[],Bt=0;Bt<It[0];Bt++)Xt.push($t(It.slice(1)));return Xt},"allocate");return At(xt)}([this.m,n]),this.V=function(xt){var At=L(function $t(It){if(It.length==0)return 0;for(var Xt=[],Bt=0;Bt<It[0];Bt++)Xt.push($t(It.slice(1)));return Xt},"allocate");return At(xt)}([this.n,this.n]);for(var r=function(xt){for(var At=[];xt-- >0;)At.push(0);return At}(this.n),a=function(xt){for(var At=[];xt-- >0;)At.push(0);return At}(this.m),c=!0,t=!0,u=Math.min(this.m-1,this.n),i=Math.max(0,Math.min(this.n-2,this.m)),e=0;e<Math.max(u,i);e++){if(e<u){this.s[e]=0;for(var l=e;l<this.m;l++)this.s[e]=g.hypot(this.s[e],o[l][e]);if(this.s[e]!==0){o[e][e]<0&&(this.s[e]=-this.s[e]);for(var f=e;f<this.m;f++)o[f][e]/=this.s[e];o[e][e]+=1}this.s[e]=-this.s[e]}for(var h=e+1;h<this.n;h++){if(function(xt,At){return xt&&At}(e<u,this.s[e]!==0)){for(var A=0,v=e;v<this.m;v++)A+=o[v][e]*o[v][h];A=-A/o[e][e];for(var y=e;y<this.m;y++)o[y][h]+=A*o[y][e]}r[h]=o[e][h]}if(function(xt,At){return xt&&At}(c,e<u))for(var N=e;N<this.m;N++)this.U[N][e]=o[N][e];if(e<i){r[e]=0;for(var S=e+1;S<this.n;S++)r[e]=g.hypot(r[e],r[S]);if(r[e]!==0){r[e+1]<0&&(r[e]=-r[e]);for(var M=e+1;M<this.n;M++)r[M]/=r[e];r[e+1]+=1}if(r[e]=-r[e],function(xt,At){return xt&&At}(e+1<this.m,r[e]!==0)){for(var b=e+1;b<this.m;b++)a[b]=0;for(var $=e+1;$<this.n;$++)for(var X=e+1;X<this.m;X++)a[X]+=r[$]*o[X][$];for(var et=e+1;et<this.n;et++)for(var R=-r[et]/r[e+1],J=e+1;J<this.m;J++)o[J][et]+=R*a[J]}if(t)for(var s=e+1;s<this.n;s++)this.V[s][e]=r[s]}}var m=Math.min(this.n,this.m+1);if(u<this.n&&(this.s[u]=o[u][u]),this.m<m&&(this.s[m-1]=0),i+1<m&&(r[i]=o[i][m-1]),r[m-1]=0,c){for(var p=u;p<n;p++){for(var E=0;E<this.m;E++)this.U[E][p]=0;this.U[p][p]=1}for(var d=u-1;d>=0;d--)if(this.s[d]!==0){for(var O=d+1;O<n;O++){for(var x=0,G=d;G<this.m;G++)x+=this.U[G][d]*this.U[G][O];x=-x/this.U[d][d];for(var F=d;F<this.m;F++)this.U[F][O]+=x*this.U[F][d]}for(var I=d;I<this.m;I++)this.U[I][d]=-this.U[I][d];this.U[d][d]=1+this.U[d][d];for(var Z=0;Z<d-1;Z++)this.U[Z][d]=0}else{for(var tt=0;tt<this.m;tt++)this.U[tt][d]=0;this.U[d][d]=1}}if(t)for(var P=this.n-1;P>=0;P--){if(function(xt,At){return xt&&At}(P<i,r[P]!==0))for(var _=P+1;_<n;_++){for(var B=0,w=P+1;w<this.n;w++)B+=this.V[w][P]*this.V[w][_];B=-B/this.V[P+1][P];for(var U=P+1;U<this.n;U++)this.V[U][_]+=B*this.V[U][P]}for(var H=0;H<this.n;H++)this.V[H][P]=0;this.V[P][P]=1}for(var K=m-1,ht=0,Nt=Math.pow(2,-52),St=Math.pow(2,-966);m>0;){var Q=void 0,Yt=void 0;for(Q=m-2;Q>=-1&&Q!==-1;Q--)if(Math.abs(r[Q])<=St+Nt*(Math.abs(this.s[Q])+Math.abs(this.s[Q+1]))){r[Q]=0;break}if(Q===m-2)Yt=4;else{var Mt=void 0;for(Mt=m-1;Mt>=Q&&Mt!==Q;Mt--){var ot=(Mt!==m?Math.abs(r[Mt]):0)+(Mt!==Q+1?Math.abs(r[Mt-1]):0);if(Math.abs(this.s[Mt])<=St+Nt*ot){this.s[Mt]=0;break}}Mt===Q?Yt=3:Mt===m-1?Yt=1:(Yt=2,Q=Mt)}switch(Q++,Yt){case 1:{var rt=r[m-2];r[m-2]=0;for(var vt=m-2;vt>=Q;vt--){var mt=g.hypot(this.s[vt],rt),Lt=this.s[vt]/mt,Et=rt/mt;if(this.s[vt]=mt,vt!==Q&&(rt=-Et*r[vt-1],r[vt-1]=Lt*r[vt-1]),t)for(var Tt=0;Tt<this.n;Tt++)mt=Lt*this.V[Tt][vt]+Et*this.V[Tt][m-1],this.V[Tt][m-1]=-Et*this.V[Tt][vt]+Lt*this.V[Tt][m-1],this.V[Tt][vt]=mt}}break;case 2:{var wt=r[Q-1];r[Q-1]=0;for(var Rt=Q;Rt<m;Rt++){var Wt=g.hypot(this.s[Rt],wt),Pt=this.s[Rt]/Wt,Ut=wt/Wt;if(this.s[Rt]=Wt,wt=-Ut*r[Rt],r[Rt]=Pt*r[Rt],c)for(var Ft=0;Ft<this.m;Ft++)Wt=Pt*this.U[Ft][Rt]+Ut*this.U[Ft][Q-1],this.U[Ft][Q-1]=-Ut*this.U[Ft][Rt]+Pt*this.U[Ft][Q-1],this.U[Ft][Rt]=Wt}}break;case 3:{var Y=Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[m-1]),Math.abs(this.s[m-2])),Math.abs(r[m-2])),Math.abs(this.s[Q])),Math.abs(r[Q])),W=this.s[m-1]/Y,V=this.s[m-2]/Y,z=r[m-2]/Y,k=this.s[Q]/Y,j=r[Q]/Y,gt=((V+W)*(V-W)+z*z)/2,ft=W*z*(W*z),q=0;(function(xt,At){return xt||At})(gt!==0,ft!==0)&&(q=Math.sqrt(gt*gt+ft),gt<0&&(q=-q),q=ft/(gt+q));for(var lt=(k+W)*(k-W)+q,ut=k*j,it=Q;it<m-1;it++){var pt=g.hypot(lt,ut),Dt=lt/pt,st=ut/pt;if(it!==Q&&(r[it-1]=pt),lt=Dt*this.s[it]+st*r[it],r[it]=Dt*r[it]-st*this.s[it],ut=st*this.s[it+1],this.s[it+1]=Dt*this.s[it+1],t)for(var nt=0;nt<this.n;nt++)pt=Dt*this.V[nt][it]+st*this.V[nt][it+1],this.V[nt][it+1]=-st*this.V[nt][it]+Dt*this.V[nt][it+1],this.V[nt][it]=pt;if(pt=g.hypot(lt,ut),Dt=lt/pt,st=ut/pt,this.s[it]=pt,lt=Dt*r[it]+st*this.s[it+1],this.s[it+1]=-st*r[it]+Dt*this.s[it+1],ut=st*r[it+1],r[it+1]=Dt*r[it+1],c&&it<this.m-1)for(var dt=0;dt<this.m;dt++)pt=Dt*this.U[dt][it]+st*this.U[dt][it+1],this.U[dt][it+1]=-st*this.U[dt][it]+Dt*this.U[dt][it+1],this.U[dt][it]=pt}r[m-2]=lt,ht=ht+1}break;case 4:{if(this.s[Q]<=0&&(this.s[Q]=this.s[Q]<0?-this.s[Q]:0,t))for(var at=0;at<=K;at++)this.V[at][Q]=-this.V[at][Q];for(;Q<K&&!(this.s[Q]>=this.s[Q+1]);){var ct=this.s[Q];if(this.s[Q]=this.s[Q+1],this.s[Q+1]=ct,t&&Q<this.n-1)for(var bt=0;bt<this.n;bt++)ct=this.V[bt][Q+1],this.V[bt][Q+1]=this.V[bt][Q],this.V[bt][Q]=ct;if(c&&Q<this.m-1)for(var Ot=0;Ot<this.m;Ot++)ct=this.U[Ot][Q+1],this.U[Ot][Q+1]=this.U[Ot][Q],this.U[Ot][Q]=ct;Q++}ht=0,m--}break}}var Vt={U:this.U,V:this.V,S:this.s};return Vt},g.hypot=function(o,n){var r=void 0;return Math.abs(o)>Math.abs(n)?(r=n/o,r=Math.abs(o)*Math.sqrt(1+r*r)):n!=0?(r=o/n,r=Math.abs(n)*Math.sqrt(1+r*r)):r=0,r},D.exports=g},function(D,C,T){"use strict";var g=function(){function r(a,c){for(var t=0;t<c.length;t++){var u=c[t];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(a,u.key,u)}}return L(r,"defineProperties"),function(a,c,t){return c&&r(a.prototype,c),t&&r(a,t),a}}();function o(r,a){if(!(r instanceof a))throw new TypeError("Cannot call a class as a function")}L(o,"_classCallCheck");var n=function(){function r(a,c){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,u=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;o(this,r),this.sequence1=a,this.sequence2=c,this.match_score=t,this.mismatch_penalty=u,this.gap_penalty=i,this.iMax=a.length+1,this.jMax=c.length+1,this.grid=new Array(this.iMax);for(var e=0;e<this.iMax;e++){this.grid[e]=new Array(this.jMax);for(var l=0;l<this.jMax;l++)this.grid[e][l]=0}this.tracebackGrid=new Array(this.iMax);for(var f=0;f<this.iMax;f++){this.tracebackGrid[f]=new Array(this.jMax);for(var h=0;h<this.jMax;h++)this.tracebackGrid[f][h]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return L(r,"NeedlemanWunsch"),g(r,[{key:"getScore",value:L(function(){return this.score},"getScore")},{key:"getAlignments",value:L(function(){return this.alignments},"getAlignments")},{key:"computeGrids",value:L(function(){for(var c=1;c<this.jMax;c++)this.grid[0][c]=this.grid[0][c-1]+this.gap_penalty,this.tracebackGrid[0][c]=[!1,!1,!0];for(var t=1;t<this.iMax;t++)this.grid[t][0]=this.grid[t-1][0]+this.gap_penalty,this.tracebackGrid[t][0]=[!1,!0,!1];for(var u=1;u<this.iMax;u++)for(var i=1;i<this.jMax;i++){var e=void 0;this.sequence1[u-1]===this.sequence2[i-1]?e=this.grid[u-1][i-1]+this.match_score:e=this.grid[u-1][i-1]+this.mismatch_penalty;var l=this.grid[u-1][i]+this.gap_penalty,f=this.grid[u][i-1]+this.gap_penalty,h=[e,l,f],A=this.arrayAllMaxIndexes(h);this.grid[u][i]=h[A[0]],this.tracebackGrid[u][i]=[A.includes(0),A.includes(1),A.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]},"computeGrids")},{key:"alignmentTraceback",value:L(function(){var c=[];for(c.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});c[0];){var t=c[0],u=this.tracebackGrid[t.pos[0]][t.pos[1]];u[0]&&c.push({pos:[t.pos[0]-1,t.pos[1]-1],seq1:this.sequence1[t.pos[0]-1]+t.seq1,seq2:this.sequence2[t.pos[1]-1]+t.seq2}),u[1]&&c.push({pos:[t.pos[0]-1,t.pos[1]],seq1:this.sequence1[t.pos[0]-1]+t.seq1,seq2:"-"+t.seq2}),u[2]&&c.push({pos:[t.pos[0],t.pos[1]-1],seq1:"-"+t.seq1,seq2:this.sequence2[t.pos[1]-1]+t.seq2}),t.pos[0]===0&&t.pos[1]===0&&this.alignments.push({sequence1:t.seq1,sequence2:t.seq2}),c.shift()}return this.alignments},"alignmentTraceback")},{key:"getAllIndexes",value:L(function(c,t){for(var u=[],i=-1;(i=c.indexOf(t,i+1))!==-1;)u.push(i);return u},"getAllIndexes")},{key:"arrayAllMaxIndexes",value:L(function(c){return this.getAllIndexes(c,Math.max.apply(null,c))},"arrayAllMaxIndexes")}]),r}();D.exports=n},function(D,C,T){"use strict";var g=L(function(){},"layoutBase");g.FDLayout=T(18),g.FDLayoutConstants=T(4),g.FDLayoutEdge=T(19),g.FDLayoutNode=T(20),g.DimensionD=T(21),g.HashMap=T(22),g.HashSet=T(23),g.IGeometry=T(8),g.IMath=T(9),g.Integer=T(10),g.Point=T(12),g.PointD=T(5),g.RandomSeed=T(16),g.RectangleD=T(13),g.Transform=T(17),g.UniqueIDGeneretor=T(14),g.Quicksort=T(25),g.LinkedList=T(11),g.LGraphObject=T(2),g.LGraph=T(6),g.LEdge=T(1),g.LGraphManager=T(7),g.LNode=T(3),g.Layout=T(15),g.LayoutConstants=T(0),g.NeedlemanWunsch=T(27),g.Matrix=T(24),g.SVD=T(26),D.exports=g},function(D,C,T){"use strict";function g(){this.listeners=[]}L(g,"Emitter");var o=g.prototype;o.addListener=function(n,r){this.listeners.push({event:n,callback:r})},o.removeListener=function(n,r){for(var a=this.listeners.length;a>=0;a--){var c=this.listeners[a];c.event===n&&c.callback===r&&this.listeners.splice(a,1)}},o.emit=function(n,r){for(var a=0;a<this.listeners.length;a++){var c=this.listeners[a];n===c.event&&c.callback(r)}},D.exports=g}])})});var Re=Ae((he,Oe)=>{"use strict";L(function(C,T){typeof he=="object"&&typeof Oe=="object"?Oe.exports=T(xe()):typeof define=="function"&&define.amd?define(["layout-base"],T):typeof he=="object"?he.coseBase=T(xe()):C.coseBase=T(C.layoutBase)},"webpackUniversalModuleDefinition")(he,function(D){return(()=>{"use strict";var C={45:(n,r,a)=>{var c={};c.layoutBase=a(551),c.CoSEConstants=a(806),c.CoSEEdge=a(767),c.CoSEGraph=a(880),c.CoSEGraphManager=a(578),c.CoSELayout=a(765),c.CoSENode=a(991),c.ConstraintHandler=a(902),n.exports=c},806:(n,r,a)=>{var c=a(551).FDLayoutConstants;function t(){}L(t,"CoSEConstants");for(var u in c)t[u]=c[u];t.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,t.DEFAULT_RADIAL_SEPARATION=c.DEFAULT_EDGE_LENGTH,t.DEFAULT_COMPONENT_SEPERATION=60,t.TILE=!0,t.TILING_PADDING_VERTICAL=10,t.TILING_PADDING_HORIZONTAL=10,t.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,t.ENFORCE_CONSTRAINTS=!0,t.APPLY_LAYOUT=!0,t.RELAX_MOVEMENT_ON_CONSTRAINTS=!0,t.TREE_REDUCTION_ON_INCREMENTAL=!0,t.PURE_INCREMENTAL=t.DEFAULT_INCREMENTAL,n.exports=t},767:(n,r,a)=>{var c=a(551).FDLayoutEdge;function t(i,e,l){c.call(this,i,e,l)}L(t,"CoSEEdge"),t.prototype=Object.create(c.prototype);for(var u in c)t[u]=c[u];n.exports=t},880:(n,r,a)=>{var c=a(551).LGraph;function t(i,e,l){c.call(this,i,e,l)}L(t,"CoSEGraph"),t.prototype=Object.create(c.prototype);for(var u in c)t[u]=c[u];n.exports=t},578:(n,r,a)=>{var c=a(551).LGraphManager;function t(i){c.call(this,i)}L(t,"CoSEGraphManager"),t.prototype=Object.create(c.prototype);for(var u in c)t[u]=c[u];n.exports=t},765:(n,r,a)=>{var c=a(551).FDLayout,t=a(578),u=a(880),i=a(991),e=a(767),l=a(806),f=a(902),h=a(551).FDLayoutConstants,A=a(551).LayoutConstants,v=a(551).Point,y=a(551).PointD,N=a(551).DimensionD,S=a(551).Layout,M=a(551).Integer,b=a(551).IGeometry,$=a(551).LGraph,X=a(551).Transform,et=a(551).LinkedList;function R(){c.call(this),this.toBeTiled={},this.constraints={}}L(R,"CoSELayout"),R.prototype=Object.create(c.prototype);for(var J in c)R[J]=c[J];R.prototype.newGraphManager=function(){var s=new t(this);return this.graphManager=s,s},R.prototype.newGraph=function(s){return new u(null,this.graphManager,s)},R.prototype.newNode=function(s){return new i(this.graphManager,s)},R.prototype.newEdge=function(s){return new e(null,null,s)},R.prototype.initParameters=function(){c.prototype.initParameters.call(this,arguments),this.isSubLayout||(l.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=l.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=l.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=h.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=h.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=h.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=h.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1)},R.prototype.initSpringEmbedder=function(){c.prototype.initSpringEmbedder.call(this),this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/h.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=.04,this.coolingAdjuster=1},R.prototype.layout=function(){var s=A.DEFAULT_CREATE_BENDS_AS_NEEDED;return s&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},R.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(l.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var m=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(O){return m.has(O)});this.graphManager.setAllNodesToApplyGravitation(p)}}else{var s=this.getFlatForest();if(s.length>0)this.positionNodesRadially(s);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var m=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(E){return m.has(E)});this.graphManager.setAllNodesToApplyGravitation(p),this.positionNodesRandomly()}}return Object.keys(this.constraints).length>0&&(f.handleConstraints(this),this.initConstraintVariables()),this.initSpringEmbedder(),l.APPLY_LAYOUT&&this.runSpringEmbedder(),!0},R.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%h.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var s=new Set(this.getAllNodes()),m=this.nodesWithGravity.filter(function(d){return s.has(d)});this.graphManager.setAllNodesToApplyGravitation(m),this.graphManager.updateBounds(),this.updateGrid(),l.PURE_INCREMENTAL?this.coolingFactor=h.DEFAULT_COOLING_FACTOR_INCREMENTAL/2:this.coolingFactor=h.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),l.PURE_INCREMENTAL?this.coolingFactor=h.DEFAULT_COOLING_FACTOR_INCREMENTAL/2*((100-this.afterGrowthIterations)/100):this.coolingFactor=h.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var p=!this.isTreeGrowing&&!this.isGrowthFinished,E=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(p,E),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},R.prototype.getPositionsData=function(){for(var s=this.graphManager.getAllNodes(),m={},p=0;p<s.length;p++){var E=s[p].rect,d=s[p].id;m[d]={id:d,x:E.getCenterX(),y:E.getCenterY(),w:E.width,h:E.height}}return m},R.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var s=!1;if(h.ANIMATE==="during")this.emit("layoutstarted");else{for(;!s;)s=this.tick();this.graphManager.updateBounds()}},R.prototype.moveNodes=function(){for(var s=this.getAllNodes(),m,p=0;p<s.length;p++)m=s[p],m.calculateDisplacement();Object.keys(this.constraints).length>0&&this.updateDisplacements();for(var p=0;p<s.length;p++)m=s[p],m.move()},R.prototype.initConstraintVariables=function(){var s=this;this.idToNodeMap=new Map,this.fixedNodeSet=new Set;for(var m=this.graphManager.getAllNodes(),p=0;p<m.length;p++){var E=m[p];this.idToNodeMap.set(E.id,E)}var d=L(function w(U){for(var H=U.getChild().getNodes(),K,ht=0,Nt=0;Nt<H.length;Nt++)K=H[Nt],K.getChild()==null?s.fixedNodeSet.has(K.id)&&(ht+=100):ht+=w(K);return ht},"calculateCompoundWeight");if(this.constraints.fixedNodeConstraint){this.constraints.fixedNodeConstraint.forEach(function(H){s.fixedNodeSet.add(H.nodeId)});for(var m=this.graphManager.getAllNodes(),E,p=0;p<m.length;p++)if(E=m[p],E.getChild()!=null){var O=d(E);O>0&&(E.fixedNodeWeight=O)}}if(this.constraints.relativePlacementConstraint){var x=new Map,G=new Map;if(this.dummyToNodeForVerticalAlignment=new Map,this.dummyToNodeForHorizontalAlignment=new Map,this.fixedNodesOnHorizontal=new Set,this.fixedNodesOnVertical=new Set,this.fixedNodeSet.forEach(function(w){s.fixedNodesOnHorizontal.add(w),s.fixedNodesOnVertical.add(w)}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var F=this.constraints.alignmentConstraint.vertical,p=0;p<F.length;p++)this.dummyToNodeForVerticalAlignment.set("dummy"+p,[]),F[p].forEach(function(U){x.set(U,"dummy"+p),s.dummyToNodeForVerticalAlignment.get("dummy"+p).push(U),s.fixedNodeSet.has(U)&&s.fixedNodesOnHorizontal.add("dummy"+p)});if(this.constraints.alignmentConstraint.horizontal)for(var I=this.constraints.alignmentConstraint.horizontal,p=0;p<I.length;p++)this.dummyToNodeForHorizontalAlignment.set("dummy"+p,[]),I[p].forEach(function(U){G.set(U,"dummy"+p),s.dummyToNodeForHorizontalAlignment.get("dummy"+p).push(U),s.fixedNodeSet.has(U)&&s.fixedNodesOnVertical.add("dummy"+p)})}if(l.RELAX_MOVEMENT_ON_CONSTRAINTS)this.shuffle=function(w){var U,H,K;for(K=w.length-1;K>=2*w.length/3;K--)U=Math.floor(Math.random()*(K+1)),H=w[K],w[K]=w[U],w[U]=H;return w},this.nodesInRelativeHorizontal=[],this.nodesInRelativeVertical=[],this.nodeToRelativeConstraintMapHorizontal=new Map,this.nodeToRelativeConstraintMapVertical=new Map,this.nodeToTempPositionMapHorizontal=new Map,this.nodeToTempPositionMapVertical=new Map,this.constraints.relativePlacementConstraint.forEach(function(w){if(w.left){var U=x.has(w.left)?x.get(w.left):w.left,H=x.has(w.right)?x.get(w.right):w.right;s.nodesInRelativeHorizontal.includes(U)||(s.nodesInRelativeHorizontal.push(U),s.nodeToRelativeConstraintMapHorizontal.set(U,[]),s.dummyToNodeForVerticalAlignment.has(U)?s.nodeToTempPositionMapHorizontal.set(U,s.idToNodeMap.get(s.dummyToNodeForVerticalAlignment.get(U)[0]).getCenterX()):s.nodeToTempPositionMapHorizontal.set(U,s.idToNodeMap.get(U).getCenterX())),s.nodesInRelativeHorizontal.includes(H)||(s.nodesInRelativeHorizontal.push(H),s.nodeToRelativeConstraintMapHorizontal.set(H,[]),s.dummyToNodeForVerticalAlignment.has(H)?s.nodeToTempPositionMapHorizontal.set(H,s.idToNodeMap.get(s.dummyToNodeForVerticalAlignment.get(H)[0]).getCenterX()):s.nodeToTempPositionMapHorizontal.set(H,s.idToNodeMap.get(H).getCenterX())),s.nodeToRelativeConstraintMapHorizontal.get(U).push({right:H,gap:w.gap}),s.nodeToRelativeConstraintMapHorizontal.get(H).push({left:U,gap:w.gap})}else{var K=G.has(w.top)?G.get(w.top):w.top,ht=G.has(w.bottom)?G.get(w.bottom):w.bottom;s.nodesInRelativeVertical.includes(K)||(s.nodesInRelativeVertical.push(K),s.nodeToRelativeConstraintMapVertical.set(K,[]),s.dummyToNodeForHorizontalAlignment.has(K)?s.nodeToTempPositionMapVertical.set(K,s.idToNodeMap.get(s.dummyToNodeForHorizontalAlignment.get(K)[0]).getCenterY()):s.nodeToTempPositionMapVertical.set(K,s.idToNodeMap.get(K).getCenterY())),s.nodesInRelativeVertical.includes(ht)||(s.nodesInRelativeVertical.push(ht),s.nodeToRelativeConstraintMapVertical.set(ht,[]),s.dummyToNodeForHorizontalAlignment.has(ht)?s.nodeToTempPositionMapVertical.set(ht,s.idToNodeMap.get(s.dummyToNodeForHorizontalAlignment.get(ht)[0]).getCenterY()):s.nodeToTempPositionMapVertical.set(ht,s.idToNodeMap.get(ht).getCenterY())),s.nodeToRelativeConstraintMapVertical.get(K).push({bottom:ht,gap:w.gap}),s.nodeToRelativeConstraintMapVertical.get(ht).push({top:K,gap:w.gap})}});else{var Z=new Map,tt=new Map;this.constraints.relativePlacementConstraint.forEach(function(w){if(w.left){var U=x.has(w.left)?x.get(w.left):w.left,H=x.has(w.right)?x.get(w.right):w.right;Z.has(U)?Z.get(U).push(H):Z.set(U,[H]),Z.has(H)?Z.get(H).push(U):Z.set(H,[U])}else{var K=G.has(w.top)?G.get(w.top):w.top,ht=G.has(w.bottom)?G.get(w.bottom):w.bottom;tt.has(K)?tt.get(K).push(ht):tt.set(K,[ht]),tt.has(ht)?tt.get(ht).push(K):tt.set(ht,[K])}});var P=L(function(U,H){var K=[],ht=[],Nt=new et,St=new Set,Q=0;return U.forEach(function(Yt,Mt){if(!St.has(Mt)){K[Q]=[],ht[Q]=!1;var ot=Mt;for(Nt.push(ot),St.add(ot),K[Q].push(ot);Nt.length!=0;){ot=Nt.shift(),H.has(ot)&&(ht[Q]=!0);var rt=U.get(ot);rt.forEach(function(vt){St.has(vt)||(Nt.push(vt),St.add(vt),K[Q].push(vt))})}Q++}}),{components:K,isFixed:ht}},"constructComponents"),_=P(Z,s.fixedNodesOnHorizontal);this.componentsOnHorizontal=_.components,this.fixedComponentsOnHorizontal=_.isFixed;var B=P(tt,s.fixedNodesOnVertical);this.componentsOnVertical=B.components,this.fixedComponentsOnVertical=B.isFixed}}},R.prototype.updateDisplacements=function(){var s=this;if(this.constraints.fixedNodeConstraint&&this.constraints.fixedNodeConstraint.forEach(function(B){var w=s.idToNodeMap.get(B.nodeId);w.displacementX=0,w.displacementY=0}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var m=this.constraints.alignmentConstraint.vertical,p=0;p<m.length;p++){for(var E=0,d=0;d<m[p].length;d++){if(this.fixedNodeSet.has(m[p][d])){E=0;break}E+=this.idToNodeMap.get(m[p][d]).displacementX}for(var O=E/m[p].length,d=0;d<m[p].length;d++)this.idToNodeMap.get(m[p][d]).displacementX=O}if(this.constraints.alignmentConstraint.horizontal)for(var x=this.constraints.alignmentConstraint.horizontal,p=0;p<x.length;p++){for(var G=0,d=0;d<x[p].length;d++){if(this.fixedNodeSet.has(x[p][d])){G=0;break}G+=this.idToNodeMap.get(x[p][d]).displacementY}for(var F=G/x[p].length,d=0;d<x[p].length;d++)this.idToNodeMap.get(x[p][d]).displacementY=F}}if(this.constraints.relativePlacementConstraint)if(l.RELAX_MOVEMENT_ON_CONSTRAINTS)this.totalIterations%10==0&&(this.shuffle(this.nodesInRelativeHorizontal),this.shuffle(this.nodesInRelativeVertical)),this.nodesInRelativeHorizontal.forEach(function(B){if(!s.fixedNodesOnHorizontal.has(B)){var w=0;s.dummyToNodeForVerticalAlignment.has(B)?w=s.idToNodeMap.get(s.dummyToNodeForVerticalAlignment.get(B)[0]).displacementX:w=s.idToNodeMap.get(B).displacementX,s.nodeToRelativeConstraintMapHorizontal.get(B).forEach(function(U){if(U.right){var H=s.nodeToTempPositionMapHorizontal.get(U.right)-s.nodeToTempPositionMapHorizontal.get(B)-w;H<U.gap&&(w-=U.gap-H)}else{var H=s.nodeToTempPositionMapHorizontal.get(B)-s.nodeToTempPositionMapHorizontal.get(U.left)+w;H<U.gap&&(w+=U.gap-H)}}),s.nodeToTempPositionMapHorizontal.set(B,s.nodeToTempPositionMapHorizontal.get(B)+w),s.dummyToNodeForVerticalAlignment.has(B)?s.dummyToNodeForVerticalAlignment.get(B).forEach(function(U){s.idToNodeMap.get(U).displacementX=w}):s.idToNodeMap.get(B).displacementX=w}}),this.nodesInRelativeVertical.forEach(function(B){if(!s.fixedNodesOnHorizontal.has(B)){var w=0;s.dummyToNodeForHorizontalAlignment.has(B)?w=s.idToNodeMap.get(s.dummyToNodeForHorizontalAlignment.get(B)[0]).displacementY:w=s.idToNodeMap.get(B).displacementY,s.nodeToRelativeConstraintMapVertical.get(B).forEach(function(U){if(U.bottom){var H=s.nodeToTempPositionMapVertical.get(U.bottom)-s.nodeToTempPositionMapVertical.get(B)-w;H<U.gap&&(w-=U.gap-H)}else{var H=s.nodeToTempPositionMapVertical.get(B)-s.nodeToTempPositionMapVertical.get(U.top)+w;H<U.gap&&(w+=U.gap-H)}}),s.nodeToTempPositionMapVertical.set(B,s.nodeToTempPositionMapVertical.get(B)+w),s.dummyToNodeForHorizontalAlignment.has(B)?s.dummyToNodeForHorizontalAlignment.get(B).forEach(function(U){s.idToNodeMap.get(U).displacementY=w}):s.idToNodeMap.get(B).displacementY=w}});else{for(var p=0;p<this.componentsOnHorizontal.length;p++){var I=this.componentsOnHorizontal[p];if(this.fixedComponentsOnHorizontal[p])for(var d=0;d<I.length;d++)this.dummyToNodeForVerticalAlignment.has(I[d])?this.dummyToNodeForVerticalAlignment.get(I[d]).forEach(function(U){s.idToNodeMap.get(U).displacementX=0}):this.idToNodeMap.get(I[d]).displacementX=0;else{for(var Z=0,tt=0,d=0;d<I.length;d++)if(this.dummyToNodeForVerticalAlignment.has(I[d])){var P=this.dummyToNodeForVerticalAlignment.get(I[d]);Z+=P.length*this.idToNodeMap.get(P[0]).displacementX,tt+=P.length}else Z+=this.idToNodeMap.get(I[d]).displacementX,tt++;for(var _=Z/tt,d=0;d<I.length;d++)this.dummyToNodeForVerticalAlignment.has(I[d])?this.dummyToNodeForVerticalAlignment.get(I[d]).forEach(function(U){s.idToNodeMap.get(U).displacementX=_}):this.idToNodeMap.get(I[d]).displacementX=_}}for(var p=0;p<this.componentsOnVertical.length;p++){var I=this.componentsOnVertical[p];if(this.fixedComponentsOnVertical[p])for(var d=0;d<I.length;d++)this.dummyToNodeForHorizontalAlignment.has(I[d])?this.dummyToNodeForHorizontalAlignment.get(I[d]).forEach(function(H){s.idToNodeMap.get(H).displacementY=0}):this.idToNodeMap.get(I[d]).displacementY=0;else{for(var Z=0,tt=0,d=0;d<I.length;d++)if(this.dummyToNodeForHorizontalAlignment.has(I[d])){var P=this.dummyToNodeForHorizontalAlignment.get(I[d]);Z+=P.length*this.idToNodeMap.get(P[0]).displacementY,tt+=P.length}else Z+=this.idToNodeMap.get(I[d]).displacementY,tt++;for(var _=Z/tt,d=0;d<I.length;d++)this.dummyToNodeForHorizontalAlignment.has(I[d])?this.dummyToNodeForHorizontalAlignment.get(I[d]).forEach(function(Nt){s.idToNodeMap.get(Nt).displacementY=_}):this.idToNodeMap.get(I[d]).displacementY=_}}}},R.prototype.calculateNodesToApplyGravitationTo=function(){var s=[],m,p=this.graphManager.getGraphs(),E=p.length,d;for(d=0;d<E;d++)m=p[d],m.updateConnected(),m.isConnected||(s=s.concat(m.getNodes()));return s},R.prototype.createBendpoints=function(){var s=[];s=s.concat(this.graphManager.getAllEdges());var m=new Set,p;for(p=0;p<s.length;p++){var E=s[p];if(!m.has(E)){var d=E.getSource(),O=E.getTarget();if(d==O)E.getBendpoints().push(new y),E.getBendpoints().push(new y),this.createDummyNodesForBendpoints(E),m.add(E);else{var x=[];if(x=x.concat(d.getEdgeListToNode(O)),x=x.concat(O.getEdgeListToNode(d)),!m.has(x[0])){if(x.length>1){var G;for(G=0;G<x.length;G++){var F=x[G];F.getBendpoints().push(new y),this.createDummyNodesForBendpoints(F)}}x.forEach(function(I){m.add(I)})}}}if(m.size==s.length)break}},R.prototype.positionNodesRadially=function(s){for(var m=new v(0,0),p=Math.ceil(Math.sqrt(s.length)),E=0,d=0,O=0,x=new y(0,0),G=0;G<s.length;G++){G%p==0&&(O=0,d=E,G!=0&&(d+=l.DEFAULT_COMPONENT_SEPERATION),E=0);var F=s[G],I=S.findCenterOfTree(F);m.x=O,m.y=d,x=R.radialLayout(F,I,m),x.y>E&&(E=Math.floor(x.y)),O=Math.floor(x.x+l.DEFAULT_COMPONENT_SEPERATION)}this.transform(new y(A.WORLD_CENTER_X-x.x/2,A.WORLD_CENTER_Y-x.y/2))},R.radialLayout=function(s,m,p){var E=Math.max(this.maxDiagonalInTree(s),l.DEFAULT_RADIAL_SEPARATION);R.branchRadialLayout(m,null,0,359,0,E);var d=$.calculateBounds(s),O=new X;O.setDeviceOrgX(d.getMinX()),O.setDeviceOrgY(d.getMinY()),O.setWorldOrgX(p.x),O.setWorldOrgY(p.y);for(var x=0;x<s.length;x++){var G=s[x];G.transform(O)}var F=new y(d.getMaxX(),d.getMaxY());return O.inverseTransformPoint(F)},R.branchRadialLayout=function(s,m,p,E,d,O){var x=(E-p+1)/2;x<0&&(x+=180);var G=(x+p)%360,F=G*b.TWO_PI/360,I=Math.cos(F),Z=d*Math.cos(F),tt=d*Math.sin(F);s.setCenter(Z,tt);var P=[];P=P.concat(s.getEdges());var _=P.length;m!=null&&_--;for(var B=0,w=P.length,U,H=s.getEdgesBetween(m);H.length>1;){var K=H[0];H.splice(0,1);var ht=P.indexOf(K);ht>=0&&P.splice(ht,1),w--,_--}m!=null?U=(P.indexOf(H[0])+1)%w:U=0;for(var Nt=Math.abs(E-p)/_,St=U;B!=_;St=++St%w){var Q=P[St].getOtherEnd(s);if(Q!=m){var Yt=(p+B*Nt)%360,Mt=(Yt+Nt)%360;R.branchRadialLayout(Q,s,Yt,Mt,d+O,O),B++}}},R.maxDiagonalInTree=function(s){for(var m=M.MIN_VALUE,p=0;p<s.length;p++){var E=s[p],d=E.getDiagonal();d>m&&(m=d)}return m},R.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},R.prototype.groupZeroDegreeMembers=function(){var s=this,m={};this.memberGroups={},this.idToDummyNode={};for(var p=[],E=this.graphManager.getAllNodes(),d=0;d<E.length;d++){var O=E[d],x=O.getParent();this.getNodeDegreeWithChildren(O)===0&&(x.id==null||!this.getToBeTiled(x))&&p.push(O)}for(var d=0;d<p.length;d++){var O=p[d],G=O.getParent().id;typeof m[G]>"u"&&(m[G]=[]),m[G]=m[G].concat(O)}Object.keys(m).forEach(function(F){if(m[F].length>1){var I="DummyCompound_"+F;s.memberGroups[I]=m[F];var Z=m[F][0].getParent(),tt=new i(s.graphManager);tt.id=I,tt.paddingLeft=Z.paddingLeft||0,tt.paddingRight=Z.paddingRight||0,tt.paddingBottom=Z.paddingBottom||0,tt.paddingTop=Z.paddingTop||0,s.idToDummyNode[I]=tt;var P=s.getGraphManager().add(s.newGraph(),tt),_=Z.getChild();_.add(tt);for(var B=0;B<m[F].length;B++){var w=m[F][B];_.remove(w),P.add(w)}}})},R.prototype.clearCompounds=function(){var s={},m={};this.performDFSOnCompounds();for(var p=0;p<this.compoundOrder.length;p++)m[this.compoundOrder[p].id]=this.compoundOrder[p],s[this.compoundOrder[p].id]=[].concat(this.compoundOrder[p].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[p].getChild()),this.compoundOrder[p].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(s,m)},R.prototype.clearZeroDegreeMembers=function(){var s=this,m=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(p){var E=s.idToDummyNode[p];if(m[p]=s.tileNodes(s.memberGroups[p],E.paddingLeft+E.paddingRight),E.rect.width=m[p].width,E.rect.height=m[p].height,E.setCenter(m[p].centerX,m[p].centerY),E.labelMarginLeft=0,E.labelMarginTop=0,l.NODE_DIMENSIONS_INCLUDE_LABELS){var d=E.rect.width,O=E.rect.height;E.labelWidth&&(E.labelPosHorizontal=="left"?(E.rect.x-=E.labelWidth,E.setWidth(d+E.labelWidth),E.labelMarginLeft=E.labelWidth):E.labelPosHorizontal=="center"&&E.labelWidth>d?(E.rect.x-=(E.labelWidth-d)/2,E.setWidth(E.labelWidth),E.labelMarginLeft=(E.labelWidth-d)/2):E.labelPosHorizontal=="right"&&E.setWidth(d+E.labelWidth)),E.labelHeight&&(E.labelPosVertical=="top"?(E.rect.y-=E.labelHeight,E.setHeight(O+E.labelHeight),E.labelMarginTop=E.labelHeight):E.labelPosVertical=="center"&&E.labelHeight>O?(E.rect.y-=(E.labelHeight-O)/2,E.setHeight(E.labelHeight),E.labelMarginTop=(E.labelHeight-O)/2):E.labelPosVertical=="bottom"&&E.setHeight(O+E.labelHeight))}})},R.prototype.repopulateCompounds=function(){for(var s=this.compoundOrder.length-1;s>=0;s--){var m=this.compoundOrder[s],p=m.id,E=m.paddingLeft,d=m.paddingTop,O=m.labelMarginLeft,x=m.labelMarginTop;this.adjustLocations(this.tiledMemberPack[p],m.rect.x,m.rect.y,E,d,O,x)}},R.prototype.repopulateZeroDegreeMembers=function(){var s=this,m=this.tiledZeroDegreePack;Object.keys(m).forEach(function(p){var E=s.idToDummyNode[p],d=E.paddingLeft,O=E.paddingTop,x=E.labelMarginLeft,G=E.labelMarginTop;s.adjustLocations(m[p],E.rect.x,E.rect.y,d,O,x,G)})},R.prototype.getToBeTiled=function(s){var m=s.id;if(this.toBeTiled[m]!=null)return this.toBeTiled[m];var p=s.getChild();if(p==null)return this.toBeTiled[m]=!1,!1;for(var E=p.getNodes(),d=0;d<E.length;d++){var O=E[d];if(this.getNodeDegree(O)>0)return this.toBeTiled[m]=!1,!1;if(O.getChild()==null){this.toBeTiled[O.id]=!1;continue}if(!this.getToBeTiled(O))return this.toBeTiled[m]=!1,!1}return this.toBeTiled[m]=!0,!0},R.prototype.getNodeDegree=function(s){for(var m=s.id,p=s.getEdges(),E=0,d=0;d<p.length;d++){var O=p[d];O.getSource().id!==O.getTarget().id&&(E=E+1)}return E},R.prototype.getNodeDegreeWithChildren=function(s){var m=this.getNodeDegree(s);if(s.getChild()==null)return m;for(var p=s.getChild().getNodes(),E=0;E<p.length;E++){var d=p[E];m+=this.getNodeDegreeWithChildren(d)}return m},R.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},R.prototype.fillCompexOrderByDFS=function(s){for(var m=0;m<s.length;m++){var p=s[m];p.getChild()!=null&&this.fillCompexOrderByDFS(p.getChild().getNodes()),this.getToBeTiled(p)&&this.compoundOrder.push(p)}},R.prototype.adjustLocations=function(s,m,p,E,d,O,x){m+=E+O,p+=d+x;for(var G=m,F=0;F<s.rows.length;F++){var I=s.rows[F];m=G;for(var Z=0,tt=0;tt<I.length;tt++){var P=I[tt];P.rect.x=m,P.rect.y=p,m+=P.rect.width+s.horizontalPadding,P.rect.height>Z&&(Z=P.rect.height)}p+=Z+s.verticalPadding}},R.prototype.tileCompoundMembers=function(s,m){var p=this;this.tiledMemberPack=[],Object.keys(s).forEach(function(E){var d=m[E];if(p.tiledMemberPack[E]=p.tileNodes(s[E],d.paddingLeft+d.paddingRight),d.rect.width=p.tiledMemberPack[E].width,d.rect.height=p.tiledMemberPack[E].height,d.setCenter(p.tiledMemberPack[E].centerX,p.tiledMemberPack[E].centerY),d.labelMarginLeft=0,d.labelMarginTop=0,l.NODE_DIMENSIONS_INCLUDE_LABELS){var O=d.rect.width,x=d.rect.height;d.labelWidth&&(d.labelPosHorizontal=="left"?(d.rect.x-=d.labelWidth,d.setWidth(O+d.labelWidth),d.labelMarginLeft=d.labelWidth):d.labelPosHorizontal=="center"&&d.labelWidth>O?(d.rect.x-=(d.labelWidth-O)/2,d.setWidth(d.labelWidth),d.labelMarginLeft=(d.labelWidth-O)/2):d.labelPosHorizontal=="right"&&d.setWidth(O+d.labelWidth)),d.labelHeight&&(d.labelPosVertical=="top"?(d.rect.y-=d.labelHeight,d.setHeight(x+d.labelHeight),d.labelMarginTop=d.labelHeight):d.labelPosVertical=="center"&&d.labelHeight>x?(d.rect.y-=(d.labelHeight-x)/2,d.setHeight(d.labelHeight),d.labelMarginTop=(d.labelHeight-x)/2):d.labelPosVertical=="bottom"&&d.setHeight(x+d.labelHeight))}})},R.prototype.tileNodes=function(s,m){var p=this.tileNodesByFavoringDim(s,m,!0),E=this.tileNodesByFavoringDim(s,m,!1),d=this.getOrgRatio(p),O=this.getOrgRatio(E),x;return O<d?x=E:x=p,x},R.prototype.getOrgRatio=function(s){var m=s.width,p=s.height,E=m/p;return E<1&&(E=1/E),E},R.prototype.calcIdealRowWidth=function(s,m){var p=l.TILING_PADDING_VERTICAL,E=l.TILING_PADDING_HORIZONTAL,d=s.length,O=0,x=0,G=0;s.forEach(function(B){O+=B.getWidth(),x+=B.getHeight(),B.getWidth()>G&&(G=B.getWidth())});var F=O/d,I=x/d,Z=Math.pow(p-E,2)+4*(F+E)*(I+p)*d,tt=(E-p+Math.sqrt(Z))/(2*(F+E)),P;m?(P=Math.ceil(tt),P==tt&&P++):P=Math.floor(tt);var _=P*(F+E)-E;return G>_&&(_=G),_+=E*2,_},R.prototype.tileNodesByFavoringDim=function(s,m,p){var E=l.TILING_PADDING_VERTICAL,d=l.TILING_PADDING_HORIZONTAL,O=l.TILING_COMPARE_BY,x={rows:[],rowWidth:[],rowHeight:[],width:0,height:m,verticalPadding:E,horizontalPadding:d,centerX:0,centerY:0};O&&(x.idealRowWidth=this.calcIdealRowWidth(s,p));var G=L(function(w){return w.rect.width*w.rect.height},"getNodeArea"),F=L(function(w,U){return G(U)-G(w)},"areaCompareFcn");s.sort(function(B,w){var U=F;return x.idealRowWidth?(U=O,U(B.id,w.id)):U(B,w)});for(var I=0,Z=0,tt=0;tt<s.length;tt++){var P=s[tt];I+=P.getCenterX(),Z+=P.getCenterY()}x.centerX=I/s.length,x.centerY=Z/s.length;for(var tt=0;tt<s.length;tt++){var P=s[tt];if(x.rows.length==0)this.insertNodeToRow(x,P,0,m);else if(this.canAddHorizontal(x,P.rect.width,P.rect.height)){var _=x.rows.length-1;x.idealRowWidth||(_=this.getShortestRowIndex(x)),this.insertNodeToRow(x,P,_,m)}else this.insertNodeToRow(x,P,x.rows.length,m);this.shiftToLastRow(x)}return x},R.prototype.insertNodeToRow=function(s,m,p,E){var d=E;if(p==s.rows.length){var O=[];s.rows.push(O),s.rowWidth.push(d),s.rowHeight.push(0)}var x=s.rowWidth[p]+m.rect.width;s.rows[p].length>0&&(x+=s.horizontalPadding),s.rowWidth[p]=x,s.width<x&&(s.width=x);var G=m.rect.height;p>0&&(G+=s.verticalPadding);var F=0;G>s.rowHeight[p]&&(F=s.rowHeight[p],s.rowHeight[p]=G,F=s.rowHeight[p]-F),s.height+=F,s.rows[p].push(m)},R.prototype.getShortestRowIndex=function(s){for(var m=-1,p=Number.MAX_VALUE,E=0;E<s.rows.length;E++)s.rowWidth[E]<p&&(m=E,p=s.rowWidth[E]);return m},R.prototype.getLongestRowIndex=function(s){for(var m=-1,p=Number.MIN_VALUE,E=0;E<s.rows.length;E++)s.rowWidth[E]>p&&(m=E,p=s.rowWidth[E]);return m},R.prototype.canAddHorizontal=function(s,m,p){if(s.idealRowWidth){var E=s.rows.length-1,d=s.rowWidth[E];return d+m+s.horizontalPadding<=s.idealRowWidth}var O=this.getShortestRowIndex(s);if(O<0)return!0;var x=s.rowWidth[O];if(x+s.horizontalPadding+m<=s.width)return!0;var G=0;s.rowHeight[O]<p&&O>0&&(G=p+s.verticalPadding-s.rowHeight[O]);var F;s.width-x>=m+s.horizontalPadding?F=(s.height+G)/(x+m+s.horizontalPadding):F=(s.height+G)/s.width,G=p+s.verticalPadding;var I;return s.width<m?I=(s.height+G)/m:I=(s.height+G)/s.width,I<1&&(I=1/I),F<1&&(F=1/F),F<I},R.prototype.shiftToLastRow=function(s){var m=this.getLongestRowIndex(s),p=s.rowWidth.length-1,E=s.rows[m],d=E[E.length-1],O=d.width+s.horizontalPadding;if(s.width-s.rowWidth[p]>O&&m!=p){E.splice(-1,1),s.rows[p].push(d),s.rowWidth[m]=s.rowWidth[m]-O,s.rowWidth[p]=s.rowWidth[p]+O,s.width=s.rowWidth[instance.getLongestRowIndex(s)];for(var x=Number.MIN_VALUE,G=0;G<E.length;G++)E[G].height>x&&(x=E[G].height);m>0&&(x+=s.verticalPadding);var F=s.rowHeight[m]+s.rowHeight[p];s.rowHeight[m]=x,s.rowHeight[p]<d.height+s.verticalPadding&&(s.rowHeight[p]=d.height+s.verticalPadding);var I=s.rowHeight[m]+s.rowHeight[p];s.height+=I-F,this.shiftToLastRow(s)}},R.prototype.tilingPreLayout=function(){l.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},R.prototype.tilingPostLayout=function(){l.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},R.prototype.reduceTrees=function(){for(var s=[],m=!0,p;m;){var E=this.graphManager.getAllNodes(),d=[];m=!1;for(var O=0;O<E.length;O++)if(p=E[O],p.getEdges().length==1&&!p.getEdges()[0].isInterGraph&&p.getChild()==null){if(l.PURE_INCREMENTAL){var x=p.getEdges()[0].getOtherEnd(p),G=new N(p.getCenterX()-x.getCenterX(),p.getCenterY()-x.getCenterY());d.push([p,p.getEdges()[0],p.getOwner(),G])}else d.push([p,p.getEdges()[0],p.getOwner()]);m=!0}if(m==!0){for(var F=[],I=0;I<d.length;I++)d[I][0].getEdges().length==1&&(F.push(d[I]),d[I][0].getOwner().remove(d[I][0]));s.push(F),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=s},R.prototype.growTree=function(s){for(var m=s.length,p=s[m-1],E,d=0;d<p.length;d++)E=p[d],this.findPlaceforPrunedNode(E),E[2].add(E[0]),E[2].add(E[1],E[1].source,E[1].target);s.splice(s.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},R.prototype.findPlaceforPrunedNode=function(s){var m,p,E=s[0];if(E==s[1].source?p=s[1].target:p=s[1].source,l.PURE_INCREMENTAL)E.setCenter(p.getCenterX()+s[3].getWidth(),p.getCenterY()+s[3].getHeight());else{var d=p.startX,O=p.finishX,x=p.startY,G=p.finishY,F=0,I=0,Z=0,tt=0,P=[F,Z,I,tt];if(x>0)for(var _=d;_<=O;_++)P[0]+=this.grid[_][x-1].length+this.grid[_][x].length-1;if(O<this.grid.length-1)for(var _=x;_<=G;_++)P[1]+=this.grid[O+1][_].length+this.grid[O][_].length-1;if(G<this.grid[0].length-1)for(var _=d;_<=O;_++)P[2]+=this.grid[_][G+1].length+this.grid[_][G].length-1;if(d>0)for(var _=x;_<=G;_++)P[3]+=this.grid[d-1][_].length+this.grid[d][_].length-1;for(var B=M.MAX_VALUE,w,U,H=0;H<P.length;H++)P[H]<B?(B=P[H],w=1,U=H):P[H]==B&&w++;if(w==3&&B==0)P[0]==0&&P[1]==0&&P[2]==0?m=1:P[0]==0&&P[1]==0&&P[3]==0?m=0:P[0]==0&&P[2]==0&&P[3]==0?m=3:P[1]==0&&P[2]==0&&P[3]==0&&(m=2);else if(w==2&&B==0){var K=Math.floor(Math.random()*2);P[0]==0&&P[1]==0?K==0?m=0:m=1:P[0]==0&&P[2]==0?K==0?m=0:m=2:P[0]==0&&P[3]==0?K==0?m=0:m=3:P[1]==0&&P[2]==0?K==0?m=1:m=2:P[1]==0&&P[3]==0?K==0?m=1:m=3:K==0?m=2:m=3}else if(w==4&&B==0){var K=Math.floor(Math.random()*4);m=K}else m=U;m==0?E.setCenter(p.getCenterX(),p.getCenterY()-p.getHeight()/2-h.DEFAULT_EDGE_LENGTH-E.getHeight()/2):m==1?E.setCenter(p.getCenterX()+p.getWidth()/2+h.DEFAULT_EDGE_LENGTH+E.getWidth()/2,p.getCenterY()):m==2?E.setCenter(p.getCenterX(),p.getCenterY()+p.getHeight()/2+h.DEFAULT_EDGE_LENGTH+E.getHeight()/2):E.setCenter(p.getCenterX()-p.getWidth()/2-h.DEFAULT_EDGE_LENGTH-E.getWidth()/2,p.getCenterY())}},n.exports=R},991:(n,r,a)=>{var c=a(551).FDLayoutNode,t=a(551).IMath;function u(e,l,f,h){c.call(this,e,l,f,h)}L(u,"CoSENode"),u.prototype=Object.create(c.prototype);for(var i in c)u[i]=c[i];u.prototype.calculateDisplacement=function(){var e=this.graphManager.getLayout();this.getChild()!=null&&this.fixedNodeWeight?(this.displacementX+=e.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.fixedNodeWeight,this.displacementY+=e.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.fixedNodeWeight):(this.displacementX+=e.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY+=e.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren),Math.abs(this.displacementX)>e.coolingFactor*e.maxNodeDisplacement&&(this.displacementX=e.coolingFactor*e.maxNodeDisplacement*t.sign(this.displacementX)),Math.abs(this.displacementY)>e.coolingFactor*e.maxNodeDisplacement&&(this.displacementY=e.coolingFactor*e.maxNodeDisplacement*t.sign(this.displacementY)),this.child&&this.child.getNodes().length>0&&this.propogateDisplacementToChildren(this.displacementX,this.displacementY)},u.prototype.propogateDisplacementToChildren=function(e,l){for(var f=this.getChild().getNodes(),h,A=0;A<f.length;A++)h=f[A],h.getChild()==null?(h.displacementX+=e,h.displacementY+=l):h.propogateDisplacementToChildren(e,l)},u.prototype.move=function(){var e=this.graphManager.getLayout();(this.child==null||this.child.getNodes().length==0)&&(this.moveBy(this.displacementX,this.displacementY),e.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY)),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},u.prototype.setPred1=function(e){this.pred1=e},u.prototype.getPred1=function(){return pred1},u.prototype.getPred2=function(){return pred2},u.prototype.setNext=function(e){this.next=e},u.prototype.getNext=function(){return next},u.prototype.setProcessed=function(e){this.processed=e},u.prototype.isProcessed=function(){return processed},n.exports=u},902:(n,r,a)=>{function c(f){if(Array.isArray(f)){for(var h=0,A=Array(f.length);h<f.length;h++)A[h]=f[h];return A}else return Array.from(f)}L(c,"_toConsumableArray");var t=a(806),u=a(551).LinkedList,i=a(551).Matrix,e=a(551).SVD;function l(){}L(l,"ConstraintHandler"),l.handleConstraints=function(f){var h={};h.fixedNodeConstraint=f.constraints.fixedNodeConstraint,h.alignmentConstraint=f.constraints.alignmentConstraint,h.relativePlacementConstraint=f.constraints.relativePlacementConstraint;for(var A=new Map,v=new Map,y=[],N=[],S=f.getAllNodes(),M=0,b=0;b<S.length;b++){var $=S[b];$.getChild()==null&&(v.set($.id,M++),y.push($.getCenterX()),N.push($.getCenterY()),A.set($.id,$))}h.relativePlacementConstraint&&h.relativePlacementConstraint.forEach(function(Y){!Y.gap&&Y.gap!=0&&(Y.left?Y.gap=t.DEFAULT_EDGE_LENGTH+A.get(Y.left).getWidth()/2+A.get(Y.right).getWidth()/2:Y.gap=t.DEFAULT_EDGE_LENGTH+A.get(Y.top).getHeight()/2+A.get(Y.bottom).getHeight()/2)});var X=L(function(W,V){return{x:W.x-V.x,y:W.y-V.y}},"calculatePositionDiff"),et=L(function(W){var V=0,z=0;return W.forEach(function(k){V+=y[v.get(k)],z+=N[v.get(k)]}),{x:V/W.size,y:z/W.size}},"calculateAvgPosition"),R=L(function(W,V,z,k,j){function gt(st,nt){var dt=new Set(st),at=!0,ct=!1,bt=void 0;try{for(var Ot=nt[Symbol.iterator](),Vt;!(at=(Vt=Ot.next()).done);at=!0){var xt=Vt.value;dt.add(xt)}}catch(At){ct=!0,bt=At}finally{try{!at&&Ot.return&&Ot.return()}finally{if(ct)throw bt}}return dt}L(gt,"setUnion");var ft=new Map;W.forEach(function(st,nt){ft.set(nt,0)}),W.forEach(function(st,nt){st.forEach(function(dt){ft.set(dt.id,ft.get(dt.id)+1)})});var q=new Map,lt=new Map,ut=new u;ft.forEach(function(st,nt){st==0?(ut.push(nt),z||(V=="horizontal"?q.set(nt,v.has(nt)?y[v.get(nt)]:k.get(nt)):q.set(nt,v.has(nt)?N[v.get(nt)]:k.get(nt)))):q.set(nt,Number.NEGATIVE_INFINITY),z&&lt.set(nt,new Set([nt]))}),z&&j.forEach(function(st){var nt=[];if(st.forEach(function(ct){z.has(ct)&&nt.push(ct)}),nt.length>0){var dt=0;nt.forEach(function(ct){V=="horizontal"?(q.set(ct,v.has(ct)?y[v.get(ct)]:k.get(ct)),dt+=q.get(ct)):(q.set(ct,v.has(ct)?N[v.get(ct)]:k.get(ct)),dt+=q.get(ct))}),dt=dt/nt.length,st.forEach(function(ct){z.has(ct)||q.set(ct,dt)})}else{var at=0;st.forEach(function(ct){V=="horizontal"?at+=v.has(ct)?y[v.get(ct)]:k.get(ct):at+=v.has(ct)?N[v.get(ct)]:k.get(ct)}),at=at/st.length,st.forEach(function(ct){q.set(ct,at)})}});for(var it=L(function(){var nt=ut.shift(),dt=W.get(nt);dt.forEach(function(at){if(q.get(at.id)<q.get(nt)+at.gap)if(z&&z.has(at.id)){var ct=void 0;if(V=="horizontal"?ct=v.has(at.id)?y[v.get(at.id)]:k.get(at.id):ct=v.has(at.id)?N[v.get(at.id)]:k.get(at.id),q.set(at.id,ct),ct<q.get(nt)+at.gap){var bt=q.get(nt)+at.gap-ct;lt.get(nt).forEach(function(Ot){q.set(Ot,q.get(Ot)-bt)})}}else q.set(at.id,q.get(nt)+at.gap);ft.set(at.id,ft.get(at.id)-1),ft.get(at.id)==0&&ut.push(at.id),z&&lt.set(at.id,gt(lt.get(nt),lt.get(at.id)))})},"_loop");ut.length!=0;)it();if(z){var pt=new Set;W.forEach(function(st,nt){st.length==0&&pt.add(nt)});var Dt=[];lt.forEach(function(st,nt){if(pt.has(nt)){var dt=!1,at=!0,ct=!1,bt=void 0;try{for(var Ot=st[Symbol.iterator](),Vt;!(at=(Vt=Ot.next()).done);at=!0){var xt=Vt.value;z.has(xt)&&(dt=!0)}}catch(It){ct=!0,bt=It}finally{try{!at&&Ot.return&&Ot.return()}finally{if(ct)throw bt}}if(!dt){var At=!1,$t=void 0;Dt.forEach(function(It,Xt){It.has([].concat(c(st))[0])&&(At=!0,$t=Xt)}),At?st.forEach(function(It){Dt[$t].add(It)}):Dt.push(new Set(st))}}}),Dt.forEach(function(st,nt){var dt=Number.POSITIVE_INFINITY,at=Number.POSITIVE_INFINITY,ct=Number.NEGATIVE_INFINITY,bt=Number.NEGATIVE_INFINITY,Ot=!0,Vt=!1,xt=void 0;try{for(var At=st[Symbol.iterator](),$t;!(Ot=($t=At.next()).done);Ot=!0){var It=$t.value,Xt=void 0;V=="horizontal"?Xt=v.has(It)?y[v.get(It)]:k.get(It):Xt=v.has(It)?N[v.get(It)]:k.get(It);var Bt=q.get(It);Xt<dt&&(dt=Xt),Xt>ct&&(ct=Xt),Bt<at&&(at=Bt),Bt>bt&&(bt=Bt)}}catch(ee){Vt=!0,xt=ee}finally{try{!Ot&&At.return&&At.return()}finally{if(Vt)throw xt}}var ve=(dt+ct)/2-(at+bt)/2,qt=!0,jt=!1,_t=void 0;try{for(var Qt=st[Symbol.iterator](),ce;!(qt=(ce=Qt.next()).done);qt=!0){var te=ce.value;q.set(te,q.get(te)+ve)}}catch(ee){jt=!0,_t=ee}finally{try{!qt&&Qt.return&&Qt.return()}finally{if(jt)throw _t}}})}return q},"findAppropriatePositionForRelativePlacement"),J=L(function(W){var V=0,z=0,k=0,j=0;if(W.forEach(function(lt){lt.left?y[v.get(lt.left)]-y[v.get(lt.right)]>=0?V++:z++:N[v.get(lt.top)]-N[v.get(lt.bottom)]>=0?k++:j++}),V>z&&k>j)for(var gt=0;gt<v.size;gt++)y[gt]=-1*y[gt],N[gt]=-1*N[gt];else if(V>z)for(var ft=0;ft<v.size;ft++)y[ft]=-1*y[ft];else if(k>j)for(var q=0;q<v.size;q++)N[q]=-1*N[q]},"applyReflectionForRelativePlacement"),s=L(function(W){var V=[],z=new u,k=new Set,j=0;return W.forEach(function(gt,ft){if(!k.has(ft)){V[j]=[];var q=ft;for(z.push(q),k.add(q),V[j].push(q);z.length!=0;){q=z.shift();var lt=W.get(q);lt.forEach(function(ut){k.has(ut.id)||(z.push(ut.id),k.add(ut.id),V[j].push(ut.id))})}j++}}),V},"findComponents"),m=L(function(W){var V=new Map;return W.forEach(function(z,k){V.set(k,[])}),W.forEach(function(z,k){z.forEach(function(j){V.get(k).push(j),V.get(j.id).push({id:k,gap:j.gap,direction:j.direction})})}),V},"dagToUndirected"),p=L(function(W){var V=new Map;return W.forEach(function(z,k){V.set(k,[])}),W.forEach(function(z,k){z.forEach(function(j){V.get(j.id).push({id:k,gap:j.gap,direction:j.direction})})}),V},"dagToReversed"),E=[],d=[],O=!1,x=!1,G=new Set,F=new Map,I=new Map,Z=[];if(h.fixedNodeConstraint&&h.fixedNodeConstraint.forEach(function(Y){G.add(Y.nodeId)}),h.relativePlacementConstraint&&(h.relativePlacementConstraint.forEach(function(Y){Y.left?(F.has(Y.left)?F.get(Y.left).push({id:Y.right,gap:Y.gap,direction:"horizontal"}):F.set(Y.left,[{id:Y.right,gap:Y.gap,direction:"horizontal"}]),F.has(Y.right)||F.set(Y.right,[])):(F.has(Y.top)?F.get(Y.top).push({id:Y.bottom,gap:Y.gap,direction:"vertical"}):F.set(Y.top,[{id:Y.bottom,gap:Y.gap,direction:"vertical"}]),F.has(Y.bottom)||F.set(Y.bottom,[]))}),I=m(F),Z=s(I)),t.TRANSFORM_ON_CONSTRAINT_HANDLING){if(h.fixedNodeConstraint&&h.fixedNodeConstraint.length>1)h.fixedNodeConstraint.forEach(function(Y,W){E[W]=[Y.position.x,Y.position.y],d[W]=[y[v.get(Y.nodeId)],N[v.get(Y.nodeId)]]}),O=!0;else if(h.alignmentConstraint)(function(){var Y=0;if(h.alignmentConstraint.vertical){for(var W=h.alignmentConstraint.vertical,V=L(function(q){var lt=new Set;W[q].forEach(function(pt){lt.add(pt)});var ut=new Set([].concat(c(lt)).filter(function(pt){return G.has(pt)})),it=void 0;ut.size>0?it=y[v.get(ut.values().next().value)]:it=et(lt).x,W[q].forEach(function(pt){E[Y]=[it,N[v.get(pt)]],d[Y]=[y[v.get(pt)],N[v.get(pt)]],Y++})},"_loop2"),z=0;z<W.length;z++)V(z);O=!0}if(h.alignmentConstraint.horizontal){for(var k=h.alignmentConstraint.horizontal,j=L(function(q){var lt=new Set;k[q].forEach(function(pt){lt.add(pt)});var ut=new Set([].concat(c(lt)).filter(function(pt){return G.has(pt)})),it=void 0;ut.size>0?it=y[v.get(ut.values().next().value)]:it=et(lt).y,k[q].forEach(function(pt){E[Y]=[y[v.get(pt)],it],d[Y]=[y[v.get(pt)],N[v.get(pt)]],Y++})},"_loop3"),gt=0;gt<k.length;gt++)j(gt);O=!0}h.relativePlacementConstraint&&(x=!0)})();else if(h.relativePlacementConstraint){for(var tt=0,P=0,_=0;_<Z.length;_++)Z[_].length>tt&&(tt=Z[_].length,P=_);if(tt<I.size/2)J(h.relativePlacementConstraint),O=!1,x=!1;else{var B=new Map,w=new Map,U=[];Z[P].forEach(function(Y){F.get(Y).forEach(function(W){W.direction=="horizontal"?(B.has(Y)?B.get(Y).push(W):B.set(Y,[W]),B.has(W.id)||B.set(W.id,[]),U.push({left:Y,right:W.id})):(w.has(Y)?w.get(Y).push(W):w.set(Y,[W]),w.has(W.id)||w.set(W.id,[]),U.push({top:Y,bottom:W.id}))})}),J(U),x=!1;var H=R(B,"horizontal"),K=R(w,"vertical");Z[P].forEach(function(Y,W){d[W]=[y[v.get(Y)],N[v.get(Y)]],E[W]=[],H.has(Y)?E[W][0]=H.get(Y):E[W][0]=y[v.get(Y)],K.has(Y)?E[W][1]=K.get(Y):E[W][1]=N[v.get(Y)]}),O=!0}}if(O){for(var ht=void 0,Nt=i.transpose(E),St=i.transpose(d),Q=0;Q<Nt.length;Q++)Nt[Q]=i.multGamma(Nt[Q]),St[Q]=i.multGamma(St[Q]);var Yt=i.multMat(Nt,i.transpose(St)),Mt=e.svd(Yt);ht=i.multMat(Mt.V,i.transpose(Mt.U));for(var ot=0;ot<v.size;ot++){var rt=[y[ot],N[ot]],vt=[ht[0][0],ht[1][0]],mt=[ht[0][1],ht[1][1]];y[ot]=i.dotProduct(rt,vt),N[ot]=i.dotProduct(rt,mt)}x&&J(h.relativePlacementConstraint)}}if(t.ENFORCE_CONSTRAINTS){if(h.fixedNodeConstraint&&h.fixedNodeConstraint.length>0){var Lt={x:0,y:0};h.fixedNodeConstraint.forEach(function(Y,W){var V={x:y[v.get(Y.nodeId)],y:N[v.get(Y.nodeId)]},z=Y.position,k=X(z,V);Lt.x+=k.x,Lt.y+=k.y}),Lt.x/=h.fixedNodeConstraint.length,Lt.y/=h.fixedNodeConstraint.length,y.forEach(function(Y,W){y[W]+=Lt.x}),N.forEach(function(Y,W){N[W]+=Lt.y}),h.fixedNodeConstraint.forEach(function(Y){y[v.get(Y.nodeId)]=Y.position.x,N[v.get(Y.nodeId)]=Y.position.y})}if(h.alignmentConstraint){if(h.alignmentConstraint.vertical)for(var Et=h.alignmentConstraint.vertical,Tt=L(function(W){var V=new Set;Et[W].forEach(function(j){V.add(j)});var z=new Set([].concat(c(V)).filter(function(j){return G.has(j)})),k=void 0;z.size>0?k=y[v.get(z.values().next().value)]:k=et(V).x,V.forEach(function(j){G.has(j)||(y[v.get(j)]=k)})},"_loop4"),wt=0;wt<Et.length;wt++)Tt(wt);if(h.alignmentConstraint.horizontal)for(var Rt=h.alignmentConstraint.horizontal,Wt=L(function(W){var V=new Set;Rt[W].forEach(function(j){V.add(j)});var z=new Set([].concat(c(V)).filter(function(j){return G.has(j)})),k=void 0;z.size>0?k=N[v.get(z.values().next().value)]:k=et(V).y,V.forEach(function(j){G.has(j)||(N[v.get(j)]=k)})},"_loop5"),Pt=0;Pt<Rt.length;Pt++)Wt(Pt)}h.relativePlacementConstraint&&function(){var Y=new Map,W=new Map,V=new Map,z=new Map,k=new Map,j=new Map,gt=new Set,ft=new Set;if(G.forEach(function(Gt){gt.add(Gt),ft.add(Gt)}),h.alignmentConstraint){if(h.alignmentConstraint.vertical)for(var q=h.alignmentConstraint.vertical,lt=L(function(yt){V.set("dummy"+yt,[]),q[yt].forEach(function(Ct){Y.set(Ct,"dummy"+yt),V.get("dummy"+yt).push(Ct),G.has(Ct)&&gt.add("dummy"+yt)}),k.set("dummy"+yt,y[v.get(q[yt][0])])},"_loop6"),ut=0;ut<q.length;ut++)lt(ut);if(h.alignmentConstraint.horizontal)for(var it=h.alignmentConstraint.horizontal,pt=L(function(yt){z.set("dummy"+yt,[]),it[yt].forEach(function(Ct){W.set(Ct,"dummy"+yt),z.get("dummy"+yt).push(Ct),G.has(Ct)&&ft.add("dummy"+yt)}),j.set("dummy"+yt,N[v.get(it[yt][0])])},"_loop7"),Dt=0;Dt<it.length;Dt++)pt(Dt)}var st=new Map,nt=new Map,dt=L(function(yt){F.get(yt).forEach(function(Ct){var Zt=void 0,zt=void 0;Ct.direction=="horizontal"?(Zt=Y.get(yt)?Y.get(yt):yt,Y.get(Ct.id)?zt={id:Y.get(Ct.id),gap:Ct.gap,direction:Ct.direction}:zt=Ct,st.has(Zt)?st.get(Zt).push(zt):st.set(Zt,[zt]),st.has(zt.id)||st.set(zt.id,[])):(Zt=W.get(yt)?W.get(yt):yt,W.get(Ct.id)?zt={id:W.get(Ct.id),gap:Ct.gap,direction:Ct.direction}:zt=Ct,nt.has(Zt)?nt.get(Zt).push(zt):nt.set(Zt,[zt]),nt.has(zt.id)||nt.set(zt.id,[]))})},"_loop8"),at=!0,ct=!1,bt=void 0;try{for(var Ot=F.keys()[Symbol.iterator](),Vt;!(at=(Vt=Ot.next()).done);at=!0){var xt=Vt.value;dt(xt)}}catch(Gt){ct=!0,bt=Gt}finally{try{!at&&Ot.return&&Ot.return()}finally{if(ct)throw bt}}var At=m(st),$t=m(nt),It=s(At),Xt=s($t),Bt=p(st),ve=p(nt),qt=[],jt=[];It.forEach(function(Gt,yt){qt[yt]=[],Gt.forEach(function(Ct){Bt.get(Ct).length==0&&qt[yt].push(Ct)})}),Xt.forEach(function(Gt,yt){jt[yt]=[],Gt.forEach(function(Ct){ve.get(Ct).length==0&&jt[yt].push(Ct)})});var _t=R(st,"horizontal",gt,k,qt),Qt=R(nt,"vertical",ft,j,jt),ce=L(function(yt){V.get(yt)?V.get(yt).forEach(function(Ct){y[v.get(Ct)]=_t.get(yt)}):y[v.get(yt)]=_t.get(yt)},"_loop9"),te=!0,ee=!1,Se=void 0;try{for(var ye=_t.keys()[Symbol.iterator](),be;!(te=(be=ye.next()).done);te=!0){var me=be.value;ce(me)}}catch(Gt){ee=!0,Se=Gt}finally{try{!te&&ye.return&&ye.return()}finally{if(ee)throw Se}}var vr=L(function(yt){z.get(yt)?z.get(yt).forEach(function(Ct){N[v.get(Ct)]=Qt.get(yt)}):N[v.get(yt)]=Qt.get(yt)},"_loop10"),Ee=!0,Fe=!1,Pe=void 0;try{for(var Te=Qt.keys()[Symbol.iterator](),Ge;!(Ee=(Ge=Te.next()).done);Ee=!0){var me=Ge.value;vr(me)}}catch(Gt){Fe=!0,Pe=Gt}finally{try{!Ee&&Te.return&&Te.return()}finally{if(Fe)throw Pe}}}()}for(var Ut=0;Ut<S.length;Ut++){var Ft=S[Ut];Ft.getChild()==null&&Ft.setCenter(y[v.get(Ft.id)],N[v.get(Ft.id)])}},n.exports=l},551:n=>{n.exports=D}},T={};function g(n){var r=T[n];if(r!==void 0)return r.exports;var a=T[n]={exports:{}};return C[n](a,a.exports,g),a.exports}L(g,"__webpack_require__");var o=g(45);return o})()})});var lr=Ae((le,Ie)=>{"use strict";L(function(C,T){typeof le=="object"&&typeof Ie=="object"?Ie.exports=T(Re()):typeof define=="function"&&define.amd?define(["cose-base"],T):typeof le=="object"?le.cytoscapeFcose=T(Re()):C.cytoscapeFcose=T(C.coseBase)},"webpackUniversalModuleDefinition")(le,function(D){return(()=>{"use strict";var C={658:n=>{n.exports=Object.assign!=null?Object.assign.bind(Object):function(r){for(var a=arguments.length,c=Array(a>1?a-1:0),t=1;t<a;t++)c[t-1]=arguments[t];return c.forEach(function(u){Object.keys(u).forEach(function(i){return r[i]=u[i]})}),r}},548:(n,r,a)=>{var c=function(){function i(e,l){var f=[],h=!0,A=!1,v=void 0;try{for(var y=e[Symbol.iterator](),N;!(h=(N=y.next()).done)&&(f.push(N.value),!(l&&f.length===l));h=!0);}catch(S){A=!0,v=S}finally{try{!h&&y.return&&y.return()}finally{if(A)throw v}}return f}return L(i,"sliceIterator"),function(e,l){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return i(e,l);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),t=a(140).layoutBase.LinkedList,u={};u.getTopMostNodes=function(i){for(var e={},l=0;l<i.length;l++)e[i[l].id()]=!0;var f=i.filter(function(h,A){typeof h=="number"&&(h=A);for(var v=h.parent()[0];v!=null;){if(e[v.id()])return!1;v=v.parent()[0]}return!0});return f},u.connectComponents=function(i,e,l,f){var h=new t,A=new Set,v=[],y=void 0,N=void 0,S=void 0,M=!1,b=1,$=[],X=[],et=L(function(){var J=i.collection();X.push(J);var s=l[0],m=i.collection();m.merge(s).merge(s.descendants().intersection(e)),v.push(s),m.forEach(function(d){h.push(d),A.add(d),J.merge(d)});for(var p=L(function(){s=h.shift();var O=i.collection();s.neighborhood().nodes().forEach(function(I){e.intersection(s.edgesWith(I)).length>0&&O.merge(I)});for(var x=0;x<O.length;x++){var G=O[x];if(y=l.intersection(G.union(G.ancestors())),y!=null&&!A.has(y[0])){var F=y.union(y.descendants());F.forEach(function(I){h.push(I),A.add(I),J.merge(I),l.has(I)&&v.push(I)})}}},"_loop2");h.length!=0;)p();if(J.forEach(function(d){e.intersection(d.connectedEdges()).forEach(function(O){J.has(O.source())&&J.has(O.target())&&J.merge(O)})}),v.length==l.length&&(M=!0),!M||M&&b>1){N=v[0],S=N.connectedEdges().length,v.forEach(function(d){d.connectedEdges().length<S&&(S=d.connectedEdges().length,N=d)}),$.push(N.id());var E=i.collection();E.merge(v[0]),v.forEach(function(d){E.merge(d)}),v=[],l=l.difference(E),b++}},"_loop");do et();while(!M);return f&&$.length>0&&f.set("dummy"+(f.size+1),$),X},u.relocateComponent=function(i,e,l){if(!l.fixedNodeConstraint){var f=Number.POSITIVE_INFINITY,h=Number.NEGATIVE_INFINITY,A=Number.POSITIVE_INFINITY,v=Number.NEGATIVE_INFINITY;if(l.quality=="draft"){var y=!0,N=!1,S=void 0;try{for(var M=e.nodeIndexes[Symbol.iterator](),b;!(y=(b=M.next()).done);y=!0){var $=b.value,X=c($,2),et=X[0],R=X[1],J=l.cy.getElementById(et);if(J){var s=J.boundingBox(),m=e.xCoords[R]-s.w/2,p=e.xCoords[R]+s.w/2,E=e.yCoords[R]-s.h/2,d=e.yCoords[R]+s.h/2;m<f&&(f=m),p>h&&(h=p),E<A&&(A=E),d>v&&(v=d)}}}catch(I){N=!0,S=I}finally{try{!y&&M.return&&M.return()}finally{if(N)throw S}}var O=i.x-(h+f)/2,x=i.y-(v+A)/2;e.xCoords=e.xCoords.map(function(I){return I+O}),e.yCoords=e.yCoords.map(function(I){return I+x})}else{Object.keys(e).forEach(function(I){var Z=e[I],tt=Z.getRect().x,P=Z.getRect().x+Z.getRect().width,_=Z.getRect().y,B=Z.getRect().y+Z.getRect().height;tt<f&&(f=tt),P>h&&(h=P),_<A&&(A=_),B>v&&(v=B)});var G=i.x-(h+f)/2,F=i.y-(v+A)/2;Object.keys(e).forEach(function(I){var Z=e[I];Z.setCenter(Z.getCenterX()+G,Z.getCenterY()+F)})}}},u.calcBoundingBox=function(i,e,l,f){for(var h=Number.MAX_SAFE_INTEGER,A=Number.MIN_SAFE_INTEGER,v=Number.MAX_SAFE_INTEGER,y=Number.MIN_SAFE_INTEGER,N=void 0,S=void 0,M=void 0,b=void 0,$=i.descendants().not(":parent"),X=$.length,et=0;et<X;et++){var R=$[et];N=e[f.get(R.id())]-R.width()/2,S=e[f.get(R.id())]+R.width()/2,M=l[f.get(R.id())]-R.height()/2,b=l[f.get(R.id())]+R.height()/2,h>N&&(h=N),A<S&&(A=S),v>M&&(v=M),y<b&&(y=b)}var J={};return J.topLeftX=h,J.topLeftY=v,J.width=A-h,J.height=y-v,J},u.calcParentsWithoutChildren=function(i,e){var l=i.collection();return e.nodes(":parent").forEach(function(f){var h=!1;f.children().forEach(function(A){A.css("display")!="none"&&(h=!0)}),h||l.merge(f)}),l},n.exports=u},816:(n,r,a)=>{var c=a(548),t=a(140).CoSELayout,u=a(140).CoSENode,i=a(140).layoutBase.PointD,e=a(140).layoutBase.DimensionD,l=a(140).layoutBase.LayoutConstants,f=a(140).layoutBase.FDLayoutConstants,h=a(140).CoSEConstants,A=L(function(y,N){var S=y.cy,M=y.eles,b=M.nodes(),$=M.edges(),X=void 0,et=void 0,R=void 0,J={};y.randomize&&(X=N.nodeIndexes,et=N.xCoords,R=N.yCoords);var s=L(function(I){return typeof I=="function"},"isFn"),m=L(function(I,Z){return s(I)?I(Z):I},"optFn"),p=c.calcParentsWithoutChildren(S,M),E=L(function F(I,Z,tt,P){for(var _=Z.length,B=0;B<_;B++){var w=Z[B],U=null;w.intersection(p).length==0&&(U=w.children());var H=void 0,K=w.layoutDimensions({nodeDimensionsIncludeLabels:P.nodeDimensionsIncludeLabels});if(w.outerWidth()!=null&&w.outerHeight()!=null)if(P.randomize)if(!w.isParent())H=I.add(new u(tt.graphManager,new i(et[X.get(w.id())]-K.w/2,R[X.get(w.id())]-K.h/2),new e(parseFloat(K.w),parseFloat(K.h))));else{var ht=c.calcBoundingBox(w,et,R,X);w.intersection(p).length==0?H=I.add(new u(tt.graphManager,new i(ht.topLeftX,ht.topLeftY),new e(ht.width,ht.height))):H=I.add(new u(tt.graphManager,new i(ht.topLeftX,ht.topLeftY),new e(parseFloat(K.w),parseFloat(K.h))))}else H=I.add(new u(tt.graphManager,new i(w.position("x")-K.w/2,w.position("y")-K.h/2),new e(parseFloat(K.w),parseFloat(K.h))));else H=I.add(new u(this.graphManager));if(H.id=w.data("id"),H.nodeRepulsion=m(P.nodeRepulsion,w),H.paddingLeft=parseInt(w.css("padding")),H.paddingTop=parseInt(w.css("padding")),H.paddingRight=parseInt(w.css("padding")),H.paddingBottom=parseInt(w.css("padding")),P.nodeDimensionsIncludeLabels&&(H.labelWidth=w.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).w,H.labelHeight=w.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).h,H.labelPosVertical=w.css("text-valign"),H.labelPosHorizontal=w.css("text-halign")),J[w.data("id")]=H,isNaN(H.rect.x)&&(H.rect.x=0),isNaN(H.rect.y)&&(H.rect.y=0),U!=null&&U.length>0){var Nt=void 0;Nt=tt.getGraphManager().add(tt.newGraph(),H),F(Nt,U,tt,P)}}},"processChildrenList"),d=L(function(I,Z,tt){for(var P=0,_=0,B=0;B<tt.length;B++){var w=tt[B],U=J[w.data("source")],H=J[w.data("target")];if(U&&H&&U!==H&&U.getEdgesBetween(H).length==0){var K=Z.add(I.newEdge(),U,H);K.id=w.id(),K.idealLength=m(y.idealEdgeLength,w),K.edgeElasticity=m(y.edgeElasticity,w),P+=K.idealLength,_++}}y.idealEdgeLength!=null&&(_>0?h.DEFAULT_EDGE_LENGTH=f.DEFAULT_EDGE_LENGTH=P/_:s(y.idealEdgeLength)?h.DEFAULT_EDGE_LENGTH=f.DEFAULT_EDGE_LENGTH=50:h.DEFAULT_EDGE_LENGTH=f.DEFAULT_EDGE_LENGTH=y.idealEdgeLength,h.MIN_REPULSION_DIST=f.MIN_REPULSION_DIST=f.DEFAULT_EDGE_LENGTH/10,h.DEFAULT_RADIAL_SEPARATION=f.DEFAULT_EDGE_LENGTH)},"processEdges"),O=L(function(I,Z){Z.fixedNodeConstraint&&(I.constraints.fixedNodeConstraint=Z.fixedNodeConstraint),Z.alignmentConstraint&&(I.constraints.alignmentConstraint=Z.alignmentConstraint),Z.relativePlacementConstraint&&(I.constraints.relativePlacementConstraint=Z.relativePlacementConstraint)},"processConstraints");y.nestingFactor!=null&&(h.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=f.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=y.nestingFactor),y.gravity!=null&&(h.DEFAULT_GRAVITY_STRENGTH=f.DEFAULT_GRAVITY_STRENGTH=y.gravity),y.numIter!=null&&(h.MAX_ITERATIONS=f.MAX_ITERATIONS=y.numIter),y.gravityRange!=null&&(h.DEFAULT_GRAVITY_RANGE_FACTOR=f.DEFAULT_GRAVITY_RANGE_FACTOR=y.gravityRange),y.gravityCompound!=null&&(h.DEFAULT_COMPOUND_GRAVITY_STRENGTH=f.DEFAULT_COMPOUND_GRAVITY_STRENGTH=y.gravityCompound),y.gravityRangeCompound!=null&&(h.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=f.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=y.gravityRangeCompound),y.initialEnergyOnIncremental!=null&&(h.DEFAULT_COOLING_FACTOR_INCREMENTAL=f.DEFAULT_COOLING_FACTOR_INCREMENTAL=y.initialEnergyOnIncremental),y.tilingCompareBy!=null&&(h.TILING_COMPARE_BY=y.tilingCompareBy),y.quality=="proof"?l.QUALITY=2:l.QUALITY=0,h.NODE_DIMENSIONS_INCLUDE_LABELS=f.NODE_DIMENSIONS_INCLUDE_LABELS=l.NODE_DIMENSIONS_INCLUDE_LABELS=y.nodeDimensionsIncludeLabels,h.DEFAULT_INCREMENTAL=f.DEFAULT_INCREMENTAL=l.DEFAULT_INCREMENTAL=!y.randomize,h.ANIMATE=f.ANIMATE=l.ANIMATE=y.animate,h.TILE=y.tile,h.TILING_PADDING_VERTICAL=typeof y.tilingPaddingVertical=="function"?y.tilingPaddingVertical.call():y.tilingPaddingVertical,h.TILING_PADDING_HORIZONTAL=typeof y.tilingPaddingHorizontal=="function"?y.tilingPaddingHorizontal.call():y.tilingPaddingHorizontal,h.DEFAULT_INCREMENTAL=f.DEFAULT_INCREMENTAL=l.DEFAULT_INCREMENTAL=!0,h.PURE_INCREMENTAL=!y.randomize,l.DEFAULT_UNIFORM_LEAF_NODE_SIZES=y.uniformNodeDimensions,y.step=="transformed"&&(h.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,h.ENFORCE_CONSTRAINTS=!1,h.APPLY_LAYOUT=!1),y.step=="enforced"&&(h.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,h.ENFORCE_CONSTRAINTS=!0,h.APPLY_LAYOUT=!1),y.step=="cose"&&(h.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,h.ENFORCE_CONSTRAINTS=!1,h.APPLY_LAYOUT=!0),y.step=="all"&&(y.randomize?h.TRANSFORM_ON_CONSTRAINT_HANDLING=!0:h.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,h.ENFORCE_CONSTRAINTS=!0,h.APPLY_LAYOUT=!0),y.fixedNodeConstraint||y.alignmentConstraint||y.relativePlacementConstraint?h.TREE_REDUCTION_ON_INCREMENTAL=!1:h.TREE_REDUCTION_ON_INCREMENTAL=!0;var x=new t,G=x.newGraphManager();return E(G.addRoot(),c.getTopMostNodes(b),x,y),d(x,G,$),O(x,y),x.runLayout(),J},"coseLayout");n.exports={coseLayout:A}},212:(n,r,a)=>{var c=function(){function y(N,S){for(var M=0;M<S.length;M++){var b=S[M];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(N,b.key,b)}}return L(y,"defineProperties"),function(N,S,M){return S&&y(N.prototype,S),M&&y(N,M),N}}();function t(y,N){if(!(y instanceof N))throw new TypeError("Cannot call a class as a function")}L(t,"_classCallCheck");var u=a(658),i=a(548),e=a(657),l=e.spectralLayout,f=a(816),h=f.coseLayout,A=Object.freeze({quality:"default",randomize:!0,animate:!0,animationDuration:1e3,animationEasing:void 0,fit:!0,padding:30,nodeDimensionsIncludeLabels:!1,uniformNodeDimensions:!1,packComponents:!0,step:"all",samplingType:!0,sampleSize:25,nodeSeparation:75,piTol:1e-7,nodeRepulsion:L(function(N){return 4500},"nodeRepulsion"),idealEdgeLength:L(function(N){return 50},"idealEdgeLength"),edgeElasticity:L(function(N){return .45},"edgeElasticity"),nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,tilingCompareBy:void 0,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.3,fixedNodeConstraint:void 0,alignmentConstraint:void 0,relativePlacementConstraint:void 0,ready:L(function(){},"ready"),stop:L(function(){},"stop")}),v=function(){function y(N){t(this,y),this.options=u({},A,N)}return L(y,"Layout"),c(y,[{key:"run",value:L(function(){var S=this,M=this.options,b=M.cy,$=M.eles,X=[],et=void 0,R=void 0,J=[],s=void 0,m=[];M.fixedNodeConstraint&&(!Array.isArray(M.fixedNodeConstraint)||M.fixedNodeConstraint.length==0)&&(M.fixedNodeConstraint=void 0),M.alignmentConstraint&&(M.alignmentConstraint.vertical&&(!Array.isArray(M.alignmentConstraint.vertical)||M.alignmentConstraint.vertical.length==0)&&(M.alignmentConstraint.vertical=void 0),M.alignmentConstraint.horizontal&&(!Array.isArray(M.alignmentConstraint.horizontal)||M.alignmentConstraint.horizontal.length==0)&&(M.alignmentConstraint.horizontal=void 0)),M.relativePlacementConstraint&&(!Array.isArray(M.relativePlacementConstraint)||M.relativePlacementConstraint.length==0)&&(M.relativePlacementConstraint=void 0);var p=M.fixedNodeConstraint||M.alignmentConstraint||M.relativePlacementConstraint;p&&(M.tile=!1,M.packComponents=!1);var E=void 0,d=!1;if(b.layoutUtilities&&M.packComponents&&(E=b.layoutUtilities("get"),E||(E=b.layoutUtilities()),d=!0),$.nodes().length>0)if(d){var G=i.getTopMostNodes(M.eles.nodes());if(s=i.connectComponents(b,M.eles,G),s.forEach(function(ot){var rt=ot.boundingBox();m.push({x:rt.x1+rt.w/2,y:rt.y1+rt.h/2})}),M.randomize&&s.forEach(function(ot){M.eles=ot,X.push(l(M))}),M.quality=="default"||M.quality=="proof"){var F=b.collection();if(M.tile){var I=new Map,Z=[],tt=[],P=0,_={nodeIndexes:I,xCoords:Z,yCoords:tt},B=[];if(s.forEach(function(ot,rt){ot.edges().length==0&&(ot.nodes().forEach(function(vt,mt){F.merge(ot.nodes()[mt]),vt.isParent()||(_.nodeIndexes.set(ot.nodes()[mt].id(),P++),_.xCoords.push(ot.nodes()[0].position().x),_.yCoords.push(ot.nodes()[0].position().y))}),B.push(rt))}),F.length>1){var w=F.boundingBox();m.push({x:w.x1+w.w/2,y:w.y1+w.h/2}),s.push(F),X.push(_);for(var U=B.length-1;U>=0;U--)s.splice(B[U],1),X.splice(B[U],1),m.splice(B[U],1)}}s.forEach(function(ot,rt){M.eles=ot,J.push(h(M,X[rt])),i.relocateComponent(m[rt],J[rt],M)})}else s.forEach(function(ot,rt){i.relocateComponent(m[rt],X[rt],M)});var H=new Set;if(s.length>1){var K=[],ht=$.filter(function(ot){return ot.css("display")=="none"});s.forEach(function(ot,rt){var vt=void 0;if(M.quality=="draft"&&(vt=X[rt].nodeIndexes),ot.nodes().not(ht).length>0){var mt={};mt.edges=[],mt.nodes=[];var Lt=void 0;ot.nodes().not(ht).forEach(function(Et){if(M.quality=="draft")if(!Et.isParent())Lt=vt.get(Et.id()),mt.nodes.push({x:X[rt].xCoords[Lt]-Et.boundingbox().w/2,y:X[rt].yCoords[Lt]-Et.boundingbox().h/2,width:Et.boundingbox().w,height:Et.boundingbox().h});else{var Tt=i.calcBoundingBox(Et,X[rt].xCoords,X[rt].yCoords,vt);mt.nodes.push({x:Tt.topLeftX,y:Tt.topLeftY,width:Tt.width,height:Tt.height})}else J[rt][Et.id()]&&mt.nodes.push({x:J[rt][Et.id()].getLeft(),y:J[rt][Et.id()].getTop(),width:J[rt][Et.id()].getWidth(),height:J[rt][Et.id()].getHeight()})}),ot.edges().forEach(function(Et){var Tt=Et.source(),wt=Et.target();if(Tt.css("display")!="none"&&wt.css("display")!="none")if(M.quality=="draft"){var Rt=vt.get(Tt.id()),Wt=vt.get(wt.id()),Pt=[],Ut=[];if(Tt.isParent()){var Ft=i.calcBoundingBox(Tt,X[rt].xCoords,X[rt].yCoords,vt);Pt.push(Ft.topLeftX+Ft.width/2),Pt.push(Ft.topLeftY+Ft.height/2)}else Pt.push(X[rt].xCoords[Rt]),Pt.push(X[rt].yCoords[Rt]);if(wt.isParent()){var Y=i.calcBoundingBox(wt,X[rt].xCoords,X[rt].yCoords,vt);Ut.push(Y.topLeftX+Y.width/2),Ut.push(Y.topLeftY+Y.height/2)}else Ut.push(X[rt].xCoords[Wt]),Ut.push(X[rt].yCoords[Wt]);mt.edges.push({startX:Pt[0],startY:Pt[1],endX:Ut[0],endY:Ut[1]})}else J[rt][Tt.id()]&&J[rt][wt.id()]&&mt.edges.push({startX:J[rt][Tt.id()].getCenterX(),startY:J[rt][Tt.id()].getCenterY(),endX:J[rt][wt.id()].getCenterX(),endY:J[rt][wt.id()].getCenterY()})}),mt.nodes.length>0&&(K.push(mt),H.add(rt))}});var Nt=E.packComponents(K,M.randomize).shifts;if(M.quality=="draft")X.forEach(function(ot,rt){var vt=ot.xCoords.map(function(Lt){return Lt+Nt[rt].dx}),mt=ot.yCoords.map(function(Lt){return Lt+Nt[rt].dy});ot.xCoords=vt,ot.yCoords=mt});else{var St=0;H.forEach(function(ot){Object.keys(J[ot]).forEach(function(rt){var vt=J[ot][rt];vt.setCenter(vt.getCenterX()+Nt[St].dx,vt.getCenterY()+Nt[St].dy)}),St++})}}}else{var O=M.eles.boundingBox();if(m.push({x:O.x1+O.w/2,y:O.y1+O.h/2}),M.randomize){var x=l(M);X.push(x)}M.quality=="default"||M.quality=="proof"?(J.push(h(M,X[0])),i.relocateComponent(m[0],J[0],M)):i.relocateComponent(m[0],X[0],M)}var Q=L(function(rt,vt){if(M.quality=="default"||M.quality=="proof"){typeof rt=="number"&&(rt=vt);var mt=void 0,Lt=void 0,Et=rt.data("id");return J.forEach(function(wt){Et in wt&&(mt={x:wt[Et].getRect().getCenterX(),y:wt[Et].getRect().getCenterY()},Lt=wt[Et])}),M.nodeDimensionsIncludeLabels&&(Lt.labelWidth&&(Lt.labelPosHorizontal=="left"?mt.x+=Lt.labelWidth/2:Lt.labelPosHorizontal=="right"&&(mt.x-=Lt.labelWidth/2)),Lt.labelHeight&&(Lt.labelPosVertical=="top"?mt.y+=Lt.labelHeight/2:Lt.labelPosVertical=="bottom"&&(mt.y-=Lt.labelHeight/2))),mt==null&&(mt={x:rt.position("x"),y:rt.position("y")}),{x:mt.x,y:mt.y}}else{var Tt=void 0;return X.forEach(function(wt){var Rt=wt.nodeIndexes.get(rt.id());Rt!=null&&(Tt={x:wt.xCoords[Rt],y:wt.yCoords[Rt]})}),Tt==null&&(Tt={x:rt.position("x"),y:rt.position("y")}),{x:Tt.x,y:Tt.y}}},"getPositions");if(M.quality=="default"||M.quality=="proof"||M.randomize){var Yt=i.calcParentsWithoutChildren(b,$),Mt=$.filter(function(ot){return ot.css("display")=="none"});M.eles=$.not(Mt),$.nodes().not(":parent").not(Mt).layoutPositions(S,M,Q),Yt.length>0&&Yt.forEach(function(ot){ot.position(Q(ot))})}else console.log("If randomize option is set to false, then quality option must be 'default' or 'proof'.")},"run")}]),y}();n.exports=v},657:(n,r,a)=>{var c=a(548),t=a(140).layoutBase.Matrix,u=a(140).layoutBase.SVD,i=L(function(l){var f=l.cy,h=l.eles,A=h.nodes(),v=h.nodes(":parent"),y=new Map,N=new Map,S=new Map,M=[],b=[],$=[],X=[],et=[],R=[],J=[],s=[],m=void 0,p=void 0,E=1e8,d=1e-9,O=l.piTol,x=l.samplingType,G=l.nodeSeparation,F=void 0,I=L(function(){for(var V=0,z=0,k=!1;z<F;){V=Math.floor(Math.random()*p),k=!1;for(var j=0;j<z;j++)if(X[j]==V){k=!0;break}if(!k)X[z]=V,z++;else continue}},"randomSampleCR"),Z=L(function(V,z,k){for(var j=[],gt=0,ft=0,q=0,lt=void 0,ut=[],it=0,pt=1,Dt=0;Dt<p;Dt++)ut[Dt]=E;for(j[ft]=V,ut[V]=0;ft>=gt;){q=j[gt++];for(var st=M[q],nt=0;nt<st.length;nt++)lt=N.get(st[nt]),ut[lt]==E&&(ut[lt]=ut[q]+1,j[++ft]=lt);R[q][z]=ut[q]*G}if(k){for(var dt=0;dt<p;dt++)R[dt][z]<et[dt]&&(et[dt]=R[dt][z]);for(var at=0;at<p;at++)et[at]>it&&(it=et[at],pt=at)}return pt},"BFS"),tt=L(function(V){var z=void 0;if(V){z=Math.floor(Math.random()*p),m=z;for(var j=0;j<p;j++)et[j]=E;for(var gt=0;gt<F;gt++)X[gt]=z,z=Z(z,gt,V)}else{I();for(var k=0;k<F;k++)Z(X[k],k,V,!1)}for(var ft=0;ft<p;ft++)for(var q=0;q<F;q++)R[ft][q]*=R[ft][q];for(var lt=0;lt<F;lt++)J[lt]=[];for(var ut=0;ut<F;ut++)for(var it=0;it<F;it++)J[ut][it]=R[X[it]][ut]},"allBFS"),P=L(function(){for(var V=u.svd(J),z=V.S,k=V.U,j=V.V,gt=z[0]*z[0]*z[0],ft=[],q=0;q<F;q++){ft[q]=[];for(var lt=0;lt<F;lt++)ft[q][lt]=0,q==lt&&(ft[q][lt]=z[q]/(z[q]*z[q]+gt/(z[q]*z[q])))}s=t.multMat(t.multMat(j,ft),t.transpose(k))},"sample"),_=L(function(){for(var V=void 0,z=void 0,k=[],j=[],gt=[],ft=[],q=0;q<p;q++)k[q]=Math.random(),j[q]=Math.random();k=t.normalize(k),j=t.normalize(j);for(var lt=0,ut=d,it=d,pt=void 0;;){lt++;for(var Dt=0;Dt<p;Dt++)gt[Dt]=k[Dt];if(k=t.multGamma(t.multL(t.multGamma(gt),R,s)),V=t.dotProduct(gt,k),k=t.normalize(k),ut=t.dotProduct(gt,k),pt=Math.abs(ut/it),pt<=1+O&&pt>=1)break;it=ut}for(var st=0;st<p;st++)gt[st]=k[st];for(lt=0,it=d;;){lt++;for(var nt=0;nt<p;nt++)ft[nt]=j[nt];if(ft=t.minusOp(ft,t.multCons(gt,t.dotProduct(gt,ft))),j=t.multGamma(t.multL(t.multGamma(ft),R,s)),z=t.dotProduct(ft,j),j=t.normalize(j),ut=t.dotProduct(ft,j),pt=Math.abs(ut/it),pt<=1+O&&pt>=1)break;it=ut}for(var dt=0;dt<p;dt++)ft[dt]=j[dt];b=t.multCons(gt,Math.sqrt(Math.abs(V))),$=t.multCons(ft,Math.sqrt(Math.abs(z)))},"powerIteration");c.connectComponents(f,h,c.getTopMostNodes(A),y),v.forEach(function(W){c.connectComponents(f,h,c.getTopMostNodes(W.descendants().intersection(h)),y)});for(var B=0,w=0;w<A.length;w++)A[w].isParent()||N.set(A[w].id(),B++);var U=!0,H=!1,K=void 0;try{for(var ht=y.keys()[Symbol.iterator](),Nt;!(U=(Nt=ht.next()).done);U=!0){var St=Nt.value;N.set(St,B++)}}catch(W){H=!0,K=W}finally{try{!U&&ht.return&&ht.return()}finally{if(H)throw K}}for(var Q=0;Q<N.size;Q++)M[Q]=[];v.forEach(function(W){for(var V=W.children().intersection(h);V.nodes(":childless").length==0;)V=V.nodes()[0].children().intersection(h);var z=0,k=V.nodes(":childless")[0].connectedEdges().length;V.nodes(":childless").forEach(function(j,gt){j.connectedEdges().length<k&&(k=j.connectedEdges().length,z=gt)}),S.set(W.id(),V.nodes(":childless")[z].id())}),A.forEach(function(W){var V=void 0;W.isParent()?V=N.get(S.get(W.id())):V=N.get(W.id()),W.neighborhood().nodes().forEach(function(z){h.intersection(W.edgesWith(z)).length>0&&(z.isParent()?M[V].push(S.get(z.id())):M[V].push(z.id()))})});var Yt=L(function(V){var z=N.get(V),k=void 0;y.get(V).forEach(function(j){f.getElementById(j).isParent()?k=S.get(j):k=j,M[z].push(k),M[N.get(k)].push(V)})},"_loop"),Mt=!0,ot=!1,rt=void 0;try{for(var vt=y.keys()[Symbol.iterator](),mt;!(Mt=(mt=vt.next()).done);Mt=!0){var Lt=mt.value;Yt(Lt)}}catch(W){ot=!0,rt=W}finally{try{!Mt&&vt.return&&vt.return()}finally{if(ot)throw rt}}p=N.size;var Et=void 0;if(p>2){F=p<l.sampleSize?p:l.sampleSize;for(var Tt=0;Tt<p;Tt++)R[Tt]=[];for(var wt=0;wt<F;wt++)s[wt]=[];return l.quality=="draft"||l.step=="all"?(tt(x),P(),_(),Et={nodeIndexes:N,xCoords:b,yCoords:$}):(N.forEach(function(W,V){b.push(f.getElementById(V).position("x")),$.push(f.getElementById(V).position("y"))}),Et={nodeIndexes:N,xCoords:b,yCoords:$}),Et}else{var Rt=N.keys(),Wt=f.getElementById(Rt.next().value),Pt=Wt.position(),Ut=Wt.outerWidth();if(b.push(Pt.x),$.push(Pt.y),p==2){var Ft=f.getElementById(Rt.next().value),Y=Ft.outerWidth();b.push(Pt.x+Ut/2+Y/2+l.idealEdgeLength),$.push(Pt.y)}return Et={nodeIndexes:N,xCoords:b,yCoords:$},Et}},"spectralLayout");n.exports={spectralLayout:i}},579:(n,r,a)=>{var c=a(212),t=L(function(i){i&&i("layout","fcose",c)},"register");typeof cytoscape<"u"&&t(cytoscape),n.exports=t},140:n=>{n.exports=D}},T={};function g(n){var r=T[n];if(r!==void 0)return r.exports;var a=T[n]={exports:{}};return C[n](a,a.exports,g),a.exports}L(g,"__webpack_require__");var o=g(579);return o})()})});var Le={L:"left",R:"right",T:"top",B:"bottom"},De={L:L(D=>`${D},${D/2} 0,${D} 0,0`,"L"),R:L(D=>`0,${D/2} ${D},0 ${D},${D}`,"R"),T:L(D=>`0,0 ${D},0 ${D/2},${D}`,"T"),B:L(D=>`${D/2},0 ${D},${D} 0,${D}`,"B")},ne={L:L((D,C)=>D-C+2,"L"),R:L((D,C)=>D-2,"R"),T:L((D,C)=>D-C+2,"T"),B:L((D,C)=>D-2,"B")},er=L(function(D){return Ht(D)?D==="L"?"R":"L":D==="T"?"B":"T"},"getOppositeArchitectureDirection"),Ce=L(function(D){let C=D;return C==="L"||C==="R"||C==="T"||C==="B"},"isArchitectureDirection"),Ht=L(function(D){let C=D;return C==="L"||C==="R"},"isArchitectureDirectionX"),kt=L(function(D){let C=D;return C==="T"||C==="B"},"isArchitectureDirectionY"),ae=L(function(D,C){let T=Ht(D)&&kt(C),g=kt(D)&&Ht(C);return T||g},"isArchitectureDirectionXY"),rr=L(function(D){let C=D[0],T=D[1],g=Ht(C)&&kt(T),o=kt(C)&&Ht(T);return g||o},"isArchitecturePairXY"),mr=L(function(D){return D!=="LL"&&D!=="RR"&&D!=="TT"&&D!=="BB"},"isValidArchitectureDirectionPair"),oe=L(function(D,C){let T=`${D}${C}`;return mr(T)?T:void 0},"getArchitectureDirectionPair"),ir=L(function([D,C],T){let g=T[0],o=T[1];return Ht(g)?kt(o)?[D+(g==="L"?-1:1),C+(o==="T"?1:-1)]:[D+(g==="L"?-1:1),C]:Ht(o)?[D+(o==="L"?1:-1),C+(g==="T"?1:-1)]:[D,C+(g==="T"?1:-1)]},"shiftPositionByArchitectureDirectionPair"),nr=L(function(D){return D==="LT"||D==="TL"?[1,1]:D==="BL"||D==="LB"?[1,-1]:D==="BR"||D==="RB"?[-1,-1]:[-1,1]},"getArchitectureDirectionXYFactors"),ar=L(function(D,C){return ae(D,C)?"bend":Ht(D)?"horizontal":"vertical"},"getArchitectureDirectionAlignment"),or=L(function(D){return D.type==="service"},"isArchitectureService"),sr=L(function(D){return D.type==="junction"},"isArchitectureJunction"),pe=L(D=>D.data(),"edgeData"),Jt=L(D=>D.data(),"nodeData");var Er=Ye.architecture,re=class{constructor(){this.nodes={};this.groups={};this.edges=[];this.registeredIds={};this.elements={};this.setAccTitle=ze;this.getAccTitle=$e;this.setDiagramTitle=Je;this.getDiagramTitle=qe;this.getAccDescription=Ze;this.setAccDescription=ke;this.clear()}static{L(this,"ArchitectureDB")}clear(){this.nodes={},this.groups={},this.edges=[],this.registeredIds={},this.dataStructures=void 0,this.elements={},Be()}addService({id:C,icon:T,in:g,title:o,iconText:n}){if(this.registeredIds[C]!==void 0)throw new Error(`The service id [${C}] is already in use by another ${this.registeredIds[C]}`);if(g!==void 0){if(C===g)throw new Error(`The service [${C}] cannot be placed within itself`);if(this.registeredIds[g]===void 0)throw new Error(`The service [${C}]'s parent does not exist. Please make sure the parent is created before this service`);if(this.registeredIds[g]==="node")throw new Error(`The service [${C}]'s parent is not a group`)}this.registeredIds[C]="node",this.nodes[C]={id:C,type:"service",icon:T,iconText:n,title:o,edges:[],in:g}}getServices(){return Object.values(this.nodes).filter(or)}addJunction({id:C,in:T}){this.registeredIds[C]="node",this.nodes[C]={id:C,type:"junction",edges:[],in:T}}getJunctions(){return Object.values(this.nodes).filter(sr)}getNodes(){return Object.values(this.nodes)}getNode(C){return this.nodes[C]??null}addGroup({id:C,icon:T,in:g,title:o}){if(this.registeredIds?.[C]!==void 0)throw new Error(`The group id [${C}] is already in use by another ${this.registeredIds[C]}`);if(g!==void 0){if(C===g)throw new Error(`The group [${C}] cannot be placed within itself`);if(this.registeredIds?.[g]===void 0)throw new Error(`The group [${C}]'s parent does not exist. Please make sure the parent is created before this group`);if(this.registeredIds?.[g]==="node")throw new Error(`The group [${C}]'s parent is not a group`)}this.registeredIds[C]="group",this.groups[C]={id:C,icon:T,title:o,in:g}}getGroups(){return Object.values(this.groups)}addEdge({lhsId:C,rhsId:T,lhsDir:g,rhsDir:o,lhsInto:n,rhsInto:r,lhsGroup:a,rhsGroup:c,title:t}){if(!Ce(g))throw new Error(`Invalid direction given for left hand side of edge ${C}--${T}. Expected (L,R,T,B) got ${String(g)}`);if(!Ce(o))throw new Error(`Invalid direction given for right hand side of edge ${C}--${T}. Expected (L,R,T,B) got ${String(o)}`);if(this.nodes[C]===void 0&&this.groups[C]===void 0)throw new Error(`The left-hand id [${C}] does not yet exist. Please create the service/group before declaring an edge to it.`);if(this.nodes[T]===void 0&&this.groups[T]===void 0)throw new Error(`The right-hand id [${T}] does not yet exist. Please create the service/group before declaring an edge to it.`);let u=this.nodes[C].in,i=this.nodes[T].in;if(a&&u&&i&&u==i)throw new Error(`The left-hand id [${C}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);if(c&&u&&i&&u==i)throw new Error(`The right-hand id [${T}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);let e={lhsId:C,lhsDir:g,lhsInto:n,lhsGroup:a,rhsId:T,rhsDir:o,rhsInto:r,rhsGroup:c,title:t};this.edges.push(e),this.nodes[C]&&this.nodes[T]&&(this.nodes[C].edges.push(this.edges[this.edges.length-1]),this.nodes[T].edges.push(this.edges[this.edges.length-1]))}getEdges(){return this.edges}getDataStructures(){if(this.dataStructures===void 0){let C={},T=Object.entries(this.nodes).reduce((c,[t,u])=>(c[t]=u.edges.reduce((i,e)=>{let l=this.getNode(e.lhsId)?.in,f=this.getNode(e.rhsId)?.in;if(l&&f&&l!==f){let h=ar(e.lhsDir,e.rhsDir);h!=="bend"&&(C[l]??={},C[l][f]=h,C[f]??={},C[f][l]=h)}if(e.lhsId===t){let h=oe(e.lhsDir,e.rhsDir);h&&(i[h]=e.rhsId)}else{let h=oe(e.rhsDir,e.lhsDir);h&&(i[h]=e.lhsId)}return i},{}),c),{}),g=Object.keys(T)[0],o={[g]:1},n=Object.keys(T).reduce((c,t)=>t===g?c:{...c,[t]:1},{}),r=L(c=>{let t={[c]:[0,0]},u=[c];for(;u.length>0;){let i=u.shift();if(i){o[i]=1,delete n[i];let e=T[i],[l,f]=t[i];Object.entries(e).forEach(([h,A])=>{o[A]||(t[A]=ir([l,f],h),u.push(A))})}}return t},"BFS"),a=[r(g)];for(;Object.keys(n).length>0;)a.push(r(Object.keys(n)[0]));this.dataStructures={adjList:T,spatialMaps:a,groupAlignments:C}}return this.dataStructures}setElementForId(C,T){this.elements[C]=T}getElementById(C){return this.elements[C]}getConfig(){return Ke({...Er,...Ue().architecture})}getConfigField(C){return this.getConfig()[C]}};var Tr=L((D,C)=>{_e(D,C),D.groups.map(T=>C.addGroup(T)),D.services.map(T=>C.addService({...T,type:"service"})),D.junctions.map(T=>C.addJunction({...T,type:"junction"})),D.edges.map(T=>C.addEdge(T))},"populateDb"),Me={parser:{yy:void 0},parse:L(async D=>{let C=await je("architecture",D);fe.debug(C);let T=Me.parser?.yy;if(!(T instanceof re))throw new Error("parser.parser?.yy was not a ArchitectureDB. This is due to a bug within Mermaid, please report this issue at https://github.com/mermaid-js/mermaid/issues.");Tr(C,T)},"parse")};var Ar=L(D=>`
  .edge {
    stroke-width: ${D.archEdgeWidth};
    stroke: ${D.archEdgeColor};
    fill: none;
  }

  .arrow {
    fill: ${D.archEdgeArrowColor};
  }

  .node-bkg {
    fill: none;
    stroke: ${D.archGroupBorderColor};
    stroke-width: ${D.archGroupBorderWidth};
    stroke-dasharray: 8;
  }
  .node-icon-text {
    display: flex; 
    align-items: center;
  }
  
  .node-icon-text > div {
    color: #fff;
    margin: 1px;
    height: fit-content;
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
`,"getStyles"),hr=Ar;var dr=yr(lr(),1);var ie=L(D=>`<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/>${D}</g>`,"wrapIcon"),Kt={prefix:"mermaid-architecture",height:80,width:80,icons:{database:{body:ie('<path id="b" data-name="4" d="m20,57.86c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="c" data-name="3" d="m20,45.95c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="d" data-name="2" d="m20,34.05c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse id="e" data-name="1" cx="40" cy="22.14" rx="20" ry="7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="20" y1="57.86" x2="20" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="60" y1="57.86" x2="60" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},server:{body:ie('<rect x="17.5" y="17.5" width="45" height="45" rx="2" ry="2" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="32.5" x2="62.5" y2="32.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="47.5" x2="62.5" y2="47.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><g><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g>')},disk:{body:ie('<rect x="20" y="15" width="40" height="50" rx="1" ry="1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="14" ry="14.58" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="4" ry="4.17" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m37.51,42.52l-4.83,13.22c-.26.71-1.1,1.02-1.76.64l-4.18-2.42c-.66-.38-.81-1.26-.33-1.84l9.01-10.8c.88-1.05,2.56-.08,2.09,1.2Z" style="fill: #fff; stroke-width: 0px;"/>')},internet:{body:ie('<circle cx="40" cy="40" r="22.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="40" y1="17.5" x2="40" y2="62.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="40" x2="62.5" y2="40" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m39.99,17.51c-15.28,11.1-15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m40.01,17.51c15.28,11.1,15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="30.1" x2="60.25" y2="30.1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="49.9" x2="60.25" y2="49.9" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},cloud:{body:ie('<path d="m65,47.5c0,2.76-2.24,5-5,5H20c-2.76,0-5-2.24-5-5,0-1.87,1.03-3.51,2.56-4.36-.04-.21-.06-.42-.06-.64,0-2.6,2.48-4.74,5.65-4.97,1.65-4.51,6.34-7.76,11.85-7.76.86,0,1.69.08,2.5.23,2.09-1.57,4.69-2.5,7.5-2.5,6.1,0,11.19,4.38,12.28,10.17,2.14.56,3.72,2.51,3.72,4.83,0,.03,0,.07-.01.1,2.29.46,4.01,2.48,4.01,4.9Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},unknown:He,blank:{body:ie("")}}};var cr=L(async function(D,C,T){let g=T.getConfigField("padding"),o=T.getConfigField("iconSize"),n=o/2,r=o/6,a=r/2;await Promise.all(C.edges().map(async c=>{let{source:t,sourceDir:u,sourceArrow:i,sourceGroup:e,target:l,targetDir:f,targetArrow:h,targetGroup:A,label:v}=pe(c),{x:y,y:N}=c[0].sourceEndpoint(),{x:S,y:M}=c[0].midpoint(),{x:b,y:$}=c[0].targetEndpoint(),X=g+4;if(e&&(Ht(u)?y+=u==="L"?-X:X:N+=u==="T"?-X:X+18),A&&(Ht(f)?b+=f==="L"?-X:X:$+=f==="T"?-X:X+18),!e&&T.getNode(t)?.type==="junction"&&(Ht(u)?y+=u==="L"?n:-n:N+=u==="T"?n:-n),!A&&T.getNode(l)?.type==="junction"&&(Ht(f)?b+=f==="L"?n:-n:$+=f==="T"?n:-n),c[0]._private.rscratch){let et=D.insert("g");if(et.insert("path").attr("d",`M ${y},${N} L ${S},${M} L${b},${$} `).attr("class","edge"),i){let R=Ht(u)?ne[u](y,r):y-a,J=kt(u)?ne[u](N,r):N-a;et.insert("polygon").attr("points",De[u](r)).attr("transform",`translate(${R},${J})`).attr("class","arrow")}if(h){let R=Ht(f)?ne[f](b,r):b-a,J=kt(f)?ne[f]($,r):$-a;et.insert("polygon").attr("points",De[f](r)).attr("transform",`translate(${R},${J})`).attr("class","arrow")}if(v){let R=ae(u,f)?"XY":Ht(u)?"X":"Y",J=0;R==="X"?J=Math.abs(y-b):R==="Y"?J=Math.abs(N-$)/1.5:J=Math.abs(y-b)/2;let s=et.append("g");if(await de(s,v,{useHtmlLabels:!1,width:J,classes:"architecture-service-label"},ge()),s.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),R==="X")s.attr("transform","translate("+S+", "+M+")");else if(R==="Y")s.attr("transform","translate("+S+", "+M+") rotate(-90)");else if(R==="XY"){let m=oe(u,f);if(m&&rr(m)){let p=s.node().getBoundingClientRect(),[E,d]=nr(m);s.attr("dominant-baseline","auto").attr("transform",`rotate(${-1*E*d*45})`);let O=s.node().getBoundingClientRect();s.attr("transform",`
                translate(${S}, ${M-p.height/2})
                translate(${E*O.width/2}, ${d*O.height/2})
                rotate(${-1*E*d*45}, 0, ${p.height/2})
              `)}}}}}))},"drawEdges"),fr=L(async function(D,C,T){let o=T.getConfigField("padding")*.75,n=T.getConfigField("fontSize"),a=T.getConfigField("iconSize")/2;await Promise.all(C.nodes().map(async c=>{let t=Jt(c);if(t.type==="group"){let{h:u,w:i,x1:e,y1:l}=c.boundingBox();D.append("rect").attr("x",e+a).attr("y",l+a).attr("width",i).attr("height",u).attr("class","node-bkg");let f=D.append("g"),h=e,A=l;if(t.icon){let v=f.append("g");v.html(`<g>${await ue(t.icon,{height:o,width:o,fallbackPrefix:Kt.prefix})}</g>`),v.attr("transform","translate("+(h+a+1)+", "+(A+a+1)+")"),h+=o,A+=n/2-1-2}if(t.label){let v=f.append("g");await de(v,t.label,{useHtmlLabels:!1,width:i,classes:"architecture-service-label"},ge()),v.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","start").attr("text-anchor","start"),v.attr("transform","translate("+(h+a+4)+", "+(A+a+2)+")")}}}))},"drawGroups"),ur=L(async function(D,C,T){let g=ge();for(let o of T){let n=C.append("g"),r=D.getConfigField("iconSize");if(o.title){let u=n.append("g");await de(u,o.title,{useHtmlLabels:!1,width:r*1.5,classes:"architecture-service-label"},g),u.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),u.attr("transform","translate("+r/2+", "+r+")")}let a=n.append("g");if(o.icon)a.html(`<g>${await ue(o.icon,{height:r,width:r,fallbackPrefix:Kt.prefix})}</g>`);else if(o.iconText){a.html(`<g>${await ue("blank",{height:r,width:r,fallbackPrefix:Kt.prefix})}</g>`);let e=a.append("g").append("foreignObject").attr("width",r).attr("height",r).append("div").attr("class","node-icon-text").attr("style",`height: ${r}px;`).append("div").html(Xe(o.iconText,g)),l=parseInt(window.getComputedStyle(e.node(),null).getPropertyValue("font-size").replace(/\D/g,""))??16;e.attr("style",`-webkit-line-clamp: ${Math.floor((r-2)/l)};`)}else a.append("path").attr("class","node-bkg").attr("id","node-"+o.id).attr("d",`M0 ${r} v${-r} q0,-5 5,-5 h${r} q5,0 5,5 v${r} H0 Z`);n.attr("class","architecture-service");let{width:c,height:t}=n._groups[0][0].getBBox();o.width=c,o.height=t,D.setElementForId(o.id,n)}return 0},"drawServices"),gr=L(function(D,C,T){T.forEach(g=>{let o=C.append("g"),n=D.getConfigField("iconSize");o.append("g").append("rect").attr("id","node-"+g.id).attr("fill-opacity","0").attr("width",n).attr("height",n),o.attr("class","architecture-junction");let{width:a,height:c}=o._groups[0][0].getBBox();o.width=a,o.height=c,D.setElementForId(g.id,o)})},"drawJunctions");We([{name:Kt.prefix,icons:Kt}]);Ne.use(dr.default);function Nr(D,C,T){D.forEach(g=>{C.add({group:"nodes",data:{type:"service",id:g.id,icon:g.icon,label:g.title,parent:g.in,width:T.getConfigField("iconSize"),height:T.getConfigField("iconSize")},classes:"node-service"})})}L(Nr,"addServices");function Lr(D,C,T){D.forEach(g=>{C.add({group:"nodes",data:{type:"junction",id:g.id,parent:g.in,width:T.getConfigField("iconSize"),height:T.getConfigField("iconSize")},classes:"node-junction"})})}L(Lr,"addJunctions");function Dr(D,C){C.nodes().map(T=>{let g=Jt(T);if(g.type==="group")return;g.x=T.position().x,g.y=T.position().y,D.getElementById(g.id).attr("transform","translate("+(g.x||0)+","+(g.y||0)+")")})}L(Dr,"positionNodes");function Cr(D,C){D.forEach(T=>{C.add({group:"nodes",data:{type:"group",id:T.id,icon:T.icon,label:T.title,parent:T.in},classes:"node-group"})})}L(Cr,"addGroups");function Mr(D,C){D.forEach(T=>{let{lhsId:g,rhsId:o,lhsInto:n,lhsGroup:r,rhsInto:a,lhsDir:c,rhsDir:t,rhsGroup:u,title:i}=T,e=ae(T.lhsDir,T.rhsDir)?"segments":"straight",l={id:`${g}-${o}`,label:i,source:g,sourceDir:c,sourceArrow:n,sourceGroup:r,sourceEndpoint:c==="L"?"0 50%":c==="R"?"100% 50%":c==="T"?"50% 0":"50% 100%",target:o,targetDir:t,targetArrow:a,targetGroup:u,targetEndpoint:t==="L"?"0 50%":t==="R"?"100% 50%":t==="T"?"50% 0":"50% 100%"};C.add({group:"edges",data:l,classes:e})})}L(Mr,"addEdges");function wr(D,C,T){let g=L((a,c)=>Object.entries(a).reduce((t,[u,i])=>{let e=0,l=Object.entries(i);if(l.length===1)return t[u]=l[0][1],t;for(let f=0;f<l.length-1;f++)for(let h=f+1;h<l.length;h++){let[A,v]=l[f],[y,N]=l[h];if(T[A]?.[y]===c)t[u]??=[],t[u]=[...t[u],...v,...N];else if(A==="default"||y==="default")t[u]??=[],t[u]=[...t[u],...v,...N];else{let M=`${u}-${e++}`;t[M]=v;let b=`${u}-${e++}`;t[b]=N}}return t},{}),"flattenAlignments"),o=C.map(a=>{let c={},t={};return Object.entries(a).forEach(([u,[i,e]])=>{let l=D.getNode(u)?.in??"default";c[e]??={},c[e][l]??=[],c[e][l].push(u),t[i]??={},t[i][l]??=[],t[i][l].push(u)}),{horiz:Object.values(g(c,"horizontal")).filter(u=>u.length>1),vert:Object.values(g(t,"vertical")).filter(u=>u.length>1)}}),[n,r]=o.reduce(([a,c],{horiz:t,vert:u})=>[[...a,...t],[...c,...u]],[[],[]]);return{horizontal:n,vertical:r}}L(wr,"getAlignments");function xr(D,C){let T=[],g=L(n=>`${n[0]},${n[1]}`,"posToStr"),o=L(n=>n.split(",").map(r=>parseInt(r)),"strToPos");return D.forEach(n=>{let r=Object.fromEntries(Object.entries(n).map(([u,i])=>[g(i),u])),a=[g([0,0])],c={},t={L:[-1,0],R:[1,0],T:[0,1],B:[0,-1]};for(;a.length>0;){let u=a.shift();if(u){c[u]=1;let i=r[u];if(i){let e=o(u);Object.entries(t).forEach(([l,f])=>{let h=g([e[0]+f[0],e[1]+f[1]]),A=r[h];A&&!c[h]&&(a.push(h),T.push({[Le[l]]:A,[Le[er(l)]]:i,gap:1.5*C.getConfigField("iconSize")}))})}}}}),T}L(xr,"getRelativeConstraints");function Or(D,C,T,g,o,{spatialMaps:n,groupAlignments:r}){return new Promise(a=>{let c=Qe("body").append("div").attr("id","cy").attr("style","display:none"),t=Ne({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"straight",label:"data(label)","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"edge.segments",style:{"curve-style":"segments","segment-weights":"0","segment-distances":[.5],"edge-distances":"endpoints","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"node",style:{"compound-sizing-wrt-labels":"include"}},{selector:"node[label]",style:{"text-valign":"bottom","text-halign":"center","font-size":`${o.getConfigField("fontSize")}px`}},{selector:".node-service",style:{label:"data(label)",width:"data(width)",height:"data(height)"}},{selector:".node-junction",style:{width:"data(width)",height:"data(height)"}},{selector:".node-group",style:{padding:`${o.getConfigField("padding")}px`}}],layout:{name:"grid",boundingBox:{x1:0,x2:100,y1:0,y2:100}}});c.remove(),Cr(T,t),Nr(D,t,o),Lr(C,t,o),Mr(g,t);let u=wr(o,n,r),i=xr(n,o),e=t.layout({name:"fcose",quality:"proof",styleEnabled:!1,animate:!1,nodeDimensionsIncludeLabels:!1,idealEdgeLength(l){let[f,h]=l.connectedNodes(),{parent:A}=Jt(f),{parent:v}=Jt(h);return A===v?1.5*o.getConfigField("iconSize"):.5*o.getConfigField("iconSize")},edgeElasticity(l){let[f,h]=l.connectedNodes(),{parent:A}=Jt(f),{parent:v}=Jt(h);return A===v?.45:.001},alignmentConstraint:u,relativePlacementConstraint:i});e.one("layoutstop",()=>{function l(f,h,A,v){let y,N,{x:S,y:M}=f,{x:b,y:$}=h;N=(v-M+(S-A)*(M-$)/(S-b))/Math.sqrt(1+Math.pow((M-$)/(S-b),2)),y=Math.sqrt(Math.pow(v-M,2)+Math.pow(A-S,2)-Math.pow(N,2));let X=Math.sqrt(Math.pow(b-S,2)+Math.pow($-M,2));y=y/X;let et=(b-S)*(v-M)-($-M)*(A-S);switch(!0){case et>=0:et=1;break;case et<0:et=-1;break}let R=(b-S)*(A-S)+($-M)*(v-M);switch(!0){case R>=0:R=1;break;case R<0:R=-1;break}return N=Math.abs(N)*et,y=y*R,{distances:N,weights:y}}L(l,"getSegmentWeights"),t.startBatch();for(let f of Object.values(t.edges()))if(f.data?.()){let{x:h,y:A}=f.source().position(),{x:v,y}=f.target().position();if(h!==v&&A!==y){let N=f.sourceEndpoint(),S=f.targetEndpoint(),{sourceDir:M}=pe(f),[b,$]=kt(M)?[N.x,S.y]:[S.x,N.y],{weights:X,distances:et}=l(N,S,b,$);f.style("segment-distances",et),f.style("segment-weights",X)}}t.endBatch(),e.run()}),e.run(),t.ready(l=>{fe.info("Ready",l),a(t)})})}L(Or,"layoutArchitecture");var Rr=L(async(D,C,T,g)=>{let o=g.db,n=o.getServices(),r=o.getJunctions(),a=o.getGroups(),c=o.getEdges(),t=o.getDataStructures(),u=tr(C),i=u.append("g");i.attr("class","architecture-edges");let e=u.append("g");e.attr("class","architecture-services");let l=u.append("g");l.attr("class","architecture-groups"),await ur(o,e,n),gr(o,e,r);let f=await Or(n,r,a,c,o,t);await cr(i,f,o),await fr(l,f,o),Dr(o,f),Ve(void 0,u,o.getConfigField("padding"),o.getConfigField("useMaxWidth"))},"draw"),pr={draw:Rr};var Li={parser:Me,get db(){return new re},renderer:pr,styles:hr};export{Li as diagram};
