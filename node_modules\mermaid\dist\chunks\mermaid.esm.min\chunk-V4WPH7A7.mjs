import{a as s,b as l,c as n,d,e as u,f as o,g as m,n as c,o as p,q as T}from"./chunk-L6MQJ2ZU.mjs";import{a as i}from"./chunk-GTKDMUJJ.mjs";var v=class extends T{static{i(this,"TreemapTokenBuilder")}static{o(this,"TreemapTokenBuilder")}constructor(){super(["treemap"])}},g=/classDef\s+([A-Z_a-z]\w+)(?:\s+([^\n\r;]*))?;?/,h=class extends p{static{i(this,"TreemapValueConverter")}static{o(this,"TreemapValueConverter")}runCustomConverter(r,e,t){if(r.name==="NUMBER2")return parseFloat(e.replace(/,/g,""));if(r.name==="SEPARATOR")return e.substring(1,e.length-1);if(r.name==="STRING2")return e.substring(1,e.length-1);if(r.name==="INDENTATION")return e.length;if(r.name==="ClassDef"){if(typeof e!="string")return e;let a=g.exec(e);if(a)return{$type:"ClassDefStatement",className:a[1],styleText:a[2]||void 0}}}};function f(r){let e=r.validation.TreemapValidator,t=r.validation.ValidationRegistry;if(t){let a={Treemap:e.checkSingleRoot.bind(e)};t.register(a,e)}}i(f,"registerValidationChecks");o(f,"registerValidationChecks");var C=class{static{i(this,"TreemapValidator")}static{o(this,"TreemapValidator")}checkSingleRoot(r,e){let t;for(let a of r.TreemapRows)a.item&&(t===void 0&&a.indent===void 0?t=0:a.indent===void 0?e("error","Multiple root nodes are not allowed in a treemap.",{node:a,property:"item"}):t!==void 0&&t>=parseInt(a.indent,10)&&e("error","Multiple root nodes are not allowed in a treemap.",{node:a,property:"item"}))}},V={parser:{TokenBuilder:o(()=>new v,"TokenBuilder"),ValueConverter:o(()=>new h,"ValueConverter")},validation:{TreemapValidator:o(()=>new C,"TreemapValidator")}};function M(r=d){let e=n(l(r),m),t=n(s({shared:e}),c,V);return e.ServiceRegistry.register(t),f(t),{shared:e,Treemap:t}}i(M,"createTreemapServices");o(M,"createTreemapServices");export{V as a,M as b};
