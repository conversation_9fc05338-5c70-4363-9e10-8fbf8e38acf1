import{a as ht}from"./chunks/mermaid.esm.min/chunk-YU6XO2NZ.mjs";import{a as Yt}from"./chunks/mermaid.esm.min/chunk-U7M5BGKE.mjs";import{a as Ut,b as qt}from"./chunks/mermaid.esm.min/chunk-7LIB5WBN.mjs";import{a as Bt}from"./chunks/mermaid.esm.min/chunk-YKNY556I.mjs";import"./chunks/mermaid.esm.min/chunk-JOFIKEML.mjs";import"./chunks/mermaid.esm.min/chunk-S67DUUA5.mjs";import"./chunks/mermaid.esm.min/chunk-DPMNACAB.mjs";import"./chunks/mermaid.esm.min/chunk-LM6QDVU5.mjs";import"./chunks/mermaid.esm.min/chunk-Z2NOIGJN.mjs";import{b as Ot,d as Pt}from"./chunks/mermaid.esm.min/chunk-IXVBHSNP.mjs";import{b as Vt,j as yt,l as $t,m as V,n as Nt,o as Ht}from"./chunks/mermaid.esm.min/chunk-3R3PQ5PD.mjs";import"./chunks/mermaid.esm.min/chunk-TI4EEUUG.mjs";import{A as jt,G as Ft,I as It,J as X,K as rt,L as W,M as _t,N as Gt,P as zt,a as St,ba as z,c as g,ca as K,d as lt,ha as k,k as tt,n as ut,o as Mt,p as At,q as Tt,r as Dt,s as Ct,t as G,u as Rt,v as Y,w as kt}from"./chunks/mermaid.esm.min/chunk-F632ZYSZ.mjs";import{d as xt}from"./chunks/mermaid.esm.min/chunk-YPUTD6PB.mjs";import"./chunks/mermaid.esm.min/chunk-6BY5RJGC.mjs";import{a as r}from"./chunks/mermaid.esm.min/chunk-GTKDMUJJ.mjs";var Xt="c4",_e=r(t=>/^\s*C4Context|C4Container|C4Component|C4Dynamic|C4Deployment/.test(t),"detector"),Ge=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/c4Diagram-S26XAWAN.mjs");return{id:Xt,diagram:t}},"loader"),ze={id:Xt,detector:_e,loader:Ge},Wt=ze;var Kt="flowchart",Ve=r((t,e)=>e?.flowchart?.defaultRenderer==="dagre-wrapper"||e?.flowchart?.defaultRenderer==="elk"?!1:/^\s*graph/.test(t),"detector"),$e=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/flowDiagram-7YKJRSJY.mjs");return{id:Kt,diagram:t}},"loader"),Ne={id:Kt,detector:Ve,loader:$e},Qt=Ne;var Jt="flowchart-v2",He=r((t,e)=>e?.flowchart?.defaultRenderer==="dagre-d3"?!1:(e?.flowchart?.defaultRenderer==="elk"&&(e.layout="elk"),/^\s*graph/.test(t)&&e?.flowchart?.defaultRenderer==="dagre-wrapper"?!0:/^\s*flowchart/.test(t)),"detector"),Ue=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/flowDiagram-7YKJRSJY.mjs");return{id:Jt,diagram:t}},"loader"),qe={id:Jt,detector:He,loader:Ue},Zt=qe;var tr="er",Be=r(t=>/^\s*erDiagram/.test(t),"detector"),Ye=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/erDiagram-DAYQTJMR.mjs");return{id:tr,diagram:t}},"loader"),Xe={id:tr,detector:Be,loader:Ye},rr=Xe;var er="gitGraph",We=r(t=>/^\s*gitGraph/.test(t),"detector"),Ke=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/gitGraphDiagram-LJPR7UNW.mjs");return{id:er,diagram:t}},"loader"),Qe={id:er,detector:We,loader:Ke},ar=Qe;var ir="gantt",Je=r(t=>/^\s*gantt/.test(t),"detector"),Ze=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/ganttDiagram-5J5FM7L2.mjs");return{id:ir,diagram:t}},"loader"),ta={id:ir,detector:Je,loader:Ze},or=ta;var nr="info",ra=r(t=>/^\s*info/.test(t),"detector"),ea=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/infoDiagram-SWUXXWDM.mjs");return{id:nr,diagram:t}},"loader"),sr={id:nr,detector:ra,loader:ea};var cr="pie",aa=r(t=>/^\s*pie/.test(t),"detector"),ia=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/pieDiagram-C2LPN6SB.mjs");return{id:cr,diagram:t}},"loader"),mr={id:cr,detector:aa,loader:ia};var pr="quadrantChart",oa=r(t=>/^\s*quadrantChart/.test(t),"detector"),na=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/quadrantDiagram-5BXOXB7R.mjs");return{id:pr,diagram:t}},"loader"),sa={id:pr,detector:oa,loader:na},dr=sa;var fr="xychart",ca=r(t=>/^\s*xychart(-beta)?/.test(t),"detector"),ma=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/xychartDiagram-WBSIB3YZ.mjs");return{id:fr,diagram:t}},"loader"),pa={id:fr,detector:ca,loader:ma},gr=pa;var lr="requirement",da=r(t=>/^\s*requirement(Diagram)?/.test(t),"detector"),fa=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/requirementDiagram-CG6N56KZ.mjs");return{id:lr,diagram:t}},"loader"),ga={id:lr,detector:da,loader:fa},ur=ga;var Dr="sequence",la=r(t=>/^\s*sequenceDiagram/.test(t),"detector"),ua=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/sequenceDiagram-TKKJIMEC.mjs");return{id:Dr,diagram:t}},"loader"),Da={id:Dr,detector:la,loader:ua},yr=Da;var xr="class",ya=r((t,e)=>e?.class?.defaultRenderer==="dagre-wrapper"?!1:/^\s*classDiagram/.test(t),"detector"),xa=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/classDiagram-V7QDPDRR.mjs");return{id:xr,diagram:t}},"loader"),ha={id:xr,detector:ya,loader:xa},hr=ha;var Er="classDiagram",Ea=r((t,e)=>/^\s*classDiagram/.test(t)&&e?.class?.defaultRenderer==="dagre-wrapper"?!0:/^\s*classDiagram-v2/.test(t),"detector"),wa=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/classDiagram-v2-I452LMQG.mjs");return{id:Er,diagram:t}},"loader"),La={id:Er,detector:Ea,loader:wa},wr=La;var Lr="state",ba=r((t,e)=>e?.state?.defaultRenderer==="dagre-wrapper"?!1:/^\s*stateDiagram/.test(t),"detector"),va=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/stateDiagram-3M5G37A4.mjs");return{id:Lr,diagram:t}},"loader"),Sa={id:Lr,detector:ba,loader:va},br=Sa;var vr="stateDiagram",Ma=r((t,e)=>!!(/^\s*stateDiagram-v2/.test(t)||/^\s*stateDiagram/.test(t)&&e?.state?.defaultRenderer==="dagre-wrapper"),"detector"),Aa=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/stateDiagram-v2-B2ICEZYC.mjs");return{id:vr,diagram:t}},"loader"),Ta={id:vr,detector:Ma,loader:Aa},Sr=Ta;var Mr="journey",Ca=r(t=>/^\s*journey/.test(t),"detector"),Ra=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/journeyDiagram-74NXYP6Y.mjs");return{id:Mr,diagram:t}},"loader"),ka={id:Mr,detector:Ca,loader:Ra},Ar=ka;var ja=r((t,e,a)=>{g.debug(`rendering svg for syntax error
`);let i=Yt(e),o=i.append("g");i.attr("viewBox","0 0 2412 512"),Gt(i,100,512,!0),o.append("path").attr("class","error-icon").attr("d","m411.313,123.313c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32-9.375,9.375-20.688-20.688c-12.484-12.5-32.766-12.5-45.25,0l-16,16c-1.261,1.261-2.304,2.648-3.31,4.051-21.739-8.561-45.324-13.426-70.065-13.426-105.867,0-192,86.133-192,192s86.133,192 192,192 192-86.133 192-192c0-24.741-4.864-48.327-13.426-70.065 1.402-1.007 2.79-2.049 4.051-3.31l16-16c12.5-12.492 12.5-32.758 0-45.25l-20.688-20.688 9.375-9.375 32.001-31.999zm-219.313,100.687c-52.938,0-96,43.063-96,96 0,8.836-7.164,16-16,16s-16-7.164-16-16c0-70.578 57.***********-128 8.836,0 16,7.164 16,16s-7.164,16-16,16z"),o.append("path").attr("class","error-icon").attr("d","m459.02,148.98c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l16,16c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16.001-16z"),o.append("path").attr("class","error-icon").attr("d","m340.395,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16-16c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l15.999,16z"),o.append("path").attr("class","error-icon").attr("d","m400,64c8.844,0 16-7.164 16-16v-32c0-8.836-7.156-16-16-16-8.844,0-16,7.164-16,16v32c0,8.836 7.156,16 16,16z"),o.append("path").attr("class","error-icon").attr("d","m496,96.586h-32c-8.844,0-16,7.164-16,16 0,8.836 7.156,16 16,16h32c8.844,0 16-7.164 16-16 0-8.836-7.156-16-16-16z"),o.append("path").attr("class","error-icon").attr("d","m436.98,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688l32-32c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32c-6.251,6.25-6.251,16.375-0.001,22.625z"),o.append("text").attr("class","error-text").attr("x",1440).attr("y",250).attr("font-size","150px").style("text-anchor","middle").text("Syntax error in text"),o.append("text").attr("class","error-text").attr("x",1250).attr("y",400).attr("font-size","100px").style("text-anchor","middle").text(`mermaid version ${a}`)},"draw"),Et={draw:ja},Tr=Et;var Oa={db:{},renderer:Et,parser:{parse:r(()=>{},"parse")}},Cr=Oa;var Rr="flowchart-elk",Pa=r((t,e={})=>/^\s*flowchart-elk/.test(t)||/^\s*(flowchart|graph)/.test(t)&&e?.flowchart?.defaultRenderer==="elk"?(e.layout="elk",!0):!1,"detector"),Fa=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/flowDiagram-7YKJRSJY.mjs");return{id:Rr,diagram:t}},"loader"),Ia={id:Rr,detector:Pa,loader:Fa},kr=Ia;var jr="timeline",_a=r(t=>/^\s*timeline/.test(t),"detector"),Ga=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/timeline-definition-LVESLDUR.mjs");return{id:jr,diagram:t}},"loader"),za={id:jr,detector:_a,loader:Ga},Or=za;var Pr="mindmap",Va=r(t=>/^\s*mindmap/.test(t),"detector"),$a=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/mindmap-definition-75HPFGON.mjs");return{id:Pr,diagram:t}},"loader"),Na={id:Pr,detector:Va,loader:$a},Fr=Na;var Ir="kanban",Ha=r(t=>/^\s*kanban/.test(t),"detector"),Ua=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/kanban-definition-6U23TIYA.mjs");return{id:Ir,diagram:t}},"loader"),qa={id:Ir,detector:Ha,loader:Ua},_r=qa;var Gr="sankey",Ba=r(t=>/^\s*sankey(-beta)?/.test(t),"detector"),Ya=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/sankeyDiagram-RGKAZ5T4.mjs");return{id:Gr,diagram:t}},"loader"),Xa={id:Gr,detector:Ba,loader:Ya},zr=Xa;var Vr="packet",Wa=r(t=>/^\s*packet(-beta)?/.test(t),"detector"),Ka=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/diagram-BBIVWCGE.mjs");return{id:Vr,diagram:t}},"loader"),$r={id:Vr,detector:Wa,loader:Ka};var Nr="radar",Qa=r(t=>/^\s*radar-beta/.test(t),"detector"),Ja=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/diagram-67HW3K5X.mjs");return{id:Nr,diagram:t}},"loader"),Hr={id:Nr,detector:Qa,loader:Ja};var Ur="block",Za=r(t=>/^\s*block(-beta)?/.test(t),"detector"),ti=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/blockDiagram-HV7WRKX4.mjs");return{id:Ur,diagram:t}},"loader"),ri={id:Ur,detector:Za,loader:ti},qr=ri;var Br="architecture",ei=r(t=>/^\s*architecture/.test(t),"detector"),ai=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/architectureDiagram-GXAZU6X5.mjs");return{id:Br,diagram:t}},"loader"),ii={id:Br,detector:ei,loader:ai},Yr=ii;var Xr="treemap",oi=r(t=>/^\s*treemap/.test(t),"detector"),ni=r(async()=>{let{diagram:t}=await import("./chunks/mermaid.esm.min/diagram-6ZJQA7GM.mjs");return{id:Xr,diagram:t}},"loader"),Wr={id:Xr,detector:oi,loader:ni};var Kr=!1,$=r(()=>{Kr||(Kr=!0,z("error",Cr,t=>t.toLowerCase().trim()==="error"),z("---",{db:{clear:r(()=>{},"clear")},styles:{},renderer:{draw:r(()=>{},"draw")},parser:{parse:r(()=>{throw new Error("Diagrams beginning with --- are not valid. If you were trying to use a YAML front-matter, please ensure that you've correctly opened and closed the YAML front-matter with un-indented `---` blocks")},"parse")},init:r(()=>null,"init")},t=>t.toLowerCase().trimStart().startsWith("---")),W(kr,Fr,Yr),W(Wt,_r,wr,hr,rr,or,sr,mr,ur,yr,Zt,Qt,Or,ar,Sr,br,Ar,dr,zr,$r,gr,qr,Hr,Wr))},"addDiagrams");var Qr=r(async()=>{g.debug("Loading registered diagrams");let e=(await Promise.allSettled(Object.entries(X).map(async([a,{detector:i,loader:o}])=>{if(o)try{K(a)}catch{try{let{diagram:n,id:m}=await o();z(m,n,i)}catch(n){throw g.error(`Failed to load external diagram with key ${a}. Removing from detectors.`),delete X[a],n}}}))).filter(a=>a.status==="rejected");if(e.length>0){g.error(`Failed to load ${e.length} external diagrams`);for(let a of e)g.error(a);throw new Error(`Failed to load ${e.length} external diagrams`)}},"loadRegisteredDiagrams");var et="comm",at="rule",it="decl";var Jr="@import";var Zr="@namespace",te="@keyframes";var re="@layer";var wt=Math.abs,Q=String.fromCharCode;function ot(t){return t.trim()}r(ot,"trim");function J(t,e,a){return t.replace(e,a)}r(J,"replace");function ee(t,e,a){return t.indexOf(e,a)}r(ee,"indexof");function P(t,e){return t.charCodeAt(e)|0}r(P,"charat");function F(t,e,a){return t.slice(e,a)}r(F,"substr");function h(t){return t.length}r(h,"strlen");function ae(t){return t.length}r(ae,"sizeof");function N(t,e){return e.push(t),t}r(N,"append");var nt=1,H=1,ie=0,w=0,D=0,q="";function st(t,e,a,i,o,n,m,s){return{value:t,root:e,parent:a,type:i,props:o,children:n,line:nt,column:H,length:m,return:"",siblings:s}}r(st,"node");function oe(){return D}r(oe,"char");function ne(){return D=w>0?P(q,--w):0,H--,D===10&&(H=1,nt--),D}r(ne,"prev");function L(){return D=w<ie?P(q,w++):0,H++,D===10&&(H=1,nt++),D}r(L,"next");function j(){return P(q,w)}r(j,"peek");function Z(){return w}r(Z,"caret");function ct(t,e){return F(q,t,e)}r(ct,"slice");function U(t){switch(t){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}r(U,"token");function se(t){return nt=H=1,ie=h(q=t),w=0,[]}r(se,"alloc");function ce(t){return q="",t}r(ce,"dealloc");function mt(t){return ot(ct(w-1,Lt(t===91?t+2:t===40?t+1:t)))}r(mt,"delimit");function me(t){for(;(D=j())&&D<33;)L();return U(t)>2||U(D)>3?"":" "}r(me,"whitespace");function pe(t,e){for(;--e&&L()&&!(D<48||D>102||D>57&&D<65||D>70&&D<97););return ct(t,Z()+(e<6&&j()==32&&L()==32))}r(pe,"escaping");function Lt(t){for(;L();)switch(D){case t:return w;case 34:case 39:t!==34&&t!==39&&Lt(D);break;case 40:t===41&&Lt(t);break;case 92:L();break}return w}r(Lt,"delimiter");function de(t,e){for(;L()&&t+D!==57;)if(t+D===84&&j()===47)break;return"/*"+ct(e,w-1)+"*"+Q(t===47?t:L())}r(de,"commenter");function fe(t){for(;!U(j());)L();return ct(t,w)}r(fe,"identifier");function ue(t){return ce(pt("",null,null,null,[""],t=se(t),0,[0],t))}r(ue,"compile");function pt(t,e,a,i,o,n,m,s,c){for(var l=0,y=0,p=m,x=0,A=0,b=0,f=1,C=1,v=1,u=0,S="",R=o,T=n,E=i,d=S;C;)switch(b=u,u=L()){case 40:if(b!=108&&P(d,p-1)==58){ee(d+=J(mt(u),"&","&\f"),"&\f",wt(l?s[l-1]:0))!=-1&&(v=-1);break}case 34:case 39:case 91:d+=mt(u);break;case 9:case 10:case 13:case 32:d+=me(b);break;case 92:d+=pe(Z()-1,7);continue;case 47:switch(j()){case 42:case 47:N(si(de(L(),Z()),e,a,c),c),(U(b||1)==5||U(j()||1)==5)&&h(d)&&F(d,-1,void 0)!==" "&&(d+=" ");break;default:d+="/"}break;case 123*f:s[l++]=h(d)*v;case 125*f:case 59:case 0:switch(u){case 0:case 125:C=0;case 59+y:v==-1&&(d=J(d,/\f/g,"")),A>0&&(h(d)-p||f===0&&b===47)&&N(A>32?le(d+";",i,a,p-1,c):le(J(d," ","")+";",i,a,p-2,c),c);break;case 59:d+=";";default:if(N(E=ge(d,e,a,l,y,o,s,S,R=[],T=[],p,n),n),u===123)if(y===0)pt(d,e,E,E,R,n,p,s,T);else{switch(x){case 99:if(P(d,3)===110)break;case 108:if(P(d,2)===97)break;default:y=0;case 100:case 109:case 115:}y?pt(t,E,E,i&&N(ge(t,E,E,0,0,o,s,S,o,R=[],p,T),T),o,T,p,s,i?R:T):pt(d,E,E,E,[""],T,0,s,T)}}l=y=A=0,f=v=1,S=d="",p=m;break;case 58:p=1+h(d),A=b;default:if(f<1){if(u==123)--f;else if(u==125&&f++==0&&ne()==125)continue}switch(d+=Q(u),u*f){case 38:v=y>0?1:(d+="\f",-1);break;case 44:s[l++]=(h(d)-1)*v,v=1;break;case 64:j()===45&&(d+=mt(L())),x=j(),y=p=h(S=d+=fe(Z())),u++;break;case 45:b===45&&h(d)==2&&(f=0)}}return n}r(pt,"parse");function ge(t,e,a,i,o,n,m,s,c,l,y,p){for(var x=o-1,A=o===0?n:[""],b=ae(A),f=0,C=0,v=0;f<i;++f)for(var u=0,S=F(t,x+1,x=wt(C=m[f])),R=t;u<b;++u)(R=ot(C>0?A[u]+" "+S:J(S,/&\f/g,A[u])))&&(c[v++]=R);return st(t,e,a,o===0?at:s,c,l,y,p)}r(ge,"ruleset");function si(t,e,a,i){return st(t,e,a,et,Q(oe()),F(t,2,-2),0,i)}r(si,"comment");function le(t,e,a,i,o){return st(t,e,a,it,F(t,0,i),F(t,i+1,-1),i,o)}r(le,"declaration");function dt(t,e){for(var a="",i=0;i<t.length;i++)a+=e(t[i],i,t,e)||"";return a}r(dt,"serialize");function De(t,e,a,i){switch(t.type){case re:if(t.children.length)break;case Jr:case Zr:case it:return t.return=t.return||t.value;case et:return"";case te:return t.return=t.value+"{"+dt(t.children,i)+"}";case at:if(!h(t.value=t.props.join(",")))return""}return h(a=dt(t.children,i))?t.return=t.value+"{"+a+"}":""}r(De,"stringify");var ci="graphics-document document";function ye(t,e){t.attr("role",ci),e!==""&&t.attr("aria-roledescription",e)}r(ye,"setA11yDiagramInfo");function xe(t,e,a,i){if(t.insert!==void 0){if(a){let o=`chart-desc-${i}`;t.attr("aria-describedby",o),t.insert("desc",":first-child").attr("id",o).text(a)}if(e){let o=`chart-title-${i}`;t.attr("aria-labelledby",o),t.insert("title",":first-child").attr("id",o).text(e)}}}r(xe,"addSVGa11yTitleDescription");var B=class t{constructor(e,a,i,o,n){this.type=e;this.text=a;this.db=i;this.parser=o;this.renderer=n}static{r(this,"Diagram")}static async fromText(e,a={}){let i=G(),o=rt(e,i);e=Nt(e)+`
`;try{K(o)}catch{let l=_t(o);if(!l)throw new It(`Diagram ${o} not found.`);let{id:y,diagram:p}=await l();z(y,p)}let{db:n,parser:m,renderer:s,init:c}=K(o);return m.parser&&(m.parser.yy=n),n.clear?.(),c?.(i),a.title&&n.setDiagramTitle?.(a.title),await m.parse(e),new t(o,e,n,m,s)}async render(e,a){await this.renderer.draw(this.text,e,a,this)}getParser(){return this.parser}getType(){return this.type}};var he=[];var Ee=r(()=>{he.forEach(t=>{t()}),he=[]},"attachFunctions");var we=r(t=>t.replace(/^\s*%%(?!{)[^\n]+\n?/gm,"").trimStart(),"cleanupComments");function Le(t){let e=t.match(Ft);if(!e)return{text:t,metadata:{}};let a=qt(e[1],{schema:Ut})??{};a=typeof a=="object"&&!Array.isArray(a)?a:{};let i={};return a.displayMode&&(i.displayMode=a.displayMode.toString()),a.title&&(i.title=a.title.toString()),a.config&&(i.config=a.config),{text:t.slice(e[0].length),metadata:i}}r(Le,"extractFrontMatter");var pi=r(t=>t.replace(/\r\n?/g,`
`).replace(/<(\w+)([^>]*)>/g,(e,a,i)=>"<"+a+i.replace(/="([^"]*)"/g,"='$1'")+">"),"cleanupText"),di=r(t=>{let{text:e,metadata:a}=Le(t),{displayMode:i,title:o,config:n={}}=a;return i&&(n.gantt||(n.gantt={}),n.gantt.displayMode=i),{title:o,config:n,text:e}},"processFrontmatter"),fi=r(t=>{let e=V.detectInit(t)??{},a=V.detectDirective(t,"wrap");return Array.isArray(a)?e.wrap=a.some(({type:i})=>i==="wrap"):a?.type==="wrap"&&(e.wrap=!0),{text:Vt(t),directive:e}},"processDirectives");function bt(t){let e=pi(t),a=di(e),i=fi(a.text),o=$t(a.config,i.directive);return t=we(i.text),{code:t,title:a.title,config:o}}r(bt,"preprocessDiagram");function be(t){let e=new TextEncoder().encode(t),a=Array.from(e,i=>String.fromCodePoint(i)).join("");return btoa(a)}r(be,"toBase64");var gi=5e4,li="graph TB;a[Maximum text size in diagram exceeded];style a fill:#faa",ui="sandbox",Di="loose",yi="http://www.w3.org/2000/svg",xi="http://www.w3.org/1999/xlink",hi="http://www.w3.org/1999/xhtml",Ei="100%",wi="100%",Li="border:0;margin:0;",bi="margin:0",vi="allow-top-navigation-by-user-activation allow-popups",Si='The "iframe" tag is not supported by your browser.',Mi=["foreignobject"],Ai=["dominant-baseline"];function Ae(t){let e=bt(t);return Y(),Rt(e.config??{}),e}r(Ae,"processAndSetConfigs");async function Ti(t,e){$();try{let{code:a,config:i}=Ae(t);return{diagramType:(await Te(a)).type,config:i}}catch(a){if(e?.suppressErrors)return!1;throw a}}r(Ti,"parse");var ve=r((t,e,a=[])=>`
.${t} ${e} { ${a.join(" !important; ")} !important; }`,"cssImportantStyles"),Ci=r((t,e=new Map)=>{let a="";if(t.themeCSS!==void 0&&(a+=`
${t.themeCSS}`),t.fontFamily!==void 0&&(a+=`
:root { --mermaid-font-family: ${t.fontFamily}}`),t.altFontFamily!==void 0&&(a+=`
:root { --mermaid-alt-font-family: ${t.altFontFamily}}`),e instanceof Map){let m=t.htmlLabels??t.flowchart?.htmlLabels?["> *","span"]:["rect","polygon","ellipse","circle","path"];e.forEach(s=>{xt(s.styles)||m.forEach(c=>{a+=ve(s.id,c,s.styles)}),xt(s.textStyles)||(a+=ve(s.id,"tspan",(s?.textStyles||[]).map(c=>c.replace("color","fill"))))})}return a},"createCssStyles"),Ri=r((t,e,a,i)=>{let o=Ci(t,a),n=zt(e,o,t.themeVariables);return dt(ue(`${i}{${n}}`),De)},"createUserStyles"),ki=r((t="",e,a)=>{let i=t;return!a&&!e&&(i=i.replace(/marker-end="url\([\d+./:=?A-Za-z-]*?#/g,'marker-end="url(#')),i=Ht(i),i=i.replace(/<br>/g,"<br/>"),i},"cleanUpSvgCode"),ji=r((t="",e)=>{let a=e?.viewBox?.baseVal?.height?e.viewBox.baseVal.height+"px":wi,i=be(`<body style="${bi}">${t}</body>`);return`<iframe style="width:${Ei};height:${a};${Li}" src="data:text/html;charset=UTF-8;base64,${i}" sandbox="${vi}">
  ${Si}
</iframe>`},"putIntoIFrame"),Se=r((t,e,a,i,o)=>{let n=t.append("div");n.attr("id",a),i&&n.attr("style",i);let m=n.append("svg").attr("id",e).attr("width","100%").attr("xmlns",yi);return o&&m.attr("xmlns:xlink",o),m.append("g"),t},"appendDivSvgG");function Me(t,e){return t.append("iframe").attr("id",e).attr("style","width: 100%; height: 100%;").attr("sandbox","")}r(Me,"sandboxedIframe");var Oi=r((t,e,a,i)=>{t.getElementById(e)?.remove(),t.getElementById(a)?.remove(),t.getElementById(i)?.remove()},"removeExistingElements"),Pi=r(async function(t,e,a){$();let i=Ae(e);e=i.code;let o=G();g.debug(o),e.length>(o?.maxTextSize??gi)&&(e=li);let n="#"+t,m="i"+t,s="#"+m,c="d"+t,l="#"+c,y=r(()=>{let gt=k(x?s:l).node();gt&&"remove"in gt&&gt.remove()},"removeTempElements"),p=k("body"),x=o.securityLevel===ui,A=o.securityLevel===Di,b=o.fontFamily;if(a!==void 0){if(a&&(a.innerHTML=""),x){let M=Me(k(a),m);p=k(M.nodes()[0].contentDocument.body),p.node().style.margin=0}else p=k(a);Se(p,t,c,`font-family: ${b}`,xi)}else{if(Oi(document,t,c,m),x){let M=Me(k("body"),m);p=k(M.nodes()[0].contentDocument.body),p.node().style.margin=0}else p=k("body");Se(p,t,c)}let f,C;try{f=await B.fromText(e,{title:i.title})}catch(M){if(o.suppressErrorRendering)throw y(),M;f=await B.fromText("error"),C=M}let v=p.select(l).node(),u=f.type,S=v.firstChild,R=S.firstChild,T=f.renderer.getClasses?.(e,f),E=Ri(o,u,T,n),d=document.createElement("style");d.innerHTML=E,S.insertBefore(d,R);try{await f.renderer.draw(e,t,ht.version,f)}catch(M){throw o.suppressErrorRendering?y():Tr.draw(e,t,ht.version),M}let Pe=p.select(`${l} svg`),Fe=f.db.getAccTitle?.(),Ie=f.db.getAccDescription?.();Ii(u,Pe,Fe,Ie),p.select(`[id="${t}"]`).selectAll("foreignobject > *").attr("xmlns",hi);let _=p.select(l).node().innerHTML;if(g.debug("config.arrowMarkerAbsolute",o.arrowMarkerAbsolute),_=ki(_,x,jt(o.arrowMarkerAbsolute)),x){let M=p.select(l+" svg").node();_=ji(_,M)}else A||(_=kt.sanitize(_,{ADD_TAGS:Mi,ADD_ATTR:Ai,HTML_INTEGRATION_POINTS:{foreignobject:!0}}));if(Ee(),C)throw C;return y(),{diagramType:u,svg:_,bindFunctions:f.db.bindFunctions}},"render");function Fi(t={}){let e=St({},t);e?.fontFamily&&!e.themeVariables?.fontFamily&&(e.themeVariables||(e.themeVariables={}),e.themeVariables.fontFamily=e.fontFamily),At(e),e?.theme&&e.theme in tt?e.themeVariables=tt[e.theme].getThemeVariables(e.themeVariables):e&&(e.themeVariables=tt.default.getThemeVariables(e.themeVariables));let a=typeof e=="object"?Mt(e):Dt();lt(a.logLevel),$()}r(Fi,"initialize");var Te=r((t,e={})=>{let{code:a}=bt(t);return B.fromText(a,e)},"getDiagramFromText");function Ii(t,e,a,i){ye(e,t),xe(e,a,i,e.attr("id"))}r(Ii,"addA11yInfo");var I=Object.freeze({render:Pi,parse:Ti,getDiagramFromText:Te,initialize:Fi,getConfig:G,setConfig:Ct,getSiteConfig:Dt,updateSiteConfig:Tt,reset:r(()=>{Y()},"reset"),globalReset:r(()=>{Y(ut)},"globalReset"),defaultConfig:ut});lt(G().logLevel);Y(G());var _i=r((t,e,a)=>{g.warn(t),yt(t)?(a&&a(t.str,t.hash),e.push({...t,message:t.str,error:t})):(a&&a(t),t instanceof Error&&e.push({str:t.message,message:t.message,hash:t.name,error:t}))},"handleError"),Ce=r(async function(t={querySelector:".mermaid"}){try{await Gi(t)}catch(e){if(yt(e)&&g.error(e.str),O.parseError&&O.parseError(e),!t.suppressErrors)throw g.error("Use the suppressErrors option to suppress these errors"),e}},"run"),Gi=r(async function({postRenderCallback:t,querySelector:e,nodes:a}={querySelector:".mermaid"}){let i=I.getConfig();g.debug(`${t?"":"No "}Callback function found`);let o;if(a)o=a;else if(e)o=document.querySelectorAll(e);else throw new Error("Nodes and querySelector are both undefined");g.debug(`Found ${o.length} diagrams`),i?.startOnLoad!==void 0&&(g.debug("Start On Load: "+i?.startOnLoad),I.updateSiteConfig({startOnLoad:i?.startOnLoad}));let n=new V.InitIDGenerator(i.deterministicIds,i.deterministicIDSeed),m,s=[];for(let c of Array.from(o)){g.info("Rendering diagram: "+c.id);if(c.getAttribute("data-processed"))continue;c.setAttribute("data-processed","true");let l=`mermaid-${n.next()}`;m=c.innerHTML,m=Pt(V.entityDecode(m)).trim().replace(/<br\s*\/?>/gi,"<br/>");let y=V.detectInit(m);y&&g.debug("Detected early reinit: ",y);try{let{svg:p,bindFunctions:x}=await Oe(l,m,c);c.innerHTML=p,t&&await t(l),x&&x(c)}catch(p){_i(p,s,O.parseError)}}if(s.length>0)throw s[0]},"runThrowsErrors"),Re=r(function(t){I.initialize(t)},"initialize"),zi=r(async function(t,e,a){g.warn("mermaid.init is deprecated. Please use run instead."),t&&Re(t);let i={postRenderCallback:a,querySelector:".mermaid"};typeof e=="string"?i.querySelector=e:e&&(e instanceof HTMLElement?i.nodes=[e]:i.nodes=e),await Ce(i)},"init"),Vi=r(async(t,{lazyLoad:e=!0}={})=>{$(),W(...t),e===!1&&await Qr()},"registerExternalDiagrams"),ke=r(function(){if(O.startOnLoad){let{startOnLoad:t}=I.getConfig();t&&O.run().catch(e=>g.error("Mermaid failed to initialize",e))}},"contentLoaded");if(typeof document<"u"){window.addEventListener("load",ke,!1)}var $i=r(function(t){O.parseError=t},"setParseErrorHandler"),ft=[],vt=!1,je=r(async()=>{if(!vt){for(vt=!0;ft.length>0;){let t=ft.shift();if(t)try{await t()}catch(e){g.error("Error executing queue",e)}}vt=!1}},"executeQueue"),Ni=r(async(t,e)=>new Promise((a,i)=>{let o=r(()=>new Promise((n,m)=>{I.parse(t,e).then(s=>{n(s),a(s)},s=>{g.error("Error parsing",s),O.parseError?.(s),m(s),i(s)})}),"performCall");ft.push(o),je().catch(i)}),"parse"),Oe=r((t,e,a)=>new Promise((i,o)=>{let n=r(()=>new Promise((m,s)=>{I.render(t,e,a).then(c=>{m(c),i(c)},c=>{g.error("Error parsing",c),O.parseError?.(c),s(c),o(c)})}),"performCall");ft.push(n),je().catch(o)}),"render"),Hi=r(()=>Object.keys(X).map(t=>({id:t})),"getRegisteredDiagramsMetadata"),O={startOnLoad:!0,mermaidAPI:I,parse:Ni,render:Oe,init:zi,run:Ce,registerExternalDiagrams:Vi,registerLayoutLoaders:Bt,initialize:Re,parseError:void 0,contentLoaded:ke,setParseErrorHandler:$i,detectType:rt,registerIconPacks:Ot,getRegisteredDiagramsMetadata:Hi},Qs=O;export{Qs as default};
/*! Check if previously processed */
/*!
 * Wait for document loaded before starting the execution
 */
