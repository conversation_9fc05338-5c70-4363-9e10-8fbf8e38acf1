import{a as s}from"./chunk-RPR3SYFS.mjs";import"./chunk-P6UA7CIO.mjs";import"./chunk-4AZJR7FE.mjs";import"./chunk-XAVRVNBM.mjs";import"./chunk-JHXWDPGM.mjs";import"./chunk-V4WPH7A7.mjs";import{a as p}from"./chunk-YU6XO2NZ.mjs";import{a}from"./chunk-U7M5BGKE.mjs";import{N as n,c as o}from"./chunk-F632ZYSZ.mjs";import"./chunk-WSUO5DN6.mjs";import"./chunk-JCWWVGLQ.mjs";import"./chunk-L6MQJ2ZU.mjs";import"./chunk-5ZJXQJOJ.mjs";import"./chunk-YPUTD6PB.mjs";import"./chunk-6BY5RJGC.mjs";import{a as r}from"./chunk-GTKDMUJJ.mjs";var m={parse:r(async t=>{let e=await s("info",t);o.debug(e)},"parse")};var c={version:p.version+""},y=r(()=>c.version,"getVersion"),f={getVersion:y};var D=r((t,e,d)=>{o.debug(`rendering info diagram
`+t);let i=a(e);n(i,100,400,!0),i.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${d}`)},"draw"),g={draw:D};var L={parser:m,db:f,renderer:g};export{L as diagram};
