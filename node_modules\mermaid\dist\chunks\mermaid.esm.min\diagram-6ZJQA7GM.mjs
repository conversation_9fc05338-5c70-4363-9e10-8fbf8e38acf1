import{a as me}from"./chunk-4KE642ED.mjs";import{a as pe}from"./chunk-RPR3SYFS.mjs";import"./chunk-P6UA7CIO.mjs";import"./chunk-4AZJR7FE.mjs";import"./chunk-XAVRVNBM.mjs";import"./chunk-JHXWDPGM.mjs";import"./chunk-V4WPH7A7.mjs";import{a as de}from"./chunk-U7M5BGKE.mjs";import{a as ce}from"./chunk-EJZ3NKMF.mjs";import{c as ie,d as v}from"./chunk-Z2NOIGJN.mjs";import{l as I}from"./chunk-3R3PQ5PD.mjs";import"./chunk-TI4EEUUG.mjs";import{N as Q,Q as Z,R as ee,S as te,T as ae,U as re,V as le,W as se,c as M,ha as V,ja as L,ka as oe,l as K,la as ne,ma as _,t as H}from"./chunk-F632ZYSZ.mjs";import"./chunk-WSUO5DN6.mjs";import"./chunk-JCWWVGLQ.mjs";import"./chunk-L6MQJ2ZU.mjs";import"./chunk-5ZJXQJOJ.mjs";import"./chunk-YPUTD6PB.mjs";import"./chunk-6BY5RJGC.mjs";import{a as p}from"./chunk-GTKDMUJJ.mjs";var $=class{constructor(){this.nodes=[];this.levels=new Map;this.outerNodes=[];this.classes=new Map;this.setAccTitle=ee;this.getAccTitle=te;this.setDiagramTitle=le;this.getDiagramTitle=se;this.getAccDescription=re;this.setAccDescription=ae}static{p(this,"TreeMapDB")}getNodes(){return this.nodes}getConfig(){let a=K,n=H();return I({...a.treemap,...n.treemap??{}})}addNode(a,n){this.nodes.push(a),this.levels.set(a,n),n===0&&(this.outerNodes.push(a),this.root??=a)}getRoot(){return{name:"",children:this.outerNodes}}addClass(a,n){let o=this.classes.get(a)??{id:a,styles:[],textStyles:[]},c=n.replace(/\\,/g,"\xA7\xA7\xA7").replace(/,/g,";").replace(/§§§/g,",").split(";");c&&c.forEach(l=>{ie(l)&&(o?.textStyles?o.textStyles.push(l):o.textStyles=[l]),o?.styles?o.styles.push(l):o.styles=[l]}),this.classes.set(a,o)}getClasses(){return this.classes}getStylesForClass(a){return this.classes.get(a)?.styles??[]}clear(){Z(),this.nodes=[],this.levels=new Map,this.outerNodes=[],this.classes=new Map,this.root=void 0}};function fe(m){if(!m.length)return[];let a=[],n=[];return m.forEach(o=>{let c={name:o.name,children:o.type==="Leaf"?void 0:[]};for(c.classSelector=o?.classSelector,o?.cssCompiledStyles&&(c.cssCompiledStyles=[o.cssCompiledStyles]),o.type==="Leaf"&&o.value!==void 0&&(c.value=o.value);n.length>0&&n[n.length-1].level>=o.level;)n.pop();if(n.length===0)a.push(c);else{let l=n[n.length-1].node;l.children?l.children.push(c):l.children=[c]}o.type!=="Leaf"&&n.push({node:c,level:o.level})}),a}p(fe,"buildHierarchy");var De=p((m,a)=>{me(m,a);let n=[];for(let l of m.TreemapRows??[])l.$type==="ClassDefStatement"&&a.addClass(l.className??"",l.styleText??"");for(let l of m.TreemapRows??[]){let d=l.item;if(!d)continue;let u=l.indent?parseInt(l.indent):0,W=ve(d),r=d.classSelector?a.getStylesForClass(d.classSelector):[],P=r.length>0?r.join(";"):void 0,T={level:u,name:W,type:d.$type,value:d.value,classSelector:d.classSelector,cssCompiledStyles:P};n.push(T)}let o=fe(n),c=p((l,d)=>{for(let u of l)a.addNode(u,d),u.children&&u.children.length>0&&c(u.children,d+1)},"addNodesRecursively");c(o,0)},"populate"),ve=p(m=>m.name?String(m.name):"","getItemName"),O={parser:{yy:void 0},parse:p(async m=>{try{let n=await pe("treemap",m);M.debug("Treemap AST:",n);let o=O.parser?.yy;if(!(o instanceof $))throw new Error("parser.parser?.yy was not a TreemapDB. This is due to a bug within Mermaid, please report this issue at https://github.com/mermaid-js/mermaid/issues.");De(n,o)}catch(a){throw M.error("Error parsing treemap:",a),a}},"parse")};var we=10,F=10,E=25,Ne=p((m,a,n,o)=>{let c=o.db,l=c.getConfig(),d=l.padding??we,u=c.getDiagramTitle(),W=c.getRoot(),{themeVariables:r}=H();if(!W)return;let P=u?30:0,T=de(a),X=l.nodeWidth?l.nodeWidth*F:960,Y=l.nodeHeight?l.nodeHeight*F:500,G=X,q=Y+P;T.attr("viewBox",`0 0 ${G} ${q}`),Q(T,q,G,l.useMaxWidth);let C;try{let e=l.valueFormat||",";if(e==="$0,0")C=p(t=>"$"+L(",")(t),"valueFormat");else if(e.startsWith("$")&&e.includes(",")){let t=/\.\d+/.exec(e),s=t?t[0]:"";C=p(f=>"$"+L(","+s)(f),"valueFormat")}else if(e.startsWith("$")){let t=e.substring(1);C=p(s=>"$"+L(t||"")(s),"valueFormat")}else C=L(e)}catch(e){M.error("Error creating format function:",e),C=L(",")}let A=_().range(["transparent",r.cScale0,r.cScale1,r.cScale2,r.cScale3,r.cScale4,r.cScale5,r.cScale6,r.cScale7,r.cScale8,r.cScale9,r.cScale10,r.cScale11]),he=_().range(["transparent",r.cScalePeer0,r.cScalePeer1,r.cScalePeer2,r.cScalePeer3,r.cScalePeer4,r.cScalePeer5,r.cScalePeer6,r.cScalePeer7,r.cScalePeer8,r.cScalePeer9,r.cScalePeer10,r.cScalePeer11]),B=_().range([r.cScaleLabel0,r.cScaleLabel1,r.cScaleLabel2,r.cScaleLabel3,r.cScaleLabel4,r.cScaleLabel5,r.cScaleLabel6,r.cScaleLabel7,r.cScaleLabel8,r.cScaleLabel9,r.cScaleLabel10,r.cScaleLabel11]);u&&T.append("text").attr("x",G/2).attr("y",P/2).attr("class","treemapTitle").attr("text-anchor","middle").attr("dominant-baseline","middle").text(u);let U=T.append("g").attr("transform",`translate(0, ${P})`).attr("class","treemapContainer"),ye=oe(W).sum(e=>e.value??0).sort((e,t)=>(t.value??0)-(e.value??0)),J=ne().size([X,Y]).paddingTop(e=>e.children&&e.children.length>0?E+F:0).paddingInner(d).paddingLeft(e=>e.children&&e.children.length>0?F:0).paddingRight(e=>e.children&&e.children.length>0?F:0).paddingBottom(e=>e.children&&e.children.length>0?F:0).round(!0)(ye),Se=J.descendants().filter(e=>e.children&&e.children.length>0),k=U.selectAll(".treemapSection").data(Se).enter().append("g").attr("class","treemapSection").attr("transform",e=>`translate(${e.x0},${e.y0})`);k.append("rect").attr("width",e=>e.x1-e.x0).attr("height",E).attr("class","treemapSectionHeader").attr("fill","none").attr("fill-opacity",.6).attr("stroke-width",.6).attr("style",e=>e.depth===0?"display: none;":""),k.append("clipPath").attr("id",(e,t)=>`clip-section-${a}-${t}`).append("rect").attr("width",e=>Math.max(0,e.x1-e.x0-12)).attr("height",E),k.append("rect").attr("width",e=>e.x1-e.x0).attr("height",e=>e.y1-e.y0).attr("class",(e,t)=>`treemapSection section${t}`).attr("fill",e=>A(e.data.name)).attr("fill-opacity",.6).attr("stroke",e=>he(e.data.name)).attr("stroke-width",2).attr("stroke-opacity",.4).attr("style",e=>{if(e.depth===0)return"display: none;";let t=v({cssCompiledStyles:e.data.cssCompiledStyles});return t.nodeStyles+";"+t.borderStyles.join(";")}),k.append("text").attr("class","treemapSectionLabel").attr("x",6).attr("y",E/2).attr("dominant-baseline","middle").text(e=>e.depth===0?"":e.data.name).attr("font-weight","bold").attr("style",e=>{if(e.depth===0)return"display: none;";let t="dominant-baseline: middle; font-size: 12px; fill:"+B(e.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",s=v({cssCompiledStyles:e.data.cssCompiledStyles});return t+s.labelStyles.replace("color:","fill:")}).each(function(e){if(e.depth===0)return;let t=V(this),s=e.data.name;t.text(s);let f=e.x1-e.x0,S=6,b;l.showValues!==!1&&e.value?b=f-10-30-10-S:b=f-S-6;let h=Math.max(15,b),i=t.node();if(i.getComputedTextLength()>h){let g="...",y=s;for(;y.length>0;){if(y=s.substring(0,y.length-1),y.length===0){t.text(g),i.getComputedTextLength()>h&&t.text("");break}if(t.text(y+g),i.getComputedTextLength()<=h)break}}}),l.showValues!==!1&&k.append("text").attr("class","treemapSectionValue").attr("x",e=>e.x1-e.x0-10).attr("y",E/2).attr("text-anchor","end").attr("dominant-baseline","middle").text(e=>e.value?C(e.value):"").attr("font-style","italic").attr("style",e=>{if(e.depth===0)return"display: none;";let t="text-anchor: end; dominant-baseline: middle; font-size: 10px; fill:"+B(e.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",s=v({cssCompiledStyles:e.data.cssCompiledStyles});return t+s.labelStyles.replace("color:","fill:")});let be=J.leaves(),R=U.selectAll(".treemapLeafGroup").data(be).enter().append("g").attr("class",(e,t)=>`treemapNode treemapLeafGroup leaf${t}${e.data.classSelector?` ${e.data.classSelector}`:""}x`).attr("transform",e=>`translate(${e.x0},${e.y0})`);R.append("rect").attr("width",e=>e.x1-e.x0).attr("height",e=>e.y1-e.y0).attr("class","treemapLeaf").attr("fill",e=>e.parent?A(e.parent.data.name):A(e.data.name)).attr("style",e=>v({cssCompiledStyles:e.data.cssCompiledStyles}).nodeStyles).attr("fill-opacity",.3).attr("stroke",e=>e.parent?A(e.parent.data.name):A(e.data.name)).attr("stroke-width",3),R.append("clipPath").attr("id",(e,t)=>`clip-${a}-${t}`).append("rect").attr("width",e=>Math.max(0,e.x1-e.x0-4)).attr("height",e=>Math.max(0,e.y1-e.y0-4)),R.append("text").attr("class","treemapLabel").attr("x",e=>(e.x1-e.x0)/2).attr("y",e=>(e.y1-e.y0)/2).attr("style",e=>{let t="text-anchor: middle; dominant-baseline: middle; font-size: 38px;fill:"+B(e.data.name)+";",s=v({cssCompiledStyles:e.data.cssCompiledStyles});return t+s.labelStyles.replace("color:","fill:")}).attr("clip-path",(e,t)=>`url(#clip-${a}-${t})`).text(e=>e.data.name).each(function(e){let t=V(this),s=e.x1-e.x0,f=e.y1-e.y0,S=t.node(),b=4,w=s-2*b,h=f-2*b;if(w<10||h<10){t.style("display","none");return}let i=parseInt(t.style("font-size"),10),D=8,g=28,y=.6,x=6,z=2;for(;S.getComputedTextLength()>w&&i>D;)i--,t.style("font-size",`${i}px`);let N=Math.max(x,Math.min(g,Math.round(i*y))),j=i+z+N;for(;j>h&&i>D&&(i--,N=Math.max(x,Math.min(g,Math.round(i*y))),!(N<x&&i===D));)t.style("font-size",`${i}px`),j=i+z+N,N<=x&&j>h;t.style("font-size",`${i}px`),(S.getComputedTextLength()>w||i<D||h<i)&&t.style("display","none")}),l.showValues!==!1&&R.append("text").attr("class","treemapValue").attr("x",t=>(t.x1-t.x0)/2).attr("y",function(t){return(t.y1-t.y0)/2}).attr("style",t=>{let s="text-anchor: middle; dominant-baseline: hanging; font-size: 28px;fill:"+B(t.data.name)+";",f=v({cssCompiledStyles:t.data.cssCompiledStyles});return s+f.labelStyles.replace("color:","fill:")}).attr("clip-path",(t,s)=>`url(#clip-${a}-${s})`).text(t=>t.value?C(t.value):"").each(function(t){let s=V(this),f=this.parentNode;if(!f){s.style("display","none");return}let S=V(f).select(".treemapLabel");if(S.empty()||S.style("display")==="none"){s.style("display","none");return}let b=parseFloat(S.style("font-size")),w=28,h=.6,i=6,D=2,g=Math.max(i,Math.min(w,Math.round(b*h)));s.style("font-size",`${g}px`);let x=(t.y1-t.y0)/2+b/2+D;s.attr("y",x);let z=t.x1-t.x0,Te=t.y1-t.y0-4,Ce=z-2*4;s.node().getComputedTextLength()>Ce||x+g>Te||g<i?s.style("display","none"):s.style("display",null)});let xe=l.diagramPadding??8;ce(T,xe,"flowchart",l?.useMaxWidth||!1)},"draw"),Le=p(function(m,a){return a.db.getClasses()},"getClasses"),ge={draw:Ne,getClasses:Le};var $e={sectionStrokeColor:"black",sectionStrokeWidth:"1",sectionFillColor:"#efefef",leafStrokeColor:"black",leafStrokeWidth:"1",leafFillColor:"#efefef",labelColor:"black",labelFontSize:"12px",valueFontSize:"10px",valueColor:"black",titleColor:"black",titleFontSize:"14px"},Fe=p(({treemap:m}={})=>{let a=I($e,m);return`
  .treemapNode.section {
    stroke: ${a.sectionStrokeColor};
    stroke-width: ${a.sectionStrokeWidth};
    fill: ${a.sectionFillColor};
  }
  .treemapNode.leaf {
    stroke: ${a.leafStrokeColor};
    stroke-width: ${a.leafStrokeWidth};
    fill: ${a.leafFillColor};
  }
  .treemapLabel {
    fill: ${a.labelColor};
    font-size: ${a.labelFontSize};
  }
  .treemapValue {
    fill: ${a.valueColor};
    font-size: ${a.valueFontSize};
  }
  .treemapTitle {
    fill: ${a.titleColor};
    font-size: ${a.titleFontSize};
  }
  `},"getStyles"),ue=Fe;var pt={parser:O,get db(){return new $},renderer:ge,styles:ue};export{pt as diagram};
