import{A as oe,B as ho,C as et,D as be,G as zu,I as Ve,L as ue,M as Yt,O as Is,R as Qn,S as qu,a as de,b as Et,c as ke,d as he,e as te,g as At,i as Jn,j as Xt,k as vt,l as pe,m as Ft,n as $,o as Pe,p as xe,q as kt,r as $e,s as E,t as Ie,w as Hu,x as I,y as Ne,z as re}from"./chunk-5ZJXQJOJ.mjs";import{d as M}from"./chunk-YPUTD6PB.mjs";import{M as fo,d as ju,e as ot,z as ge}from"./chunk-6BY5RJGC.mjs";import{a,b as $s,c as qt,d as B,e as Vu}from"./chunk-GTKDMUJJ.mjs";var au=$s(su=>{"use strict";Object.defineProperty(su,"__esModule",{value:!0});var nu;function iu(){if(nu===void 0)throw new Error("No runtime abstraction layer installed");return nu}a(iu,"RAL");(function(r){function e(t){if(t===void 0)throw new Error("No runtime abstraction layer provided");nu=t}a(e,"install"),r.install=e})(iu||(iu={}));su.default=iu});var Vd=$s(_e=>{"use strict";Object.defineProperty(_e,"__esModule",{value:!0});_e.stringArray=_e.array=_e.func=_e.error=_e.number=_e.string=_e.boolean=void 0;function tg(r){return r===!0||r===!1}a(tg,"boolean");_e.boolean=tg;function Wd(r){return typeof r=="string"||r instanceof String}a(Wd,"string");_e.string=Wd;function rg(r){return typeof r=="number"||r instanceof Number}a(rg,"number");_e.number=rg;function ng(r){return r instanceof Error}a(ng,"error");_e.error=ng;function ig(r){return typeof r=="function"}a(ig,"func");_e.func=ig;function Kd(r){return Array.isArray(r)}a(Kd,"array");_e.array=Kd;function sg(r){return Kd(r)&&r.every(e=>Wd(e))}a(sg,"stringArray");_e.stringArray=sg});var lu=$s(Kn=>{"use strict";Object.defineProperty(Kn,"__esModule",{value:!0});Kn.Emitter=Kn.Event=void 0;var ag=au(),jd;(function(r){let e={dispose(){}};r.None=function(){return e}})(jd||(Kn.Event=jd={}));var ou=class{static{a(this,"CallbackList")}add(e,t=null,n){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(t),Array.isArray(n)&&n.push({dispose:a(()=>this.remove(e,t),"dispose")})}remove(e,t=null){if(!this._callbacks)return;let n=!1;for(let i=0,s=this._callbacks.length;i<s;i++)if(this._callbacks[i]===e)if(this._contexts[i]===t){this._callbacks.splice(i,1),this._contexts.splice(i,1);return}else n=!0;if(n)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];let t=[],n=this._callbacks.slice(0),i=this._contexts.slice(0);for(let s=0,o=n.length;s<o;s++)try{t.push(n[s].apply(i[s],e))}catch(l){(0,ag.default)().console.error(l)}return t}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}},Pa=class r{static{a(this,"Emitter")}constructor(e){this._options=e}get event(){return this._event||(this._event=(e,t,n)=>{this._callbacks||(this._callbacks=new ou),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,t);let i={dispose:a(()=>{this._callbacks&&(this._callbacks.remove(e,t),i.dispose=r._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))},"dispose")};return Array.isArray(n)&&n.push(i),i}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}};Kn.Emitter=Pa;Pa._noop=function(){}});var Hd=$s(Vn=>{"use strict";Object.defineProperty(Vn,"__esModule",{value:!0});Vn.CancellationTokenSource=Vn.CancellationToken=void 0;var og=au(),lg=Vd(),uu=lu(),ba;(function(r){r.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:uu.Event.None}),r.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:uu.Event.None});function e(t){let n=t;return n&&(n===r.None||n===r.Cancelled||lg.boolean(n.isCancellationRequested)&&!!n.onCancellationRequested)}a(e,"is"),r.is=e})(ba||(Vn.CancellationToken=ba={}));var ug=Object.freeze(function(r,e){let t=(0,og.default)().timer.setTimeout(r.bind(e),0);return{dispose(){t.dispose()}}}),Ma=class{static{a(this,"MutableToken")}constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?ug:(this._emitter||(this._emitter=new uu.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}},cu=class{static{a(this,"CancellationTokenSource")}get token(){return this._token||(this._token=new Ma),this._token}cancel(){this._token?this._token.cancel():this._token=ba.Cancelled}dispose(){this._token?this._token instanceof Ma&&this._token.dispose():this._token=ba.None}};Vn.CancellationTokenSource=cu});var fe={};qt(fe,{AbstractAstReflection:()=>Jt,AbstractCstNode:()=>Wi,AbstractLangiumParser:()=>Ki,AbstractParserErrorMessageProvider:()=>La,AbstractThreadedAsyncParser:()=>Au,AstUtils:()=>Fs,BiMap:()=>Rr,Cancellation:()=>S,CompositeCstNodeImpl:()=>gr,ContextCache:()=>xr,CstNodeBuilder:()=>Bi,CstUtils:()=>Ss,DEFAULT_TOKENIZE_OPTIONS:()=>Wa,DONE_RESULT:()=>Ce,DatatypeSymbol:()=>_a,DefaultAstNodeDescriptionProvider:()=>as,DefaultAstNodeLocator:()=>ls,DefaultAsyncParser:()=>Ts,DefaultCommentProvider:()=>ys,DefaultConfigurationProvider:()=>us,DefaultDocumentBuilder:()=>cs,DefaultDocumentValidator:()=>ss,DefaultHydrator:()=>xs,DefaultIndexManager:()=>fs,DefaultJsonSerializer:()=>rs,DefaultLangiumDocumentFactory:()=>zi,DefaultLangiumDocuments:()=>qi,DefaultLexer:()=>Ar,DefaultLexerErrorMessageProvider:()=>hs,DefaultLinker:()=>Xi,DefaultNameProvider:()=>Yi,DefaultReferenceDescriptionProvider:()=>os,DefaultReferences:()=>Ji,DefaultScopeComputation:()=>Qi,DefaultScopeProvider:()=>ts,DefaultServiceRegistry:()=>ns,DefaultTokenBuilder:()=>bt,DefaultValueConverter:()=>Tr,DefaultWorkspaceLock:()=>Rs,DefaultWorkspaceManager:()=>ds,Deferred:()=>Ge,Disposable:()=>jt,DisposableCache:()=>qn,DocumentCache:()=>Ua,DocumentState:()=>Y,DocumentValidator:()=>Qe,EMPTY_SCOPE:()=>fg,EMPTY_STREAM:()=>ei,EmptyFileSystem:()=>Su,EmptyFileSystemProvider:()=>Xa,ErrorWithLocation:()=>tr,GrammarAST:()=>ui,GrammarUtils:()=>Ks,IndentationAwareLexer:()=>Cu,IndentationAwareTokenBuilder:()=>qa,JSDocDocumentationProvider:()=>gs,LangiumCompletionParser:()=>ji,LangiumParser:()=>Vi,LangiumParserErrorMessageProvider:()=>Wn,LeafCstNodeImpl:()=>mr,LexingMode:()=>vr,MapScope:()=>Zi,Module:()=>Iu,MultiMap:()=>st,OperationCancelled:()=>mt,ParserWorker:()=>vu,Reduction:()=>Ir,RegExpUtils:()=>Bs,RootCstNodeImpl:()=>Bn,SimpleCache:()=>es,StreamImpl:()=>je,StreamScope:()=>zn,TextDocument:()=>jn,TreeStreamImpl:()=>lt,URI:()=>Ue,UriUtils:()=>Be,ValidationCategory:()=>Yn,ValidationRegistry:()=>is,ValueConverter:()=>pt,WorkspaceCache:()=>Xn,assertUnreachable:()=>ct,createCompletionParser:()=>tu,createDefaultCoreModule:()=>ku,createDefaultSharedCoreModule:()=>$u,createGrammarConfig:()=>il,createLangiumParser:()=>ru,createParser:()=>Hi,delayNextTick:()=>fu,diagnosticData:()=>Er,eagerLoad:()=>dh,getDiagnosticRange:()=>th,indentationBuilderDefaultOptions:()=>Nu,inject:()=>za,interruptAndCheck:()=>ce,isAstNode:()=>le,isAstNodeDescription:()=>po,isAstNodeWithComment:()=>pu,isCompositeCstNode:()=>tt,isIMultiModeLexerDefinition:()=>gu,isJSDoc:()=>xu,isLeafCstNode:()=>Gt,isLinkingError:()=>Qt,isNamed:()=>Zd,isOperationCancelled:()=>gt,isReference:()=>Ee,isRootCstNode:()=>Zn,isTokenTypeArray:()=>Ka,isTokenTypeDictionary:()=>mu,loadGrammarFromJson:()=>yt,parseJSDoc:()=>Ru,prepareLangiumParser:()=>Bd,setInterruptionPeriod:()=>qd,startCancelableOperation:()=>Fa,stream:()=>j,toDiagnosticData:()=>rh,toDiagnosticSeverity:()=>Ba});var Ss={};qt(Ss,{DefaultNameRegexp:()=>Cs,RangeComparison:()=>ut,compareRange:()=>Yu,findCommentNode:()=>To,findDeclarationNodeAtOffset:()=>Ih,findLeafNodeAtOffset:()=>Ro,findLeafNodeBeforeOffset:()=>Ju,flattenCst:()=>$h,getInteriorNodes:()=>Sh,getNextNode:()=>Nh,getPreviousNode:()=>Zu,getStartlineNode:()=>Ch,inRange:()=>yo,isChildNode:()=>go,isCommentNode:()=>mo,streamCst:()=>Zt,toDocumentSegment:()=>er,tokenToRange:()=>Nr});function le(r){return typeof r=="object"&&r!==null&&typeof r.$type=="string"}a(le,"isAstNode");function Ee(r){return typeof r=="object"&&r!==null&&typeof r.$refText=="string"}a(Ee,"isReference");function po(r){return typeof r=="object"&&r!==null&&typeof r.name=="string"&&typeof r.type=="string"&&typeof r.path=="string"}a(po,"isAstNodeDescription");function Qt(r){return typeof r=="object"&&r!==null&&le(r.container)&&Ee(r.reference)&&typeof r.message=="string"}a(Qt,"isLinkingError");var Jt=class{static{a(this,"AbstractAstReflection")}constructor(){this.subtypes={},this.allSubtypes={}}isInstance(e,t){return le(e)&&this.isSubtype(e.$type,t)}isSubtype(e,t){if(e===t)return!0;let n=this.subtypes[e];n||(n=this.subtypes[e]={});let i=n[t];if(i!==void 0)return i;{let s=this.computeIsSubtype(e,t);return n[t]=s,s}}getAllSubTypes(e){let t=this.allSubtypes[e];if(t)return t;{let n=this.getAllTypes(),i=[];for(let s of n)this.isSubtype(s,e)&&i.push(s);return this.allSubtypes[e]=i,i}}};function tt(r){return typeof r=="object"&&r!==null&&Array.isArray(r.content)}a(tt,"isCompositeCstNode");function Gt(r){return typeof r=="object"&&r!==null&&typeof r.tokenType=="object"}a(Gt,"isLeafCstNode");function Zn(r){return tt(r)&&typeof r.fullText=="string"}a(Zn,"isRootCstNode");var je=class r{static{a(this,"StreamImpl")}constructor(e,t){this.startFn=e,this.nextFn=t}iterator(){let e={state:this.startFn(),next:a(()=>this.nextFn(e.state),"next"),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){return!!this.iterator().next().done}count(){let e=this.iterator(),t=0,n=e.next();for(;!n.done;)t++,n=e.next();return t}toArray(){let e=[],t=this.iterator(),n;do n=t.next(),n.value!==void 0&&e.push(n.value);while(!n.done);return e}toSet(){return new Set(this)}toMap(e,t){let n=this.map(i=>[e?e(i):i,t?t(i):i]);return new Map(n)}toString(){return this.join()}concat(e){return new r(()=>({first:this.startFn(),firstDone:!1,iterator:e[Symbol.iterator]()}),t=>{let n;if(!t.firstDone){do if(n=this.nextFn(t.first),!n.done)return n;while(!n.done);t.firstDone=!0}do if(n=t.iterator.next(),!n.done)return n;while(!n.done);return Ce})}join(e=","){let t=this.iterator(),n="",i,s=!1;do i=t.next(),i.done||(s&&(n+=e),n+=kh(i.value)),s=!0;while(!i.done);return n}indexOf(e,t=0){let n=this.iterator(),i=0,s=n.next();for(;!s.done;){if(i>=t&&s.value===e)return i;s=n.next(),i++}return-1}every(e){let t=this.iterator(),n=t.next();for(;!n.done;){if(!e(n.value))return!1;n=t.next()}return!0}some(e){let t=this.iterator(),n=t.next();for(;!n.done;){if(e(n.value))return!0;n=t.next()}return!1}forEach(e){let t=this.iterator(),n=0,i=t.next();for(;!i.done;)e(i.value,n),i=t.next(),n++}map(e){return new r(this.startFn,t=>{let{done:n,value:i}=this.nextFn(t);return n?Ce:{done:!1,value:e(i)}})}filter(e){return new r(this.startFn,t=>{let n;do if(n=this.nextFn(t),!n.done&&e(n.value))return n;while(!n.done);return Ce})}nonNullable(){return this.filter(e=>e!=null)}reduce(e,t){let n=this.iterator(),i=t,s=n.next();for(;!s.done;)i===void 0?i=s.value:i=e(i,s.value),s=n.next();return i}reduceRight(e,t){return this.recursiveReduce(this.iterator(),e,t)}recursiveReduce(e,t,n){let i=e.next();if(i.done)return n;let s=this.recursiveReduce(e,t,n);return s===void 0?i.value:t(s,i.value)}find(e){let t=this.iterator(),n=t.next();for(;!n.done;){if(e(n.value))return n.value;n=t.next()}}findIndex(e){let t=this.iterator(),n=0,i=t.next();for(;!i.done;){if(e(i.value))return n;i=t.next(),n++}return-1}includes(e){let t=this.iterator(),n=t.next();for(;!n.done;){if(n.value===e)return!0;n=t.next()}return!1}flatMap(e){return new r(()=>({this:this.startFn()}),t=>{do{if(t.iterator){let s=t.iterator.next();if(s.done)t.iterator=void 0;else return s}let{done:n,value:i}=this.nextFn(t.this);if(!n){let s=e(i);if(Ns(s))t.iterator=s[Symbol.iterator]();else return{done:!1,value:s}}}while(t.iterator);return Ce})}flat(e){if(e===void 0&&(e=1),e<=0)return this;let t=e>1?this.flat(e-1):this;return new r(()=>({this:t.startFn()}),n=>{do{if(n.iterator){let o=n.iterator.next();if(o.done)n.iterator=void 0;else return o}let{done:i,value:s}=t.nextFn(n.this);if(!i)if(Ns(s))n.iterator=s[Symbol.iterator]();else return{done:!1,value:s}}while(n.iterator);return Ce})}head(){let t=this.iterator().next();if(!t.done)return t.value}tail(e=1){return new r(()=>{let t=this.startFn();for(let n=0;n<e;n++)if(this.nextFn(t).done)return t;return t},this.nextFn)}limit(e){return new r(()=>({size:0,state:this.startFn()}),t=>(t.size++,t.size>e?Ce:this.nextFn(t.state)))}distinct(e){return new r(()=>({set:new Set,internalState:this.startFn()}),t=>{let n;do if(n=this.nextFn(t.internalState),!n.done){let i=e?e(n.value):n.value;if(!t.set.has(i))return t.set.add(i),n}while(!n.done);return Ce})}exclude(e,t){let n=new Set;for(let i of e){let s=t?t(i):i;n.add(s)}return this.filter(i=>{let s=t?t(i):i;return!n.has(s)})}};function kh(r){return typeof r=="string"?r:typeof r>"u"?"undefined":typeof r.toString=="function"?r.toString():Object.prototype.toString.call(r)}a(kh,"toString");function Ns(r){return!!r&&typeof r[Symbol.iterator]=="function"}a(Ns,"isIterable");var ei=new je(()=>{},()=>Ce),Ce=Object.freeze({done:!0,value:void 0});function j(...r){if(r.length===1){let e=r[0];if(e instanceof je)return e;if(Ns(e))return new je(()=>e[Symbol.iterator](),t=>t.next());if(typeof e.length=="number")return new je(()=>({index:0}),t=>t.index<e.length?{done:!1,value:e[t.index++]}:Ce)}return r.length>1?new je(()=>({collIndex:0,arrIndex:0}),e=>{do{if(e.iterator){let t=e.iterator.next();if(!t.done)return t;e.iterator=void 0}if(e.array){if(e.arrIndex<e.array.length)return{done:!1,value:e.array[e.arrIndex++]};e.array=void 0,e.arrIndex=0}if(e.collIndex<r.length){let t=r[e.collIndex++];Ns(t)?e.iterator=t[Symbol.iterator]():t&&typeof t.length=="number"&&(e.array=t)}}while(e.iterator||e.array||e.collIndex<r.length);return Ce}):ei}a(j,"stream");var lt=class extends je{static{a(this,"TreeStreamImpl")}constructor(e,t,n){super(()=>({iterators:n?.includeRoot?[[e][Symbol.iterator]()]:[t(e)[Symbol.iterator]()],pruned:!1}),i=>{for(i.pruned&&(i.iterators.pop(),i.pruned=!1);i.iterators.length>0;){let o=i.iterators[i.iterators.length-1].next();if(o.done)i.iterators.pop();else return i.iterators.push(t(o.value)[Symbol.iterator]()),o}return Ce})}iterator(){let e={state:this.startFn(),next:a(()=>this.nextFn(e.state),"next"),prune:a(()=>{e.state.pruned=!0},"prune"),[Symbol.iterator]:()=>e};return e}},Ir;(function(r){function e(s){return s.reduce((o,l)=>o+l,0)}a(e,"sum"),r.sum=e;function t(s){return s.reduce((o,l)=>o*l,0)}a(t,"product"),r.product=t;function n(s){return s.reduce((o,l)=>Math.min(o,l))}a(n,"min"),r.min=n;function i(s){return s.reduce((o,l)=>Math.max(o,l))}a(i,"max"),r.max=i})(Ir||(Ir={}));function Zt(r){return new lt(r,e=>tt(e)?e.content:[],{includeRoot:!0})}a(Zt,"streamCst");function $h(r){return Zt(r).filter(Gt)}a($h,"flattenCst");function go(r,e){for(;r.container;)if(r=r.container,r===e)return!0;return!1}a(go,"isChildNode");function Nr(r){return{start:{character:r.startColumn-1,line:r.startLine-1},end:{character:r.endColumn,line:r.endLine-1}}}a(Nr,"tokenToRange");function er(r){if(!r)return;let{offset:e,end:t,range:n}=r;return{range:n,offset:e,end:t,length:t-e}}a(er,"toDocumentSegment");var ut;(function(r){r[r.Before=0]="Before",r[r.After=1]="After",r[r.OverlapFront=2]="OverlapFront",r[r.OverlapBack=3]="OverlapBack",r[r.Inside=4]="Inside",r[r.Outside=5]="Outside"})(ut||(ut={}));function Yu(r,e){if(r.end.line<e.start.line||r.end.line===e.start.line&&r.end.character<=e.start.character)return ut.Before;if(r.start.line>e.end.line||r.start.line===e.end.line&&r.start.character>=e.end.character)return ut.After;let t=r.start.line>e.start.line||r.start.line===e.start.line&&r.start.character>=e.start.character,n=r.end.line<e.end.line||r.end.line===e.end.line&&r.end.character<=e.end.character;return t&&n?ut.Inside:t?ut.OverlapBack:n?ut.OverlapFront:ut.Outside}a(Yu,"compareRange");function yo(r,e){return Yu(r,e)>ut.After}a(yo,"inRange");var Cs=/^[\w\p{L}]$/u;function Ih(r,e,t=Cs){if(r){if(e>0){let n=e-r.offset,i=r.text.charAt(n);t.test(i)||e--}return Ro(r,e)}}a(Ih,"findDeclarationNodeAtOffset");function To(r,e){if(r){let t=Zu(r,!0);if(t&&mo(t,e))return t;if(Zn(r)){let n=r.content.findIndex(i=>!i.hidden);for(let i=n-1;i>=0;i--){let s=r.content[i];if(mo(s,e))return s}}}}a(To,"findCommentNode");function mo(r,e){return Gt(r)&&e.includes(r.tokenType.name)}a(mo,"isCommentNode");function Ro(r,e){if(Gt(r))return r;if(tt(r)){let t=Qu(r,e,!1);if(t)return Ro(t,e)}}a(Ro,"findLeafNodeAtOffset");function Ju(r,e){if(Gt(r))return r;if(tt(r)){let t=Qu(r,e,!0);if(t)return Ju(t,e)}}a(Ju,"findLeafNodeBeforeOffset");function Qu(r,e,t){let n=0,i=r.content.length-1,s;for(;n<=i;){let o=Math.floor((n+i)/2),l=r.content[o];if(l.offset<=e&&l.end>e)return l;l.end<=e?(s=t?l:void 0,n=o+1):i=o-1}return s}a(Qu,"binarySearch");function Zu(r,e=!0){for(;r.container;){let t=r.container,n=t.content.indexOf(r);for(;n>0;){n--;let i=t.content[n];if(e||!i.hidden)return i}r=t}}a(Zu,"getPreviousNode");function Nh(r,e=!0){for(;r.container;){let t=r.container,n=t.content.indexOf(r),i=t.content.length-1;for(;n<i;){n++;let s=t.content[n];if(e||!s.hidden)return s}r=t}}a(Nh,"getNextNode");function Ch(r){if(r.range.start.character===0)return r;let e=r.range.start.line,t=r,n;for(;r.container;){let i=r.container,s=n??i.content.indexOf(r);if(s===0?(r=i,n=void 0):(n=s-1,r=i.content[n]),r.range.start.line!==e)break;t=r}return t}a(Ch,"getStartlineNode");function Sh(r,e){let t=wh(r,e);return t?t.parent.content.slice(t.a+1,t.b):[]}a(Sh,"getInteriorNodes");function wh(r,e){let t=Xu(r),n=Xu(e),i;for(let s=0;s<t.length&&s<n.length;s++){let o=t[s],l=n[s];if(o.parent===l.parent)i={parent:o.parent,a:o.index,b:l.index};else break}return i}a(wh,"getCommonParent");function Xu(r){let e=[];for(;r.container;){let t=r.container,n=t.content.indexOf(r);e.push({parent:t,index:n}),r=t}return e.reverse()}a(Xu,"getParentChain");var Ks={};qt(Ks,{findAssignment:()=>tl,findNameAssignment:()=>Ws,findNodeForKeyword:()=>Zo,findNodeForProperty:()=>gi,findNodesForKeyword:()=>Jh,findNodesForKeywordInternal:()=>el,findNodesForProperty:()=>Jo,getActionAtElement:()=>uc,getActionType:()=>fc,getAllReachableRules:()=>mi,getCrossReferenceTerminal:()=>Xo,getEntryRule:()=>sc,getExplicitRuleType:()=>yn,getHiddenRules:()=>ac,getRuleType:()=>rl,getRuleTypeName:()=>rp,getTypeName:()=>Ti,isArrayCardinality:()=>Zh,isArrayOperator:()=>ep,isCommentTerminal:()=>Yo,isDataType:()=>tp,isDataTypeRule:()=>yi,isOptionalCardinality:()=>Qh,terminalRegex:()=>Tn});var tr=class extends Error{static{a(this,"ErrorWithLocation")}constructor(e,t){super(e?`${t} at ${e.range.start.line}:${e.range.start.character}`:t)}};function ct(r){throw new Error("Error! The input value was not handled.")}a(ct,"assertUnreachable");var ui={};qt(ui,{AbstractElement:()=>wr,AbstractRule:()=>Cr,AbstractType:()=>Sr,Action:()=>Xr,Alternatives:()=>Yr,ArrayLiteral:()=>_r,ArrayType:()=>Lr,Assignment:()=>Jr,BooleanLiteral:()=>Or,CharacterRange:()=>Qr,Condition:()=>ti,Conjunction:()=>Pr,CrossReference:()=>Zr,Disjunction:()=>br,EndOfFile:()=>en,Grammar:()=>Mr,GrammarImport:()=>ni,Group:()=>tn,InferredType:()=>Dr,Interface:()=>Fr,Keyword:()=>rn,LangiumGrammarAstReflection:()=>hn,LangiumGrammarTerminals:()=>_h,NamedArgument:()=>ii,NegatedToken:()=>nn,Negation:()=>Gr,NumberLiteral:()=>Ur,Parameter:()=>Br,ParameterReference:()=>Wr,ParserRule:()=>Kr,ReferenceType:()=>Vr,RegexToken:()=>sn,ReturnType:()=>si,RuleCall:()=>an,SimpleType:()=>jr,StringLiteral:()=>Hr,TerminalAlternatives:()=>on,TerminalGroup:()=>ln,TerminalRule:()=>rr,TerminalRuleCall:()=>un,Type:()=>zr,TypeAttribute:()=>ai,TypeDefinition:()=>ws,UnionType:()=>qr,UnorderedGroup:()=>cn,UntilToken:()=>fn,ValueLiteral:()=>ri,Wildcard:()=>dn,isAbstractElement:()=>oi,isAbstractRule:()=>Lh,isAbstractType:()=>Oh,isAction:()=>$t,isAlternatives:()=>Ps,isArrayLiteral:()=>Fh,isArrayType:()=>xo,isAssignment:()=>rt,isBooleanLiteral:()=>Eo,isCharacterRange:()=>So,isCondition:()=>Ph,isConjunction:()=>Ao,isCrossReference:()=>nr,isDisjunction:()=>vo,isEndOfFile:()=>wo,isFeatureName:()=>bh,isGrammar:()=>Gh,isGrammarImport:()=>Uh,isGroup:()=>Ut,isInferredType:()=>_s,isInterface:()=>Ls,isKeyword:()=>Ye,isNamedArgument:()=>Bh,isNegatedToken:()=>_o,isNegation:()=>ko,isNumberLiteral:()=>Wh,isParameter:()=>Kh,isParameterReference:()=>$o,isParserRule:()=>Se,isPrimitiveType:()=>ec,isReferenceType:()=>Io,isRegexToken:()=>Lo,isReturnType:()=>No,isRuleCall:()=>nt,isSimpleType:()=>Os,isStringLiteral:()=>Vh,isTerminalAlternatives:()=>Oo,isTerminalGroup:()=>Po,isTerminalRule:()=>He,isTerminalRuleCall:()=>bs,isType:()=>li,isTypeAttribute:()=>jh,isTypeDefinition:()=>Mh,isUnionType:()=>Co,isUnorderedGroup:()=>Ms,isUntilToken:()=>bo,isValueLiteral:()=>Dh,isWildcard:()=>Mo,reflection:()=>_});var _h={ID:/\^?[_a-zA-Z][\w_]*/,STRING:/"(\\.|[^"\\])*"|'(\\.|[^'\\])*'/,NUMBER:/NaN|-?((\d*\.\d+|\d+)([Ee][+-]?\d+)?|Infinity)/,RegexLiteral:/\/(?![*+?])(?:[^\r\n\[/\\]|\\.|\[(?:[^\r\n\]\\]|\\.)*\])+\/[a-z]*/,WS:/\s+/,ML_COMMENT:/\/\*[\s\S]*?\*\//,SL_COMMENT:/\/\/[^\n\r]*/},Cr="AbstractRule";function Lh(r){return _.isInstance(r,Cr)}a(Lh,"isAbstractRule");var Sr="AbstractType";function Oh(r){return _.isInstance(r,Sr)}a(Oh,"isAbstractType");var ti="Condition";function Ph(r){return _.isInstance(r,ti)}a(Ph,"isCondition");function bh(r){return ec(r)||r==="current"||r==="entry"||r==="extends"||r==="false"||r==="fragment"||r==="grammar"||r==="hidden"||r==="import"||r==="interface"||r==="returns"||r==="terminal"||r==="true"||r==="type"||r==="infer"||r==="infers"||r==="with"||typeof r=="string"&&/\^?[_a-zA-Z][\w_]*/.test(r)}a(bh,"isFeatureName");function ec(r){return r==="string"||r==="number"||r==="boolean"||r==="Date"||r==="bigint"}a(ec,"isPrimitiveType");var ws="TypeDefinition";function Mh(r){return _.isInstance(r,ws)}a(Mh,"isTypeDefinition");var ri="ValueLiteral";function Dh(r){return _.isInstance(r,ri)}a(Dh,"isValueLiteral");var wr="AbstractElement";function oi(r){return _.isInstance(r,wr)}a(oi,"isAbstractElement");var _r="ArrayLiteral";function Fh(r){return _.isInstance(r,_r)}a(Fh,"isArrayLiteral");var Lr="ArrayType";function xo(r){return _.isInstance(r,Lr)}a(xo,"isArrayType");var Or="BooleanLiteral";function Eo(r){return _.isInstance(r,Or)}a(Eo,"isBooleanLiteral");var Pr="Conjunction";function Ao(r){return _.isInstance(r,Pr)}a(Ao,"isConjunction");var br="Disjunction";function vo(r){return _.isInstance(r,br)}a(vo,"isDisjunction");var Mr="Grammar";function Gh(r){return _.isInstance(r,Mr)}a(Gh,"isGrammar");var ni="GrammarImport";function Uh(r){return _.isInstance(r,ni)}a(Uh,"isGrammarImport");var Dr="InferredType";function _s(r){return _.isInstance(r,Dr)}a(_s,"isInferredType");var Fr="Interface";function Ls(r){return _.isInstance(r,Fr)}a(Ls,"isInterface");var ii="NamedArgument";function Bh(r){return _.isInstance(r,ii)}a(Bh,"isNamedArgument");var Gr="Negation";function ko(r){return _.isInstance(r,Gr)}a(ko,"isNegation");var Ur="NumberLiteral";function Wh(r){return _.isInstance(r,Ur)}a(Wh,"isNumberLiteral");var Br="Parameter";function Kh(r){return _.isInstance(r,Br)}a(Kh,"isParameter");var Wr="ParameterReference";function $o(r){return _.isInstance(r,Wr)}a($o,"isParameterReference");var Kr="ParserRule";function Se(r){return _.isInstance(r,Kr)}a(Se,"isParserRule");var Vr="ReferenceType";function Io(r){return _.isInstance(r,Vr)}a(Io,"isReferenceType");var si="ReturnType";function No(r){return _.isInstance(r,si)}a(No,"isReturnType");var jr="SimpleType";function Os(r){return _.isInstance(r,jr)}a(Os,"isSimpleType");var Hr="StringLiteral";function Vh(r){return _.isInstance(r,Hr)}a(Vh,"isStringLiteral");var rr="TerminalRule";function He(r){return _.isInstance(r,rr)}a(He,"isTerminalRule");var zr="Type";function li(r){return _.isInstance(r,zr)}a(li,"isType");var ai="TypeAttribute";function jh(r){return _.isInstance(r,ai)}a(jh,"isTypeAttribute");var qr="UnionType";function Co(r){return _.isInstance(r,qr)}a(Co,"isUnionType");var Xr="Action";function $t(r){return _.isInstance(r,Xr)}a($t,"isAction");var Yr="Alternatives";function Ps(r){return _.isInstance(r,Yr)}a(Ps,"isAlternatives");var Jr="Assignment";function rt(r){return _.isInstance(r,Jr)}a(rt,"isAssignment");var Qr="CharacterRange";function So(r){return _.isInstance(r,Qr)}a(So,"isCharacterRange");var Zr="CrossReference";function nr(r){return _.isInstance(r,Zr)}a(nr,"isCrossReference");var en="EndOfFile";function wo(r){return _.isInstance(r,en)}a(wo,"isEndOfFile");var tn="Group";function Ut(r){return _.isInstance(r,tn)}a(Ut,"isGroup");var rn="Keyword";function Ye(r){return _.isInstance(r,rn)}a(Ye,"isKeyword");var nn="NegatedToken";function _o(r){return _.isInstance(r,nn)}a(_o,"isNegatedToken");var sn="RegexToken";function Lo(r){return _.isInstance(r,sn)}a(Lo,"isRegexToken");var an="RuleCall";function nt(r){return _.isInstance(r,an)}a(nt,"isRuleCall");var on="TerminalAlternatives";function Oo(r){return _.isInstance(r,on)}a(Oo,"isTerminalAlternatives");var ln="TerminalGroup";function Po(r){return _.isInstance(r,ln)}a(Po,"isTerminalGroup");var un="TerminalRuleCall";function bs(r){return _.isInstance(r,un)}a(bs,"isTerminalRuleCall");var cn="UnorderedGroup";function Ms(r){return _.isInstance(r,cn)}a(Ms,"isUnorderedGroup");var fn="UntilToken";function bo(r){return _.isInstance(r,fn)}a(bo,"isUntilToken");var dn="Wildcard";function Mo(r){return _.isInstance(r,dn)}a(Mo,"isWildcard");var hn=class extends Jt{static{a(this,"LangiumGrammarAstReflection")}getAllTypes(){return[wr,Cr,Sr,Xr,Yr,_r,Lr,Jr,Or,Qr,ti,Pr,Zr,br,en,Mr,ni,tn,Dr,Fr,rn,ii,nn,Gr,Ur,Br,Wr,Kr,Vr,sn,si,an,jr,Hr,on,ln,rr,un,zr,ai,ws,qr,cn,fn,ri,dn]}computeIsSubtype(e,t){switch(e){case Xr:case Yr:case Jr:case Qr:case Zr:case en:case tn:case rn:case nn:case sn:case an:case on:case ln:case un:case cn:case fn:case dn:return this.isSubtype(wr,t);case _r:case Ur:case Hr:return this.isSubtype(ri,t);case Lr:case Vr:case jr:case qr:return this.isSubtype(ws,t);case Or:return this.isSubtype(ti,t)||this.isSubtype(ri,t);case Pr:case br:case Gr:case Wr:return this.isSubtype(ti,t);case Dr:case Fr:case zr:return this.isSubtype(Sr,t);case Kr:return this.isSubtype(Cr,t)||this.isSubtype(Sr,t);case rr:return this.isSubtype(Cr,t);default:return!1}}getReferenceType(e){let t=`${e.container.$type}:${e.property}`;switch(t){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return Sr;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return Cr;case"Grammar:usedGrammars":return Mr;case"NamedArgument:parameter":case"ParameterReference:parameter":return Br;case"TerminalRuleCall:rule":return rr;default:throw new Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case wr:return{name:wr,properties:[{name:"cardinality"},{name:"lookahead"}]};case _r:return{name:_r,properties:[{name:"elements",defaultValue:[]}]};case Lr:return{name:Lr,properties:[{name:"elementType"}]};case Or:return{name:Or,properties:[{name:"true",defaultValue:!1}]};case Pr:return{name:Pr,properties:[{name:"left"},{name:"right"}]};case br:return{name:br,properties:[{name:"left"},{name:"right"}]};case Mr:return{name:Mr,properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case ni:return{name:ni,properties:[{name:"path"}]};case Dr:return{name:Dr,properties:[{name:"name"}]};case Fr:return{name:Fr,properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case ii:return{name:ii,properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case Gr:return{name:Gr,properties:[{name:"value"}]};case Ur:return{name:Ur,properties:[{name:"value"}]};case Br:return{name:Br,properties:[{name:"name"}]};case Wr:return{name:Wr,properties:[{name:"parameter"}]};case Kr:return{name:Kr,properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case Vr:return{name:Vr,properties:[{name:"referenceType"}]};case si:return{name:si,properties:[{name:"name"}]};case jr:return{name:jr,properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case Hr:return{name:Hr,properties:[{name:"value"}]};case rr:return{name:rr,properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case zr:return{name:zr,properties:[{name:"name"},{name:"type"}]};case ai:return{name:ai,properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case qr:return{name:qr,properties:[{name:"types",defaultValue:[]}]};case Xr:return{name:Xr,properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case Yr:return{name:Yr,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Jr:return{name:Jr,properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case Qr:return{name:Qr,properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case Zr:return{name:Zr,properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case en:return{name:en,properties:[{name:"cardinality"},{name:"lookahead"}]};case tn:return{name:tn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case rn:return{name:rn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case nn:return{name:nn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case sn:return{name:sn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case an:return{name:an,properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case on:return{name:on,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case ln:return{name:ln,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case un:return{name:un,properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case cn:return{name:cn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case fn:return{name:fn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case dn:return{name:dn,properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:e,properties:[]}}}},_=new hn;var Fs={};qt(Fs,{assignMandatoryProperties:()=>Go,copyAstNode:()=>Fo,findLocalReferences:()=>zh,findRootNode:()=>ci,getContainerOfType:()=>ir,getDocument:()=>we,hasContainerOfType:()=>Hh,linkContentToContainer:()=>Ds,streamAllContents:()=>ft,streamAst:()=>Je,streamContents:()=>fi,streamReferences:()=>pn});function Ds(r){for(let[e,t]of Object.entries(r))e.startsWith("$")||(Array.isArray(t)?t.forEach((n,i)=>{le(n)&&(n.$container=r,n.$containerProperty=e,n.$containerIndex=i)}):le(t)&&(t.$container=r,t.$containerProperty=e))}a(Ds,"linkContentToContainer");function ir(r,e){let t=r;for(;t;){if(e(t))return t;t=t.$container}}a(ir,"getContainerOfType");function Hh(r,e){let t=r;for(;t;){if(e(t))return!0;t=t.$container}return!1}a(Hh,"hasContainerOfType");function we(r){let t=ci(r).$document;if(!t)throw new Error("AST node has no document.");return t}a(we,"getDocument");function ci(r){for(;r.$container;)r=r.$container;return r}a(ci,"findRootNode");function fi(r,e){if(!r)throw new Error("Node must be an AstNode.");let t=e?.range;return new je(()=>({keys:Object.keys(r),keyIndex:0,arrayIndex:0}),n=>{for(;n.keyIndex<n.keys.length;){let i=n.keys[n.keyIndex];if(!i.startsWith("$")){let s=r[i];if(le(s)){if(n.keyIndex++,Do(s,t))return{done:!1,value:s}}else if(Array.isArray(s)){for(;n.arrayIndex<s.length;){let o=n.arrayIndex++,l=s[o];if(le(l)&&Do(l,t))return{done:!1,value:l}}n.arrayIndex=0}}n.keyIndex++}return Ce})}a(fi,"streamContents");function ft(r,e){if(!r)throw new Error("Root node must be an AstNode.");return new lt(r,t=>fi(t,e))}a(ft,"streamAllContents");function Je(r,e){if(r){if(e?.range&&!Do(r,e.range))return new lt(r,()=>[])}else throw new Error("Root node must be an AstNode.");return new lt(r,t=>fi(t,e),{includeRoot:!0})}a(Je,"streamAst");function Do(r,e){var t;if(!e)return!0;let n=(t=r.$cstNode)===null||t===void 0?void 0:t.range;return n?yo(n,e):!1}a(Do,"isAstNodeInRange");function pn(r){return new je(()=>({keys:Object.keys(r),keyIndex:0,arrayIndex:0}),e=>{for(;e.keyIndex<e.keys.length;){let t=e.keys[e.keyIndex];if(!t.startsWith("$")){let n=r[t];if(Ee(n))return e.keyIndex++,{done:!1,value:{reference:n,container:r,property:t}};if(Array.isArray(n)){for(;e.arrayIndex<n.length;){let i=e.arrayIndex++,s=n[i];if(Ee(s))return{done:!1,value:{reference:s,container:r,property:t,index:i}}}e.arrayIndex=0}}e.keyIndex++}return Ce})}a(pn,"streamReferences");function zh(r,e=we(r).parseResult.value){let t=[];return Je(e).forEach(n=>{pn(n).forEach(i=>{i.reference.ref===r&&t.push(i.reference)})}),j(t)}a(zh,"findLocalReferences");function Go(r,e){let t=r.getTypeMetaData(e.$type),n=e;for(let i of t.properties)i.defaultValue!==void 0&&n[i.name]===void 0&&(n[i.name]=tc(i.defaultValue))}a(Go,"assignMandatoryProperties");function tc(r){return Array.isArray(r)?[...r.map(tc)]:r}a(tc,"copyDefaultValue");function Fo(r,e){let t={$type:r.$type};for(let[n,i]of Object.entries(r))if(!n.startsWith("$"))if(le(i))t[n]=Fo(i,e);else if(Ee(i))t[n]=e(t,n,i.$refNode,i.$refText);else if(Array.isArray(i)){let s=[];for(let o of i)le(o)?s.push(Fo(o,e)):Ee(o)?s.push(e(t,n,o.$refNode,o.$refText)):s.push(o);t[n]=s}else t[n]=i;return Ds(t),t}a(Fo,"copyAstNode");var Bs={};qt(Bs,{NEWLINE_REGEXP:()=>Ko,escapeRegExp:()=>lr,getCaseInsensitivePattern:()=>jo,getTerminalParts:()=>Yh,isMultilineComment:()=>Vo,isWhitespace:()=>gn,partialMatches:()=>Ho,partialRegExp:()=>ic,whitespaceCharacters:()=>nc});function w(r){return r.charCodeAt(0)}a(w,"cc");function Gs(r,e){Array.isArray(r)?r.forEach(function(t){e.push(t)}):e.push(r)}a(Gs,"insertToSet");function mn(r,e){if(r[e]===!0)throw"duplicate flag "+e;let t=r[e];r[e]=!0}a(mn,"addFlag");function sr(r){if(r===void 0)throw Error("Internal Error - Should never get here!");return!0}a(sr,"ASSERT_EXISTS");function di(){throw Error("Internal Error - Should never get here!")}a(di,"ASSERT_NEVER_REACH_HERE");function Uo(r){return r.type==="Character"}a(Uo,"isCharacter");var hi=[];for(let r=w("0");r<=w("9");r++)hi.push(r);var pi=[w("_")].concat(hi);for(let r=w("a");r<=w("z");r++)pi.push(r);for(let r=w("A");r<=w("Z");r++)pi.push(r);var Bo=[w(" "),w("\f"),w(`
`),w("\r"),w("	"),w("\v"),w("	"),w("\xA0"),w("\u1680"),w("\u2000"),w("\u2001"),w("\u2002"),w("\u2003"),w("\u2004"),w("\u2005"),w("\u2006"),w("\u2007"),w("\u2008"),w("\u2009"),w("\u200A"),w("\u2028"),w("\u2029"),w("\u202F"),w("\u205F"),w("\u3000"),w("\uFEFF")];var qh=/[0-9a-fA-F]/,Us=/[0-9]/,Xh=/[1-9]/,ar=class{static{a(this,"RegExpParser")}constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx,this.input=e.input,this.groupIdx=e.groupIdx}pattern(e){this.idx=0,this.input=e,this.groupIdx=0,this.consumeChar("/");let t=this.disjunction();this.consumeChar("/");let n={type:"Flags",loc:{begin:this.idx,end:e.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":mn(n,"global");break;case"i":mn(n,"ignoreCase");break;case"m":mn(n,"multiLine");break;case"u":mn(n,"unicode");break;case"y":mn(n,"sticky");break}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:n,value:t,loc:this.loc(0)}}disjunction(){let e=[],t=this.idx;for(e.push(this.alternative());this.peekChar()==="|";)this.consumeChar("|"),e.push(this.alternative());return{type:"Disjunction",value:e,loc:this.loc(t)}}alternative(){let e=[],t=this.idx;for(;this.isTerm();)e.push(this.term());return{type:"Alternative",value:e,loc:this.loc(t)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){let e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":this.consumeChar("?");let t;switch(this.popChar()){case"=":t="Lookahead";break;case"!":t="NegativeLookahead";break}sr(t);let n=this.disjunction();return this.consumeChar(")"),{type:t,value:n,loc:this.loc(e)}}return di()}quantifier(e=!1){let t,n=this.idx;switch(this.popChar()){case"*":t={atLeast:0,atMost:1/0};break;case"+":t={atLeast:1,atMost:1/0};break;case"?":t={atLeast:0,atMost:1};break;case"{":let i=this.integerIncludingZero();switch(this.popChar()){case"}":t={atLeast:i,atMost:i};break;case",":let s;this.isDigit()?(s=this.integerIncludingZero(),t={atLeast:i,atMost:s}):t={atLeast:i,atMost:1/0},this.consumeChar("}");break}if(e===!0&&t===void 0)return;sr(t);break}if(!(e===!0&&t===void 0)&&sr(t))return this.peekChar(0)==="?"?(this.consumeChar("?"),t.greedy=!1):t.greedy=!0,t.type="Quantifier",t.loc=this.loc(n),t}atom(){let e,t=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group();break}return e===void 0&&this.isPatternCharacter()&&(e=this.patternCharacter()),sr(e)?(e.loc=this.loc(t),this.isQuantifier()&&(e.quantifier=this.quantifier()),e):di()}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[w(`
`),w("\r"),w("\u2028"),w("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let e,t=!1;switch(this.popChar()){case"d":e=hi;break;case"D":e=hi,t=!0;break;case"s":e=Bo;break;case"S":e=Bo,t=!0;break;case"w":e=pi;break;case"W":e=pi,t=!0;break}return sr(e)?{type:"Set",value:e,complement:t}:di()}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=w("\f");break;case"n":e=w(`
`);break;case"r":e=w("\r");break;case"t":e=w("	");break;case"v":e=w("\v");break}return sr(e)?{type:"Character",value:e}:di()}controlLetterEscapeAtom(){this.consumeChar("c");let e=this.popChar();if(/[a-zA-Z]/.test(e)===!1)throw Error("Invalid ");return{type:"Character",value:e.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:w("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){let e=this.popChar();return{type:"Character",value:w(e)}}classPatternCharacterAtom(){switch(this.peekChar()){case`
`:case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:let e=this.popChar();return{type:"Character",value:w(e)}}}characterClass(){let e=[],t=!1;for(this.consumeChar("["),this.peekChar(0)==="^"&&(this.consumeChar("^"),t=!0);this.isClassAtom();){let n=this.classAtom(),i=n.type==="Character";if(Uo(n)&&this.isRangeDash()){this.consumeChar("-");let s=this.classAtom(),o=s.type==="Character";if(Uo(s)){if(s.value<n.value)throw Error("Range out of order in character class");e.push({from:n.value,to:s.value})}else Gs(n.value,e),e.push(w("-")),Gs(s.value,e)}else Gs(n.value,e)}return this.consumeChar("]"),{type:"Set",complement:t,value:e}}classAtom(){switch(this.peekChar()){case"]":case`
`:case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:w("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=!0;switch(this.consumeChar("("),this.peekChar(0)){case"?":this.consumeChar("?"),this.consumeChar(":"),e=!1;break;default:this.groupIdx++;break}let t=this.disjunction();this.consumeChar(")");let n={type:"Group",capturing:e,value:t};return e&&(n.idx=this.groupIdx),n}positiveInteger(){let e=this.popChar();if(Xh.test(e)===!1)throw Error("Expecting a positive integer");for(;Us.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if(Us.test(e)===!1)throw Error("Expecting an integer");for(;Us.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}patternCharacter(){let e=this.popChar();switch(e){case`
`:case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:w(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return this.peekChar()==="-"&&this.isClassAtom(1)}isDigit(){return Us.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return this.peekChar(1)==="?"&&(this.peekChar(2)==="="||this.peekChar(2)==="!");default:return!1}}isQuantifier(){let e=this.saveState();try{return this.quantifier(!0)!==void 0}catch{return!1}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(e){let t="";for(let i=0;i<e;i++){let s=this.popChar();if(qh.test(s)===!1)throw Error("Expecting a HexDecimal digits");t+=s}return{type:"Character",value:parseInt(t,16)}}peekChar(e=0){return this.input[this.idx+e]}popChar(){let e=this.peekChar(0);return this.consumeChar(void 0),e}consumeChar(e){if(e!==void 0&&this.input[this.idx]!==e)throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(e){return{begin:e,end:this.idx}}};var dt=class{static{a(this,"BaseRegExpVisitor")}visitChildren(e){for(let t in e){let n=e[t];e.hasOwnProperty(t)&&(n.type!==void 0?this.visit(n):Array.isArray(n)&&n.forEach(i=>{this.visit(i)},this))}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e);break}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}};var Ko=/\r?\n/gm,rc=new ar,Wo=class extends dt{static{a(this,"TerminalRegExpVisitor")}constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(e){this.multiline=!1,this.regex=e,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(e){e.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(e){let t=String.fromCharCode(e.value);if(!this.multiline&&t===`
`&&(this.multiline=!0),e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{let n=lr(t);this.endRegexpStack.push(n),this.isStarting&&(this.startRegexp+=n)}}visitSet(e){if(!this.multiline){let t=this.regex.substring(e.loc.begin,e.loc.end),n=new RegExp(t);this.multiline=!!`
`.match(n)}if(e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{let t=this.regex.substring(e.loc.begin,e.loc.end);this.endRegexpStack.push(t),this.isStarting&&(this.startRegexp+=t)}}visitChildren(e){e.type==="Group"&&e.quantifier||super.visitChildren(e)}},or=new Wo;function Yh(r){try{typeof r!="string"&&(r=r.source),r=`/${r}/`;let e=rc.pattern(r),t=[];for(let n of e.value.value)or.reset(r),or.visit(n),t.push({start:or.startRegexp,end:or.endRegex});return t}catch{return[]}}a(Yh,"getTerminalParts");function Vo(r){try{return typeof r=="string"&&(r=new RegExp(r)),r=r.toString(),or.reset(r),or.visit(rc.pattern(r)),or.multiline}catch{return!1}}a(Vo,"isMultilineComment");var nc=`\f
\r	\v \xA0\u1680\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF`.split("");function gn(r){let e=typeof r=="string"?new RegExp(r):r;return nc.some(t=>e.test(t))}a(gn,"isWhitespace");function lr(r){return r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}a(lr,"escapeRegExp");function jo(r){return Array.prototype.map.call(r,e=>/\w/.test(e)?`[${e.toLowerCase()}${e.toUpperCase()}]`:lr(e)).join("")}a(jo,"getCaseInsensitivePattern");function Ho(r,e){let t=ic(r),n=e.match(t);return!!n&&n[0].length>0}a(Ho,"partialMatches");function ic(r){typeof r=="string"&&(r=new RegExp(r));let e=r,t=r.source,n=0;function i(){let s="",o;function l(c){s+=t.substr(n,c),n+=c}a(l,"appendRaw");function u(c){s+="(?:"+t.substr(n,c)+"|$)",n+=c}for(a(u,"appendOptional");n<t.length;)switch(t[n]){case"\\":switch(t[n+1]){case"c":u(3);break;case"x":u(4);break;case"u":e.unicode?t[n+2]==="{"?u(t.indexOf("}",n)-n+1):u(6):u(2);break;case"p":case"P":e.unicode?u(t.indexOf("}",n)-n+1):u(2);break;case"k":u(t.indexOf(">",n)-n+1);break;default:u(2);break}break;case"[":o=/\[(?:\\.|.)*?\]/g,o.lastIndex=n,o=o.exec(t)||[],u(o[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":l(1);break;case"{":o=/\{\d+,?\d*\}/g,o.lastIndex=n,o=o.exec(t),o?l(o[0].length):u(1);break;case"(":if(t[n+1]==="?")switch(t[n+2]){case":":s+="(?:",n+=3,s+=i()+"|$)";break;case"=":s+="(?=",n+=3,s+=i()+")";break;case"!":o=n,n+=3,i(),s+=t.substr(o,n-o);break;case"<":switch(t[n+3]){case"=":case"!":o=n,n+=4,i(),s+=t.substr(o,n-o);break;default:l(t.indexOf(">",n)-n+1),s+=i()+"|$)";break}break}else l(1),s+=i()+"|$)";break;case")":return++n,s;default:u(1);break}return s}return a(i,"process"),new RegExp(i(),r.flags)}a(ic,"partialRegExp");function sc(r){return r.rules.find(e=>Se(e)&&e.entry)}a(sc,"getEntryRule");function ac(r){return r.rules.filter(e=>He(e)&&e.hidden)}a(ac,"getHiddenRules");function mi(r,e){let t=new Set,n=sc(r);if(!n)return new Set(r.rules);let i=[n].concat(ac(r));for(let o of i)oc(o,t,e);let s=new Set;for(let o of r.rules)(t.has(o.name)||He(o)&&o.hidden)&&s.add(o);return s}a(mi,"getAllReachableRules");function oc(r,e,t){e.add(r.name),ft(r).forEach(n=>{if(nt(n)||t&&bs(n)){let i=n.rule.ref;i&&!e.has(i.name)&&oc(i,e,t)}})}a(oc,"ruleDfs");function Xo(r){if(r.terminal)return r.terminal;if(r.type.ref){let e=Ws(r.type.ref);return e?.terminal}}a(Xo,"getCrossReferenceTerminal");function Yo(r){return r.hidden&&!gn(Tn(r))}a(Yo,"isCommentTerminal");function Jo(r,e){return!r||!e?[]:Qo(r,e,r.astNode,!0)}a(Jo,"findNodesForProperty");function gi(r,e,t){if(!r||!e)return;let n=Qo(r,e,r.astNode,!0);if(n.length!==0)return t!==void 0?t=Math.max(0,Math.min(t,n.length-1)):t=0,n[t]}a(gi,"findNodeForProperty");function Qo(r,e,t,n){if(!n){let i=ir(r.grammarSource,rt);if(i&&i.feature===e)return[r]}return tt(r)&&r.astNode===t?r.content.flatMap(i=>Qo(i,e,t,!1)):[]}a(Qo,"findNodesForPropertyInternal");function Jh(r,e){return r?el(r,e,r?.astNode):[]}a(Jh,"findNodesForKeyword");function Zo(r,e,t){if(!r)return;let n=el(r,e,r?.astNode);if(n.length!==0)return t!==void 0?t=Math.max(0,Math.min(t,n.length-1)):t=0,n[t]}a(Zo,"findNodeForKeyword");function el(r,e,t){if(r.astNode!==t)return[];if(Ye(r.grammarSource)&&r.grammarSource.value===e)return[r];let n=Zt(r).iterator(),i,s=[];do if(i=n.next(),!i.done){let o=i.value;o.astNode===t?Ye(o.grammarSource)&&o.grammarSource.value===e&&s.push(o):n.prune()}while(!i.done);return s}a(el,"findNodesForKeywordInternal");function tl(r){var e;let t=r.astNode;for(;t===((e=r.container)===null||e===void 0?void 0:e.astNode);){let n=ir(r.grammarSource,rt);if(n)return n;r=r.container}}a(tl,"findAssignment");function Ws(r){let e=r;return _s(e)&&($t(e.$container)?e=e.$container.$container:Se(e.$container)?e=e.$container:ct(e.$container)),lc(r,e,new Map)}a(Ws,"findNameAssignment");function lc(r,e,t){var n;function i(s,o){let l;return ir(s,rt)||(l=lc(o,o,t)),t.set(r,l),l}if(a(i,"go"),t.has(r))return t.get(r);t.set(r,void 0);for(let s of ft(e)){if(rt(s)&&s.feature.toLowerCase()==="name")return t.set(r,s),s;if(nt(s)&&Se(s.rule.ref))return i(s,s.rule.ref);if(Os(s)&&(!((n=s.typeRef)===null||n===void 0)&&n.ref))return i(s,s.typeRef.ref)}}a(lc,"findNameAssignmentInternal");function uc(r){let e=r.$container;if(Ut(e)){let t=e.elements,n=t.indexOf(r);for(let i=n-1;i>=0;i--){let s=t[i];if($t(s))return s;{let o=ft(t[i]).find($t);if(o)return o}}}if(oi(e))return uc(e)}a(uc,"getActionAtElement");function Qh(r,e){return r==="?"||r==="*"||Ut(e)&&!!e.guardCondition}a(Qh,"isOptionalCardinality");function Zh(r){return r==="*"||r==="+"}a(Zh,"isArrayCardinality");function ep(r){return r==="+="}a(ep,"isArrayOperator");function yi(r){return cc(r,new Set)}a(yi,"isDataTypeRule");function cc(r,e){if(e.has(r))return!0;e.add(r);for(let t of ft(r))if(nt(t)){if(!t.rule.ref||Se(t.rule.ref)&&!cc(t.rule.ref,e))return!1}else{if(rt(t))return!1;if($t(t))return!1}return!!r.definition}a(cc,"isDataTypeRuleInternal");function tp(r){return qo(r.type,new Set)}a(tp,"isDataType");function qo(r,e){if(e.has(r))return!0;if(e.add(r),xo(r))return!1;if(Io(r))return!1;if(Co(r))return r.types.every(t=>qo(t,e));if(Os(r)){if(r.primitiveType!==void 0)return!0;if(r.stringType!==void 0)return!0;if(r.typeRef!==void 0){let t=r.typeRef.ref;return li(t)?qo(t.type,e):!1}else return!1}else return!1}a(qo,"isDataTypeInternal");function yn(r){if(r.inferredType)return r.inferredType.name;if(r.dataType)return r.dataType;if(r.returnType){let e=r.returnType.ref;if(e){if(Se(e))return e.name;if(Ls(e)||li(e))return e.name}}}a(yn,"getExplicitRuleType");function Ti(r){var e;if(Se(r))return yi(r)?r.name:(e=yn(r))!==null&&e!==void 0?e:r.name;if(Ls(r)||li(r)||No(r))return r.name;if($t(r)){let t=fc(r);if(t)return t}else if(_s(r))return r.name;throw new Error("Cannot get name of Unknown Type")}a(Ti,"getTypeName");function fc(r){var e;if(r.inferredType)return r.inferredType.name;if(!((e=r.type)===null||e===void 0)&&e.ref)return Ti(r.type.ref)}a(fc,"getActionType");function rp(r){var e,t,n;return He(r)?(t=(e=r.type)===null||e===void 0?void 0:e.name)!==null&&t!==void 0?t:"string":yi(r)?r.name:(n=yn(r))!==null&&n!==void 0?n:r.name}a(rp,"getRuleTypeName");function rl(r){var e,t,n;return He(r)?(t=(e=r.type)===null||e===void 0?void 0:e.name)!==null&&t!==void 0?t:"string":(n=yn(r))!==null&&n!==void 0?n:r.name}a(rl,"getRuleType");function Tn(r){let e={s:!1,i:!1,u:!1},t=Rn(r.definition,e),n=Object.entries(e).filter(([,i])=>i).map(([i])=>i).join("");return new RegExp(t,n)}a(Tn,"terminalRegex");var nl=/[\s\S]/.source;function Rn(r,e){if(Oo(r))return np(r);if(Po(r))return ip(r);if(So(r))return op(r);if(bs(r)){let t=r.rule.ref;if(!t)throw new Error("Missing rule reference.");return It(Rn(t.definition),{cardinality:r.cardinality,lookahead:r.lookahead})}else{if(_o(r))return ap(r);if(bo(r))return sp(r);if(Lo(r)){let t=r.regex.lastIndexOf("/"),n=r.regex.substring(1,t),i=r.regex.substring(t+1);return e&&(e.i=i.includes("i"),e.s=i.includes("s"),e.u=i.includes("u")),It(n,{cardinality:r.cardinality,lookahead:r.lookahead,wrap:!1})}else{if(Mo(r))return It(nl,{cardinality:r.cardinality,lookahead:r.lookahead});throw new Error(`Invalid terminal element: ${r?.$type}`)}}}a(Rn,"abstractElementToRegex");function np(r){return It(r.elements.map(e=>Rn(e)).join("|"),{cardinality:r.cardinality,lookahead:r.lookahead})}a(np,"terminalAlternativesToRegex");function ip(r){return It(r.elements.map(e=>Rn(e)).join(""),{cardinality:r.cardinality,lookahead:r.lookahead})}a(ip,"terminalGroupToRegex");function sp(r){return It(`${nl}*?${Rn(r.terminal)}`,{cardinality:r.cardinality,lookahead:r.lookahead})}a(sp,"untilTokenToRegex");function ap(r){return It(`(?!${Rn(r.terminal)})${nl}*?`,{cardinality:r.cardinality,lookahead:r.lookahead})}a(ap,"negateTokenToRegex");function op(r){return r.right?It(`[${zo(r.left)}-${zo(r.right)}]`,{cardinality:r.cardinality,lookahead:r.lookahead,wrap:!1}):It(zo(r.left),{cardinality:r.cardinality,lookahead:r.lookahead,wrap:!1})}a(op,"characterRangeToRegex");function zo(r){return lr(r.value)}a(zo,"keywordToRegex");function It(r,e){var t;return(e.wrap!==!1||e.lookahead)&&(r=`(${(t=e.lookahead)!==null&&t!==void 0?t:""}${r})`),e.cardinality?`${r}${e.cardinality}`:r}a(It,"withCardinality");function il(r){let e=[],t=r.Grammar;for(let n of t.rules)He(n)&&Yo(n)&&Vo(Tn(n))&&e.push(n.name);return{multilineCommentRules:e,nameRegexp:Cs}}a(il,"createGrammarConfig");function xn(r){console&&console.error&&console.error(`Error: ${r}`)}a(xn,"PRINT_ERROR");function Ri(r){console&&console.warn&&console.warn(`Warning: ${r}`)}a(Ri,"PRINT_WARNING");function xi(r){let e=new Date().getTime(),t=r();return{time:new Date().getTime()-e,value:t}}a(xi,"timer");function Ei(r){function e(){}a(e,"FakeConstructor"),e.prototype=r;let t=new e;function n(){return typeof t.bar}return a(n,"fakeAccess"),n(),n(),r;(0,eval)(r)}a(Ei,"toFastProperties");function lp(r){return up(r)?r.LABEL:r.name}a(lp,"tokenLabel");function up(r){return Ne(r.LABEL)&&r.LABEL!==""}a(up,"hasTokenLabel");var ze=class{static{a(this,"AbstractProduction")}get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this),$(this.definition,t=>{t.accept(e)})}},H=class extends ze{static{a(this,"NonTerminal")}constructor(e){super([]),this.idx=1,ke(this,Ve(e,t=>t!==void 0))}set definition(e){}get definition(){return this.referencedRule!==void 0?this.referencedRule.definition:[]}accept(e){e.visit(this)}},Me=class extends ze{static{a(this,"Rule")}constructor(e){super(e.definition),this.orgText="",ke(this,Ve(e,t=>t!==void 0))}},J=class extends ze{static{a(this,"Alternative")}constructor(e){super(e.definition),this.ignoreAmbiguities=!1,ke(this,Ve(e,t=>t!==void 0))}},z=class extends ze{static{a(this,"Option")}constructor(e){super(e.definition),this.idx=1,ke(this,Ve(e,t=>t!==void 0))}},Q=class extends ze{static{a(this,"RepetitionMandatory")}constructor(e){super(e.definition),this.idx=1,ke(this,Ve(e,t=>t!==void 0))}},Z=class extends ze{static{a(this,"RepetitionMandatoryWithSeparator")}constructor(e){super(e.definition),this.idx=1,ke(this,Ve(e,t=>t!==void 0))}},G=class extends ze{static{a(this,"Repetition")}constructor(e){super(e.definition),this.idx=1,ke(this,Ve(e,t=>t!==void 0))}},q=class extends ze{static{a(this,"RepetitionWithSeparator")}constructor(e){super(e.definition),this.idx=1,ke(this,Ve(e,t=>t!==void 0))}},X=class extends ze{static{a(this,"Alternation")}get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,ke(this,Ve(e,t=>t!==void 0))}},D=class{static{a(this,"Terminal")}constructor(e){this.idx=1,ke(this,Ve(e,t=>t!==void 0))}accept(e){e.visit(this)}};function Vs(r){return E(r,En)}a(Vs,"serializeGrammar");function En(r){function e(t){return E(t,En)}if(a(e,"convertDefinition"),r instanceof H){let t={type:"NonTerminal",name:r.nonTerminalName,idx:r.idx};return Ne(r.label)&&(t.label=r.label),t}else{if(r instanceof J)return{type:"Alternative",definition:e(r.definition)};if(r instanceof z)return{type:"Option",idx:r.idx,definition:e(r.definition)};if(r instanceof Q)return{type:"RepetitionMandatory",idx:r.idx,definition:e(r.definition)};if(r instanceof Z)return{type:"RepetitionMandatoryWithSeparator",idx:r.idx,separator:En(new D({terminalType:r.separator})),definition:e(r.definition)};if(r instanceof q)return{type:"RepetitionWithSeparator",idx:r.idx,separator:En(new D({terminalType:r.separator})),definition:e(r.definition)};if(r instanceof G)return{type:"Repetition",idx:r.idx,definition:e(r.definition)};if(r instanceof X)return{type:"Alternation",idx:r.idx,definition:e(r.definition)};if(r instanceof D){let t={type:"Terminal",name:r.terminalType.name,label:lp(r.terminalType),idx:r.idx};Ne(r.label)&&(t.terminalLabel=r.label);let n=r.terminalType.PATTERN;return r.terminalType.PATTERN&&(t.pattern=et(n)?n.source:n),t}else{if(r instanceof Me)return{type:"Rule",name:r.name,orgText:r.orgText,definition:e(r.definition)};throw Error("non exhaustive match")}}}a(En,"serializeProduction");var De=class{static{a(this,"GAstVisitor")}visit(e){let t=e;switch(t.constructor){case H:return this.visitNonTerminal(t);case J:return this.visitAlternative(t);case z:return this.visitOption(t);case Q:return this.visitRepetitionMandatory(t);case Z:return this.visitRepetitionMandatoryWithSeparator(t);case q:return this.visitRepetitionWithSeparator(t);case G:return this.visitRepetition(t);case X:return this.visitAlternation(t);case D:return this.visitTerminal(t);case Me:return this.visitRule(t);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}};function sl(r){return r instanceof J||r instanceof z||r instanceof G||r instanceof Q||r instanceof Z||r instanceof q||r instanceof D||r instanceof Me}a(sl,"isSequenceProd");function ur(r,e=[]){return r instanceof z||r instanceof G||r instanceof q?!0:r instanceof X?Is(r.definition,n=>ur(n,e)):r instanceof H&&oe(e,r)?!1:r instanceof ze?(r instanceof H&&e.push(r),Pe(r.definition,n=>ur(n,e))):!1}a(ur,"isOptionalProd");function al(r){return r instanceof X}a(al,"isBranchingProd");function We(r){if(r instanceof H)return"SUBRULE";if(r instanceof z)return"OPTION";if(r instanceof X)return"OR";if(r instanceof Q)return"AT_LEAST_ONE";if(r instanceof Z)return"AT_LEAST_ONE_SEP";if(r instanceof q)return"MANY_SEP";if(r instanceof G)return"MANY";if(r instanceof D)return"CONSUME";throw Error("non exhaustive match")}a(We,"getProductionDslName");var Nt=class{static{a(this,"RestWalker")}walk(e,t=[]){$(e.definition,(n,i)=>{let s=pe(e.definition,i+1);if(n instanceof H)this.walkProdRef(n,s,t);else if(n instanceof D)this.walkTerminal(n,s,t);else if(n instanceof J)this.walkFlat(n,s,t);else if(n instanceof z)this.walkOption(n,s,t);else if(n instanceof Q)this.walkAtLeastOne(n,s,t);else if(n instanceof Z)this.walkAtLeastOneSep(n,s,t);else if(n instanceof q)this.walkManySep(n,s,t);else if(n instanceof G)this.walkMany(n,s,t);else if(n instanceof X)this.walkOr(n,s,t);else throw Error("non exhaustive match")})}walkTerminal(e,t,n){}walkProdRef(e,t,n){}walkFlat(e,t,n){let i=t.concat(n);this.walk(e,i)}walkOption(e,t,n){let i=t.concat(n);this.walk(e,i)}walkAtLeastOne(e,t,n){let i=[new z({definition:e.definition})].concat(t,n);this.walk(e,i)}walkAtLeastOneSep(e,t,n){let i=dc(e,t,n);this.walk(e,i)}walkMany(e,t,n){let i=[new z({definition:e.definition})].concat(t,n);this.walk(e,i)}walkManySep(e,t,n){let i=dc(e,t,n);this.walk(e,i)}walkOr(e,t,n){let i=t.concat(n);$(e.definition,s=>{let o=new J({definition:[s]});this.walk(o,i)})}};function dc(r,e,t){return[new z({definition:[new D({terminalType:r.separator})].concat(r.definition)})].concat(e,t)}a(dc,"restForRepetitionWithSeparator");function cr(r){if(r instanceof H)return cr(r.referencedRule);if(r instanceof D)return dp(r);if(sl(r))return cp(r);if(al(r))return fp(r);throw Error("non exhaustive match")}a(cr,"first");function cp(r){let e=[],t=r.definition,n=0,i=t.length>n,s,o=!0;for(;i&&o;)s=t[n],o=ur(s),e=e.concat(cr(s)),n=n+1,i=t.length>n;return Qn(e)}a(cp,"firstForSequence");function fp(r){let e=E(r.definition,t=>cr(t));return Qn(he(e))}a(fp,"firstForBranching");function dp(r){return[r.terminalType]}a(dp,"firstForTerminal");var js="_~IN~_";var ol=class extends Nt{static{a(this,"ResyncFollowsWalker")}constructor(e){super(),this.topProd=e,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(e,t,n){}walkProdRef(e,t,n){let i=hp(e.referencedRule,e.idx)+this.topProd.name,s=t.concat(n),o=new J({definition:s}),l=cr(o);this.follows[i]=l}};function hc(r){let e={};return $(r,t=>{let n=new ol(t).startWalking();ke(e,n)}),e}a(hc,"computeAllProdsFollows");function hp(r,e){return r.name+e+js}a(hp,"buildBetweenProdsFollowPrefix");var Hs={},pp=new ar;function An(r){let e=r.toString();if(Hs.hasOwnProperty(e))return Hs[e];{let t=pp.pattern(e);return Hs[e]=t,t}}a(An,"getRegExpAst");function pc(){Hs={}}a(pc,"clearRegExpParserCache");var gc="Complement Sets are not supported for first char optimization",Ai=`Unable to use "first char" lexer optimizations:
`;function yc(r,e=!1){try{let t=An(r);return ll(t.value,{},t.flags.ignoreCase)}catch(t){if(t.message===gc)e&&Ri(`${Ai}	Unable to optimize: < ${r.toString()} >
	Complement Sets cannot be automatically optimized.
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let n="";e&&(n=`
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.`),xn(`${Ai}
	Failed parsing: < ${r.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+n)}}return[]}a(yc,"getOptimizedStartCodesIndices");function ll(r,e,t){switch(r.type){case"Disjunction":for(let i=0;i<r.value.length;i++)ll(r.value[i],e,t);break;case"Alternative":let n=r.value;for(let i=0;i<n.length;i++){let s=n[i];switch(s.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}let o=s;switch(o.type){case"Character":zs(o.value,e,t);break;case"Set":if(o.complement===!0)throw Error(gc);$(o.value,u=>{if(typeof u=="number")zs(u,e,t);else{let c=u;if(t===!0)for(let f=c.from;f<=c.to;f++)zs(f,e,t);else{for(let f=c.from;f<=c.to&&f<vn;f++)zs(f,e,t);if(c.to>=vn){let f=c.from>=vn?c.from:vn,d=c.to,h=ht(f),p=ht(d);for(let g=h;g<=p;g++)e[g]=g}}}});break;case"Group":ll(o.value,e,t);break;default:throw Error("Non Exhaustive Match")}let l=o.quantifier!==void 0&&o.quantifier.atLeast===0;if(o.type==="Group"&&ul(o)===!1||o.type!=="Group"&&l===!1)break}break;default:throw Error("non exhaustive match!")}return re(e)}a(ll,"firstCharOptimizedIndices");function zs(r,e,t){let n=ht(r);e[n]=n,t===!0&&mp(r,e)}a(zs,"addOptimizedIdxToResult");function mp(r,e){let t=String.fromCharCode(r),n=t.toUpperCase();if(n!==t){let i=ht(n.charCodeAt(0));e[i]=i}else{let i=t.toLowerCase();if(i!==t){let s=ht(i.charCodeAt(0));e[s]=s}}}a(mp,"handleIgnoreCase");function mc(r,e){return kt(r.value,t=>{if(typeof t=="number")return oe(e,t);{let n=t;return kt(e,i=>n.from<=i&&i<=n.to)!==void 0}})}a(mc,"findCode");function ul(r){let e=r.quantifier;return e&&e.atLeast===0?!0:r.value?ge(r.value)?Pe(r.value,ul):ul(r.value):!1}a(ul,"isWholeOptional");var cl=class extends dt{static{a(this,"CharCodeFinder")}constructor(e){super(),this.targetCharCodes=e,this.found=!1}visitChildren(e){if(this.found!==!0){switch(e.type){case"Lookahead":this.visitLookahead(e);return;case"NegativeLookahead":this.visitNegativeLookahead(e);return}super.visitChildren(e)}}visitCharacter(e){oe(this.targetCharCodes,e.value)&&(this.found=!0)}visitSet(e){e.complement?mc(e,this.targetCharCodes)===void 0&&(this.found=!0):mc(e,this.targetCharCodes)!==void 0&&(this.found=!0)}};function qs(r,e){if(e instanceof RegExp){let t=An(e),n=new cl(r);return n.visit(t),n.found}else return kt(e,t=>oe(r,t.charCodeAt(0)))!==void 0}a(qs,"canMatchCharCode");var fr="PATTERN",kn="defaultMode",Xs="modes",dl=typeof new RegExp("(?:)").sticky=="boolean";function xc(r,e){e=Jn(e,{useSticky:dl,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r",`
`],tracer:a((A,R)=>R(),"tracer")});let t=e.tracer;t("initCharCodeToOptimizedIndexMap",()=>{Op()});let n;t("Reject Lexer.NA",()=>{n=Yt(r,A=>A[fr]===se.NA)});let i=!1,s;t("Transform Patterns",()=>{i=!1,s=E(n,A=>{let R=A[fr];if(et(R)){let P=R.source;return P.length===1&&P!=="^"&&P!=="$"&&P!=="."&&!R.ignoreCase?P:P.length===2&&P[0]==="\\"&&!oe(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],P[1])?P[1]:e.useSticky?Rc(R):Tc(R)}else{if(ot(R))return i=!0,{exec:R};if(typeof R=="object")return i=!0,R;if(typeof R=="string"){if(R.length===1)return R;{let P=R.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),b=new RegExp(P);return e.useSticky?Rc(b):Tc(b)}}else throw Error("non exhaustive match")}})});let o,l,u,c,f;t("misc mapping",()=>{o=E(n,A=>A.tokenTypeIdx),l=E(n,A=>{let R=A.GROUP;if(R!==se.SKIPPED){if(Ne(R))return R;if(be(R))return!1;throw Error("non exhaustive match")}}),u=E(n,A=>{let R=A.LONGER_ALT;if(R)return ge(R)?E(R,b=>ho(n,b)):[ho(n,R)]}),c=E(n,A=>A.PUSH_MODE),f=E(n,A=>I(A,"POP_MODE"))});let d;t("Line Terminator Handling",()=>{let A=Cc(e.lineTerminatorCharacters);d=E(n,R=>!1),e.positionTracking!=="onlyOffset"&&(d=E(n,R=>I(R,"LINE_BREAKS")?!!R.LINE_BREAKS:Nc(R,A)===!1&&qs(A,R.PATTERN)))});let h,p,g,y;t("Misc Mapping #2",()=>{h=E(n,$c),p=E(s,_p),g=ue(n,(A,R)=>{let P=R.GROUP;return Ne(P)&&P!==se.SKIPPED&&(A[P]=[]),A},{}),y=E(s,(A,R)=>({pattern:s[R],longerAlt:u[R],canLineTerminator:d[R],isCustom:h[R],short:p[R],group:l[R],push:c[R],pop:f[R],tokenTypeIdx:o[R],tokenType:n[R]}))});let v=!0,x=[];return e.safeMode||t("First Char Optimization",()=>{x=ue(n,(A,R,P)=>{if(typeof R.PATTERN=="string"){let b=R.PATTERN.charCodeAt(0),Te=ht(b);fl(A,Te,y[P])}else if(ge(R.START_CHARS_HINT)){let b;$(R.START_CHARS_HINT,Te=>{let kr=typeof Te=="string"?Te.charCodeAt(0):Te,Ae=ht(kr);b!==Ae&&(b=Ae,fl(A,Ae,y[P]))})}else if(et(R.PATTERN))if(R.PATTERN.unicode)v=!1,e.ensureOptimizations&&xn(`${Ai}	Unable to analyze < ${R.PATTERN.toString()} > pattern.
	The regexp unicode flag is not currently supported by the regexp-to-ast library.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{let b=yc(R.PATTERN,e.ensureOptimizations);M(b)&&(v=!1),$(b,Te=>{fl(A,Te,y[P])})}else e.ensureOptimizations&&xn(`${Ai}	TokenType: <${R.name}> is using a custom token pattern without providing <start_chars_hint> parameter.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),v=!1;return A},[])}),{emptyGroups:g,patternIdxToConfig:y,charCodeToPatternIdxToConfig:x,hasCustom:i,canBeOptimized:v}}a(xc,"analyzeTokenTypes");function Ec(r,e){let t=[],n=yp(r);t=t.concat(n.errors);let i=Tp(n.valid),s=i.valid;return t=t.concat(i.errors),t=t.concat(gp(s)),t=t.concat(Ip(s)),t=t.concat(Np(s,e)),t=t.concat(Cp(s)),t}a(Ec,"validatePatterns");function gp(r){let e=[],t=xe(r,n=>et(n[fr]));return e=e.concat(xp(t)),e=e.concat(vp(t)),e=e.concat(kp(t)),e=e.concat($p(t)),e=e.concat(Ep(t)),e}a(gp,"validateRegExpPattern");function yp(r){let e=xe(r,i=>!I(i,fr)),t=E(e,i=>({message:"Token Type: ->"+i.name+"<- missing static 'PATTERN' property",type:ie.MISSING_PATTERN,tokenTypes:[i]})),n=Xt(r,e);return{errors:t,valid:n}}a(yp,"findMissingPatterns");function Tp(r){let e=xe(r,i=>{let s=i[fr];return!et(s)&&!ot(s)&&!I(s,"exec")&&!Ne(s)}),t=E(e,i=>({message:"Token Type: ->"+i.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:ie.INVALID_PATTERN,tokenTypes:[i]})),n=Xt(r,e);return{errors:t,valid:n}}a(Tp,"findInvalidPatterns");var Rp=/[^\\][$]/;function xp(r){class e extends dt{static{a(this,"EndAnchorFinder")}constructor(){super(...arguments),this.found=!1}visitEndAnchor(s){this.found=!0}}let t=xe(r,i=>{let s=i.PATTERN;try{let o=An(s),l=new e;return l.visit(o),l.found}catch{return Rp.test(s.source)}});return E(t,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain end of input anchor '$'
	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:ie.EOI_ANCHOR_FOUND,tokenTypes:[i]}))}a(xp,"findEndOfInputAnchor");function Ep(r){let e=xe(r,n=>n.PATTERN.test(""));return E(e,n=>({message:"Token Type: ->"+n.name+"<- static 'PATTERN' must not match an empty string",type:ie.EMPTY_MATCH_PATTERN,tokenTypes:[n]}))}a(Ep,"findEmptyMatchRegExps");var Ap=/[^\\[][\^]|^\^/;function vp(r){class e extends dt{static{a(this,"StartAnchorFinder")}constructor(){super(...arguments),this.found=!1}visitStartAnchor(s){this.found=!0}}let t=xe(r,i=>{let s=i.PATTERN;try{let o=An(s),l=new e;return l.visit(o),l.found}catch{return Ap.test(s.source)}});return E(t,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain start of input anchor '^'
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:ie.SOI_ANCHOR_FOUND,tokenTypes:[i]}))}a(vp,"findStartOfInputAnchor");function kp(r){let e=xe(r,n=>{let i=n[fr];return i instanceof RegExp&&(i.multiline||i.global)});return E(e,n=>({message:"Token Type: ->"+n.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:ie.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[n]}))}a(kp,"findUnsupportedFlags");function $p(r){let e=[],t=E(r,s=>ue(r,(o,l)=>(s.PATTERN.source===l.PATTERN.source&&!oe(e,l)&&l.PATTERN!==se.NA&&(e.push(l),o.push(l)),o),[]));t=At(t);let n=xe(t,s=>s.length>1);return E(n,s=>{let o=E(s,u=>u.name);return{message:`The same RegExp pattern ->${$e(s).PATTERN}<-has been used in all of the following Token Types: ${o.join(", ")} <-`,type:ie.DUPLICATE_PATTERNS_FOUND,tokenTypes:s}})}a($p,"findDuplicatePatterns");function Ip(r){let e=xe(r,n=>{if(!I(n,"GROUP"))return!1;let i=n.GROUP;return i!==se.SKIPPED&&i!==se.NA&&!Ne(i)});return E(e,n=>({message:"Token Type: ->"+n.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:ie.INVALID_GROUP_TYPE_FOUND,tokenTypes:[n]}))}a(Ip,"findInvalidGroupType");function Np(r,e){let t=xe(r,i=>i.PUSH_MODE!==void 0&&!oe(e,i.PUSH_MODE));return E(t,i=>({message:`Token Type: ->${i.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${i.PUSH_MODE}<-which does not exist`,type:ie.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[i]}))}a(Np,"findModesThatDoNotExist");function Cp(r){let e=[],t=ue(r,(n,i,s)=>{let o=i.PATTERN;return o===se.NA||(Ne(o)?n.push({str:o,idx:s,tokenType:i}):et(o)&&wp(o)&&n.push({str:o.source,idx:s,tokenType:i})),n},[]);return $(r,(n,i)=>{$(t,({str:s,idx:o,tokenType:l})=>{if(i<o&&Sp(s,n.PATTERN)){let u=`Token: ->${l.name}<- can never be matched.
Because it appears AFTER the Token Type ->${n.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;e.push({message:u,type:ie.UNREACHABLE_PATTERN,tokenTypes:[n,l]})}})}),e}a(Cp,"findUnreachablePatterns");function Sp(r,e){if(et(e)){let t=e.exec(r);return t!==null&&t.index===0}else{if(ot(e))return e(r,0,[],{});if(I(e,"exec"))return e.exec(r,0,[],{});if(typeof e=="string")return e===r;throw Error("non exhaustive match")}}a(Sp,"testTokenType");function wp(r){return kt([".","\\","[","]","|","^","$","(",")","?","*","+","{"],t=>r.source.indexOf(t)!==-1)===void 0}a(wp,"noMetaChar");function Tc(r){let e=r.ignoreCase?"i":"";return new RegExp(`^(?:${r.source})`,e)}a(Tc,"addStartOfInput");function Rc(r){let e=r.ignoreCase?"iy":"y";return new RegExp(`${r.source}`,e)}a(Rc,"addStickyFlag");function Ac(r,e,t){let n=[];return I(r,kn)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+kn+`> property in its definition
`,type:ie.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),I(r,Xs)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+Xs+`> property in its definition
`,type:ie.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),I(r,Xs)&&I(r,kn)&&!I(r.modes,r.defaultMode)&&n.push({message:`A MultiMode Lexer cannot be initialized with a ${kn}: <${r.defaultMode}>which does not exist
`,type:ie.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),I(r,Xs)&&$(r.modes,(i,s)=>{$(i,(o,l)=>{if(be(o))n.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${s}> at index: <${l}>
`,type:ie.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if(I(o,"LONGER_ALT")){let u=ge(o.LONGER_ALT)?o.LONGER_ALT:[o.LONGER_ALT];$(u,c=>{!be(c)&&!oe(i,c)&&n.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${c.name}> on token <${o.name}> outside of mode <${s}>
`,type:ie.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),n}a(Ac,"performRuntimeChecks");function vc(r,e,t){let n=[],i=!1,s=At(he(re(r.modes))),o=Yt(s,u=>u[fr]===se.NA),l=Cc(t);return e&&$(o,u=>{let c=Nc(u,l);if(c!==!1){let d={message:Lp(u,c),type:c.issue,tokenType:u};n.push(d)}else I(u,"LINE_BREAKS")?u.LINE_BREAKS===!0&&(i=!0):qs(l,u.PATTERN)&&(i=!0)}),e&&!i&&n.push({message:`Warning: No LINE_BREAKS Found.
	This Lexer has been defined to track line and column information,
	But none of the Token Types can be identified as matching a line terminator.
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS 
	for details.`,type:ie.NO_LINE_BREAKS_FLAGS}),n}a(vc,"performWarningRuntimeChecks");function kc(r){let e={},t=Et(r);return $(t,n=>{let i=r[n];if(ge(i))e[n]=[];else throw Error("non exhaustive match")}),e}a(kc,"cloneEmptyGroups");function $c(r){let e=r.PATTERN;if(et(e))return!1;if(ot(e))return!0;if(I(e,"exec"))return!0;if(Ne(e))return!1;throw Error("non exhaustive match")}a($c,"isCustomPattern");function _p(r){return Ne(r)&&r.length===1?r.charCodeAt(0):!1}a(_p,"isShortPattern");var Ic={test:a(function(r){let e=r.length;for(let t=this.lastIndex;t<e;t++){let n=r.charCodeAt(t);if(n===10)return this.lastIndex=t+1,!0;if(n===13)return r.charCodeAt(t+1)===10?this.lastIndex=t+2:this.lastIndex=t+1,!0}return!1},"test"),lastIndex:0};function Nc(r,e){if(I(r,"LINE_BREAKS"))return!1;if(et(r.PATTERN)){try{qs(e,r.PATTERN)}catch(t){return{issue:ie.IDENTIFY_TERMINATOR,errMsg:t.message}}return!1}else{if(Ne(r.PATTERN))return!1;if($c(r))return{issue:ie.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}}a(Nc,"checkLineBreaksIssues");function Lp(r,e){if(e.issue===ie.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${r.name}> Token Type
	 Root cause: ${e.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(e.issue===ie.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${r.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}a(Lp,"buildLineBreakIssueMessage");function Cc(r){return E(r,t=>Ne(t)?t.charCodeAt(0):t)}a(Cc,"getCharCodes");function fl(r,e,t){r[e]===void 0?r[e]=[t]:r[e].push(t)}a(fl,"addToMapOfArrays");var vn=256,Ys=[];function ht(r){return r<vn?r:Ys[r]}a(ht,"charCodeToOptimizedIndex");function Op(){if(M(Ys)){Ys=new Array(65536);for(let r=0;r<65536;r++)Ys[r]=r>255?255+~~(r/255):r}}a(Op,"initCharCodeToOptimizedIndexMap");function Ct(r,e){let t=r.tokenTypeIdx;return t===e.tokenTypeIdx?!0:e.isParent===!0&&e.categoryMatchesMap[t]===!0}a(Ct,"tokenStructuredMatcher");function $n(r,e){return r.tokenTypeIdx===e.tokenTypeIdx}a($n,"tokenStructuredMatcherNoCategories");var Sc=1,_c={};function St(r){let e=Pp(r);bp(e),Dp(e),Mp(e),$(e,t=>{t.isParent=t.categoryMatches.length>0})}a(St,"augmentTokenTypes");function Pp(r){let e=te(r),t=r,n=!0;for(;n;){t=At(he(E(t,s=>s.CATEGORIES)));let i=Xt(t,e);e=e.concat(i),M(i)?n=!1:t=i}return e}a(Pp,"expandCategories");function bp(r){$(r,e=>{hl(e)||(_c[Sc]=e,e.tokenTypeIdx=Sc++),wc(e)&&!ge(e.CATEGORIES)&&(e.CATEGORIES=[e.CATEGORIES]),wc(e)||(e.CATEGORIES=[]),Fp(e)||(e.categoryMatches=[]),Gp(e)||(e.categoryMatchesMap={})})}a(bp,"assignTokenDefaultProps");function Mp(r){$(r,e=>{e.categoryMatches=[],$(e.categoryMatchesMap,(t,n)=>{e.categoryMatches.push(_c[n].tokenTypeIdx)})})}a(Mp,"assignCategoriesTokensProp");function Dp(r){$(r,e=>{Lc([],e)})}a(Dp,"assignCategoriesMapProp");function Lc(r,e){$(r,t=>{e.categoryMatchesMap[t.tokenTypeIdx]=!0}),$(e.CATEGORIES,t=>{let n=r.concat(e);oe(n,t)||Lc(n,t)})}a(Lc,"singleAssignCategoriesToksMap");function hl(r){return I(r,"tokenTypeIdx")}a(hl,"hasShortKeyProperty");function wc(r){return I(r,"CATEGORIES")}a(wc,"hasCategoriesProperty");function Fp(r){return I(r,"categoryMatches")}a(Fp,"hasExtendingTokensTypesProperty");function Gp(r){return I(r,"categoryMatchesMap")}a(Gp,"hasExtendingTokensTypesMapProperty");function Oc(r){return I(r,"tokenTypeIdx")}a(Oc,"isTokenType");var In={buildUnableToPopLexerModeMessage(r){return`Unable to pop Lexer Mode after encountering Token ->${r.image}<- The Mode Stack is empty`},buildUnexpectedCharactersMessage(r,e,t,n,i){return`unexpected character: ->${r.charAt(e)}<- at offset: ${e}, skipped ${t} characters.`}};var ie;(function(r){r[r.MISSING_PATTERN=0]="MISSING_PATTERN",r[r.INVALID_PATTERN=1]="INVALID_PATTERN",r[r.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",r[r.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",r[r.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",r[r.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",r[r.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",r[r.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",r[r.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",r[r.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",r[r.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",r[r.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",r[r.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",r[r.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",r[r.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",r[r.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",r[r.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",r[r.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"})(ie||(ie={}));var vi={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:[`
`,"\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:In,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(vi);var se=class{static{a(this,"Lexer")}constructor(e,t=vi){if(this.lexerDefinition=e,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(i,s)=>{if(this.traceInitPerf===!0){this.traceInitIndent++;let o=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${o}--> <${i}>`);let{time:l,value:u}=xi(s),c=l>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&c(`${o}<-- <${i}> time: ${l}ms`),this.traceInitIndent--,u}else return s()},typeof t=="boolean")throw Error(`The second argument to the Lexer constructor is now an ILexerConfig Object.
a boolean 2nd argument is no longer supported`);this.config=ke({},vi,t);let n=this.config.traceInitPerf;n===!0?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):typeof n=="number"&&(this.traceInitMaxIdent=n,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let i,s=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===vi.lineTerminatorsPattern)this.config.lineTerminatorsPattern=Ic;else if(this.config.lineTerminatorCharacters===vi.lineTerminatorCharacters)throw Error(`Error: Missing <lineTerminatorCharacters> property on the Lexer config.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS`);if(t.safeMode&&t.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),ge(e)?i={modes:{defaultMode:te(e)},defaultMode:kn}:(s=!1,i=te(e))}),this.config.skipValidations===!1&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(Ac(i,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(vc(i,this.trackStartLines,this.config.lineTerminatorCharacters))})),i.modes=i.modes?i.modes:{},$(i.modes,(l,u)=>{i.modes[u]=Yt(l,c=>be(c))});let o=Et(i.modes);if($(i.modes,(l,u)=>{this.TRACE_INIT(`Mode: <${u}> processing`,()=>{if(this.modes.push(u),this.config.skipValidations===!1&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(Ec(l,o))}),M(this.lexerDefinitionErrors)){St(l);let c;this.TRACE_INIT("analyzeTokenTypes",()=>{c=xc(l,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:t.positionTracking,ensureOptimizations:t.ensureOptimizations,safeMode:t.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[u]=c.patternIdxToConfig,this.charCodeToPatternIdxToConfig[u]=c.charCodeToPatternIdxToConfig,this.emptyGroups=ke({},this.emptyGroups,c.emptyGroups),this.hasCustom=c.hasCustom||this.hasCustom,this.canModeBeOptimized[u]=c.canBeOptimized}})}),this.defaultMode=i.defaultMode,!M(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){let u=E(this.lexerDefinitionErrors,c=>c.message).join(`-----------------------
`);throw new Error(`Errors detected in definition of Lexer:
`+u)}$(this.lexerDefinitionWarning,l=>{Ri(l.message)}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(dl?(this.chopInput=fo,this.match=this.matchWithTest):(this.updateLastIndex=de,this.match=this.matchWithExec),s&&(this.handleModes=de),this.trackStartLines===!1&&(this.computeNewColumn=fo),this.trackEndLines===!1&&(this.updateTokenEndLineColumnLocation=de),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else if(/onlyOffset/i.test(this.config.positionTracking))this.createTokenInstance=this.createOffsetOnlyToken;else throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{let l=ue(this.canModeBeOptimized,(u,c,f)=>(c===!1&&u.push(f),u),[]);if(t.ensureOptimizations&&!M(l))throw Error(`Lexer Modes: < ${l.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{pc()}),this.TRACE_INIT("toFastProperties",()=>{Ei(this)})})}tokenize(e,t=this.defaultMode){if(!M(this.lexerDefinitionErrors)){let i=E(this.lexerDefinitionErrors,s=>s.message).join(`-----------------------
`);throw new Error(`Unable to Tokenize because Errors detected in definition of Lexer:
`+i)}return this.tokenizeInternal(e,t)}tokenizeInternal(e,t){let n,i,s,o,l,u,c,f,d,h,p,g,y,v,x,A,R=e,P=R.length,b=0,Te=0,kr=this.hasCustom?0:Math.floor(e.length/10),Ae=new Array(kr),Mt=[],Tt=this.trackStartLines?1:void 0,k=this.trackStartLines?1:void 0,T=kc(this.emptyGroups),C=this.trackStartLines,N=this.config.lineTerminatorsPattern,V=0,O=[],L=[],Le=[],Oe=[];Object.freeze(Oe);let ee;function Ht(){return O}a(Ht,"getPossiblePatternsSlow");function Uu(ve){let Xe=ht(ve),$r=L[Xe];return $r===void 0?Oe:$r}a(Uu,"getPossiblePatternsOptimized");let vh=a(ve=>{if(Le.length===1&&ve.tokenType.PUSH_MODE===void 0){let Xe=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(ve);Mt.push({offset:ve.startOffset,line:ve.startLine,column:ve.startColumn,length:ve.image.length,message:Xe})}else{Le.pop();let Xe=vt(Le);O=this.patternIdxToConfig[Xe],L=this.charCodeToPatternIdxToConfig[Xe],V=O.length;let $r=this.canModeBeOptimized[Xe]&&this.config.safeMode===!1;L&&$r?ee=Uu:ee=Ht}},"pop_mode");function Bu(ve){Le.push(ve),L=this.charCodeToPatternIdxToConfig[ve],O=this.patternIdxToConfig[ve],V=O.length,V=O.length;let Xe=this.canModeBeOptimized[ve]&&this.config.safeMode===!1;L&&Xe?ee=Uu:ee=Ht}a(Bu,"push_mode"),Bu.call(this,t);let Ze,Wu=this.config.recoveryEnabled;for(;b<P;){u=null;let ve=R.charCodeAt(b),Xe=ee(ve),$r=Xe.length;for(n=0;n<$r;n++){Ze=Xe[n];let Ke=Ze.pattern;c=null;let Rt=Ze.short;if(Rt!==!1?ve===Rt&&(u=Ke):Ze.isCustom===!0?(A=Ke.exec(R,b,Ae,T),A!==null?(u=A[0],A.payload!==void 0&&(c=A.payload)):u=null):(this.updateLastIndex(Ke,b),u=this.match(Ke,e,b)),u!==null){if(l=Ze.longerAlt,l!==void 0){let Dt=l.length;for(s=0;s<Dt;s++){let xt=O[l[s]],zt=xt.pattern;if(f=null,xt.isCustom===!0?(A=zt.exec(R,b,Ae,T),A!==null?(o=A[0],A.payload!==void 0&&(f=A.payload)):o=null):(this.updateLastIndex(zt,b),o=this.match(zt,e,b)),o&&o.length>u.length){u=o,c=f,Ze=xt;break}}}break}}if(u!==null){if(d=u.length,h=Ze.group,h!==void 0&&(p=Ze.tokenTypeIdx,g=this.createTokenInstance(u,b,p,Ze.tokenType,Tt,k,d),this.handlePayload(g,c),h===!1?Te=this.addToken(Ae,Te,g):T[h].push(g)),e=this.chopInput(e,d),b=b+d,k=this.computeNewColumn(k,d),C===!0&&Ze.canLineTerminator===!0){let Ke=0,Rt,Dt;N.lastIndex=0;do Rt=N.test(u),Rt===!0&&(Dt=N.lastIndex-1,Ke++);while(Rt===!0);Ke!==0&&(Tt=Tt+Ke,k=d-Dt,this.updateTokenEndLineColumnLocation(g,h,Dt,Ke,Tt,k,d))}this.handleModes(Ze,vh,Bu,g)}else{let Ke=b,Rt=Tt,Dt=k,xt=Wu===!1;for(;xt===!1&&b<P;)for(e=this.chopInput(e,1),b++,i=0;i<V;i++){let zt=O[i],co=zt.pattern,Ku=zt.short;if(Ku!==!1?R.charCodeAt(b)===Ku&&(xt=!0):zt.isCustom===!0?xt=co.exec(R,b,Ae,T)!==null:(this.updateLastIndex(co,b),xt=co.exec(e)!==null),xt===!0)break}if(y=b-Ke,k=this.computeNewColumn(k,y),x=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(R,Ke,y,Rt,Dt),Mt.push({offset:Ke,line:Rt,column:Dt,length:y,message:x}),Wu===!1)break}}return this.hasCustom||(Ae.length=Te),{tokens:Ae,groups:T,errors:Mt}}handleModes(e,t,n,i){if(e.pop===!0){let s=e.push;t(i),s!==void 0&&n.call(this,s)}else e.push!==void 0&&n.call(this,e.push)}chopInput(e,t){return e.substring(t)}updateLastIndex(e,t){e.lastIndex=t}updateTokenEndLineColumnLocation(e,t,n,i,s,o,l){let u,c;t!==void 0&&(u=n===l-1,c=u?-1:0,i===1&&u===!0||(e.endLine=s+c,e.endColumn=o-1+-c))}computeNewColumn(e,t){return e+t}createOffsetOnlyToken(e,t,n,i){return{image:e,startOffset:t,tokenTypeIdx:n,tokenType:i}}createStartOnlyToken(e,t,n,i,s,o){return{image:e,startOffset:t,startLine:s,startColumn:o,tokenTypeIdx:n,tokenType:i}}createFullToken(e,t,n,i,s,o,l){return{image:e,startOffset:t,endOffset:t+l-1,startLine:s,endLine:s,startColumn:o,endColumn:o+l-1,tokenTypeIdx:n,tokenType:i}}addTokenUsingPush(e,t,n){return e.push(n),t}addTokenUsingMemberAccess(e,t,n){return e[t]=n,t++,t}handlePayloadNoCustom(e,t){}handlePayloadWithCustom(e,t){t!==null&&(e.payload=t)}matchWithTest(e,t,n){return e.test(t)===!0?t.substring(n,e.lastIndex):null}matchWithExec(e,t){let n=e.exec(t);return n!==null?n[0]:null}};se.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.";se.NA=/NOT_APPLICABLE/;function wt(r){return pl(r)?r.LABEL:r.name}a(wt,"tokenLabel");function pl(r){return Ne(r.LABEL)&&r.LABEL!==""}a(pl,"hasTokenLabel");var Up="parent",Pc="categories",bc="label",Mc="group",Dc="push_mode",Fc="pop_mode",Gc="longer_alt",Uc="line_breaks",Bc="start_chars_hint";function Bt(r){return Bp(r)}a(Bt,"createToken");function Bp(r){let e=r.pattern,t={};if(t.name=r.name,be(e)||(t.PATTERN=e),I(r,Up))throw`The parent property is no longer supported.
See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.`;return I(r,Pc)&&(t.CATEGORIES=r[Pc]),St([t]),I(r,bc)&&(t.LABEL=r[bc]),I(r,Mc)&&(t.GROUP=r[Mc]),I(r,Fc)&&(t.POP_MODE=r[Fc]),I(r,Dc)&&(t.PUSH_MODE=r[Dc]),I(r,Gc)&&(t.LONGER_ALT=r[Gc]),I(r,Uc)&&(t.LINE_BREAKS=r[Uc]),I(r,Bc)&&(t.START_CHARS_HINT=r[Bc]),t}a(Bp,"createTokenInternal");var qe=Bt({name:"EOF",pattern:se.NA});St([qe]);function _t(r,e,t,n,i,s,o,l){return{image:e,startOffset:t,endOffset:n,startLine:i,endLine:s,startColumn:o,endColumn:l,tokenTypeIdx:r.tokenTypeIdx,tokenType:r}}a(_t,"createTokenInstance");function ki(r,e){return Ct(r,e)}a(ki,"tokenMatcher");var Lt={buildMismatchTokenMessage({expected:r,actual:e,previous:t,ruleName:n}){return`Expecting ${pl(r)?`--> ${wt(r)} <--`:`token of type --> ${r.name} <--`} but found --> '${e.image}' <--`},buildNotAllInputParsedMessage({firstRedundant:r,ruleName:e}){return"Redundant input, expecting EOF but found: "+r.image},buildNoViableAltMessage({expectedPathsPerAlt:r,actual:e,previous:t,customUserDescription:n,ruleName:i}){let s="Expecting: ",l=`
but found: '`+$e(e).image+"'";if(n)return s+n+l;{let u=ue(r,(h,p)=>h.concat(p),[]),c=E(u,h=>`[${E(h,p=>wt(p)).join(", ")}]`),d=`one of these possible Token sequences:
${E(c,(h,p)=>`  ${p+1}. ${h}`).join(`
`)}`;return s+d+l}},buildEarlyExitMessage({expectedIterationPaths:r,actual:e,customUserDescription:t,ruleName:n}){let i="Expecting: ",o=`
but found: '`+$e(e).image+"'";if(t)return i+t+o;{let u=`expecting at least one iteration which starts with one of these possible Token sequences::
  <${E(r,c=>`[${E(c,f=>wt(f)).join(",")}]`).join(" ,")}>`;return i+u+o}}};Object.freeze(Lt);var Wc={buildRuleNotFoundError(r,e){return"Invalid grammar, reference to a rule which is not defined: ->"+e.nonTerminalName+`<-
inside top level rule: ->`+r.name+"<-"}},it={buildDuplicateFoundError(r,e){function t(f){return f instanceof D?f.terminalType.name:f instanceof H?f.nonTerminalName:""}a(t,"getExtraProductionArgument");let n=r.name,i=$e(e),s=i.idx,o=We(i),l=t(i),u=s>0,c=`->${o}${u?s:""}<- ${l?`with argument: ->${l}<-`:""}
                  appears more than once (${e.length} times) in the top level rule: ->${n}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return c=c.replace(/[ \t]+/g," "),c=c.replace(/\s\s+/g,`
`),c},buildNamespaceConflictError(r){return`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${r.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`},buildAlternationPrefixAmbiguityError(r){let e=E(r.prefixPath,i=>wt(i)).join(", "),t=r.alternation.idx===0?"":r.alternation.idx;return`Ambiguous alternatives: <${r.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${t}> inside <${r.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`},buildAlternationAmbiguityError(r){let e=E(r.prefixPath,i=>wt(i)).join(", "),t=r.alternation.idx===0?"":r.alternation.idx,n=`Ambiguous Alternatives Detected: <${r.ambiguityIndices.join(" ,")}> in <OR${t}> inside <${r.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return n=n+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,n},buildEmptyRepetitionError(r){let e=We(r.repetition);return r.repetition.idx!==0&&(e+=r.repetition.idx),`The repetition <${e}> within Rule <${r.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`},buildTokenNameError(r){return"deprecated"},buildEmptyAlternationError(r){return`Ambiguous empty alternative: <${r.emptyChoiceIdx+1}> in <OR${r.alternation.idx}> inside <${r.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`},buildTooManyAlternativesError(r){return`An Alternation cannot have more than 256 alternatives:
<OR${r.alternation.idx}> inside <${r.topLevelRule.name}> Rule.
 has ${r.alternation.definition.length+1} alternatives.`},buildLeftRecursionError(r){let e=r.topLevelRule.name,t=E(r.leftRecursionPath,s=>s.name),n=`${e} --> ${t.concat([e]).join(" --> ")}`;return`Left Recursion found in grammar.
rule: <${e}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${n}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError(r){return"deprecated"},buildDuplicateRuleNameError(r){let e;return r.topLevelRule instanceof Me?e=r.topLevelRule.name:e=r.topLevelRule,`Duplicate definition, rule: ->${e}<- is already defined in the grammar: ->${r.grammarName}<-`}};function Kc(r,e){let t=new ml(r,e);return t.resolveRefs(),t.errors}a(Kc,"resolveGrammar");var ml=class extends De{static{a(this,"GastRefResolverVisitor")}constructor(e,t){super(),this.nameToTopRule=e,this.errMsgProvider=t,this.errors=[]}resolveRefs(){$(re(this.nameToTopRule),e=>{this.currTopLevel=e,e.accept(this)})}visitNonTerminal(e){let t=this.nameToTopRule[e.nonTerminalName];if(t)e.referencedRule=t;else{let n=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:n,type:ye.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}}};var gl=class extends Nt{static{a(this,"AbstractNextPossibleTokensWalker")}constructor(e,t){super(),this.topProd=e,this.path=t,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=te(this.path.ruleStack).reverse(),this.occurrenceStack=te(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(e,t=[]){this.found||super.walk(e,t)}walkProdRef(e,t,n){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){let i=t.concat(n);this.updateExpectedNext(),this.walk(e.referencedRule,i)}}updateExpectedNext(){M(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}},Js=class extends gl{static{a(this,"NextAfterTokenWalker")}constructor(e,t){super(e,t),this.path=t,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,t,n){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){let i=t.concat(n),s=new J({definition:i});this.possibleTokTypes=cr(s),this.found=!0}}},Nn=class extends Nt{static{a(this,"AbstractNextTerminalAfterProductionWalker")}constructor(e,t){super(),this.topRule=e,this.occurrence=t,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}},Qs=class extends Nn{static{a(this,"NextTerminalAfterManyWalker")}walkMany(e,t,n){if(e.idx===this.occurrence){let i=$e(t.concat(n));this.result.isEndOfRule=i===void 0,i instanceof D&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkMany(e,t,n)}},$i=class extends Nn{static{a(this,"NextTerminalAfterManySepWalker")}walkManySep(e,t,n){if(e.idx===this.occurrence){let i=$e(t.concat(n));this.result.isEndOfRule=i===void 0,i instanceof D&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkManySep(e,t,n)}},Zs=class extends Nn{static{a(this,"NextTerminalAfterAtLeastOneWalker")}walkAtLeastOne(e,t,n){if(e.idx===this.occurrence){let i=$e(t.concat(n));this.result.isEndOfRule=i===void 0,i instanceof D&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOne(e,t,n)}},Ii=class extends Nn{static{a(this,"NextTerminalAfterAtLeastOneSepWalker")}walkAtLeastOneSep(e,t,n){if(e.idx===this.occurrence){let i=$e(t.concat(n));this.result.isEndOfRule=i===void 0,i instanceof D&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOneSep(e,t,n)}};function ea(r,e,t=[]){t=te(t);let n=[],i=0;function s(l){return l.concat(pe(r,i+1))}a(s,"remainingPathWith");function o(l){let u=ea(s(l),e,t);return n.concat(u)}for(a(o,"getAlternativesForProd");t.length<e&&i<r.length;){let l=r[i];if(l instanceof J)return o(l.definition);if(l instanceof H)return o(l.definition);if(l instanceof z)n=o(l.definition);else if(l instanceof Q){let u=l.definition.concat([new G({definition:l.definition})]);return o(u)}else if(l instanceof Z){let u=[new J({definition:l.definition}),new G({definition:[new D({terminalType:l.separator})].concat(l.definition)})];return o(u)}else if(l instanceof q){let u=l.definition.concat([new G({definition:[new D({terminalType:l.separator})].concat(l.definition)})]);n=o(u)}else if(l instanceof G){let u=l.definition.concat([new G({definition:l.definition})]);n=o(u)}else{if(l instanceof X)return $(l.definition,u=>{M(u.definition)===!1&&(n=o(u.definition))}),n;if(l instanceof D)t.push(l.terminalType);else throw Error("non exhaustive match")}i++}return n.push({partialPath:t,suffixDef:pe(r,i)}),n}a(ea,"possiblePathsFrom");function ta(r,e,t,n){let i="EXIT_NONE_TERMINAL",s=[i],o="EXIT_ALTERNATIVE",l=!1,u=e.length,c=u-n-1,f=[],d=[];for(d.push({idx:-1,def:r,ruleStack:[],occurrenceStack:[]});!M(d);){let h=d.pop();if(h===o){l&&vt(d).idx<=c&&d.pop();continue}let p=h.def,g=h.idx,y=h.ruleStack,v=h.occurrenceStack;if(M(p))continue;let x=p[0];if(x===i){let A={idx:g,def:pe(p),ruleStack:Ft(y),occurrenceStack:Ft(v)};d.push(A)}else if(x instanceof D)if(g<u-1){let A=g+1,R=e[A];if(t(R,x.terminalType)){let P={idx:A,def:pe(p),ruleStack:y,occurrenceStack:v};d.push(P)}}else if(g===u-1)f.push({nextTokenType:x.terminalType,nextTokenOccurrence:x.idx,ruleStack:y,occurrenceStack:v}),l=!0;else throw Error("non exhaustive match");else if(x instanceof H){let A=te(y);A.push(x.nonTerminalName);let R=te(v);R.push(x.idx);let P={idx:g,def:x.definition.concat(s,pe(p)),ruleStack:A,occurrenceStack:R};d.push(P)}else if(x instanceof z){let A={idx:g,def:pe(p),ruleStack:y,occurrenceStack:v};d.push(A),d.push(o);let R={idx:g,def:x.definition.concat(pe(p)),ruleStack:y,occurrenceStack:v};d.push(R)}else if(x instanceof Q){let A=new G({definition:x.definition,idx:x.idx}),R=x.definition.concat([A],pe(p)),P={idx:g,def:R,ruleStack:y,occurrenceStack:v};d.push(P)}else if(x instanceof Z){let A=new D({terminalType:x.separator}),R=new G({definition:[A].concat(x.definition),idx:x.idx}),P=x.definition.concat([R],pe(p)),b={idx:g,def:P,ruleStack:y,occurrenceStack:v};d.push(b)}else if(x instanceof q){let A={idx:g,def:pe(p),ruleStack:y,occurrenceStack:v};d.push(A),d.push(o);let R=new D({terminalType:x.separator}),P=new G({definition:[R].concat(x.definition),idx:x.idx}),b=x.definition.concat([P],pe(p)),Te={idx:g,def:b,ruleStack:y,occurrenceStack:v};d.push(Te)}else if(x instanceof G){let A={idx:g,def:pe(p),ruleStack:y,occurrenceStack:v};d.push(A),d.push(o);let R=new G({definition:x.definition,idx:x.idx}),P=x.definition.concat([R],pe(p)),b={idx:g,def:P,ruleStack:y,occurrenceStack:v};d.push(b)}else if(x instanceof X)for(let A=x.definition.length-1;A>=0;A--){let R=x.definition[A],P={idx:g,def:R.definition.concat(pe(p)),ruleStack:y,occurrenceStack:v};d.push(P),d.push(o)}else if(x instanceof J)d.push({idx:g,def:x.definition.concat(pe(p)),ruleStack:y,occurrenceStack:v});else if(x instanceof Me)d.push(Wp(x,g,y,v));else throw Error("non exhaustive match")}return f}a(ta,"nextPossibleTokensAfter");function Wp(r,e,t,n){let i=te(t);i.push(r.name);let s=te(n);return s.push(1),{idx:e,def:r.definition,ruleStack:i,occurrenceStack:s}}a(Wp,"expandTopLevelRule");var ae;(function(r){r[r.OPTION=0]="OPTION",r[r.REPETITION=1]="REPETITION",r[r.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",r[r.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",r[r.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",r[r.ALTERNATION=5]="ALTERNATION"})(ae||(ae={}));function Ni(r){if(r instanceof z||r==="Option")return ae.OPTION;if(r instanceof G||r==="Repetition")return ae.REPETITION;if(r instanceof Q||r==="RepetitionMandatory")return ae.REPETITION_MANDATORY;if(r instanceof Z||r==="RepetitionMandatoryWithSeparator")return ae.REPETITION_MANDATORY_WITH_SEPARATOR;if(r instanceof q||r==="RepetitionWithSeparator")return ae.REPETITION_WITH_SEPARATOR;if(r instanceof X||r==="Alternation")return ae.ALTERNATION;throw Error("non exhaustive match")}a(Ni,"getProdType");function na(r){let{occurrence:e,rule:t,prodType:n,maxLookahead:i}=r,s=Ni(n);return s===ae.ALTERNATION?Cn(e,t,i):Sn(e,t,s,i)}a(na,"getLookaheadPaths");function jc(r,e,t,n,i,s){let o=Cn(r,e,t),l=Jc(o)?$n:Ct;return s(o,n,l,i)}a(jc,"buildLookaheadFuncForOr");function Hc(r,e,t,n,i,s){let o=Sn(r,e,i,t),l=Jc(o)?$n:Ct;return s(o[0],l,n)}a(Hc,"buildLookaheadFuncForOptionalProd");function zc(r,e,t,n){let i=r.length,s=Pe(r,o=>Pe(o,l=>l.length===1));if(e)return function(o){let l=E(o,u=>u.GATE);for(let u=0;u<i;u++){let c=r[u],f=c.length,d=l[u];if(!(d!==void 0&&d.call(this)===!1))e:for(let h=0;h<f;h++){let p=c[h],g=p.length;for(let y=0;y<g;y++){let v=this.LA(y+1);if(t(v,p[y])===!1)continue e}return u}}};if(s&&!n){let o=E(r,u=>he(u)),l=ue(o,(u,c,f)=>($(c,d=>{I(u,d.tokenTypeIdx)||(u[d.tokenTypeIdx]=f),$(d.categoryMatches,h=>{I(u,h)||(u[h]=f)})}),u),{});return function(){let u=this.LA(1);return l[u.tokenTypeIdx]}}else return function(){for(let o=0;o<i;o++){let l=r[o],u=l.length;e:for(let c=0;c<u;c++){let f=l[c],d=f.length;for(let h=0;h<d;h++){let p=this.LA(h+1);if(t(p,f[h])===!1)continue e}return o}}}}a(zc,"buildAlternativesLookAheadFunc");function qc(r,e,t){let n=Pe(r,s=>s.length===1),i=r.length;if(n&&!t){let s=he(r);if(s.length===1&&M(s[0].categoryMatches)){let l=s[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===l}}else{let o=ue(s,(l,u,c)=>(l[u.tokenTypeIdx]=!0,$(u.categoryMatches,f=>{l[f]=!0}),l),[]);return function(){let l=this.LA(1);return o[l.tokenTypeIdx]===!0}}}else return function(){e:for(let s=0;s<i;s++){let o=r[s],l=o.length;for(let u=0;u<l;u++){let c=this.LA(u+1);if(e(c,o[u])===!1)continue e}return!0}return!1}}a(qc,"buildSingleAlternativeLookaheadFunction");var Tl=class extends Nt{static{a(this,"RestDefinitionFinderWalker")}constructor(e,t,n){super(),this.topProd=e,this.targetOccurrence=t,this.targetProdType=n}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(e,t,n,i){return e.idx===this.targetOccurrence&&this.targetProdType===t?(this.restDef=n.concat(i),!0):!1}walkOption(e,t,n){this.checkIsTarget(e,ae.OPTION,t,n)||super.walkOption(e,t,n)}walkAtLeastOne(e,t,n){this.checkIsTarget(e,ae.REPETITION_MANDATORY,t,n)||super.walkOption(e,t,n)}walkAtLeastOneSep(e,t,n){this.checkIsTarget(e,ae.REPETITION_MANDATORY_WITH_SEPARATOR,t,n)||super.walkOption(e,t,n)}walkMany(e,t,n){this.checkIsTarget(e,ae.REPETITION,t,n)||super.walkOption(e,t,n)}walkManySep(e,t,n){this.checkIsTarget(e,ae.REPETITION_WITH_SEPARATOR,t,n)||super.walkOption(e,t,n)}},ra=class extends De{static{a(this,"InsideDefinitionFinderVisitor")}constructor(e,t,n){super(),this.targetOccurrence=e,this.targetProdType=t,this.targetRef=n,this.result=[]}checkIsTarget(e,t){e.idx===this.targetOccurrence&&this.targetProdType===t&&(this.targetRef===void 0||e===this.targetRef)&&(this.result=e.definition)}visitOption(e){this.checkIsTarget(e,ae.OPTION)}visitRepetition(e){this.checkIsTarget(e,ae.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,ae.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,ae.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,ae.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,ae.ALTERNATION)}};function Vc(r){let e=new Array(r);for(let t=0;t<r;t++)e[t]=[];return e}a(Vc,"initializeArrayOfArrays");function yl(r){let e=[""];for(let t=0;t<r.length;t++){let n=r[t],i=[];for(let s=0;s<e.length;s++){let o=e[s];i.push(o+"_"+n.tokenTypeIdx);for(let l=0;l<n.categoryMatches.length;l++){let u="_"+n.categoryMatches[l];i.push(o+u)}}e=i}return e}a(yl,"pathToHashKeys");function Kp(r,e,t){for(let n=0;n<r.length;n++){if(n===t)continue;let i=r[n];for(let s=0;s<e.length;s++){let o=e[s];if(i[o]===!0)return!1}}return!0}a(Kp,"isUniquePrefixHash");function Xc(r,e){let t=E(r,o=>ea([o],1)),n=Vc(t.length),i=E(t,o=>{let l={};return $(o,u=>{let c=yl(u.partialPath);$(c,f=>{l[f]=!0})}),l}),s=t;for(let o=1;o<=e;o++){let l=s;s=Vc(l.length);for(let u=0;u<l.length;u++){let c=l[u];for(let f=0;f<c.length;f++){let d=c[f].partialPath,h=c[f].suffixDef,p=yl(d);if(Kp(i,p,u)||M(h)||d.length===e){let y=n[u];if(ia(y,d)===!1){y.push(d);for(let v=0;v<p.length;v++){let x=p[v];i[u][x]=!0}}}else{let y=ea(h,o+1,d);s[u]=s[u].concat(y),$(y,v=>{let x=yl(v.partialPath);$(x,A=>{i[u][A]=!0})})}}}}return n}a(Xc,"lookAheadSequenceFromAlternatives");function Cn(r,e,t,n){let i=new ra(r,ae.ALTERNATION,n);return e.accept(i),Xc(i.result,t)}a(Cn,"getLookaheadPathsForOr");function Sn(r,e,t,n){let i=new ra(r,t);e.accept(i);let s=i.result,l=new Tl(e,r,t).startWalking(),u=new J({definition:s}),c=new J({definition:l});return Xc([u,c],n)}a(Sn,"getLookaheadPathsForOptionalProd");function ia(r,e){e:for(let t=0;t<r.length;t++){let n=r[t];if(n.length===e.length){for(let i=0;i<n.length;i++){let s=e[i],o=n[i];if((s===o||o.categoryMatchesMap[s.tokenTypeIdx]!==void 0)===!1)continue e}return!0}}return!1}a(ia,"containsPath");function Yc(r,e){return r.length<e.length&&Pe(r,(t,n)=>{let i=e[n];return t===i||i.categoryMatchesMap[t.tokenTypeIdx]})}a(Yc,"isStrictPrefixOfPath");function Jc(r){return Pe(r,e=>Pe(e,t=>Pe(t,n=>M(n.categoryMatches))))}a(Jc,"areTokenCategoriesNotUsed");function Qc(r){let e=r.lookaheadStrategy.validate({rules:r.rules,tokenTypes:r.tokenTypes,grammarName:r.grammarName});return E(e,t=>Object.assign({type:ye.CUSTOM_LOOKAHEAD_VALIDATION},t))}a(Qc,"validateLookahead");function Zc(r,e,t,n){let i=Ie(r,u=>Vp(u,t)),s=Yp(r,e,t),o=Ie(r,u=>zp(u,t)),l=Ie(r,u=>Hp(u,r,n,t));return i.concat(s,o,l)}a(Zc,"validateGrammar");function Vp(r,e){let t=new Rl;r.accept(t);let n=t.allProductions,i=Hu(n,jp),s=Ve(i,l=>l.length>1);return E(re(s),l=>{let u=$e(l),c=e.buildDuplicateFoundError(r,l),f=We(u),d={message:c,type:ye.DUPLICATE_PRODUCTIONS,ruleName:r.name,dslName:f,occurrence:u.idx},h=ef(u);return h&&(d.parameter=h),d})}a(Vp,"validateDuplicateProductions");function jp(r){return`${We(r)}_#_${r.idx}_#_${ef(r)}`}a(jp,"identifyProductionForDuplicates");function ef(r){return r instanceof D?r.terminalType.name:r instanceof H?r.nonTerminalName:""}a(ef,"getExtraProductionArgument");var Rl=class extends De{static{a(this,"OccurrenceValidationCollector")}constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}};function Hp(r,e,t,n){let i=[];if(ue(e,(o,l)=>l.name===r.name?o+1:o,0)>1){let o=n.buildDuplicateRuleNameError({topLevelRule:r,grammarName:t});i.push({message:o,type:ye.DUPLICATE_RULE_NAME,ruleName:r.name})}return i}a(Hp,"validateRuleDoesNotAlreadyExist");function tf(r,e,t){let n=[],i;return oe(e,r)||(i=`Invalid rule override, rule: ->${r}<- cannot be overridden in the grammar: ->${t}<-as it is not defined in any of the super grammars `,n.push({message:i,type:ye.INVALID_RULE_OVERRIDE,ruleName:r})),n}a(tf,"validateRuleIsOverridden");function El(r,e,t,n=[]){let i=[],s=sa(e.definition);if(M(s))return[];{let o=r.name;oe(s,r)&&i.push({message:t.buildLeftRecursionError({topLevelRule:r,leftRecursionPath:n}),type:ye.LEFT_RECURSION,ruleName:o});let u=Xt(s,n.concat([r])),c=Ie(u,f=>{let d=te(n);return d.push(f),El(r,f,t,d)});return i.concat(c)}}a(El,"validateNoLeftRecursion");function sa(r){let e=[];if(M(r))return e;let t=$e(r);if(t instanceof H)e.push(t.referencedRule);else if(t instanceof J||t instanceof z||t instanceof Q||t instanceof Z||t instanceof q||t instanceof G)e=e.concat(sa(t.definition));else if(t instanceof X)e=he(E(t.definition,s=>sa(s.definition)));else if(!(t instanceof D))throw Error("non exhaustive match");let n=ur(t),i=r.length>1;if(n&&i){let s=pe(r);return e.concat(sa(s))}else return e}a(sa,"getFirstNoneTerminal");var Ci=class extends De{static{a(this,"OrCollector")}constructor(){super(...arguments),this.alternations=[]}visitAlternation(e){this.alternations.push(e)}};function rf(r,e){let t=new Ci;r.accept(t);let n=t.alternations;return Ie(n,s=>{let o=Ft(s.definition);return Ie(o,(l,u)=>{let c=ta([l],[],Ct,1);return M(c)?[{message:e.buildEmptyAlternationError({topLevelRule:r,alternation:s,emptyChoiceIdx:u}),type:ye.NONE_LAST_EMPTY_ALT,ruleName:r.name,occurrence:s.idx,alternative:u+1}]:[]})})}a(rf,"validateEmptyOrAlternative");function nf(r,e,t){let n=new Ci;r.accept(n);let i=n.alternations;return i=Yt(i,o=>o.ignoreAmbiguities===!0),Ie(i,o=>{let l=o.idx,u=o.maxLookahead||e,c=Cn(l,r,u,o),f=qp(c,o,r,t),d=Xp(c,o,r,t);return f.concat(d)})}a(nf,"validateAmbiguousAlternationAlternatives");var xl=class extends De{static{a(this,"RepetitionCollector")}constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}};function zp(r,e){let t=new Ci;r.accept(t);let n=t.alternations;return Ie(n,s=>s.definition.length>255?[{message:e.buildTooManyAlternativesError({topLevelRule:r,alternation:s}),type:ye.TOO_MANY_ALTS,ruleName:r.name,occurrence:s.idx}]:[])}a(zp,"validateTooManyAlts");function sf(r,e,t){let n=[];return $(r,i=>{let s=new xl;i.accept(s);let o=s.allProductions;$(o,l=>{let u=Ni(l),c=l.maxLookahead||e,f=l.idx,h=Sn(f,i,u,c)[0];if(M(he(h))){let p=t.buildEmptyRepetitionError({topLevelRule:i,repetition:l});n.push({message:p,type:ye.NO_NON_EMPTY_LOOKAHEAD,ruleName:i.name})}})}),n}a(sf,"validateSomeNonEmptyLookaheadPath");function qp(r,e,t,n){let i=[],s=ue(r,(l,u,c)=>(e.definition[c].ignoreAmbiguities===!0||$(u,f=>{let d=[c];$(r,(h,p)=>{c!==p&&ia(h,f)&&e.definition[p].ignoreAmbiguities!==!0&&d.push(p)}),d.length>1&&!ia(i,f)&&(i.push(f),l.push({alts:d,path:f}))}),l),[]);return E(s,l=>{let u=E(l.alts,f=>f+1);return{message:n.buildAlternationAmbiguityError({topLevelRule:t,alternation:e,ambiguityIndices:u,prefixPath:l.path}),type:ye.AMBIGUOUS_ALTS,ruleName:t.name,occurrence:e.idx,alternatives:l.alts}})}a(qp,"checkAlternativesAmbiguities");function Xp(r,e,t,n){let i=ue(r,(o,l,u)=>{let c=E(l,f=>({idx:u,path:f}));return o.concat(c)},[]);return At(Ie(i,o=>{if(e.definition[o.idx].ignoreAmbiguities===!0)return[];let u=o.idx,c=o.path,f=xe(i,h=>e.definition[h.idx].ignoreAmbiguities!==!0&&h.idx<u&&Yc(h.path,c));return E(f,h=>{let p=[h.idx+1,u+1],g=e.idx===0?"":e.idx;return{message:n.buildAlternationPrefixAmbiguityError({topLevelRule:t,alternation:e,ambiguityIndices:p,prefixPath:h.path}),type:ye.AMBIGUOUS_PREFIX_ALTS,ruleName:t.name,occurrence:g,alternatives:p}})}))}a(Xp,"checkPrefixAlternativesAmbiguities");function Yp(r,e,t){let n=[],i=E(e,s=>s.name);return $(r,s=>{let o=s.name;if(oe(i,o)){let l=t.buildNamespaceConflictError(s);n.push({message:l,type:ye.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:o})}}),n}a(Yp,"checkTerminalAndNoneTerminalsNameSpace");function af(r){let e=Jn(r,{errMsgProvider:Wc}),t={};return $(r.rules,n=>{t[n.name]=n}),Kc(t,e.errMsgProvider)}a(af,"resolveGrammar");function of(r){return r=Jn(r,{errMsgProvider:it}),Zc(r.rules,r.tokenTypes,r.errMsgProvider,r.grammarName)}a(of,"validateGrammar");var lf="MismatchedTokenException",uf="NoViableAltException",cf="EarlyExitException",ff="NotAllInputParsedException",df=[lf,uf,cf,ff];Object.freeze(df);function Wt(r){return oe(df,r.name)}a(Wt,"isRecognitionException");var wn=class extends Error{static{a(this,"RecognitionException")}constructor(e,t){super(e),this.token=t,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}},dr=class extends wn{static{a(this,"MismatchedTokenException")}constructor(e,t,n){super(e,t),this.previousToken=n,this.name=lf}},Si=class extends wn{static{a(this,"NoViableAltException")}constructor(e,t,n){super(e,t),this.previousToken=n,this.name=uf}},wi=class extends wn{static{a(this,"NotAllInputParsedException")}constructor(e,t){super(e,t),this.name=ff}},_i=class extends wn{static{a(this,"EarlyExitException")}constructor(e,t,n){super(e,t),this.previousToken=n,this.name=cf}};var Al={},kl="InRuleRecoveryException",vl=class extends Error{static{a(this,"InRuleRecoveryException")}constructor(e){super(e),this.name=kl}},aa=class{static{a(this,"Recoverable")}initRecoverable(e){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=I(e,"recoveryEnabled")?e.recoveryEnabled:Fe.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=Jp)}getTokenToInsert(e){let t=_t(e,"",NaN,NaN,NaN,NaN,NaN,NaN);return t.isInsertedInRecovery=!0,t}canTokenTypeBeInsertedInRecovery(e){return!0}canTokenTypeBeDeletedInRecovery(e){return!0}tryInRepetitionRecovery(e,t,n,i){let s=this.findReSyncTokenType(),o=this.exportLexerState(),l=[],u=!1,c=this.LA(1),f=this.LA(1),d=a(()=>{let h=this.LA(0),p=this.errorMessageProvider.buildMismatchTokenMessage({expected:i,actual:c,previous:h,ruleName:this.getCurrRuleFullName()}),g=new dr(p,c,this.LA(0));g.resyncedTokens=Ft(l),this.SAVE_ERROR(g)},"generateErrorMessage");for(;!u;)if(this.tokenMatcher(f,i)){d();return}else if(n.call(this)){d(),e.apply(this,t);return}else this.tokenMatcher(f,s)?u=!0:(f=this.SKIP_TOKEN(),this.addToResyncTokens(f,l));this.importLexerState(o)}shouldInRepetitionRecoveryBeTried(e,t,n){return!(n===!1||this.tokenMatcher(this.LA(1),e)||this.isBackTracking()||this.canPerformInRuleRecovery(e,this.getFollowsForInRuleRecovery(e,t)))}getFollowsForInRuleRecovery(e,t){let n=this.getCurrentGrammarPath(e,t);return this.getNextPossibleTokenTypes(n)}tryInRuleRecovery(e,t){if(this.canRecoverWithSingleTokenInsertion(e,t))return this.getTokenToInsert(e);if(this.canRecoverWithSingleTokenDeletion(e)){let n=this.SKIP_TOKEN();return this.consumeToken(),n}throw new vl("sad sad panda")}canPerformInRuleRecovery(e,t){return this.canRecoverWithSingleTokenInsertion(e,t)||this.canRecoverWithSingleTokenDeletion(e)}canRecoverWithSingleTokenInsertion(e,t){if(!this.canTokenTypeBeInsertedInRecovery(e)||M(t))return!1;let n=this.LA(1);return kt(t,s=>this.tokenMatcher(n,s))!==void 0}canRecoverWithSingleTokenDeletion(e){return this.canTokenTypeBeDeletedInRecovery(e)?this.tokenMatcher(this.LA(2),e):!1}isInCurrentRuleReSyncSet(e){let t=this.getCurrFollowKey(),n=this.getFollowSetFromFollowKey(t);return oe(n,e)}findReSyncTokenType(){let e=this.flattenFollowSet(),t=this.LA(1),n=2;for(;;){let i=kt(e,s=>ki(t,s));if(i!==void 0)return i;t=this.LA(n),n++}}getCurrFollowKey(){if(this.RULE_STACK.length===1)return Al;let e=this.getLastExplicitRuleShortName(),t=this.getLastExplicitRuleOccurrenceIndex(),n=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(e),idxInCallingRule:t,inRule:this.shortRuleNameToFullName(n)}}buildFullFollowKeyStack(){let e=this.RULE_STACK,t=this.RULE_OCCURRENCE_STACK;return E(e,(n,i)=>i===0?Al:{ruleName:this.shortRuleNameToFullName(n),idxInCallingRule:t[i],inRule:this.shortRuleNameToFullName(e[i-1])})}flattenFollowSet(){let e=E(this.buildFullFollowKeyStack(),t=>this.getFollowSetFromFollowKey(t));return he(e)}getFollowSetFromFollowKey(e){if(e===Al)return[qe];let t=e.ruleName+e.idxInCallingRule+js+e.inRule;return this.resyncFollows[t]}addToResyncTokens(e,t){return this.tokenMatcher(e,qe)||t.push(e),t}reSyncTo(e){let t=[],n=this.LA(1);for(;this.tokenMatcher(n,e)===!1;)n=this.SKIP_TOKEN(),this.addToResyncTokens(n,t);return Ft(t)}attemptInRepetitionRecovery(e,t,n,i,s,o,l){}getCurrentGrammarPath(e,t){let n=this.getHumanReadableRuleStack(),i=te(this.RULE_OCCURRENCE_STACK);return{ruleStack:n,occurrenceStack:i,lastTok:e,lastTokOccurrence:t}}getHumanReadableRuleStack(){return E(this.RULE_STACK,e=>this.shortRuleNameToFullName(e))}};function Jp(r,e,t,n,i,s,o){let l=this.getKeyForAutomaticLookahead(n,i),u=this.firstAfterRepMap[l];if(u===void 0){let h=this.getCurrRuleFullName(),p=this.getGAstProductions()[h];u=new s(p,i).startWalking(),this.firstAfterRepMap[l]=u}let c=u.token,f=u.occurrence,d=u.isEndOfRule;this.RULE_STACK.length===1&&d&&c===void 0&&(c=qe,f=1),!(c===void 0||f===void 0)&&this.shouldInRepetitionRecoveryBeTried(c,f,o)&&this.tryInRepetitionRecovery(r,e,t,c)}a(Jp,"attemptInRepetitionRecovery");function oa(r,e,t){return t|e|r}a(oa,"getKeyForAutomaticLookahead");var Ot=class{static{a(this,"LLkLookaheadStrategy")}constructor(e){var t;this.maxLookahead=(t=e?.maxLookahead)!==null&&t!==void 0?t:Fe.maxLookahead}validate(e){let t=this.validateNoLeftRecursion(e.rules);if(M(t)){let n=this.validateEmptyOrAlternatives(e.rules),i=this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead),s=this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead);return[...t,...n,...i,...s]}return t}validateNoLeftRecursion(e){return Ie(e,t=>El(t,t,it))}validateEmptyOrAlternatives(e){return Ie(e,t=>rf(t,it))}validateAmbiguousAlternationAlternatives(e,t){return Ie(e,n=>nf(n,t,it))}validateSomeNonEmptyLookaheadPath(e,t){return sf(e,t,it)}buildLookaheadForAlternation(e){return jc(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,zc)}buildLookaheadForOptional(e){return Hc(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,Ni(e.prodType),qc)}};var ua=class{static{a(this,"LooksAhead")}initLooksAhead(e){this.dynamicTokensEnabled=I(e,"dynamicTokensEnabled")?e.dynamicTokensEnabled:Fe.dynamicTokensEnabled,this.maxLookahead=I(e,"maxLookahead")?e.maxLookahead:Fe.maxLookahead,this.lookaheadStrategy=I(e,"lookaheadStrategy")?e.lookaheadStrategy:new Ot({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(e){$(e,t=>{this.TRACE_INIT(`${t.name} Rule Lookahead`,()=>{let{alternation:n,repetition:i,option:s,repetitionMandatory:o,repetitionMandatoryWithSeparator:l,repetitionWithSeparator:u}=Qp(t);$(n,c=>{let f=c.idx===0?"":c.idx;this.TRACE_INIT(`${We(c)}${f}`,()=>{let d=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:c.idx,rule:t,maxLookahead:c.maxLookahead||this.maxLookahead,hasPredicates:c.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),h=oa(this.fullRuleNameToShort[t.name],256,c.idx);this.setLaFuncCache(h,d)})}),$(i,c=>{this.computeLookaheadFunc(t,c.idx,768,"Repetition",c.maxLookahead,We(c))}),$(s,c=>{this.computeLookaheadFunc(t,c.idx,512,"Option",c.maxLookahead,We(c))}),$(o,c=>{this.computeLookaheadFunc(t,c.idx,1024,"RepetitionMandatory",c.maxLookahead,We(c))}),$(l,c=>{this.computeLookaheadFunc(t,c.idx,1536,"RepetitionMandatoryWithSeparator",c.maxLookahead,We(c))}),$(u,c=>{this.computeLookaheadFunc(t,c.idx,1280,"RepetitionWithSeparator",c.maxLookahead,We(c))})})})}computeLookaheadFunc(e,t,n,i,s,o){this.TRACE_INIT(`${o}${t===0?"":t}`,()=>{let l=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:t,rule:e,maxLookahead:s||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:i}),u=oa(this.fullRuleNameToShort[e.name],n,t);this.setLaFuncCache(u,l)})}getKeyForAutomaticLookahead(e,t){let n=this.getLastExplicitRuleShortName();return oa(n,e,t)}getLaFuncFromCache(e){return this.lookAheadFuncsCache.get(e)}setLaFuncCache(e,t){this.lookAheadFuncsCache.set(e,t)}},$l=class extends De{static{a(this,"DslMethodsCollectorVisitor")}constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(e){this.dslMethods.option.push(e)}visitRepetitionWithSeparator(e){this.dslMethods.repetitionWithSeparator.push(e)}visitRepetitionMandatory(e){this.dslMethods.repetitionMandatory.push(e)}visitRepetitionMandatoryWithSeparator(e){this.dslMethods.repetitionMandatoryWithSeparator.push(e)}visitRepetition(e){this.dslMethods.repetition.push(e)}visitAlternation(e){this.dslMethods.alternation.push(e)}},la=new $l;function Qp(r){la.reset(),r.accept(la);let e=la.dslMethods;return la.reset(),e}a(Qp,"collectMethods");function Cl(r,e){isNaN(r.startOffset)===!0?(r.startOffset=e.startOffset,r.endOffset=e.endOffset):r.endOffset<e.endOffset&&(r.endOffset=e.endOffset)}a(Cl,"setNodeLocationOnlyOffset");function Sl(r,e){isNaN(r.startOffset)===!0?(r.startOffset=e.startOffset,r.startColumn=e.startColumn,r.startLine=e.startLine,r.endOffset=e.endOffset,r.endColumn=e.endColumn,r.endLine=e.endLine):r.endOffset<e.endOffset&&(r.endOffset=e.endOffset,r.endColumn=e.endColumn,r.endLine=e.endLine)}a(Sl,"setNodeLocationFull");function hf(r,e,t){r.children[t]===void 0?r.children[t]=[e]:r.children[t].push(e)}a(hf,"addTerminalToCst");function pf(r,e,t){r.children[e]===void 0?r.children[e]=[t]:r.children[e].push(t)}a(pf,"addNoneTerminalToCst");var Zp="name";function wl(r,e){Object.defineProperty(r,Zp,{enumerable:!1,configurable:!0,writable:!1,value:e})}a(wl,"defineNameProp");function em(r,e){let t=Et(r),n=t.length;for(let i=0;i<n;i++){let s=t[i],o=r[s],l=o.length;for(let u=0;u<l;u++){let c=o[u];c.tokenTypeIdx===void 0&&this[c.name](c.children,e)}}}a(em,"defaultVisit");function mf(r,e){let t=a(function(){},"derivedConstructor");wl(t,r+"BaseSemantics");let n={visit:a(function(i,s){if(ge(i)&&(i=i[0]),!be(i))return this[i.name](i.children,s)},"visit"),validateVisitor:a(function(){let i=tm(this,e);if(!M(i)){let s=E(i,o=>o.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${s.join(`

`).replace(/\n/g,`
	`)}`)}},"validateVisitor")};return t.prototype=n,t.prototype.constructor=t,t._RULE_NAMES=e,t}a(mf,"createBaseSemanticVisitorConstructor");function gf(r,e,t){let n=a(function(){},"derivedConstructor");wl(n,r+"BaseSemanticsWithDefaults");let i=Object.create(t.prototype);return $(e,s=>{i[s]=em}),n.prototype=i,n.prototype.constructor=n,n}a(gf,"createBaseVisitorConstructorWithDefaults");var _l;(function(r){r[r.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",r[r.MISSING_METHOD=1]="MISSING_METHOD"})(_l||(_l={}));function tm(r,e){return rm(r,e)}a(tm,"validateVisitor");function rm(r,e){let t=xe(e,i=>ot(r[i])===!1),n=E(t,i=>({msg:`Missing visitor method: <${i}> on ${r.constructor.name} CST Visitor.`,type:_l.MISSING_METHOD,methodName:i}));return At(n)}a(rm,"validateMissingCstMethods");var ha=class{static{a(this,"TreeBuilder")}initTreeBuilder(e){if(this.CST_STACK=[],this.outputCst=e.outputCst,this.nodeLocationTracking=I(e,"nodeLocationTracking")?e.nodeLocationTracking:Fe.nodeLocationTracking,!this.outputCst)this.cstInvocationStateUpdate=de,this.cstFinallyStateUpdate=de,this.cstPostTerminal=de,this.cstPostNonTerminal=de,this.cstPostRule=de;else if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Sl,this.setNodeLocationFromNode=Sl,this.cstPostRule=de,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=de,this.setNodeLocationFromNode=de,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Cl,this.setNodeLocationFromNode=Cl,this.cstPostRule=de,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=de,this.setNodeLocationFromNode=de,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else if(/none/i.test(this.nodeLocationTracking))this.setNodeLocationFromToken=de,this.setNodeLocationFromNode=de,this.cstPostRule=de,this.setInitialNodeLocation=de;else throw Error(`Invalid <nodeLocationTracking> config option: "${e.nodeLocationTracking}"`)}setInitialNodeLocationOnlyOffsetRecovery(e){e.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(e){e.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(e){e.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(e){let t=this.LA(1);e.location={startOffset:t.startOffset,startLine:t.startLine,startColumn:t.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(e){let t={name:e,children:Object.create(null)};this.setInitialNodeLocation(t),this.CST_STACK.push(t)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(e){let t=this.LA(0),n=e.location;n.startOffset<=t.startOffset?(n.endOffset=t.endOffset,n.endLine=t.endLine,n.endColumn=t.endColumn):(n.startOffset=NaN,n.startLine=NaN,n.startColumn=NaN)}cstPostRuleOnlyOffset(e){let t=this.LA(0),n=e.location;n.startOffset<=t.startOffset?n.endOffset=t.endOffset:n.startOffset=NaN}cstPostTerminal(e,t){let n=this.CST_STACK[this.CST_STACK.length-1];hf(n,t,e),this.setNodeLocationFromToken(n.location,t)}cstPostNonTerminal(e,t){let n=this.CST_STACK[this.CST_STACK.length-1];pf(n,t,e),this.setNodeLocationFromNode(n.location,e.location)}getBaseCstVisitorConstructor(){if(be(this.baseCstVisitorConstructor)){let e=mf(this.className,Et(this.gastProductionsCache));return this.baseCstVisitorConstructor=e,e}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if(be(this.baseCstVisitorWithDefaultsConstructor)){let e=gf(this.className,Et(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=e,e}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){let e=this.RULE_STACK;return e[e.length-1]}getPreviousExplicitRuleShortName(){let e=this.RULE_STACK;return e[e.length-2]}getLastExplicitRuleOccurrenceIndex(){let e=this.RULE_OCCURRENCE_STACK;return e[e.length-1]}};var pa=class{static{a(this,"LexerAdapter")}initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(e){if(this.selfAnalysisDone!==!0)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=e,this.tokVectorLength=e.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):_n}LA(e){let t=this.currIdx+e;return t<0||this.tokVectorLength<=t?_n:this.tokVector[t]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(e){this.currIdx=e}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}};var ma=class{static{a(this,"RecognizerApi")}ACTION(e){return e.call(this)}consume(e,t,n){return this.consumeInternal(t,e,n)}subrule(e,t,n){return this.subruleInternal(t,e,n)}option(e,t){return this.optionInternal(t,e)}or(e,t){return this.orInternal(t,e)}many(e,t){return this.manyInternal(e,t)}atLeastOne(e,t){return this.atLeastOneInternal(e,t)}CONSUME(e,t){return this.consumeInternal(e,0,t)}CONSUME1(e,t){return this.consumeInternal(e,1,t)}CONSUME2(e,t){return this.consumeInternal(e,2,t)}CONSUME3(e,t){return this.consumeInternal(e,3,t)}CONSUME4(e,t){return this.consumeInternal(e,4,t)}CONSUME5(e,t){return this.consumeInternal(e,5,t)}CONSUME6(e,t){return this.consumeInternal(e,6,t)}CONSUME7(e,t){return this.consumeInternal(e,7,t)}CONSUME8(e,t){return this.consumeInternal(e,8,t)}CONSUME9(e,t){return this.consumeInternal(e,9,t)}SUBRULE(e,t){return this.subruleInternal(e,0,t)}SUBRULE1(e,t){return this.subruleInternal(e,1,t)}SUBRULE2(e,t){return this.subruleInternal(e,2,t)}SUBRULE3(e,t){return this.subruleInternal(e,3,t)}SUBRULE4(e,t){return this.subruleInternal(e,4,t)}SUBRULE5(e,t){return this.subruleInternal(e,5,t)}SUBRULE6(e,t){return this.subruleInternal(e,6,t)}SUBRULE7(e,t){return this.subruleInternal(e,7,t)}SUBRULE8(e,t){return this.subruleInternal(e,8,t)}SUBRULE9(e,t){return this.subruleInternal(e,9,t)}OPTION(e){return this.optionInternal(e,0)}OPTION1(e){return this.optionInternal(e,1)}OPTION2(e){return this.optionInternal(e,2)}OPTION3(e){return this.optionInternal(e,3)}OPTION4(e){return this.optionInternal(e,4)}OPTION5(e){return this.optionInternal(e,5)}OPTION6(e){return this.optionInternal(e,6)}OPTION7(e){return this.optionInternal(e,7)}OPTION8(e){return this.optionInternal(e,8)}OPTION9(e){return this.optionInternal(e,9)}OR(e){return this.orInternal(e,0)}OR1(e){return this.orInternal(e,1)}OR2(e){return this.orInternal(e,2)}OR3(e){return this.orInternal(e,3)}OR4(e){return this.orInternal(e,4)}OR5(e){return this.orInternal(e,5)}OR6(e){return this.orInternal(e,6)}OR7(e){return this.orInternal(e,7)}OR8(e){return this.orInternal(e,8)}OR9(e){return this.orInternal(e,9)}MANY(e){this.manyInternal(0,e)}MANY1(e){this.manyInternal(1,e)}MANY2(e){this.manyInternal(2,e)}MANY3(e){this.manyInternal(3,e)}MANY4(e){this.manyInternal(4,e)}MANY5(e){this.manyInternal(5,e)}MANY6(e){this.manyInternal(6,e)}MANY7(e){this.manyInternal(7,e)}MANY8(e){this.manyInternal(8,e)}MANY9(e){this.manyInternal(9,e)}MANY_SEP(e){this.manySepFirstInternal(0,e)}MANY_SEP1(e){this.manySepFirstInternal(1,e)}MANY_SEP2(e){this.manySepFirstInternal(2,e)}MANY_SEP3(e){this.manySepFirstInternal(3,e)}MANY_SEP4(e){this.manySepFirstInternal(4,e)}MANY_SEP5(e){this.manySepFirstInternal(5,e)}MANY_SEP6(e){this.manySepFirstInternal(6,e)}MANY_SEP7(e){this.manySepFirstInternal(7,e)}MANY_SEP8(e){this.manySepFirstInternal(8,e)}MANY_SEP9(e){this.manySepFirstInternal(9,e)}AT_LEAST_ONE(e){this.atLeastOneInternal(0,e)}AT_LEAST_ONE1(e){return this.atLeastOneInternal(1,e)}AT_LEAST_ONE2(e){this.atLeastOneInternal(2,e)}AT_LEAST_ONE3(e){this.atLeastOneInternal(3,e)}AT_LEAST_ONE4(e){this.atLeastOneInternal(4,e)}AT_LEAST_ONE5(e){this.atLeastOneInternal(5,e)}AT_LEAST_ONE6(e){this.atLeastOneInternal(6,e)}AT_LEAST_ONE7(e){this.atLeastOneInternal(7,e)}AT_LEAST_ONE8(e){this.atLeastOneInternal(8,e)}AT_LEAST_ONE9(e){this.atLeastOneInternal(9,e)}AT_LEAST_ONE_SEP(e){this.atLeastOneSepFirstInternal(0,e)}AT_LEAST_ONE_SEP1(e){this.atLeastOneSepFirstInternal(1,e)}AT_LEAST_ONE_SEP2(e){this.atLeastOneSepFirstInternal(2,e)}AT_LEAST_ONE_SEP3(e){this.atLeastOneSepFirstInternal(3,e)}AT_LEAST_ONE_SEP4(e){this.atLeastOneSepFirstInternal(4,e)}AT_LEAST_ONE_SEP5(e){this.atLeastOneSepFirstInternal(5,e)}AT_LEAST_ONE_SEP6(e){this.atLeastOneSepFirstInternal(6,e)}AT_LEAST_ONE_SEP7(e){this.atLeastOneSepFirstInternal(7,e)}AT_LEAST_ONE_SEP8(e){this.atLeastOneSepFirstInternal(8,e)}AT_LEAST_ONE_SEP9(e){this.atLeastOneSepFirstInternal(9,e)}RULE(e,t,n=Ln){if(oe(this.definedRulesNames,e)){let o={message:it.buildDuplicateRuleNameError({topLevelRule:e,grammarName:this.className}),type:ye.DUPLICATE_RULE_NAME,ruleName:e};this.definitionErrors.push(o)}this.definedRulesNames.push(e);let i=this.defineRule(e,t,n);return this[e]=i,i}OVERRIDE_RULE(e,t,n=Ln){let i=tf(e,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(i);let s=this.defineRule(e,t,n);return this[e]=s,s}BACKTRACK(e,t){return function(){this.isBackTrackingStack.push(1);let n=this.saveRecogState();try{return e.apply(this,t),!0}catch(i){if(Wt(i))return!1;throw i}finally{this.reloadRecogState(n),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return Vs(re(this.gastProductionsCache))}};var ga=class{static{a(this,"RecognizerEngine")}initRecognizerEngine(e,t){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=$n,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},I(t,"serializedGrammar"))throw Error(`The Parser's configuration can no longer contain a <serializedGrammar> property.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0
	For Further details.`);if(ge(e)){if(M(e))throw Error(`A Token Vocabulary cannot be empty.
	Note that the first argument for the parser constructor
	is no longer a Token vector (since v4.0).`);if(typeof e[0].startOffset=="number")throw Error(`The Parser constructor no longer accepts a token vector as the first argument.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0
	For Further details.`)}if(ge(e))this.tokensMap=ue(e,(s,o)=>(s[o.name]=o,s),{});else if(I(e,"modes")&&Pe(he(re(e.modes)),Oc)){let s=he(re(e.modes)),o=Qn(s);this.tokensMap=ue(o,(l,u)=>(l[u.name]=u,l),{})}else if(ju(e))this.tokensMap=te(e);else throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap.EOF=qe;let n=I(e,"modes")?he(re(e.modes)):re(e),i=Pe(n,s=>M(s.categoryMatches));this.tokenMatcher=i?$n:Ct,St(re(this.tokensMap))}defineRule(e,t,n){if(this.selfAnalysisDone)throw Error(`Grammar rule <${e}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);let i=I(n,"resyncEnabled")?n.resyncEnabled:Ln.resyncEnabled,s=I(n,"recoveryValueFunc")?n.recoveryValueFunc:Ln.recoveryValueFunc,o=this.ruleShortNameIdx<<12;this.ruleShortNameIdx++,this.shortRuleNameToFull[o]=e,this.fullRuleNameToShort[e]=o;let l;return this.outputCst===!0?l=a(function(...f){try{this.ruleInvocationStateUpdate(o,e,this.subruleIdx),t.apply(this,f);let d=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(d),d}catch(d){return this.invokeRuleCatch(d,i,s)}finally{this.ruleFinallyStateUpdate()}},"invokeRuleWithTry"):l=a(function(...f){try{return this.ruleInvocationStateUpdate(o,e,this.subruleIdx),t.apply(this,f)}catch(d){return this.invokeRuleCatch(d,i,s)}finally{this.ruleFinallyStateUpdate()}},"invokeRuleWithTryCst"),Object.assign(l,{ruleName:e,originalGrammarAction:t})}invokeRuleCatch(e,t,n){let i=this.RULE_STACK.length===1,s=t&&!this.isBackTracking()&&this.recoveryEnabled;if(Wt(e)){let o=e;if(s){let l=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(l))if(o.resyncedTokens=this.reSyncTo(l),this.outputCst){let u=this.CST_STACK[this.CST_STACK.length-1];return u.recoveredNode=!0,u}else return n(e);else{if(this.outputCst){let u=this.CST_STACK[this.CST_STACK.length-1];u.recoveredNode=!0,o.partialCstResult=u}throw o}}else{if(i)return this.moveToTerminatedState(),n(e);throw o}}else throw e}optionInternal(e,t){let n=this.getKeyForAutomaticLookahead(512,t);return this.optionInternalLogic(e,t,n)}optionInternalLogic(e,t,n){let i=this.getLaFuncFromCache(n),s;if(typeof e!="function"){s=e.DEF;let o=e.GATE;if(o!==void 0){let l=i;i=a(()=>o.call(this)&&l.call(this),"lookAheadFunc")}}else s=e;if(i.call(this)===!0)return s.call(this)}atLeastOneInternal(e,t){let n=this.getKeyForAutomaticLookahead(1024,e);return this.atLeastOneInternalLogic(e,t,n)}atLeastOneInternalLogic(e,t,n){let i=this.getLaFuncFromCache(n),s;if(typeof t!="function"){s=t.DEF;let o=t.GATE;if(o!==void 0){let l=i;i=a(()=>o.call(this)&&l.call(this),"lookAheadFunc")}}else s=t;if(i.call(this)===!0){let o=this.doSingleRepetition(s);for(;i.call(this)===!0&&o===!0;)o=this.doSingleRepetition(s)}else throw this.raiseEarlyExitException(e,ae.REPETITION_MANDATORY,t.ERR_MSG);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[e,t],i,1024,e,Zs)}atLeastOneSepFirstInternal(e,t){let n=this.getKeyForAutomaticLookahead(1536,e);this.atLeastOneSepFirstInternalLogic(e,t,n)}atLeastOneSepFirstInternalLogic(e,t,n){let i=t.DEF,s=t.SEP;if(this.getLaFuncFromCache(n).call(this)===!0){i.call(this);let l=a(()=>this.tokenMatcher(this.LA(1),s),"separatorLookAheadFunc");for(;this.tokenMatcher(this.LA(1),s)===!0;)this.CONSUME(s),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,s,l,i,Ii],l,1536,e,Ii)}else throw this.raiseEarlyExitException(e,ae.REPETITION_MANDATORY_WITH_SEPARATOR,t.ERR_MSG)}manyInternal(e,t){let n=this.getKeyForAutomaticLookahead(768,e);return this.manyInternalLogic(e,t,n)}manyInternalLogic(e,t,n){let i=this.getLaFuncFromCache(n),s;if(typeof t!="function"){s=t.DEF;let l=t.GATE;if(l!==void 0){let u=i;i=a(()=>l.call(this)&&u.call(this),"lookaheadFunction")}}else s=t;let o=!0;for(;i.call(this)===!0&&o===!0;)o=this.doSingleRepetition(s);this.attemptInRepetitionRecovery(this.manyInternal,[e,t],i,768,e,Qs,o)}manySepFirstInternal(e,t){let n=this.getKeyForAutomaticLookahead(1280,e);this.manySepFirstInternalLogic(e,t,n)}manySepFirstInternalLogic(e,t,n){let i=t.DEF,s=t.SEP;if(this.getLaFuncFromCache(n).call(this)===!0){i.call(this);let l=a(()=>this.tokenMatcher(this.LA(1),s),"separatorLookAheadFunc");for(;this.tokenMatcher(this.LA(1),s)===!0;)this.CONSUME(s),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,s,l,i,$i],l,1280,e,$i)}}repetitionSepSecondInternal(e,t,n,i,s){for(;n();)this.CONSUME(t),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,t,n,i,s],n,1536,e,s)}doSingleRepetition(e){let t=this.getLexerPosition();return e.call(this),this.getLexerPosition()>t}orInternal(e,t){let n=this.getKeyForAutomaticLookahead(256,t),i=ge(e)?e:e.DEF,o=this.getLaFuncFromCache(n).call(this,i);if(o!==void 0)return i[o].ALT.call(this);this.raiseNoAltException(t,e.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),this.RULE_STACK.length===0&&this.isAtEndOfInput()===!1){let e=this.LA(1),t=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:e,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new wi(t,e))}}subruleInternal(e,t,n){let i;try{let s=n!==void 0?n.ARGS:void 0;return this.subruleIdx=t,i=e.apply(this,s),this.cstPostNonTerminal(i,n!==void 0&&n.LABEL!==void 0?n.LABEL:e.ruleName),i}catch(s){throw this.subruleInternalError(s,n,e.ruleName)}}subruleInternalError(e,t,n){throw Wt(e)&&e.partialCstResult!==void 0&&(this.cstPostNonTerminal(e.partialCstResult,t!==void 0&&t.LABEL!==void 0?t.LABEL:n),delete e.partialCstResult),e}consumeInternal(e,t,n){let i;try{let s=this.LA(1);this.tokenMatcher(s,e)===!0?(this.consumeToken(),i=s):this.consumeInternalError(e,s,n)}catch(s){i=this.consumeInternalRecovery(e,t,s)}return this.cstPostTerminal(n!==void 0&&n.LABEL!==void 0?n.LABEL:e.name,i),i}consumeInternalError(e,t,n){let i,s=this.LA(0);throw n!==void 0&&n.ERR_MSG?i=n.ERR_MSG:i=this.errorMessageProvider.buildMismatchTokenMessage({expected:e,actual:t,previous:s,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new dr(i,t,s))}consumeInternalRecovery(e,t,n){if(this.recoveryEnabled&&n.name==="MismatchedTokenException"&&!this.isBackTracking()){let i=this.getFollowsForInRuleRecovery(e,t);try{return this.tryInRuleRecovery(e,i)}catch(s){throw s.name===kl?n:s}}else throw n}saveRecogState(){let e=this.errors,t=te(this.RULE_STACK);return{errors:e,lexerState:this.exportLexerState(),RULE_STACK:t,CST_STACK:this.CST_STACK}}reloadRecogState(e){this.errors=e.errors,this.importLexerState(e.lexerState),this.RULE_STACK=e.RULE_STACK}ruleInvocationStateUpdate(e,t,n){this.RULE_OCCURRENCE_STACK.push(n),this.RULE_STACK.push(e),this.cstInvocationStateUpdate(t)}isBackTracking(){return this.isBackTrackingStack.length!==0}getCurrRuleFullName(){let e=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[e]}shortRuleNameToFullName(e){return this.shortRuleNameToFull[e]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),qe)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}};var ya=class{static{a(this,"ErrorHandler")}initErrorHandler(e){this._errors=[],this.errorMessageProvider=I(e,"errorMessageProvider")?e.errorMessageProvider:Fe.errorMessageProvider}SAVE_ERROR(e){if(Wt(e))return e.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:te(this.RULE_OCCURRENCE_STACK)},this._errors.push(e),e;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return te(this._errors)}set errors(e){this._errors=e}raiseEarlyExitException(e,t,n){let i=this.getCurrRuleFullName(),s=this.getGAstProductions()[i],l=Sn(e,s,t,this.maxLookahead)[0],u=[];for(let f=1;f<=this.maxLookahead;f++)u.push(this.LA(f));let c=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:l,actual:u,previous:this.LA(0),customUserDescription:n,ruleName:i});throw this.SAVE_ERROR(new _i(c,this.LA(1),this.LA(0)))}raiseNoAltException(e,t){let n=this.getCurrRuleFullName(),i=this.getGAstProductions()[n],s=Cn(e,i,this.maxLookahead),o=[];for(let c=1;c<=this.maxLookahead;c++)o.push(this.LA(c));let l=this.LA(0),u=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:s,actual:o,previous:l,customUserDescription:t,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new Si(u,this.LA(1),l))}};var Ta=class{static{a(this,"ContentAssist")}initContentAssist(){}computeContentAssist(e,t){let n=this.gastProductionsCache[e];if(be(n))throw Error(`Rule ->${e}<- does not exist in this grammar.`);return ta([n],t,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(e){let t=$e(e.ruleStack),i=this.getGAstProductions()[t];return new Js(i,e).startWalking()}};var Ea={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(Ea);var yf=!0,Tf=Math.pow(2,8)-1,xf=Bt({name:"RECORDING_PHASE_TOKEN",pattern:se.NA});St([xf]);var Ef=_t(xf,`This IToken indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,-1,-1,-1,-1,-1,-1);Object.freeze(Ef);var im={name:`This CSTNode indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,children:{}},Ra=class{static{a(this,"GastRecorder")}initGastRecorder(e){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let e=0;e<10;e++){let t=e>0?e:"";this[`CONSUME${t}`]=function(n,i){return this.consumeInternalRecord(n,e,i)},this[`SUBRULE${t}`]=function(n,i){return this.subruleInternalRecord(n,e,i)},this[`OPTION${t}`]=function(n){return this.optionInternalRecord(n,e)},this[`OR${t}`]=function(n){return this.orInternalRecord(n,e)},this[`MANY${t}`]=function(n){this.manyInternalRecord(e,n)},this[`MANY_SEP${t}`]=function(n){this.manySepFirstInternalRecord(e,n)},this[`AT_LEAST_ONE${t}`]=function(n){this.atLeastOneInternalRecord(e,n)},this[`AT_LEAST_ONE_SEP${t}`]=function(n){this.atLeastOneSepFirstInternalRecord(e,n)}}this.consume=function(e,t,n){return this.consumeInternalRecord(t,e,n)},this.subrule=function(e,t,n){return this.subruleInternalRecord(t,e,n)},this.option=function(e,t){return this.optionInternalRecord(t,e)},this.or=function(e,t){return this.orInternalRecord(t,e)},this.many=function(e,t){this.manyInternalRecord(e,t)},this.atLeastOne=function(e,t){this.atLeastOneInternalRecord(e,t)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{let e=this;for(let t=0;t<10;t++){let n=t>0?t:"";delete e[`CONSUME${n}`],delete e[`SUBRULE${n}`],delete e[`OPTION${n}`],delete e[`OR${n}`],delete e[`MANY${n}`],delete e[`MANY_SEP${n}`],delete e[`AT_LEAST_ONE${n}`],delete e[`AT_LEAST_ONE_SEP${n}`]}delete e.consume,delete e.subrule,delete e.option,delete e.or,delete e.many,delete e.atLeastOne,delete e.ACTION,delete e.BACKTRACK,delete e.LA})}ACTION_RECORD(e){}BACKTRACK_RECORD(e,t){return()=>!0}LA_RECORD(e){return _n}topLevelRuleRecord(e,t){try{let n=new Me({definition:[],name:e});return n.name=e,this.recordingProdStack.push(n),t.call(this),this.recordingProdStack.pop(),n}catch(n){if(n.KNOWN_RECORDER_ERROR!==!0)try{n.message=n.message+`
	 This error was thrown during the "grammar recording phase" For more info see:
	https://chevrotain.io/docs/guide/internals.html#grammar-recording`}catch{throw n}throw n}}optionInternalRecord(e,t){return Oi.call(this,z,e,t)}atLeastOneInternalRecord(e,t){Oi.call(this,Q,t,e)}atLeastOneSepFirstInternalRecord(e,t){Oi.call(this,Z,t,e,yf)}manyInternalRecord(e,t){Oi.call(this,G,t,e)}manySepFirstInternalRecord(e,t){Oi.call(this,q,t,e,yf)}orInternalRecord(e,t){return sm.call(this,e,t)}subruleInternalRecord(e,t,n){if(xa(t),!e||I(e,"ruleName")===!1){let l=new Error(`<SUBRULE${Rf(t)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw l.KNOWN_RECORDER_ERROR=!0,l}let i=vt(this.recordingProdStack),s=e.ruleName,o=new H({idx:t,nonTerminalName:s,label:n?.LABEL,referencedRule:void 0});return i.definition.push(o),this.outputCst?im:Ea}consumeInternalRecord(e,t,n){if(xa(t),!hl(e)){let o=new Error(`<CONSUME${Rf(t)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw o.KNOWN_RECORDER_ERROR=!0,o}let i=vt(this.recordingProdStack),s=new D({idx:t,terminalType:e,label:n?.LABEL});return i.definition.push(s),Ef}};function Oi(r,e,t,n=!1){xa(t);let i=vt(this.recordingProdStack),s=ot(e)?e:e.DEF,o=new r({definition:[],idx:t});return n&&(o.separator=e.SEP),I(e,"MAX_LOOKAHEAD")&&(o.maxLookahead=e.MAX_LOOKAHEAD),this.recordingProdStack.push(o),s.call(this),i.definition.push(o),this.recordingProdStack.pop(),Ea}a(Oi,"recordProd");function sm(r,e){xa(e);let t=vt(this.recordingProdStack),n=ge(r)===!1,i=n===!1?r:r.DEF,s=new X({definition:[],idx:e,ignoreAmbiguities:n&&r.IGNORE_AMBIGUITIES===!0});I(r,"MAX_LOOKAHEAD")&&(s.maxLookahead=r.MAX_LOOKAHEAD);let o=Is(i,l=>ot(l.GATE));return s.hasPredicates=o,t.definition.push(s),$(i,l=>{let u=new J({definition:[]});s.definition.push(u),I(l,"IGNORE_AMBIGUITIES")?u.ignoreAmbiguities=l.IGNORE_AMBIGUITIES:I(l,"GATE")&&(u.ignoreAmbiguities=!0),this.recordingProdStack.push(u),l.ALT.call(this),this.recordingProdStack.pop()}),Ea}a(sm,"recordOrProd");function Rf(r){return r===0?"":`${r}`}a(Rf,"getIdxSuffix");function xa(r){if(r<0||r>Tf){let e=new Error(`Invalid DSL Method idx value: <${r}>
	Idx value must be a none negative value smaller than ${Tf+1}`);throw e.KNOWN_RECORDER_ERROR=!0,e}}a(xa,"assertMethodIdxIsValid");var Aa=class{static{a(this,"PerformanceTracer")}initPerformanceTracer(e){if(I(e,"traceInitPerf")){let t=e.traceInitPerf,n=typeof t=="number";this.traceInitMaxIdent=n?t:1/0,this.traceInitPerf=n?t>0:t}else this.traceInitMaxIdent=0,this.traceInitPerf=Fe.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(e,t){if(this.traceInitPerf===!0){this.traceInitIndent++;let n=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${n}--> <${e}>`);let{time:i,value:s}=xi(t),o=i>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&o(`${n}<-- <${e}> time: ${i}ms`),this.traceInitIndent--,s}else return t()}};function Af(r,e){e.forEach(t=>{let n=t.prototype;Object.getOwnPropertyNames(n).forEach(i=>{if(i==="constructor")return;let s=Object.getOwnPropertyDescriptor(n,i);s&&(s.get||s.set)?Object.defineProperty(r.prototype,i,s):r.prototype[i]=t.prototype[i]})})}a(Af,"applyMixins");var _n=_t(qe,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(_n);var Fe=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:Lt,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),Ln=Object.freeze({recoveryValueFunc:a(()=>{},"recoveryValueFunc"),resyncEnabled:!0}),ye;(function(r){r[r.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",r[r.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",r[r.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",r[r.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",r[r.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",r[r.LEFT_RECURSION=5]="LEFT_RECURSION",r[r.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",r[r.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",r[r.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",r[r.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",r[r.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",r[r.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",r[r.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",r[r.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION"})(ye||(ye={}));function va(r=void 0){return function(){return r}}a(va,"EMPTY_ALT");var Pi=class r{static{a(this,"Parser")}static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{let e;this.selfAnalysisDone=!0;let t=this.className;this.TRACE_INIT("toFastProps",()=>{Ei(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),$(this.definedRulesNames,i=>{let o=this[i].originalGrammarAction,l;this.TRACE_INIT(`${i} Rule`,()=>{l=this.topLevelRuleRecord(i,o)}),this.gastProductionsCache[i]=l})}finally{this.disableRecording()}});let n=[];if(this.TRACE_INIT("Grammar Resolving",()=>{n=af({rules:re(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(n)}),this.TRACE_INIT("Grammar Validations",()=>{if(M(n)&&this.skipValidations===!1){let i=of({rules:re(this.gastProductionsCache),tokenTypes:re(this.tokensMap),errMsgProvider:it,grammarName:t}),s=Qc({lookaheadStrategy:this.lookaheadStrategy,rules:re(this.gastProductionsCache),tokenTypes:re(this.tokensMap),grammarName:t});this.definitionErrors=this.definitionErrors.concat(i,s)}}),M(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{let i=hc(re(this.gastProductionsCache));this.resyncFollows=i}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var i,s;(s=(i=this.lookaheadStrategy).initialize)===null||s===void 0||s.call(i,{rules:re(this.gastProductionsCache)}),this.preComputeLookaheadFunctions(re(this.gastProductionsCache))})),!r.DEFER_DEFINITION_ERRORS_HANDLING&&!M(this.definitionErrors))throw e=E(this.definitionErrors,i=>i.message),new Error(`Parser Definition Errors detected:
 ${e.join(`
-------------------------------
`)}`)})}constructor(e,t){this.definitionErrors=[],this.selfAnalysisDone=!1;let n=this;if(n.initErrorHandler(t),n.initLexerAdapter(),n.initLooksAhead(t),n.initRecognizerEngine(e,t),n.initRecoverable(t),n.initTreeBuilder(t),n.initContentAssist(),n.initGastRecorder(t),n.initPerformanceTracer(t),I(t,"ignoredIssues"))throw new Error(`The <ignoredIssues> IParserConfig property has been deprecated.
	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.
	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES
	For further details.`);this.skipValidations=I(t,"skipValidations")?t.skipValidations:Fe.skipValidations}};Pi.DEFER_DEFINITION_ERRORS_HANDLING=!1;Af(Pi,[aa,ua,ha,pa,ga,ma,ya,Ta,Ra,Aa]);var bi=class extends Pi{static{a(this,"EmbeddedActionsParser")}constructor(e,t=Fe){let n=te(t);n.outputCst=!1,super(e,n)}};function hr(r,e,t){return`${r.name}_${e}_${t}`}a(hr,"buildATNKey");var Kt=1,om=2,vf=4,kf=5;var bn=7,lm=8,um=9,cm=10,fm=11,$f=12,Mi=class{static{a(this,"AbstractTransition")}constructor(e){this.target=e}isEpsilon(){return!1}},On=class extends Mi{static{a(this,"AtomTransition")}constructor(e,t){super(e),this.tokenType=t}},Di=class extends Mi{static{a(this,"EpsilonTransition")}constructor(e){super(e)}isEpsilon(){return!0}},Pn=class extends Mi{static{a(this,"RuleTransition")}constructor(e,t,n){super(e),this.rule=t,this.followState=n}isEpsilon(){return!0}};function If(r){let e={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};dm(e,r);let t=r.length;for(let n=0;n<t;n++){let i=r[n],s=pr(e,i,i);s!==void 0&&vm(e,i,s)}return e}a(If,"createATN");function dm(r,e){let t=e.length;for(let n=0;n<t;n++){let i=e[n],s=Re(r,i,void 0,{type:om}),o=Re(r,i,void 0,{type:bn});s.stop=o,r.ruleToStartState.set(i,s),r.ruleToStopState.set(i,o)}}a(dm,"createRuleStartAndStopATNStates");function Nf(r,e,t){return t instanceof D?Ol(r,e,t.terminalType,t):t instanceof H?Am(r,e,t):t instanceof X?ym(r,e,t):t instanceof z?Tm(r,e,t):t instanceof G?hm(r,e,t):t instanceof q?pm(r,e,t):t instanceof Q?mm(r,e,t):t instanceof Z?gm(r,e,t):pr(r,e,t)}a(Nf,"atom");function hm(r,e,t){let n=Re(r,e,t,{type:kf});Vt(r,n);let i=Mn(r,e,n,t,pr(r,e,t));return Sf(r,e,t,i)}a(hm,"repetition");function pm(r,e,t){let n=Re(r,e,t,{type:kf});Vt(r,n);let i=Mn(r,e,n,t,pr(r,e,t)),s=Ol(r,e,t.separator,t);return Sf(r,e,t,i,s)}a(pm,"repetitionSep");function mm(r,e,t){let n=Re(r,e,t,{type:vf});Vt(r,n);let i=Mn(r,e,n,t,pr(r,e,t));return Cf(r,e,t,i)}a(mm,"repetitionMandatory");function gm(r,e,t){let n=Re(r,e,t,{type:vf});Vt(r,n);let i=Mn(r,e,n,t,pr(r,e,t)),s=Ol(r,e,t.separator,t);return Cf(r,e,t,i,s)}a(gm,"repetitionMandatorySep");function ym(r,e,t){let n=Re(r,e,t,{type:Kt});Vt(r,n);let i=E(t.definition,o=>Nf(r,e,o));return Mn(r,e,n,t,...i)}a(ym,"alternation");function Tm(r,e,t){let n=Re(r,e,t,{type:Kt});Vt(r,n);let i=Mn(r,e,n,t,pr(r,e,t));return Rm(r,e,t,i)}a(Tm,"option");function pr(r,e,t){let n=xe(E(t.definition,i=>Nf(r,e,i)),i=>i!==void 0);return n.length===1?n[0]:n.length===0?void 0:Em(r,n)}a(pr,"block");function Cf(r,e,t,n,i){let s=n.left,o=n.right,l=Re(r,e,t,{type:fm});Vt(r,l);let u=Re(r,e,t,{type:$f});return s.loopback=l,u.loopback=l,r.decisionMap[hr(e,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",t.idx)]=l,me(o,l),i===void 0?(me(l,s),me(l,u)):(me(l,u),me(l,i.left),me(i.right,s)),{left:s,right:u}}a(Cf,"plus");function Sf(r,e,t,n,i){let s=n.left,o=n.right,l=Re(r,e,t,{type:cm});Vt(r,l);let u=Re(r,e,t,{type:$f}),c=Re(r,e,t,{type:um});return l.loopback=c,u.loopback=c,me(l,s),me(l,u),me(o,c),i!==void 0?(me(c,u),me(c,i.left),me(i.right,s)):me(c,l),r.decisionMap[hr(e,i?"RepetitionWithSeparator":"Repetition",t.idx)]=l,{left:l,right:u}}a(Sf,"star");function Rm(r,e,t,n){let i=n.left,s=n.right;return me(i,s),r.decisionMap[hr(e,"Option",t.idx)]=i,n}a(Rm,"optional");function Vt(r,e){return r.decisionStates.push(e),e.decision=r.decisionStates.length-1,e.decision}a(Vt,"defineDecisionState");function Mn(r,e,t,n,...i){let s=Re(r,e,n,{type:lm,start:t});t.end=s;for(let l of i)l!==void 0?(me(t,l.left),me(l.right,s)):me(t,s);let o={left:t,right:s};return r.decisionMap[hr(e,xm(n),n.idx)]=t,o}a(Mn,"makeAlts");function xm(r){if(r instanceof X)return"Alternation";if(r instanceof z)return"Option";if(r instanceof G)return"Repetition";if(r instanceof q)return"RepetitionWithSeparator";if(r instanceof Q)return"RepetitionMandatory";if(r instanceof Z)return"RepetitionMandatoryWithSeparator";throw new Error("Invalid production type encountered")}a(xm,"getProdType");function Em(r,e){let t=e.length;for(let s=0;s<t-1;s++){let o=e[s],l;o.left.transitions.length===1&&(l=o.left.transitions[0]);let u=l instanceof Pn,c=l,f=e[s+1].left;o.left.type===Kt&&o.right.type===Kt&&l!==void 0&&(u&&c.followState===o.right||l.target===o.right)?(u?c.followState=f:l.target=f,km(r,o.right)):me(o.right,f)}let n=e[0],i=e[t-1];return{left:n.left,right:i.right}}a(Em,"makeBlock");function Ol(r,e,t,n){let i=Re(r,e,n,{type:Kt}),s=Re(r,e,n,{type:Kt});return Pl(i,new On(s,t)),{left:i,right:s}}a(Ol,"tokenRef");function Am(r,e,t){let n=t.referencedRule,i=r.ruleToStartState.get(n),s=Re(r,e,t,{type:Kt}),o=Re(r,e,t,{type:Kt}),l=new Pn(i,n,o);return Pl(s,l),{left:s,right:o}}a(Am,"ruleRef");function vm(r,e,t){let n=r.ruleToStartState.get(e);me(n,t.left);let i=r.ruleToStopState.get(e);return me(t.right,i),{left:n,right:i}}a(vm,"buildRuleHandle");function me(r,e){let t=new Di(e);Pl(r,t)}a(me,"epsilon");function Re(r,e,t,n){let i=Object.assign({atn:r,production:t,epsilonOnlyTransitions:!1,rule:e,transitions:[],nextTokenWithinRule:[],stateNumber:r.states.length},n);return r.states.push(i),i}a(Re,"newState");function Pl(r,e){r.transitions.length===0&&(r.epsilonOnlyTransitions=e.isEpsilon()),r.transitions.push(e)}a(Pl,"addTransition");function km(r,e){r.states.splice(r.states.indexOf(e),1)}a(km,"removeState");var Fi={},Dn=class{static{a(this,"ATNConfigSet")}constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){let t=bl(e);t in this.map||(this.map[t]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return E(this.configs,e=>e.alt)}get key(){let e="";for(let t in this.map)e+=t+":";return e}};function bl(r,e=!0){return`${e?`a${r.alt}`:""}s${r.state.stateNumber}:${r.stack.map(t=>t.stateNumber.toString()).join("_")}`}a(bl,"getATNConfigKey");function $m(r,e){let t={};return n=>{let i=n.toString(),s=t[i];return s!==void 0||(s={atnStartState:r,decision:e,states:{}},t[i]=s),s}}a($m,"createDFACache");var ka=class{static{a(this,"PredicateSet")}constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,t){this.predicates[e]=t}toString(){let e="",t=this.predicates.length;for(let n=0;n<t;n++)e+=this.predicates[n]===!0?"1":"0";return e}},wf=new ka,Gi=class extends Ot{static{a(this,"LLStarLookaheadStrategy")}constructor(e){var t;super(),this.logging=(t=e?.logging)!==null&&t!==void 0?t:n=>console.log(n)}initialize(e){this.atn=If(e.rules),this.dfas=Im(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){let{prodOccurrence:t,rule:n,hasPredicates:i,dynamicTokensEnabled:s}=e,o=this.dfas,l=this.logging,u=hr(n,"Alternation",t),f=this.atn.decisionMap[u].decision,d=E(na({maxLookahead:1,occurrence:t,prodType:"Alternation",rule:n}),h=>E(h,p=>p[0]));if(_f(d,!1)&&!s){let h=ue(d,(p,g,y)=>($(g,v=>{v&&(p[v.tokenTypeIdx]=y,$(v.categoryMatches,x=>{p[x]=y}))}),p),{});return i?function(p){var g;let y=this.LA(1),v=h[y.tokenTypeIdx];if(p!==void 0&&v!==void 0){let x=(g=p[v])===null||g===void 0?void 0:g.GATE;if(x!==void 0&&x.call(this)===!1)return}return v}:function(){let p=this.LA(1);return h[p.tokenTypeIdx]}}else return i?function(h){let p=new ka,g=h===void 0?0:h.length;for(let v=0;v<g;v++){let x=h?.[v].GATE;p.set(v,x===void 0||x.call(this))}let y=Ml.call(this,o,f,p,l);return typeof y=="number"?y:void 0}:function(){let h=Ml.call(this,o,f,wf,l);return typeof h=="number"?h:void 0}}buildLookaheadForOptional(e){let{prodOccurrence:t,rule:n,prodType:i,dynamicTokensEnabled:s}=e,o=this.dfas,l=this.logging,u=hr(n,i,t),f=this.atn.decisionMap[u].decision,d=E(na({maxLookahead:1,occurrence:t,prodType:i,rule:n}),h=>E(h,p=>p[0]));if(_f(d)&&d[0][0]&&!s){let h=d[0],p=he(h);if(p.length===1&&M(p[0].categoryMatches)){let y=p[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===y}}else{let g=ue(p,(y,v)=>(v!==void 0&&(y[v.tokenTypeIdx]=!0,$(v.categoryMatches,x=>{y[x]=!0})),y),{});return function(){let y=this.LA(1);return g[y.tokenTypeIdx]===!0}}}return function(){let h=Ml.call(this,o,f,wf,l);return typeof h=="object"?!1:h===0}}};function _f(r,e=!0){let t=new Set;for(let n of r){let i=new Set;for(let s of n){if(s===void 0){if(e)break;return!1}let o=[s.tokenTypeIdx].concat(s.categoryMatches);for(let l of o)if(t.has(l)){if(!i.has(l))return!1}else t.add(l),i.add(l)}}return!0}a(_f,"isLL1Sequence");function Im(r){let e=r.decisionStates.length,t=Array(e);for(let n=0;n<e;n++)t[n]=$m(r.decisionStates[n],n);return t}a(Im,"initATNSimulator");function Ml(r,e,t,n){let i=r[e](t),s=i.start;if(s===void 0){let l=Dm(i.atnStartState);s=Pf(i,Of(l)),i.start=s}return Nm.apply(this,[i,s,t,n])}a(Ml,"adaptivePredict");function Nm(r,e,t,n){let i=e,s=1,o=[],l=this.LA(s++);for(;;){let u=Om(i,l);if(u===void 0&&(u=Cm.apply(this,[r,i,l,s,t,n])),u===Fi)return Lm(o,i,l);if(u.isAcceptState===!0)return u.prediction;i=u,o.push(l),l=this.LA(s++)}}a(Nm,"performLookahead");function Cm(r,e,t,n,i,s){let o=Pm(e.configs,t,i);if(o.size===0)return Lf(r,e,t,Fi),Fi;let l=Of(o),u=Mm(o,i);if(u!==void 0)l.isAcceptState=!0,l.prediction=u,l.configs.uniqueAlt=u;else if(Bm(o)){let c=zu(o.alts);l.isAcceptState=!0,l.prediction=c,l.configs.uniqueAlt=c,Sm.apply(this,[r,n,o.alts,s])}return l=Lf(r,e,t,l),l}a(Cm,"computeLookaheadTarget");function Sm(r,e,t,n){let i=[];for(let c=1;c<=e;c++)i.push(this.LA(c).tokenType);let s=r.atnStartState,o=s.rule,l=s.production,u=wm({topLevelRule:o,ambiguityIndices:t,production:l,prefixPath:i});n(u)}a(Sm,"reportLookaheadAmbiguity");function wm(r){let e=E(r.prefixPath,i=>wt(i)).join(", "),t=r.production.idx===0?"":r.production.idx,n=`Ambiguous Alternatives Detected: <${r.ambiguityIndices.join(", ")}> in <${_m(r.production)}${t}> inside <${r.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return n=n+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,n}a(wm,"buildAmbiguityError");function _m(r){if(r instanceof H)return"SUBRULE";if(r instanceof z)return"OPTION";if(r instanceof X)return"OR";if(r instanceof Q)return"AT_LEAST_ONE";if(r instanceof Z)return"AT_LEAST_ONE_SEP";if(r instanceof q)return"MANY_SEP";if(r instanceof G)return"MANY";if(r instanceof D)return"CONSUME";throw Error("non exhaustive match")}a(_m,"getProductionDslName");function Lm(r,e,t){let n=Ie(e.configs.elements,s=>s.state.transitions),i=qu(n.filter(s=>s instanceof On).map(s=>s.tokenType),s=>s.tokenTypeIdx);return{actualToken:t,possibleTokenTypes:i,tokenPath:r}}a(Lm,"buildAdaptivePredictError");function Om(r,e){return r.edges[e.tokenTypeIdx]}a(Om,"getExistingTargetState");function Pm(r,e,t){let n=new Dn,i=[];for(let o of r.elements){if(t.is(o.alt)===!1)continue;if(o.state.type===bn){i.push(o);continue}let l=o.state.transitions.length;for(let u=0;u<l;u++){let c=o.state.transitions[u],f=bm(c,e);f!==void 0&&n.add({state:f,alt:o.alt,stack:o.stack})}}let s;if(i.length===0&&n.size===1&&(s=n),s===void 0){s=new Dn;for(let o of n.elements)$a(o,s)}if(i.length>0&&!Gm(s))for(let o of i)s.add(o);return s}a(Pm,"computeReachSet");function bm(r,e){if(r instanceof On&&ki(e,r.tokenType))return r.target}a(bm,"getReachableTarget");function Mm(r,e){let t;for(let n of r.elements)if(e.is(n.alt)===!0){if(t===void 0)t=n.alt;else if(t!==n.alt)return}return t}a(Mm,"getUniqueAlt");function Of(r){return{configs:r,edges:{},isAcceptState:!1,prediction:-1}}a(Of,"newDFAState");function Lf(r,e,t,n){return n=Pf(r,n),e.edges[t.tokenTypeIdx]=n,n}a(Lf,"addDFAEdge");function Pf(r,e){if(e===Fi)return e;let t=e.configs.key,n=r.states[t];return n!==void 0?n:(e.configs.finalize(),r.states[t]=e,e)}a(Pf,"addDFAState");function Dm(r){let e=new Dn,t=r.transitions.length;for(let n=0;n<t;n++){let s={state:r.transitions[n].target,alt:n,stack:[]};$a(s,e)}return e}a(Dm,"computeStartState");function $a(r,e){let t=r.state;if(t.type===bn){if(r.stack.length>0){let i=[...r.stack],o={state:i.pop(),alt:r.alt,stack:i};$a(o,e)}else e.add(r);return}t.epsilonOnlyTransitions||e.add(r);let n=t.transitions.length;for(let i=0;i<n;i++){let s=t.transitions[i],o=Fm(r,s);o!==void 0&&$a(o,e)}}a($a,"closure");function Fm(r,e){if(e instanceof Di)return{state:e.target,alt:r.alt,stack:r.stack};if(e instanceof Pn){let t=[...r.stack,e.followState];return{state:e.target,alt:r.alt,stack:t}}}a(Fm,"getEpsilonTarget");function Gm(r){for(let e of r.elements)if(e.state.type===bn)return!0;return!1}a(Gm,"hasConfigInRuleStopState");function Um(r){for(let e of r.elements)if(e.state.type!==bn)return!1;return!0}a(Um,"allConfigsInRuleStopStates");function Bm(r){if(Um(r))return!0;let e=Wm(r.elements);return Km(e)&&!Vm(e)}a(Bm,"hasConflictTerminatingPrediction");function Wm(r){let e=new Map;for(let t of r){let n=bl(t,!1),i=e.get(n);i===void 0&&(i={},e.set(n,i)),i[t.alt]=!0}return e}a(Wm,"getConflictingAltSets");function Km(r){for(let e of Array.from(r.values()))if(Object.keys(e).length>1)return!0;return!1}a(Km,"hasConflictingAltSet");function Vm(r){for(let e of Array.from(r.values()))if(Object.keys(e).length===1)return!0;return!1}a(Vm,"hasStateAssociatedWithOneAlt");var bf;(function(r){function e(t){return typeof t=="string"}a(e,"is"),r.is=e})(bf||(bf={}));var Dl;(function(r){function e(t){return typeof t=="string"}a(e,"is"),r.is=e})(Dl||(Dl={}));var Mf;(function(r){r.MIN_VALUE=-2147483648,r.MAX_VALUE=2147483647;function e(t){return typeof t=="number"&&r.MIN_VALUE<=t&&t<=r.MAX_VALUE}a(e,"is"),r.is=e})(Mf||(Mf={}));var Ia;(function(r){r.MIN_VALUE=0,r.MAX_VALUE=2147483647;function e(t){return typeof t=="number"&&r.MIN_VALUE<=t&&t<=r.MAX_VALUE}a(e,"is"),r.is=e})(Ia||(Ia={}));var W;(function(r){function e(n,i){return n===Number.MAX_VALUE&&(n=Ia.MAX_VALUE),i===Number.MAX_VALUE&&(i=Ia.MAX_VALUE),{line:n,character:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&m.uinteger(i.line)&&m.uinteger(i.character)}a(t,"is"),r.is=t})(W||(W={}));var U;(function(r){function e(n,i,s,o){if(m.uinteger(n)&&m.uinteger(i)&&m.uinteger(s)&&m.uinteger(o))return{start:W.create(n,i),end:W.create(s,o)};if(W.is(n)&&W.is(i))return{start:n,end:i};throw new Error(`Range#create called with invalid arguments[${n}, ${i}, ${s}, ${o}]`)}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&W.is(i.start)&&W.is(i.end)}a(t,"is"),r.is=t})(U||(U={}));var Na;(function(r){function e(n,i){return{uri:n,range:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&U.is(i.range)&&(m.string(i.uri)||m.undefined(i.uri))}a(t,"is"),r.is=t})(Na||(Na={}));var Df;(function(r){function e(n,i,s,o){return{targetUri:n,targetRange:i,targetSelectionRange:s,originSelectionRange:o}}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&U.is(i.targetRange)&&m.string(i.targetUri)&&U.is(i.targetSelectionRange)&&(U.is(i.originSelectionRange)||m.undefined(i.originSelectionRange))}a(t,"is"),r.is=t})(Df||(Df={}));var Fl;(function(r){function e(n,i,s,o){return{red:n,green:i,blue:s,alpha:o}}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&m.numberRange(i.red,0,1)&&m.numberRange(i.green,0,1)&&m.numberRange(i.blue,0,1)&&m.numberRange(i.alpha,0,1)}a(t,"is"),r.is=t})(Fl||(Fl={}));var Ff;(function(r){function e(n,i){return{range:n,color:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&U.is(i.range)&&Fl.is(i.color)}a(t,"is"),r.is=t})(Ff||(Ff={}));var Gf;(function(r){function e(n,i,s){return{label:n,textEdit:i,additionalTextEdits:s}}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&m.string(i.label)&&(m.undefined(i.textEdit)||Gn.is(i))&&(m.undefined(i.additionalTextEdits)||m.typedArray(i.additionalTextEdits,Gn.is))}a(t,"is"),r.is=t})(Gf||(Gf={}));var Uf;(function(r){r.Comment="comment",r.Imports="imports",r.Region="region"})(Uf||(Uf={}));var Bf;(function(r){function e(n,i,s,o,l,u){let c={startLine:n,endLine:i};return m.defined(s)&&(c.startCharacter=s),m.defined(o)&&(c.endCharacter=o),m.defined(l)&&(c.kind=l),m.defined(u)&&(c.collapsedText=u),c}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&m.uinteger(i.startLine)&&m.uinteger(i.startLine)&&(m.undefined(i.startCharacter)||m.uinteger(i.startCharacter))&&(m.undefined(i.endCharacter)||m.uinteger(i.endCharacter))&&(m.undefined(i.kind)||m.string(i.kind))}a(t,"is"),r.is=t})(Bf||(Bf={}));var Gl;(function(r){function e(n,i){return{location:n,message:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&Na.is(i.location)&&m.string(i.message)}a(t,"is"),r.is=t})(Gl||(Gl={}));var Wf;(function(r){r.Error=1,r.Warning=2,r.Information=3,r.Hint=4})(Wf||(Wf={}));var Kf;(function(r){r.Unnecessary=1,r.Deprecated=2})(Kf||(Kf={}));var Vf;(function(r){function e(t){let n=t;return m.objectLiteral(n)&&m.string(n.href)}a(e,"is"),r.is=e})(Vf||(Vf={}));var Ca;(function(r){function e(n,i,s,o,l,u){let c={range:n,message:i};return m.defined(s)&&(c.severity=s),m.defined(o)&&(c.code=o),m.defined(l)&&(c.source=l),m.defined(u)&&(c.relatedInformation=u),c}a(e,"create"),r.create=e;function t(n){var i;let s=n;return m.defined(s)&&U.is(s.range)&&m.string(s.message)&&(m.number(s.severity)||m.undefined(s.severity))&&(m.integer(s.code)||m.string(s.code)||m.undefined(s.code))&&(m.undefined(s.codeDescription)||m.string((i=s.codeDescription)===null||i===void 0?void 0:i.href))&&(m.string(s.source)||m.undefined(s.source))&&(m.undefined(s.relatedInformation)||m.typedArray(s.relatedInformation,Gl.is))}a(t,"is"),r.is=t})(Ca||(Ca={}));var Fn;(function(r){function e(n,i,...s){let o={title:n,command:i};return m.defined(s)&&s.length>0&&(o.arguments=s),o}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.string(i.title)&&m.string(i.command)}a(t,"is"),r.is=t})(Fn||(Fn={}));var Gn;(function(r){function e(s,o){return{range:s,newText:o}}a(e,"replace"),r.replace=e;function t(s,o){return{range:{start:s,end:s},newText:o}}a(t,"insert"),r.insert=t;function n(s){return{range:s,newText:""}}a(n,"del"),r.del=n;function i(s){let o=s;return m.objectLiteral(o)&&m.string(o.newText)&&U.is(o.range)}a(i,"is"),r.is=i})(Gn||(Gn={}));var Ul;(function(r){function e(n,i,s){let o={label:n};return i!==void 0&&(o.needsConfirmation=i),s!==void 0&&(o.description=s),o}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&m.string(i.label)&&(m.boolean(i.needsConfirmation)||i.needsConfirmation===void 0)&&(m.string(i.description)||i.description===void 0)}a(t,"is"),r.is=t})(Ul||(Ul={}));var Un;(function(r){function e(t){let n=t;return m.string(n)}a(e,"is"),r.is=e})(Un||(Un={}));var jf;(function(r){function e(s,o,l){return{range:s,newText:o,annotationId:l}}a(e,"replace"),r.replace=e;function t(s,o,l){return{range:{start:s,end:s},newText:o,annotationId:l}}a(t,"insert"),r.insert=t;function n(s,o){return{range:s,newText:"",annotationId:o}}a(n,"del"),r.del=n;function i(s){let o=s;return Gn.is(o)&&(Ul.is(o.annotationId)||Un.is(o.annotationId))}a(i,"is"),r.is=i})(jf||(jf={}));var Bl;(function(r){function e(n,i){return{textDocument:n,edits:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&Hl.is(i.textDocument)&&Array.isArray(i.edits)}a(t,"is"),r.is=t})(Bl||(Bl={}));var Wl;(function(r){function e(n,i,s){let o={kind:"create",uri:n};return i!==void 0&&(i.overwrite!==void 0||i.ignoreIfExists!==void 0)&&(o.options=i),s!==void 0&&(o.annotationId=s),o}a(e,"create"),r.create=e;function t(n){let i=n;return i&&i.kind==="create"&&m.string(i.uri)&&(i.options===void 0||(i.options.overwrite===void 0||m.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||m.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||Un.is(i.annotationId))}a(t,"is"),r.is=t})(Wl||(Wl={}));var Kl;(function(r){function e(n,i,s,o){let l={kind:"rename",oldUri:n,newUri:i};return s!==void 0&&(s.overwrite!==void 0||s.ignoreIfExists!==void 0)&&(l.options=s),o!==void 0&&(l.annotationId=o),l}a(e,"create"),r.create=e;function t(n){let i=n;return i&&i.kind==="rename"&&m.string(i.oldUri)&&m.string(i.newUri)&&(i.options===void 0||(i.options.overwrite===void 0||m.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||m.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||Un.is(i.annotationId))}a(t,"is"),r.is=t})(Kl||(Kl={}));var Vl;(function(r){function e(n,i,s){let o={kind:"delete",uri:n};return i!==void 0&&(i.recursive!==void 0||i.ignoreIfNotExists!==void 0)&&(o.options=i),s!==void 0&&(o.annotationId=s),o}a(e,"create"),r.create=e;function t(n){let i=n;return i&&i.kind==="delete"&&m.string(i.uri)&&(i.options===void 0||(i.options.recursive===void 0||m.boolean(i.options.recursive))&&(i.options.ignoreIfNotExists===void 0||m.boolean(i.options.ignoreIfNotExists)))&&(i.annotationId===void 0||Un.is(i.annotationId))}a(t,"is"),r.is=t})(Vl||(Vl={}));var jl;(function(r){function e(t){let n=t;return n&&(n.changes!==void 0||n.documentChanges!==void 0)&&(n.documentChanges===void 0||n.documentChanges.every(i=>m.string(i.kind)?Wl.is(i)||Kl.is(i)||Vl.is(i):Bl.is(i)))}a(e,"is"),r.is=e})(jl||(jl={}));var Hf;(function(r){function e(n){return{uri:n}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.string(i.uri)}a(t,"is"),r.is=t})(Hf||(Hf={}));var zf;(function(r){function e(n,i){return{uri:n,version:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.string(i.uri)&&m.integer(i.version)}a(t,"is"),r.is=t})(zf||(zf={}));var Hl;(function(r){function e(n,i){return{uri:n,version:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.string(i.uri)&&(i.version===null||m.integer(i.version))}a(t,"is"),r.is=t})(Hl||(Hl={}));var qf;(function(r){function e(n,i,s,o){return{uri:n,languageId:i,version:s,text:o}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.string(i.uri)&&m.string(i.languageId)&&m.integer(i.version)&&m.string(i.text)}a(t,"is"),r.is=t})(qf||(qf={}));var zl;(function(r){r.PlainText="plaintext",r.Markdown="markdown";function e(t){let n=t;return n===r.PlainText||n===r.Markdown}a(e,"is"),r.is=e})(zl||(zl={}));var Ui;(function(r){function e(t){let n=t;return m.objectLiteral(t)&&zl.is(n.kind)&&m.string(n.value)}a(e,"is"),r.is=e})(Ui||(Ui={}));var Xf;(function(r){r.Text=1,r.Method=2,r.Function=3,r.Constructor=4,r.Field=5,r.Variable=6,r.Class=7,r.Interface=8,r.Module=9,r.Property=10,r.Unit=11,r.Value=12,r.Enum=13,r.Keyword=14,r.Snippet=15,r.Color=16,r.File=17,r.Reference=18,r.Folder=19,r.EnumMember=20,r.Constant=21,r.Struct=22,r.Event=23,r.Operator=24,r.TypeParameter=25})(Xf||(Xf={}));var Yf;(function(r){r.PlainText=1,r.Snippet=2})(Yf||(Yf={}));var Jf;(function(r){r.Deprecated=1})(Jf||(Jf={}));var Qf;(function(r){function e(n,i,s){return{newText:n,insert:i,replace:s}}a(e,"create"),r.create=e;function t(n){let i=n;return i&&m.string(i.newText)&&U.is(i.insert)&&U.is(i.replace)}a(t,"is"),r.is=t})(Qf||(Qf={}));var Zf;(function(r){r.asIs=1,r.adjustIndentation=2})(Zf||(Zf={}));var ed;(function(r){function e(t){let n=t;return n&&(m.string(n.detail)||n.detail===void 0)&&(m.string(n.description)||n.description===void 0)}a(e,"is"),r.is=e})(ed||(ed={}));var td;(function(r){function e(t){return{label:t}}a(e,"create"),r.create=e})(td||(td={}));var rd;(function(r){function e(t,n){return{items:t||[],isIncomplete:!!n}}a(e,"create"),r.create=e})(rd||(rd={}));var Sa;(function(r){function e(n){return n.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}a(e,"fromPlainText"),r.fromPlainText=e;function t(n){let i=n;return m.string(i)||m.objectLiteral(i)&&m.string(i.language)&&m.string(i.value)}a(t,"is"),r.is=t})(Sa||(Sa={}));var nd;(function(r){function e(t){let n=t;return!!n&&m.objectLiteral(n)&&(Ui.is(n.contents)||Sa.is(n.contents)||m.typedArray(n.contents,Sa.is))&&(t.range===void 0||U.is(t.range))}a(e,"is"),r.is=e})(nd||(nd={}));var id;(function(r){function e(t,n){return n?{label:t,documentation:n}:{label:t}}a(e,"create"),r.create=e})(id||(id={}));var sd;(function(r){function e(t,n,...i){let s={label:t};return m.defined(n)&&(s.documentation=n),m.defined(i)?s.parameters=i:s.parameters=[],s}a(e,"create"),r.create=e})(sd||(sd={}));var ad;(function(r){r.Text=1,r.Read=2,r.Write=3})(ad||(ad={}));var od;(function(r){function e(t,n){let i={range:t};return m.number(n)&&(i.kind=n),i}a(e,"create"),r.create=e})(od||(od={}));var ld;(function(r){r.File=1,r.Module=2,r.Namespace=3,r.Package=4,r.Class=5,r.Method=6,r.Property=7,r.Field=8,r.Constructor=9,r.Enum=10,r.Interface=11,r.Function=12,r.Variable=13,r.Constant=14,r.String=15,r.Number=16,r.Boolean=17,r.Array=18,r.Object=19,r.Key=20,r.Null=21,r.EnumMember=22,r.Struct=23,r.Event=24,r.Operator=25,r.TypeParameter=26})(ld||(ld={}));var ud;(function(r){r.Deprecated=1})(ud||(ud={}));var cd;(function(r){function e(t,n,i,s,o){let l={name:t,kind:n,location:{uri:s,range:i}};return o&&(l.containerName=o),l}a(e,"create"),r.create=e})(cd||(cd={}));var fd;(function(r){function e(t,n,i,s){return s!==void 0?{name:t,kind:n,location:{uri:i,range:s}}:{name:t,kind:n,location:{uri:i}}}a(e,"create"),r.create=e})(fd||(fd={}));var dd;(function(r){function e(n,i,s,o,l,u){let c={name:n,detail:i,kind:s,range:o,selectionRange:l};return u!==void 0&&(c.children=u),c}a(e,"create"),r.create=e;function t(n){let i=n;return i&&m.string(i.name)&&m.number(i.kind)&&U.is(i.range)&&U.is(i.selectionRange)&&(i.detail===void 0||m.string(i.detail))&&(i.deprecated===void 0||m.boolean(i.deprecated))&&(i.children===void 0||Array.isArray(i.children))&&(i.tags===void 0||Array.isArray(i.tags))}a(t,"is"),r.is=t})(dd||(dd={}));var hd;(function(r){r.Empty="",r.QuickFix="quickfix",r.Refactor="refactor",r.RefactorExtract="refactor.extract",r.RefactorInline="refactor.inline",r.RefactorRewrite="refactor.rewrite",r.Source="source",r.SourceOrganizeImports="source.organizeImports",r.SourceFixAll="source.fixAll"})(hd||(hd={}));var wa;(function(r){r.Invoked=1,r.Automatic=2})(wa||(wa={}));var pd;(function(r){function e(n,i,s){let o={diagnostics:n};return i!=null&&(o.only=i),s!=null&&(o.triggerKind=s),o}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.typedArray(i.diagnostics,Ca.is)&&(i.only===void 0||m.typedArray(i.only,m.string))&&(i.triggerKind===void 0||i.triggerKind===wa.Invoked||i.triggerKind===wa.Automatic)}a(t,"is"),r.is=t})(pd||(pd={}));var md;(function(r){function e(n,i,s){let o={title:n},l=!0;return typeof i=="string"?(l=!1,o.kind=i):Fn.is(i)?o.command=i:o.edit=i,l&&s!==void 0&&(o.kind=s),o}a(e,"create"),r.create=e;function t(n){let i=n;return i&&m.string(i.title)&&(i.diagnostics===void 0||m.typedArray(i.diagnostics,Ca.is))&&(i.kind===void 0||m.string(i.kind))&&(i.edit!==void 0||i.command!==void 0)&&(i.command===void 0||Fn.is(i.command))&&(i.isPreferred===void 0||m.boolean(i.isPreferred))&&(i.edit===void 0||jl.is(i.edit))}a(t,"is"),r.is=t})(md||(md={}));var gd;(function(r){function e(n,i){let s={range:n};return m.defined(i)&&(s.data=i),s}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&U.is(i.range)&&(m.undefined(i.command)||Fn.is(i.command))}a(t,"is"),r.is=t})(gd||(gd={}));var yd;(function(r){function e(n,i){return{tabSize:n,insertSpaces:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&m.uinteger(i.tabSize)&&m.boolean(i.insertSpaces)}a(t,"is"),r.is=t})(yd||(yd={}));var Td;(function(r){function e(n,i,s){return{range:n,target:i,data:s}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&U.is(i.range)&&(m.undefined(i.target)||m.string(i.target))}a(t,"is"),r.is=t})(Td||(Td={}));var Rd;(function(r){function e(n,i){return{range:n,parent:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&U.is(i.range)&&(i.parent===void 0||r.is(i.parent))}a(t,"is"),r.is=t})(Rd||(Rd={}));var xd;(function(r){r.namespace="namespace",r.type="type",r.class="class",r.enum="enum",r.interface="interface",r.struct="struct",r.typeParameter="typeParameter",r.parameter="parameter",r.variable="variable",r.property="property",r.enumMember="enumMember",r.event="event",r.function="function",r.method="method",r.macro="macro",r.keyword="keyword",r.modifier="modifier",r.comment="comment",r.string="string",r.number="number",r.regexp="regexp",r.operator="operator",r.decorator="decorator"})(xd||(xd={}));var Ed;(function(r){r.declaration="declaration",r.definition="definition",r.readonly="readonly",r.static="static",r.deprecated="deprecated",r.abstract="abstract",r.async="async",r.modification="modification",r.documentation="documentation",r.defaultLibrary="defaultLibrary"})(Ed||(Ed={}));var Ad;(function(r){function e(t){let n=t;return m.objectLiteral(n)&&(n.resultId===void 0||typeof n.resultId=="string")&&Array.isArray(n.data)&&(n.data.length===0||typeof n.data[0]=="number")}a(e,"is"),r.is=e})(Ad||(Ad={}));var vd;(function(r){function e(n,i){return{range:n,text:i}}a(e,"create"),r.create=e;function t(n){let i=n;return i!=null&&U.is(i.range)&&m.string(i.text)}a(t,"is"),r.is=t})(vd||(vd={}));var kd;(function(r){function e(n,i,s){return{range:n,variableName:i,caseSensitiveLookup:s}}a(e,"create"),r.create=e;function t(n){let i=n;return i!=null&&U.is(i.range)&&m.boolean(i.caseSensitiveLookup)&&(m.string(i.variableName)||i.variableName===void 0)}a(t,"is"),r.is=t})(kd||(kd={}));var $d;(function(r){function e(n,i){return{range:n,expression:i}}a(e,"create"),r.create=e;function t(n){let i=n;return i!=null&&U.is(i.range)&&(m.string(i.expression)||i.expression===void 0)}a(t,"is"),r.is=t})($d||($d={}));var Id;(function(r){function e(n,i){return{frameId:n,stoppedLocation:i}}a(e,"create"),r.create=e;function t(n){let i=n;return m.defined(i)&&U.is(n.stoppedLocation)}a(t,"is"),r.is=t})(Id||(Id={}));var ql;(function(r){r.Type=1,r.Parameter=2;function e(t){return t===1||t===2}a(e,"is"),r.is=e})(ql||(ql={}));var Xl;(function(r){function e(n){return{value:n}}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&(i.tooltip===void 0||m.string(i.tooltip)||Ui.is(i.tooltip))&&(i.location===void 0||Na.is(i.location))&&(i.command===void 0||Fn.is(i.command))}a(t,"is"),r.is=t})(Xl||(Xl={}));var Nd;(function(r){function e(n,i,s){let o={position:n,label:i};return s!==void 0&&(o.kind=s),o}a(e,"create"),r.create=e;function t(n){let i=n;return m.objectLiteral(i)&&W.is(i.position)&&(m.string(i.label)||m.typedArray(i.label,Xl.is))&&(i.kind===void 0||ql.is(i.kind))&&i.textEdits===void 0||m.typedArray(i.textEdits,Gn.is)&&(i.tooltip===void 0||m.string(i.tooltip)||Ui.is(i.tooltip))&&(i.paddingLeft===void 0||m.boolean(i.paddingLeft))&&(i.paddingRight===void 0||m.boolean(i.paddingRight))}a(t,"is"),r.is=t})(Nd||(Nd={}));var Cd;(function(r){function e(t){return{kind:"snippet",value:t}}a(e,"createSnippet"),r.createSnippet=e})(Cd||(Cd={}));var Sd;(function(r){function e(t,n,i,s){return{insertText:t,filterText:n,range:i,command:s}}a(e,"create"),r.create=e})(Sd||(Sd={}));var wd;(function(r){function e(t){return{items:t}}a(e,"create"),r.create=e})(wd||(wd={}));var _d;(function(r){r.Invoked=0,r.Automatic=1})(_d||(_d={}));var Ld;(function(r){function e(t,n){return{range:t,text:n}}a(e,"create"),r.create=e})(Ld||(Ld={}));var Od;(function(r){function e(t,n){return{triggerKind:t,selectedCompletionInfo:n}}a(e,"create"),r.create=e})(Od||(Od={}));var Pd;(function(r){function e(t){let n=t;return m.objectLiteral(n)&&Dl.is(n.uri)&&m.string(n.name)}a(e,"is"),r.is=e})(Pd||(Pd={}));var bd;(function(r){function e(s,o,l,u){return new Yl(s,o,l,u)}a(e,"create"),r.create=e;function t(s){let o=s;return!!(m.defined(o)&&m.string(o.uri)&&(m.undefined(o.languageId)||m.string(o.languageId))&&m.uinteger(o.lineCount)&&m.func(o.getText)&&m.func(o.positionAt)&&m.func(o.offsetAt))}a(t,"is"),r.is=t;function n(s,o){let l=s.getText(),u=i(o,(f,d)=>{let h=f.range.start.line-d.range.start.line;return h===0?f.range.start.character-d.range.start.character:h}),c=l.length;for(let f=u.length-1;f>=0;f--){let d=u[f],h=s.offsetAt(d.range.start),p=s.offsetAt(d.range.end);if(p<=c)l=l.substring(0,h)+d.newText+l.substring(p,l.length);else throw new Error("Overlapping edit");c=h}return l}a(n,"applyEdits"),r.applyEdits=n;function i(s,o){if(s.length<=1)return s;let l=s.length/2|0,u=s.slice(0,l),c=s.slice(l);i(u,o),i(c,o);let f=0,d=0,h=0;for(;f<u.length&&d<c.length;)o(u[f],c[d])<=0?s[h++]=u[f++]:s[h++]=c[d++];for(;f<u.length;)s[h++]=u[f++];for(;d<c.length;)s[h++]=c[d++];return s}a(i,"mergeSort")})(bd||(bd={}));var Yl=class{static{a(this,"FullTextDocument")}constructor(e,t,n,i){this._uri=e,this._languageId=t,this._version=n,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],t=this._content,n=!0;for(let i=0;i<t.length;i++){n&&(e.push(i),n=!1);let s=t.charAt(i);n=s==="\r"||s===`
`,s==="\r"&&i+1<t.length&&t.charAt(i+1)===`
`&&i++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,i=t.length;if(i===0)return W.create(0,e);for(;n<i;){let o=Math.floor((n+i)/2);t[o]>e?i=o:n=o+1}let s=n-1;return W.create(s,e-t[s])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],i=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,i),n)}get lineCount(){return this.getLineOffsets().length}},m;(function(r){let e=Object.prototype.toString;function t(p){return typeof p<"u"}a(t,"defined"),r.defined=t;function n(p){return typeof p>"u"}a(n,"undefined"),r.undefined=n;function i(p){return p===!0||p===!1}a(i,"boolean"),r.boolean=i;function s(p){return e.call(p)==="[object String]"}a(s,"string"),r.string=s;function o(p){return e.call(p)==="[object Number]"}a(o,"number"),r.number=o;function l(p,g,y){return e.call(p)==="[object Number]"&&g<=p&&p<=y}a(l,"numberRange"),r.numberRange=l;function u(p){return e.call(p)==="[object Number]"&&-2147483648<=p&&p<=2147483647}a(u,"integer"),r.integer=u;function c(p){return e.call(p)==="[object Number]"&&0<=p&&p<=2147483647}a(c,"uinteger"),r.uinteger=c;function f(p){return e.call(p)==="[object Function]"}a(f,"func"),r.func=f;function d(p){return p!==null&&typeof p=="object"}a(d,"objectLiteral"),r.objectLiteral=d;function h(p,g){return Array.isArray(p)&&p.every(g)}a(h,"typedArray"),r.typedArray=h})(m||(m={}));var Bi=class{static{a(this,"CstNodeBuilder")}constructor(){this.nodeStack=[]}get current(){var e;return(e=this.nodeStack[this.nodeStack.length-1])!==null&&e!==void 0?e:this.rootNode}buildRootNode(e){return this.rootNode=new Bn(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){let t=new gr;return t.grammarSource=e,t.root=this.rootNode,this.current.content.push(t),this.nodeStack.push(t),t}buildLeafNode(e,t){let n=new mr(e.startOffset,e.image.length,Nr(e),e.tokenType,!t);return n.grammarSource=t,n.root=this.rootNode,this.current.content.push(n),n}removeNode(e){let t=e.container;if(t){let n=t.content.indexOf(e);n>=0&&t.content.splice(n,1)}}addHiddenNodes(e){let t=[];for(let s of e){let o=new mr(s.startOffset,s.image.length,Nr(s),s.tokenType,!0);o.root=this.rootNode,t.push(o)}let n=this.current,i=!1;if(n.content.length>0){n.content.push(...t);return}for(;n.container;){let s=n.container.content.indexOf(n);if(s>0){n.container.content.splice(s,0,...t),i=!0;break}n=n.container}i||this.rootNode.content.unshift(...t)}construct(e){let t=this.current;typeof e.$type=="string"&&(this.current.astNode=e),e.$cstNode=t;let n=this.nodeStack.pop();n?.content.length===0&&this.removeNode(n)}},Wi=class{static{a(this,"AbstractCstNode")}get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,t;let n=typeof((e=this._astNode)===null||e===void 0?void 0:e.$type)=="string"?this._astNode:(t=this.container)===null||t===void 0?void 0:t.astNode;if(!n)throw new Error("This node has no associated AST element");return n}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}},mr=class extends Wi{static{a(this,"LeafCstNodeImpl")}get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,t,n,i,s=!1){super(),this._hidden=s,this._offset=e,this._tokenType=i,this._length=t,this._range=n}},gr=class extends Wi{static{a(this,"CompositeCstNodeImpl")}constructor(){super(...arguments),this.content=new Jl(this)}get children(){return this.content}get offset(){var e,t;return(t=(e=this.firstNonHiddenNode)===null||e===void 0?void 0:e.offset)!==null&&t!==void 0?t:0}get length(){return this.end-this.offset}get end(){var e,t;return(t=(e=this.lastNonHiddenNode)===null||e===void 0?void 0:e.end)!==null&&t!==void 0?t:0}get range(){let e=this.firstNonHiddenNode,t=this.lastNonHiddenNode;if(e&&t){if(this._rangeCache===void 0){let{range:n}=e,{range:i}=t;this._rangeCache={start:n.start,end:i.end.line<n.start.line?n.start:i.end}}return this._rangeCache}else return{start:W.create(0,0),end:W.create(0,0)}}get firstNonHiddenNode(){for(let e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){let t=this.content[e];if(!t.hidden)return t}return this.content[this.content.length-1]}},Jl=class r extends Array{static{a(this,"CstNodeContainer")}constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,r.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,t,...n){return this.addParents(n),super.splice(e,t,...n)}addParents(e){for(let t of e)t.container=this.parent}},Bn=class extends gr{static{a(this,"RootCstNodeImpl")}get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=e??""}};var _a=Symbol("Datatype");function Ql(r){return r.$type===_a}a(Ql,"isDataTypeNode");var Md="\u200B",Dd=a(r=>r.endsWith(Md)?r:r+Md,"withRuleSuffix"),Ki=class{static{a(this,"AbstractLangiumParser")}constructor(e){this._unorderedGroups=new Map,this.allRules=new Map,this.lexer=e.parser.Lexer;let t=this.lexer.definition,n=e.LanguageMetaData.mode==="production";this.wrapper=new Zl(t,Object.assign(Object.assign({},e.parser.ParserConfig),{skipValidations:n,errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,t){this.wrapper.wrapOr(e,t)}optional(e,t){this.wrapper.wrapOption(e,t)}many(e,t){this.wrapper.wrapMany(e,t)}atLeastOne(e,t){this.wrapper.wrapAtLeastOne(e,t)}getRule(e){return this.allRules.get(e)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}},Vi=class extends Ki{static{a(this,"LangiumParser")}get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new Bi,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,t){let n=this.computeRuleType(e),i=this.wrapper.DEFINE_RULE(Dd(e.name),this.startImplementation(n,t).bind(this));return this.allRules.set(e.name,i),e.entry&&(this.mainRule=i),i}computeRuleType(e){if(!e.fragment){if(yi(e))return _a;{let t=yn(e);return t??e.name}}}parse(e,t={}){this.nodeBuilder.buildRootNode(e);let n=this.lexerResult=this.lexer.tokenize(e);this.wrapper.input=n.tokens;let i=t.rule?this.allRules.get(t.rule):this.mainRule;if(!i)throw new Error(t.rule?`No rule found with name '${t.rule}'`:"No main rule available.");let s=i.call(this.wrapper,{});return this.nodeBuilder.addHiddenNodes(n.hidden),this.unorderedGroups.clear(),this.lexerResult=void 0,{value:s,lexerErrors:n.errors,lexerReport:n.report,parserErrors:this.wrapper.errors}}startImplementation(e,t){return n=>{let i=!this.isRecording()&&e!==void 0;if(i){let o={$type:e};this.stack.push(o),e===_a&&(o.value="")}let s;try{s=t(n)}catch{s=void 0}return s===void 0&&i&&(s=this.construct()),s}}extractHiddenTokens(e){let t=this.lexerResult.hidden;if(!t.length)return[];let n=e.startOffset;for(let i=0;i<t.length;i++)if(t[i].startOffset>n)return t.splice(0,i);return t.splice(0,t.length)}consume(e,t,n){let i=this.wrapper.wrapConsume(e,t);if(!this.isRecording()&&this.isValidToken(i)){let s=this.extractHiddenTokens(i);this.nodeBuilder.addHiddenNodes(s);let o=this.nodeBuilder.buildLeafNode(i,n),{assignment:l,isCrossRef:u}=this.getAssignment(n),c=this.current;if(l){let f=Ye(n)?i.image:this.converter.convert(i.image,o);this.assign(l.operator,l.feature,f,o,u)}else if(Ql(c)){let f=i.image;Ye(n)||(f=this.converter.convert(f,o).toString()),c.value+=f}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&typeof e.endOffset=="number"&&!isNaN(e.endOffset)}subrule(e,t,n,i,s){let o;!this.isRecording()&&!n&&(o=this.nodeBuilder.buildCompositeNode(i));let l=this.wrapper.wrapSubrule(e,t,s);!this.isRecording()&&o&&o.length>0&&this.performSubruleAssignment(l,i,o)}performSubruleAssignment(e,t,n){let{assignment:i,isCrossRef:s}=this.getAssignment(t);if(i)this.assign(i.operator,i.feature,e,n,s);else if(!i){let o=this.current;if(Ql(o))o.value+=e.toString();else if(typeof e=="object"&&e){let u=this.assignWithoutOverride(e,o);this.stack.pop(),this.stack.push(u)}}}action(e,t){if(!this.isRecording()){let n=this.current;if(t.feature&&t.operator){n=this.construct(),this.nodeBuilder.removeNode(n.$cstNode),this.nodeBuilder.buildCompositeNode(t).content.push(n.$cstNode);let s={$type:e};this.stack.push(s),this.assign(t.operator,t.feature,n,n.$cstNode,!1)}else n.$type=e}}construct(){if(this.isRecording())return;let e=this.current;return Ds(e),this.nodeBuilder.construct(e),this.stack.pop(),Ql(e)?this.converter.convert(e.value,e.$cstNode):(Go(this.astReflection,e),e)}getAssignment(e){if(!this.assignmentMap.has(e)){let t=ir(e,rt);this.assignmentMap.set(e,{assignment:t,isCrossRef:t?nr(t.terminal):!1})}return this.assignmentMap.get(e)}assign(e,t,n,i,s){let o=this.current,l;switch(s&&typeof n=="string"?l=this.linker.buildReference(o,t,i,n):l=n,e){case"=":{o[t]=l;break}case"?=":{o[t]=!0;break}case"+=":Array.isArray(o[t])||(o[t]=[]),o[t].push(l)}}assignWithoutOverride(e,t){for(let[i,s]of Object.entries(t)){let o=e[i];o===void 0?e[i]=s:Array.isArray(o)&&Array.isArray(s)&&(s.push(...o),e[i]=s)}let n=e.$cstNode;return n&&(n.astNode=void 0,e.$cstNode=void 0),e}get definitionErrors(){return this.wrapper.definitionErrors}},La=class{static{a(this,"AbstractParserErrorMessageProvider")}buildMismatchTokenMessage(e){return Lt.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return Lt.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return Lt.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return Lt.buildEarlyExitMessage(e)}},Wn=class extends La{static{a(this,"LangiumParserErrorMessageProvider")}buildMismatchTokenMessage({expected:e,actual:t}){return`Expecting ${e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`} but found \`${t.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}},ji=class extends Ki{static{a(this,"LangiumCompletionParser")}constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();let t=this.lexer.tokenize(e,{mode:"partial"});return this.tokens=t.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,t){let n=this.wrapper.DEFINE_RULE(Dd(e.name),this.startImplementation(t).bind(this));return this.allRules.set(e.name,n),e.entry&&(this.mainRule=n),n}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return t=>{let n=this.keepStackSize();try{e(t)}finally{this.resetStackSize(n)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){let e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,t,n){this.wrapper.wrapConsume(e,t),this.isRecording()||(this.lastElementStack=[...this.elementStack,n],this.nextTokenIndex=this.currIdx+1)}subrule(e,t,n,i,s){this.before(i),this.wrapper.wrapSubrule(e,t,s),this.after(i)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){let t=this.elementStack.lastIndexOf(e);t>=0&&this.elementStack.splice(t)}}get currIdx(){return this.wrapper.currIdx}},jm={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new Wn},Zl=class extends bi{static{a(this,"ChevrotainWrapper")}constructor(e,t){let n=t&&"maxLookahead"in t;super(e,Object.assign(Object.assign(Object.assign({},jm),{lookaheadStrategy:n?new Ot({maxLookahead:t.maxLookahead}):new Gi({logging:t.skipValidations?()=>{}:void 0})}),t))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,t){return this.RULE(e,t)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,t){return this.consume(e,t)}wrapSubrule(e,t,n){return this.subrule(e,t,{ARGS:[n]})}wrapOr(e,t){this.or(e,t)}wrapOption(e,t){this.option(e,t)}wrapMany(e,t){this.many(e,t)}wrapAtLeastOne(e,t){this.atLeastOne(e,t)}};function Hi(r,e,t){return Hm({parser:e,tokens:t,ruleNames:new Map},r),e}a(Hi,"createParser");function Hm(r,e){let t=mi(e,!1),n=j(e.rules).filter(Se).filter(i=>t.has(i));for(let i of n){let s=Object.assign(Object.assign({},r),{consume:1,optional:1,subrule:1,many:1,or:1});r.parser.rule(i,yr(s,i.definition))}}a(Hm,"buildRules");function yr(r,e,t=!1){let n;if(Ye(e))n=Zm(r,e);else if($t(e))n=zm(r,e);else if(rt(e))n=yr(r,e.terminal);else if(nr(e))n=Fd(r,e);else if(nt(e))n=qm(r,e);else if(Ps(e))n=Ym(r,e);else if(Ms(e))n=Jm(r,e);else if(Ut(e))n=Qm(r,e);else if(wo(e)){let i=r.consume++;n=a(()=>r.parser.consume(i,qe,e),"method")}else throw new tr(e.$cstNode,`Unexpected element type: ${e.$type}`);return Gd(r,t?void 0:Oa(e),n,e.cardinality)}a(yr,"buildElement");function zm(r,e){let t=Ti(e);return()=>r.parser.action(t,e)}a(zm,"buildAction");function qm(r,e){let t=e.rule.ref;if(Se(t)){let n=r.subrule++,i=t.fragment,s=e.arguments.length>0?Xm(t,e.arguments):()=>({});return o=>r.parser.subrule(n,Ud(r,t),i,e,s(o))}else if(He(t)){let n=r.consume++,i=eu(r,t.name);return()=>r.parser.consume(n,i,e)}else if(t)ct(t);else throw new tr(e.$cstNode,`Undefined rule: ${e.rule.$refText}`)}a(qm,"buildRuleCall");function Xm(r,e){let t=e.map(n=>Pt(n.value));return n=>{let i={};for(let s=0;s<t.length;s++){let o=r.parameters[s],l=t[s];i[o.name]=l(n)}return i}}a(Xm,"buildRuleCallPredicate");function Pt(r){if(vo(r)){let e=Pt(r.left),t=Pt(r.right);return n=>e(n)||t(n)}else if(Ao(r)){let e=Pt(r.left),t=Pt(r.right);return n=>e(n)&&t(n)}else if(ko(r)){let e=Pt(r.value);return t=>!e(t)}else if($o(r)){let e=r.parameter.ref.name;return t=>t!==void 0&&t[e]===!0}else if(Eo(r)){let e=!!r.true;return()=>e}ct(r)}a(Pt,"buildPredicate");function Ym(r,e){if(e.elements.length===1)return yr(r,e.elements[0]);{let t=[];for(let i of e.elements){let s={ALT:yr(r,i,!0)},o=Oa(i);o&&(s.GATE=Pt(o)),t.push(s)}let n=r.or++;return i=>r.parser.alternatives(n,t.map(s=>{let o={ALT:a(()=>s.ALT(i),"ALT")},l=s.GATE;return l&&(o.GATE=()=>l(i)),o}))}}a(Ym,"buildAlternatives");function Jm(r,e){if(e.elements.length===1)return yr(r,e.elements[0]);let t=[];for(let l of e.elements){let u={ALT:yr(r,l,!0)},c=Oa(l);c&&(u.GATE=Pt(c)),t.push(u)}let n=r.or++,i=a((l,u)=>{let c=u.getRuleStack().join("-");return`uGroup_${l}_${c}`},"idFunc"),s=a(l=>r.parser.alternatives(n,t.map((u,c)=>{let f={ALT:a(()=>!0,"ALT")},d=r.parser;f.ALT=()=>{if(u.ALT(l),!d.isRecording()){let p=i(n,d);d.unorderedGroups.get(p)||d.unorderedGroups.set(p,[]);let g=d.unorderedGroups.get(p);typeof g?.[c]>"u"&&(g[c]=!0)}};let h=u.GATE;return h?f.GATE=()=>h(l):f.GATE=()=>{let p=d.unorderedGroups.get(i(n,d));return!p?.[c]},f})),"alternatives"),o=Gd(r,Oa(e),s,"*");return l=>{o(l),r.parser.isRecording()||r.parser.unorderedGroups.delete(i(n,r.parser))}}a(Jm,"buildUnorderedGroup");function Qm(r,e){let t=e.elements.map(n=>yr(r,n));return n=>t.forEach(i=>i(n))}a(Qm,"buildGroup");function Oa(r){if(Ut(r))return r.guardCondition}a(Oa,"getGuardCondition");function Fd(r,e,t=e.terminal){if(t)if(nt(t)&&Se(t.rule.ref)){let n=t.rule.ref,i=r.subrule++;return s=>r.parser.subrule(i,Ud(r,n),!1,e,s)}else if(nt(t)&&He(t.rule.ref)){let n=r.consume++,i=eu(r,t.rule.ref.name);return()=>r.parser.consume(n,i,e)}else if(Ye(t)){let n=r.consume++,i=eu(r,t.value);return()=>r.parser.consume(n,i,e)}else throw new Error("Could not build cross reference parser");else{if(!e.type.ref)throw new Error("Could not resolve reference to type: "+e.type.$refText);let n=Ws(e.type.ref),i=n?.terminal;if(!i)throw new Error("Could not find name assignment for type: "+Ti(e.type.ref));return Fd(r,e,i)}}a(Fd,"buildCrossReference");function Zm(r,e){let t=r.consume++,n=r.tokens[e.value];if(!n)throw new Error("Could not find token for keyword: "+e.value);return()=>r.parser.consume(t,n,e)}a(Zm,"buildKeyword");function Gd(r,e,t,n){let i=e&&Pt(e);if(!n)if(i){let s=r.or++;return o=>r.parser.alternatives(s,[{ALT:a(()=>t(o),"ALT"),GATE:a(()=>i(o),"GATE")},{ALT:va(),GATE:a(()=>!i(o),"GATE")}])}else return t;if(n==="*"){let s=r.many++;return o=>r.parser.many(s,{DEF:a(()=>t(o),"DEF"),GATE:i?()=>i(o):void 0})}else if(n==="+"){let s=r.many++;if(i){let o=r.or++;return l=>r.parser.alternatives(o,[{ALT:a(()=>r.parser.atLeastOne(s,{DEF:a(()=>t(l),"DEF")}),"ALT"),GATE:a(()=>i(l),"GATE")},{ALT:va(),GATE:a(()=>!i(l),"GATE")}])}else return o=>r.parser.atLeastOne(s,{DEF:a(()=>t(o),"DEF")})}else if(n==="?"){let s=r.optional++;return o=>r.parser.optional(s,{DEF:a(()=>t(o),"DEF"),GATE:i?()=>i(o):void 0})}else ct(n)}a(Gd,"wrap");function Ud(r,e){let t=eg(r,e),n=r.parser.getRule(t);if(!n)throw new Error(`Rule "${t}" not found."`);return n}a(Ud,"getRule");function eg(r,e){if(Se(e))return e.name;if(r.ruleNames.has(e))return r.ruleNames.get(e);{let t=e,n=t.$container,i=e.$type;for(;!Se(n);)(Ut(n)||Ps(n)||Ms(n))&&(i=n.elements.indexOf(t).toString()+":"+i),t=n,n=n.$container;return i=n.name+":"+i,r.ruleNames.set(e,i),i}}a(eg,"getRuleName");function eu(r,e){let t=r.tokens[e];if(!t)throw new Error(`Token "${e}" not found."`);return t}a(eu,"getToken");function tu(r){let e=r.Grammar,t=r.parser.Lexer,n=new ji(r);return Hi(e,n,t.definition),n.finalize(),n}a(tu,"createCompletionParser");function ru(r){let e=Bd(r);return e.finalize(),e}a(ru,"createLangiumParser");function Bd(r){let e=r.Grammar,t=r.parser.Lexer,n=new Vi(r);return Hi(e,n,t.definition)}a(Bd,"prepareLangiumParser");var bt=class{static{a(this,"DefaultTokenBuilder")}constructor(){this.diagnostics=[]}buildTokens(e,t){let n=j(mi(e,!1)),i=this.buildTerminalTokens(n),s=this.buildKeywordTokens(n,i,t);return i.forEach(o=>{let l=o.PATTERN;typeof l=="object"&&l&&"test"in l&&gn(l)?s.unshift(o):s.push(o)}),s}flushLexingReport(e){return{diagnostics:this.popDiagnostics()}}popDiagnostics(){let e=[...this.diagnostics];return this.diagnostics=[],e}buildTerminalTokens(e){return e.filter(He).filter(t=>!t.fragment).map(t=>this.buildTerminalToken(t)).toArray()}buildTerminalToken(e){let t=Tn(e),n=this.requiresCustomPattern(t)?this.regexPatternFunction(t):t,i={name:e.name,PATTERN:n};return typeof n=="function"&&(i.LINE_BREAKS=!0),e.hidden&&(i.GROUP=gn(t)?se.SKIPPED:"hidden"),i}requiresCustomPattern(e){return e.flags.includes("u")||e.flags.includes("s")?!0:!!(e.source.includes("?<=")||e.source.includes("?<!"))}regexPatternFunction(e){let t=new RegExp(e,e.flags+"y");return(n,i)=>(t.lastIndex=i,t.exec(n))}buildKeywordTokens(e,t,n){return e.filter(Se).flatMap(i=>ft(i).filter(Ye)).distinct(i=>i.value).toArray().sort((i,s)=>s.value.length-i.value.length).map(i=>this.buildKeywordToken(i,t,!!n?.caseInsensitive))}buildKeywordToken(e,t,n){let i=this.buildKeywordPattern(e,n),s={name:e.value,PATTERN:i,LONGER_ALT:this.findLongerAlt(e,t)};return typeof i=="function"&&(s.LINE_BREAKS=!0),s}buildKeywordPattern(e,t){return t?new RegExp(jo(e.value)):e.value}findLongerAlt(e,t){return t.reduce((n,i)=>{let s=i?.PATTERN;return s?.source&&Ho("^"+s.source+"$",e.value)&&n.push(i),n},[])}};var Tr=class{static{a(this,"DefaultValueConverter")}convert(e,t){let n=t.grammarSource;if(nr(n)&&(n=Xo(n)),nt(n)){let i=n.rule.ref;if(!i)throw new Error("This cst node was not parsed by a rule.");return this.runConverter(i,e,t)}return e}runConverter(e,t,n){var i;switch(e.name.toUpperCase()){case"INT":return pt.convertInt(t);case"STRING":return pt.convertString(t);case"ID":return pt.convertID(t)}switch((i=rl(e))===null||i===void 0?void 0:i.toLowerCase()){case"number":return pt.convertNumber(t);case"boolean":return pt.convertBoolean(t);case"bigint":return pt.convertBigint(t);case"date":return pt.convertDate(t);default:return t}}},pt;(function(r){function e(c){let f="";for(let d=1;d<c.length-1;d++){let h=c.charAt(d);if(h==="\\"){let p=c.charAt(++d);f+=t(p)}else f+=h}return f}a(e,"convertString"),r.convertString=e;function t(c){switch(c){case"b":return"\b";case"f":return"\f";case"n":return`
`;case"r":return"\r";case"t":return"	";case"v":return"\v";case"0":return"\0";default:return c}}a(t,"convertEscapeCharacter");function n(c){return c.charAt(0)==="^"?c.substring(1):c}a(n,"convertID"),r.convertID=n;function i(c){return parseInt(c)}a(i,"convertInt"),r.convertInt=i;function s(c){return BigInt(c)}a(s,"convertBigint"),r.convertBigint=s;function o(c){return new Date(c)}a(o,"convertDate"),r.convertDate=o;function l(c){return Number(c)}a(l,"convertNumber"),r.convertNumber=l;function u(c){return c.toLowerCase()==="true"}a(u,"convertBoolean"),r.convertBoolean=u})(pt||(pt={}));var S={};B(S,Vu(Hd(),1));function fu(){return new Promise(r=>{typeof setImmediate>"u"?setTimeout(r,0):setImmediate(r)})}a(fu,"delayNextTick");var Da=0,zd=10;function Fa(){return Da=performance.now(),new S.CancellationTokenSource}a(Fa,"startCancelableOperation");function qd(r){zd=r}a(qd,"setInterruptionPeriod");var mt=Symbol("OperationCancelled");function gt(r){return r===mt}a(gt,"isOperationCancelled");async function ce(r){if(r===S.CancellationToken.None)return;let e=performance.now();if(e-Da>=zd&&(Da=e,await fu(),Da=performance.now()),r.isCancellationRequested)throw mt}a(ce,"interruptAndCheck");var Ge=class{static{a(this,"Deferred")}constructor(){this.promise=new Promise((e,t)=>{this.resolve=n=>(e(n),this),this.reject=n=>(t(n),this)})}};var Ga=class r{static{a(this,"FullTextDocument")}constructor(e,t,n,i){this._uri=e,this._languageId=t,this._version=n,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){for(let n of e)if(r.isIncremental(n)){let i=Jd(n.range),s=this.offsetAt(i.start),o=this.offsetAt(i.end);this._content=this._content.substring(0,s)+n.text+this._content.substring(o,this._content.length);let l=Math.max(i.start.line,0),u=Math.max(i.end.line,0),c=this._lineOffsets,f=Xd(n.text,!1,s);if(u-l===f.length)for(let h=0,p=f.length;h<p;h++)c[h+l+1]=f[h];else f.length<1e4?c.splice(l+1,u-l,...f):this._lineOffsets=c=c.slice(0,l+1).concat(f,c.slice(u+1));let d=n.text.length-(o-s);if(d!==0)for(let h=l+1+f.length,p=c.length;h<p;h++)c[h]=c[h]+d}else if(r.isFull(n))this._content=n.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=t}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=Xd(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,i=t.length;if(i===0)return{line:0,character:e};for(;n<i;){let o=Math.floor((n+i)/2);t[o]>e?i=o:n=o+1}let s=n-1;return e=this.ensureBeforeEOL(e,t[s]),{line:s,character:e-t[s]}}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line];if(e.character<=0)return n;let i=e.line+1<t.length?t[e.line+1]:this._content.length,s=Math.min(n+e.character,i);return this.ensureBeforeEOL(s,n)}ensureBeforeEOL(e,t){for(;e>t&&Yd(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){let t=e;return t!=null&&typeof t.text=="string"&&t.range!==void 0&&(t.rangeLength===void 0||typeof t.rangeLength=="number")}static isFull(e){let t=e;return t!=null&&typeof t.text=="string"&&t.range===void 0&&t.rangeLength===void 0}},jn;(function(r){function e(i,s,o,l){return new Ga(i,s,o,l)}a(e,"create"),r.create=e;function t(i,s,o){if(i instanceof Ga)return i.update(s,o),i;throw new Error("TextDocument.update: document must be created by TextDocument.create")}a(t,"update"),r.update=t;function n(i,s){let o=i.getText(),l=du(s.map(cg),(f,d)=>{let h=f.range.start.line-d.range.start.line;return h===0?f.range.start.character-d.range.start.character:h}),u=0,c=[];for(let f of l){let d=i.offsetAt(f.range.start);if(d<u)throw new Error("Overlapping edit");d>u&&c.push(o.substring(u,d)),f.newText.length&&c.push(f.newText),u=i.offsetAt(f.range.end)}return c.push(o.substr(u)),c.join("")}a(n,"applyEdits"),r.applyEdits=n})(jn||(jn={}));function du(r,e){if(r.length<=1)return r;let t=r.length/2|0,n=r.slice(0,t),i=r.slice(t);du(n,e),du(i,e);let s=0,o=0,l=0;for(;s<n.length&&o<i.length;)e(n[s],i[o])<=0?r[l++]=n[s++]:r[l++]=i[o++];for(;s<n.length;)r[l++]=n[s++];for(;o<i.length;)r[l++]=i[o++];return r}a(du,"mergeSort");function Xd(r,e,t=0){let n=e?[t]:[];for(let i=0;i<r.length;i++){let s=r.charCodeAt(i);Yd(s)&&(s===13&&i+1<r.length&&r.charCodeAt(i+1)===10&&i++,n.push(t+i+1))}return n}a(Xd,"computeLineOffsets");function Yd(r){return r===13||r===10}a(Yd,"isEOL");function Jd(r){let e=r.start,t=r.end;return e.line>t.line||e.line===t.line&&e.character>t.character?{start:t,end:e}:r}a(Jd,"getWellformedRange");function cg(r){let e=Jd(r.range);return e!==r.range?{newText:r.newText,range:e}:r}a(cg,"getWellformedEdit");var Qd;(()=>{"use strict";var r={470:i=>{function s(u){if(typeof u!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(u))}a(s,"e");function o(u,c){for(var f,d="",h=0,p=-1,g=0,y=0;y<=u.length;++y){if(y<u.length)f=u.charCodeAt(y);else{if(f===47)break;f=47}if(f===47){if(!(p===y-1||g===1))if(p!==y-1&&g===2){if(d.length<2||h!==2||d.charCodeAt(d.length-1)!==46||d.charCodeAt(d.length-2)!==46){if(d.length>2){var v=d.lastIndexOf("/");if(v!==d.length-1){v===-1?(d="",h=0):h=(d=d.slice(0,v)).length-1-d.lastIndexOf("/"),p=y,g=0;continue}}else if(d.length===2||d.length===1){d="",h=0,p=y,g=0;continue}}c&&(d.length>0?d+="/..":d="..",h=2)}else d.length>0?d+="/"+u.slice(p+1,y):d=u.slice(p+1,y),h=y-p-1;p=y,g=0}else f===46&&g!==-1?++g:g=-1}return d}a(o,"r");var l={resolve:a(function(){for(var u,c="",f=!1,d=arguments.length-1;d>=-1&&!f;d--){var h;d>=0?h=arguments[d]:(u===void 0&&(u=process.cwd()),h=u),s(h),h.length!==0&&(c=h+"/"+c,f=h.charCodeAt(0)===47)}return c=o(c,!f),f?c.length>0?"/"+c:"/":c.length>0?c:"."},"resolve"),normalize:a(function(u){if(s(u),u.length===0)return".";var c=u.charCodeAt(0)===47,f=u.charCodeAt(u.length-1)===47;return(u=o(u,!c)).length!==0||c||(u="."),u.length>0&&f&&(u+="/"),c?"/"+u:u},"normalize"),isAbsolute:a(function(u){return s(u),u.length>0&&u.charCodeAt(0)===47},"isAbsolute"),join:a(function(){if(arguments.length===0)return".";for(var u,c=0;c<arguments.length;++c){var f=arguments[c];s(f),f.length>0&&(u===void 0?u=f:u+="/"+f)}return u===void 0?".":l.normalize(u)},"join"),relative:a(function(u,c){if(s(u),s(c),u===c||(u=l.resolve(u))===(c=l.resolve(c)))return"";for(var f=1;f<u.length&&u.charCodeAt(f)===47;++f);for(var d=u.length,h=d-f,p=1;p<c.length&&c.charCodeAt(p)===47;++p);for(var g=c.length-p,y=h<g?h:g,v=-1,x=0;x<=y;++x){if(x===y){if(g>y){if(c.charCodeAt(p+x)===47)return c.slice(p+x+1);if(x===0)return c.slice(p+x)}else h>y&&(u.charCodeAt(f+x)===47?v=x:x===0&&(v=0));break}var A=u.charCodeAt(f+x);if(A!==c.charCodeAt(p+x))break;A===47&&(v=x)}var R="";for(x=f+v+1;x<=d;++x)x!==d&&u.charCodeAt(x)!==47||(R.length===0?R+="..":R+="/..");return R.length>0?R+c.slice(p+v):(p+=v,c.charCodeAt(p)===47&&++p,c.slice(p))},"relative"),_makeLong:a(function(u){return u},"_makeLong"),dirname:a(function(u){if(s(u),u.length===0)return".";for(var c=u.charCodeAt(0),f=c===47,d=-1,h=!0,p=u.length-1;p>=1;--p)if((c=u.charCodeAt(p))===47){if(!h){d=p;break}}else h=!1;return d===-1?f?"/":".":f&&d===1?"//":u.slice(0,d)},"dirname"),basename:a(function(u,c){if(c!==void 0&&typeof c!="string")throw new TypeError('"ext" argument must be a string');s(u);var f,d=0,h=-1,p=!0;if(c!==void 0&&c.length>0&&c.length<=u.length){if(c.length===u.length&&c===u)return"";var g=c.length-1,y=-1;for(f=u.length-1;f>=0;--f){var v=u.charCodeAt(f);if(v===47){if(!p){d=f+1;break}}else y===-1&&(p=!1,y=f+1),g>=0&&(v===c.charCodeAt(g)?--g==-1&&(h=f):(g=-1,h=y))}return d===h?h=y:h===-1&&(h=u.length),u.slice(d,h)}for(f=u.length-1;f>=0;--f)if(u.charCodeAt(f)===47){if(!p){d=f+1;break}}else h===-1&&(p=!1,h=f+1);return h===-1?"":u.slice(d,h)},"basename"),extname:a(function(u){s(u);for(var c=-1,f=0,d=-1,h=!0,p=0,g=u.length-1;g>=0;--g){var y=u.charCodeAt(g);if(y!==47)d===-1&&(h=!1,d=g+1),y===46?c===-1?c=g:p!==1&&(p=1):c!==-1&&(p=-1);else if(!h){f=g+1;break}}return c===-1||d===-1||p===0||p===1&&c===d-1&&c===f+1?"":u.slice(c,d)},"extname"),format:a(function(u){if(u===null||typeof u!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof u);return function(c,f){var d=f.dir||f.root,h=f.base||(f.name||"")+(f.ext||"");return d?d===f.root?d+h:d+"/"+h:h}(0,u)},"format"),parse:a(function(u){s(u);var c={root:"",dir:"",base:"",ext:"",name:""};if(u.length===0)return c;var f,d=u.charCodeAt(0),h=d===47;h?(c.root="/",f=1):f=0;for(var p=-1,g=0,y=-1,v=!0,x=u.length-1,A=0;x>=f;--x)if((d=u.charCodeAt(x))!==47)y===-1&&(v=!1,y=x+1),d===46?p===-1?p=x:A!==1&&(A=1):p!==-1&&(A=-1);else if(!v){g=x+1;break}return p===-1||y===-1||A===0||A===1&&p===y-1&&p===g+1?y!==-1&&(c.base=c.name=g===0&&h?u.slice(1,y):u.slice(g,y)):(g===0&&h?(c.name=u.slice(1,p),c.base=u.slice(1,y)):(c.name=u.slice(g,p),c.base=u.slice(g,y)),c.ext=u.slice(p,y)),g>0?c.dir=u.slice(0,g-1):h&&(c.dir="/"),c},"parse"),sep:"/",delimiter:":",win32:null,posix:null};l.posix=l,i.exports=l}},e={};function t(i){var s=e[i];if(s!==void 0)return s.exports;var o=e[i]={exports:{}};return r[i](o,o.exports,t),o.exports}a(t,"r"),t.d=(i,s)=>{for(var o in s)t.o(s,o)&&!t.o(i,o)&&Object.defineProperty(i,o,{enumerable:!0,get:s[o]})},t.o=(i,s)=>Object.prototype.hasOwnProperty.call(i,s),t.r=i=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})};var n={};(()=>{let i;t.r(n),t.d(n,{URI:a(()=>h,"URI"),Utils:a(()=>Tt,"Utils")}),typeof process=="object"?i=process.platform==="win32":typeof navigator=="object"&&(i=navigator.userAgent.indexOf("Windows")>=0);let s=/^\w[\w\d+.-]*$/,o=/^\//,l=/^\/\//;function u(k,T){if(!k.scheme&&T)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${k.authority}", path: "${k.path}", query: "${k.query}", fragment: "${k.fragment}"}`);if(k.scheme&&!s.test(k.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(k.path){if(k.authority){if(!o.test(k.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(l.test(k.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}a(u,"s");let c="",f="/",d=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class h{static{a(this,"f")}static isUri(T){return T instanceof h||!!T&&typeof T.authority=="string"&&typeof T.fragment=="string"&&typeof T.path=="string"&&typeof T.query=="string"&&typeof T.scheme=="string"&&typeof T.fsPath=="string"&&typeof T.with=="function"&&typeof T.toString=="function"}scheme;authority;path;query;fragment;constructor(T,C,N,V,O,L=!1){typeof T=="object"?(this.scheme=T.scheme||c,this.authority=T.authority||c,this.path=T.path||c,this.query=T.query||c,this.fragment=T.fragment||c):(this.scheme=function(Le,Oe){return Le||Oe?Le:"file"}(T,L),this.authority=C||c,this.path=function(Le,Oe){switch(Le){case"https":case"http":case"file":Oe?Oe[0]!==f&&(Oe=f+Oe):Oe=f}return Oe}(this.scheme,N||c),this.query=V||c,this.fragment=O||c,u(this,L))}get fsPath(){return A(this,!1)}with(T){if(!T)return this;let{scheme:C,authority:N,path:V,query:O,fragment:L}=T;return C===void 0?C=this.scheme:C===null&&(C=c),N===void 0?N=this.authority:N===null&&(N=c),V===void 0?V=this.path:V===null&&(V=c),O===void 0?O=this.query:O===null&&(O=c),L===void 0?L=this.fragment:L===null&&(L=c),C===this.scheme&&N===this.authority&&V===this.path&&O===this.query&&L===this.fragment?this:new g(C,N,V,O,L)}static parse(T,C=!1){let N=d.exec(T);return N?new g(N[2]||c,Te(N[4]||c),Te(N[5]||c),Te(N[7]||c),Te(N[9]||c),C):new g(c,c,c,c,c)}static file(T){let C=c;if(i&&(T=T.replace(/\\/g,f)),T[0]===f&&T[1]===f){let N=T.indexOf(f,2);N===-1?(C=T.substring(2),T=f):(C=T.substring(2,N),T=T.substring(N)||f)}return new g("file",C,T,c,c)}static from(T){let C=new g(T.scheme,T.authority,T.path,T.query,T.fragment);return u(C,!0),C}toString(T=!1){return R(this,T)}toJSON(){return this}static revive(T){if(T){if(T instanceof h)return T;{let C=new g(T);return C._formatted=T.external,C._fsPath=T._sep===p?T.fsPath:null,C}}return T}}let p=i?1:void 0;class g extends h{static{a(this,"l")}_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=A(this,!1)),this._fsPath}toString(T=!1){return T?R(this,!0):(this._formatted||(this._formatted=R(this,!1)),this._formatted)}toJSON(){let T={$mid:1};return this._fsPath&&(T.fsPath=this._fsPath,T._sep=p),this._formatted&&(T.external=this._formatted),this.path&&(T.path=this.path),this.scheme&&(T.scheme=this.scheme),this.authority&&(T.authority=this.authority),this.query&&(T.query=this.query),this.fragment&&(T.fragment=this.fragment),T}}let y={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function v(k,T,C){let N,V=-1;for(let O=0;O<k.length;O++){let L=k.charCodeAt(O);if(L>=97&&L<=122||L>=65&&L<=90||L>=48&&L<=57||L===45||L===46||L===95||L===126||T&&L===47||C&&L===91||C&&L===93||C&&L===58)V!==-1&&(N+=encodeURIComponent(k.substring(V,O)),V=-1),N!==void 0&&(N+=k.charAt(O));else{N===void 0&&(N=k.substr(0,O));let Le=y[L];Le!==void 0?(V!==-1&&(N+=encodeURIComponent(k.substring(V,O)),V=-1),N+=Le):V===-1&&(V=O)}}return V!==-1&&(N+=encodeURIComponent(k.substring(V))),N!==void 0?N:k}a(v,"d");function x(k){let T;for(let C=0;C<k.length;C++){let N=k.charCodeAt(C);N===35||N===63?(T===void 0&&(T=k.substr(0,C)),T+=y[N]):T!==void 0&&(T+=k[C])}return T!==void 0?T:k}a(x,"p");function A(k,T){let C;return C=k.authority&&k.path.length>1&&k.scheme==="file"?`//${k.authority}${k.path}`:k.path.charCodeAt(0)===47&&(k.path.charCodeAt(1)>=65&&k.path.charCodeAt(1)<=90||k.path.charCodeAt(1)>=97&&k.path.charCodeAt(1)<=122)&&k.path.charCodeAt(2)===58?T?k.path.substr(1):k.path[1].toLowerCase()+k.path.substr(2):k.path,i&&(C=C.replace(/\//g,"\\")),C}a(A,"m");function R(k,T){let C=T?x:v,N="",{scheme:V,authority:O,path:L,query:Le,fragment:Oe}=k;if(V&&(N+=V,N+=":"),(O||V==="file")&&(N+=f,N+=f),O){let ee=O.indexOf("@");if(ee!==-1){let Ht=O.substr(0,ee);O=O.substr(ee+1),ee=Ht.lastIndexOf(":"),ee===-1?N+=C(Ht,!1,!1):(N+=C(Ht.substr(0,ee),!1,!1),N+=":",N+=C(Ht.substr(ee+1),!1,!0)),N+="@"}O=O.toLowerCase(),ee=O.lastIndexOf(":"),ee===-1?N+=C(O,!1,!0):(N+=C(O.substr(0,ee),!1,!0),N+=O.substr(ee))}if(L){if(L.length>=3&&L.charCodeAt(0)===47&&L.charCodeAt(2)===58){let ee=L.charCodeAt(1);ee>=65&&ee<=90&&(L=`/${String.fromCharCode(ee+32)}:${L.substr(3)}`)}else if(L.length>=2&&L.charCodeAt(1)===58){let ee=L.charCodeAt(0);ee>=65&&ee<=90&&(L=`${String.fromCharCode(ee+32)}:${L.substr(2)}`)}N+=C(L,!0,!1)}return Le&&(N+="?",N+=C(Le,!1,!1)),Oe&&(N+="#",N+=T?Oe:v(Oe,!1,!1)),N}a(R,"y");function P(k){try{return decodeURIComponent(k)}catch{return k.length>3?k.substr(0,3)+P(k.substr(3)):k}}a(P,"v");let b=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function Te(k){return k.match(b)?k.replace(b,T=>P(T)):k}a(Te,"C");var kr=t(470);let Ae=kr.posix||kr,Mt="/";var Tt;(function(k){k.joinPath=function(T,...C){return T.with({path:Ae.join(T.path,...C)})},k.resolvePath=function(T,...C){let N=T.path,V=!1;N[0]!==Mt&&(N=Mt+N,V=!0);let O=Ae.resolve(N,...C);return V&&O[0]===Mt&&!T.authority&&(O=O.substring(1)),T.with({path:O})},k.dirname=function(T){if(T.path.length===0||T.path===Mt)return T;let C=Ae.dirname(T.path);return C.length===1&&C.charCodeAt(0)===46&&(C=""),T.with({path:C})},k.basename=function(T){return Ae.basename(T.path)},k.extname=function(T){return Ae.extname(T.path)}})(Tt||(Tt={}))})(),Qd=n})();var{URI:Ue,Utils:Hn}=Qd;var Be;(function(r){r.basename=Hn.basename,r.dirname=Hn.dirname,r.extname=Hn.extname,r.joinPath=Hn.joinPath,r.resolvePath=Hn.resolvePath;function e(i,s){return i?.toString()===s?.toString()}a(e,"equals"),r.equals=e;function t(i,s){let o=typeof i=="string"?i:i.path,l=typeof s=="string"?s:s.path,u=o.split("/").filter(p=>p.length>0),c=l.split("/").filter(p=>p.length>0),f=0;for(;f<u.length&&u[f]===c[f];f++);let d="../".repeat(u.length-f),h=c.slice(f).join("/");return d+h}a(t,"relative"),r.relative=t;function n(i){return Ue.parse(i.toString()).toString()}a(n,"normalize"),r.normalize=n})(Be||(Be={}));var Y;(function(r){r[r.Changed=0]="Changed",r[r.Parsed=1]="Parsed",r[r.IndexedContent=2]="IndexedContent",r[r.ComputedScopes=3]="ComputedScopes",r[r.Linked=4]="Linked",r[r.IndexedReferences=5]="IndexedReferences",r[r.Validated=6]="Validated"})(Y||(Y={}));var zi=class{static{a(this,"DefaultLangiumDocumentFactory")}constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}async fromUri(e,t=S.CancellationToken.None){let n=await this.fileSystemProvider.readFile(e);return this.createAsync(e,n,t)}fromTextDocument(e,t,n){return t=t??Ue.parse(e.uri),S.CancellationToken.is(n)?this.createAsync(t,e,n):this.create(t,e,n)}fromString(e,t,n){return S.CancellationToken.is(n)?this.createAsync(t,e,n):this.create(t,e,n)}fromModel(e,t){return this.create(t,{$model:e})}create(e,t,n){if(typeof t=="string"){let i=this.parse(e,t,n);return this.createLangiumDocument(i,e,void 0,t)}else if("$model"in t){let i={value:t.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(i,e)}else{let i=this.parse(e,t.getText(),n);return this.createLangiumDocument(i,e,t)}}async createAsync(e,t,n){if(typeof t=="string"){let i=await this.parseAsync(e,t,n);return this.createLangiumDocument(i,e,void 0,t)}else{let i=await this.parseAsync(e,t.getText(),n);return this.createLangiumDocument(i,e,t)}}createLangiumDocument(e,t,n,i){let s;if(n)s={parseResult:e,uri:t,state:Y.Parsed,references:[],textDocument:n};else{let o=this.createTextDocumentGetter(t,i);s={parseResult:e,uri:t,state:Y.Parsed,references:[],get textDocument(){return o()}}}return e.value.$document=s,s}async update(e,t){var n,i;let s=(n=e.parseResult.value.$cstNode)===null||n===void 0?void 0:n.root.fullText,o=(i=this.textDocuments)===null||i===void 0?void 0:i.get(e.uri.toString()),l=o?o.getText():await this.fileSystemProvider.readFile(e.uri);if(o)Object.defineProperty(e,"textDocument",{value:o});else{let u=this.createTextDocumentGetter(e.uri,l);Object.defineProperty(e,"textDocument",{get:u})}return s!==l&&(e.parseResult=await this.parseAsync(e.uri,l,t),e.parseResult.value.$document=e),e.state=Y.Parsed,e}parse(e,t,n){return this.serviceRegistry.getServices(e).parser.LangiumParser.parse(t,n)}parseAsync(e,t,n){return this.serviceRegistry.getServices(e).parser.AsyncParser.parse(t,n)}createTextDocumentGetter(e,t){let n=this.serviceRegistry,i;return()=>i??(i=jn.create(e.toString(),n.getServices(e).LanguageMetaData.languageId,0,t??""))}},qi=class{static{a(this,"DefaultLangiumDocuments")}constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.serviceRegistry=e.ServiceRegistry}get all(){return j(this.documentMap.values())}addDocument(e){let t=e.uri.toString();if(this.documentMap.has(t))throw new Error(`A document with the URI '${t}' is already present.`);this.documentMap.set(t,e)}getDocument(e){let t=e.toString();return this.documentMap.get(t)}async getOrCreateDocument(e,t){let n=this.getDocument(e);return n||(n=await this.langiumDocumentFactory.fromUri(e,t),this.addDocument(n),n)}createDocument(e,t,n){if(n)return this.langiumDocumentFactory.fromString(t,e,n).then(i=>(this.addDocument(i),i));{let i=this.langiumDocumentFactory.fromString(t,e);return this.addDocument(i),i}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){let t=e.toString(),n=this.documentMap.get(t);return n&&(this.serviceRegistry.getServices(e).references.Linker.unlink(n),n.state=Y.Changed,n.precomputedScopes=void 0,n.diagnostics=void 0),n}deleteDocument(e){let t=e.toString(),n=this.documentMap.get(t);return n&&(n.state=Y.Changed,this.documentMap.delete(t)),n}};var hu=Symbol("ref_resolving"),Xi=class{static{a(this,"DefaultLinker")}constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}async link(e,t=S.CancellationToken.None){for(let n of Je(e.parseResult.value))await ce(t),pn(n).forEach(i=>this.doLink(i,e))}doLink(e,t){var n;let i=e.reference;if(i._ref===void 0){i._ref=hu;try{let s=this.getCandidate(e);if(Qt(s))i._ref=s;else if(i._nodeDescription=s,this.langiumDocuments().hasDocument(s.documentUri)){let o=this.loadAstNode(s);i._ref=o??this.createLinkingError(e,s)}else i._ref=void 0}catch(s){console.error(`An error occurred while resolving reference to '${i.$refText}':`,s);let o=(n=s.message)!==null&&n!==void 0?n:String(s);i._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${i.$refText}': ${o}`})}t.references.push(i)}}unlink(e){for(let t of e.references)delete t._ref,delete t._nodeDescription;e.references=[]}getCandidate(e){let n=this.scopeProvider.getScope(e).getElement(e.reference.$refText);return n??this.createLinkingError(e)}buildReference(e,t,n,i){let s=this,o={$refNode:n,$refText:i,get ref(){var l;if(le(this._ref))return this._ref;if(po(this._nodeDescription)){let u=s.loadAstNode(this._nodeDescription);this._ref=u??s.createLinkingError({reference:o,container:e,property:t},this._nodeDescription)}else if(this._ref===void 0){this._ref=hu;let u=ci(e).$document,c=s.getLinkedNode({reference:o,container:e,property:t});if(c.error&&u&&u.state<Y.ComputedScopes)return this._ref=void 0;this._ref=(l=c.node)!==null&&l!==void 0?l:c.error,this._nodeDescription=c.descr,u?.references.push(this)}else if(this._ref===hu)throw new Error(`Cyclic reference resolution detected: ${s.astNodeLocator.getAstNodePath(e)}/${t} (symbol '${i}')`);return le(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return Qt(this._ref)?this._ref:void 0}};return o}getLinkedNode(e){var t;try{let n=this.getCandidate(e);if(Qt(n))return{error:n};let i=this.loadAstNode(n);return i?{node:i,descr:n}:{descr:n,error:this.createLinkingError(e,n)}}catch(n){console.error(`An error occurred while resolving reference to '${e.reference.$refText}':`,n);let i=(t=n.message)!==null&&t!==void 0?t:String(n);return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${i}`})}}}loadAstNode(e){if(e.node)return e.node;let t=this.langiumDocuments().getDocument(e.documentUri);if(t)return this.astNodeLocator.getAstNode(t.parseResult.value,e.path)}createLinkingError(e,t){let n=ci(e.container).$document;n&&n.state<Y.ComputedScopes&&console.warn(`Attempted reference resolution before document reached ComputedScopes state (${n.uri}).`);let i=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${i} named '${e.reference.$refText}'.`,targetDescription:t})}};function Zd(r){return typeof r.name=="string"}a(Zd,"isNamed");var Yi=class{static{a(this,"DefaultNameProvider")}getName(e){if(Zd(e))return e.name}getNameNode(e){return gi(e.$cstNode,"name")}};var Ji=class{static{a(this,"DefaultReferences")}constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){let t=tl(e),n=e.astNode;if(t&&n){let i=n[t.feature];if(Ee(i))return i.ref;if(Array.isArray(i)){for(let s of i)if(Ee(s)&&s.$refNode&&s.$refNode.offset<=e.offset&&s.$refNode.end>=e.end)return s.ref}}if(n){let i=this.nameProvider.getNameNode(n);if(i&&(i===e||go(e,i)))return n}}}findDeclarationNode(e){let t=this.findDeclaration(e);if(t?.$cstNode){let n=this.nameProvider.getNameNode(t);return n??t.$cstNode}}findReferences(e,t){let n=[];if(t.includeDeclaration){let s=this.getReferenceToSelf(e);s&&n.push(s)}let i=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return t.documentUri&&(i=i.filter(s=>Be.equals(s.sourceUri,t.documentUri))),n.push(...i),j(n)}getReferenceToSelf(e){let t=this.nameProvider.getNameNode(e);if(t){let n=we(e),i=this.nodeLocator.getAstNodePath(e);return{sourceUri:n.uri,sourcePath:i,targetUri:n.uri,targetPath:i,segment:er(t),local:!0}}}};var st=class{static{a(this,"MultiMap")}constructor(e){if(this.map=new Map,e)for(let[t,n]of e)this.add(t,n)}get size(){return Ir.sum(j(this.map.values()).map(e=>e.length))}clear(){this.map.clear()}delete(e,t){if(t===void 0)return this.map.delete(e);{let n=this.map.get(e);if(n){let i=n.indexOf(t);if(i>=0)return n.length===1?this.map.delete(e):n.splice(i,1),!0}return!1}}get(e){var t;return(t=this.map.get(e))!==null&&t!==void 0?t:[]}has(e,t){if(t===void 0)return this.map.has(e);{let n=this.map.get(e);return n?n.indexOf(t)>=0:!1}}add(e,t){return this.map.has(e)?this.map.get(e).push(t):this.map.set(e,[t]),this}addAll(e,t){return this.map.has(e)?this.map.get(e).push(...t):this.map.set(e,Array.from(t)),this}forEach(e){this.map.forEach((t,n)=>t.forEach(i=>e(i,n,this)))}[Symbol.iterator](){return this.entries().iterator()}entries(){return j(this.map.entries()).flatMap(([e,t])=>t.map(n=>[e,n]))}keys(){return j(this.map.keys())}values(){return j(this.map.values()).flat()}entriesGroupedByKey(){return j(this.map.entries())}},Rr=class{static{a(this,"BiMap")}get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(let[t,n]of e)this.set(t,n)}clear(){this.map.clear(),this.inverse.clear()}set(e,t){return this.map.set(e,t),this.inverse.set(t,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){let t=this.map.get(e);return t!==void 0?(this.map.delete(e),this.inverse.delete(t),!0):!1}};var Qi=class{static{a(this,"DefaultScopeComputation")}constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}async computeExports(e,t=S.CancellationToken.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,t)}async computeExportsForNode(e,t,n=fi,i=S.CancellationToken.None){let s=[];this.exportNode(e,s,t);for(let o of n(e))await ce(i),this.exportNode(o,s,t);return s}exportNode(e,t,n){let i=this.nameProvider.getName(e);i&&t.push(this.descriptions.createDescription(e,i,n))}async computeLocalScopes(e,t=S.CancellationToken.None){let n=e.parseResult.value,i=new st;for(let s of ft(n))await ce(t),this.processNode(s,e,i);return i}processNode(e,t,n){let i=e.$container;if(i){let s=this.nameProvider.getName(e);s&&n.add(i,this.descriptions.createDescription(e,s,t))}}};var zn=class{static{a(this,"StreamScope")}constructor(e,t,n){var i;this.elements=e,this.outerScope=t,this.caseInsensitive=(i=n?.caseInsensitive)!==null&&i!==void 0?i:!1}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){let t=this.caseInsensitive?this.elements.find(n=>n.name.toLowerCase()===e.toLowerCase()):this.elements.find(n=>n.name===e);if(t)return t;if(this.outerScope)return this.outerScope.getElement(e)}},Zi=class{static{a(this,"MapScope")}constructor(e,t,n){var i;this.elements=new Map,this.caseInsensitive=(i=n?.caseInsensitive)!==null&&i!==void 0?i:!1;for(let s of e){let o=this.caseInsensitive?s.name.toLowerCase():s.name;this.elements.set(o,s)}this.outerScope=t}getElement(e){let t=this.caseInsensitive?e.toLowerCase():e,n=this.elements.get(t);if(n)return n;if(this.outerScope)return this.outerScope.getElement(e)}getAllElements(){let e=j(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}},fg={getElement(){},getAllElements(){return ei}};var qn=class{static{a(this,"DisposableCache")}constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach(e=>e.dispose())}throwIfDisposed(){if(this.isDisposed)throw new Error("This cache has already been disposed")}},es=class extends qn{static{a(this,"SimpleCache")}constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,t){this.throwIfDisposed(),this.cache.set(e,t)}get(e,t){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(t){let n=t();return this.cache.set(e,n),n}else return}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}},xr=class extends qn{static{a(this,"ContextCache")}constructor(e){super(),this.cache=new Map,this.converter=e??(t=>t)}has(e,t){return this.throwIfDisposed(),this.cacheForContext(e).has(t)}set(e,t,n){this.throwIfDisposed(),this.cacheForContext(e).set(t,n)}get(e,t,n){this.throwIfDisposed();let i=this.cacheForContext(e);if(i.has(t))return i.get(t);if(n){let s=n();return i.set(t,s),s}else return}delete(e,t){return this.throwIfDisposed(),this.cacheForContext(e).delete(t)}clear(e){if(this.throwIfDisposed(),e){let t=this.converter(e);this.cache.delete(t)}else this.cache.clear()}cacheForContext(e){let t=this.converter(e),n=this.cache.get(t);return n||(n=new Map,this.cache.set(t,n)),n}},Ua=class extends xr{static{a(this,"DocumentCache")}constructor(e,t){super(n=>n.toString()),t?(this.toDispose.push(e.workspace.DocumentBuilder.onDocumentPhase(t,n=>{this.clear(n.uri.toString())})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{for(let s of i)this.clear(s)}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{let s=n.concat(i);for(let o of s)this.clear(o)}))}},Xn=class extends es{static{a(this,"WorkspaceCache")}constructor(e,t){super(),t?(this.toDispose.push(e.workspace.DocumentBuilder.onBuildPhase(t,()=>{this.clear()})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{i.length>0&&this.clear()}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(()=>{this.clear()}))}};var ts=class{static{a(this,"DefaultScopeProvider")}constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new Xn(e.shared)}getScope(e){let t=[],n=this.reflection.getReferenceType(e),i=we(e.container).precomputedScopes;if(i){let o=e.container;do{let l=i.get(o);l.length>0&&t.push(j(l).filter(u=>this.reflection.isSubtype(u.type,n))),o=o.$container}while(o)}let s=this.getGlobalScope(n,e);for(let o=t.length-1;o>=0;o--)s=this.createScope(t[o],s);return s}createScope(e,t,n){return new zn(j(e),t,n)}createScopeForNodes(e,t,n){let i=j(e).map(s=>{let o=this.nameProvider.getName(s);if(o)return this.descriptions.createDescription(s,o)}).nonNullable();return new zn(i,t,n)}getGlobalScope(e,t){return this.globalScopeCache.get(e,()=>new Zi(this.indexManager.allElements(e)))}};function pu(r){return typeof r.$comment=="string"}a(pu,"isAstNodeWithComment");function eh(r){return typeof r=="object"&&!!r&&("$ref"in r||"$error"in r)}a(eh,"isIntermediateReference");var rs=class{static{a(this,"DefaultJsonSerializer")}constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,t){let n=t??{},i=t?.replacer,s=a((l,u)=>this.replacer(l,u,n),"defaultReplacer"),o=i?(l,u)=>i(l,u,s):s;try{return this.currentDocument=we(e),JSON.stringify(e,o,t?.space)}finally{this.currentDocument=void 0}}deserialize(e,t){let n=t??{},i=JSON.parse(e);return this.linkNode(i,i,n),i}replacer(e,t,{refText:n,sourceText:i,textRegions:s,comments:o,uriConverter:l}){var u,c,f,d;if(!this.ignoreProperties.has(e))if(Ee(t)){let h=t.ref,p=n?t.$refText:void 0;if(h){let g=we(h),y="";this.currentDocument&&this.currentDocument!==g&&(l?y=l(g.uri,t):y=g.uri.toString());let v=this.astNodeLocator.getAstNodePath(h);return{$ref:`${y}#${v}`,$refText:p}}else return{$error:(c=(u=t.error)===null||u===void 0?void 0:u.message)!==null&&c!==void 0?c:"Could not resolve reference",$refText:p}}else if(le(t)){let h;if(s&&(h=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},t)),(!e||t.$document)&&h?.$textRegion&&(h.$textRegion.documentURI=(f=this.currentDocument)===null||f===void 0?void 0:f.uri.toString())),i&&!e&&(h??(h=Object.assign({},t)),h.$sourceText=(d=t.$cstNode)===null||d===void 0?void 0:d.text),o){h??(h=Object.assign({},t));let p=this.commentProvider.getComment(t);p&&(h.$comment=p.replace(/\r/g,""))}return h??t}else return t}addAstNodeRegionWithAssignmentsTo(e){let t=a(n=>({offset:n.offset,end:n.end,length:n.length,range:n.range}),"createDocumentSegment");if(e.$cstNode){let n=e.$textRegion=t(e.$cstNode),i=n.assignments={};return Object.keys(e).filter(s=>!s.startsWith("$")).forEach(s=>{let o=Jo(e.$cstNode,s).map(t);o.length!==0&&(i[s]=o)}),e}}linkNode(e,t,n,i,s,o){for(let[u,c]of Object.entries(e))if(Array.isArray(c))for(let f=0;f<c.length;f++){let d=c[f];eh(d)?c[f]=this.reviveReference(e,u,t,d,n):le(d)&&this.linkNode(d,t,n,e,u,f)}else eh(c)?e[u]=this.reviveReference(e,u,t,c,n):le(c)&&this.linkNode(c,t,n,e,u);let l=e;l.$container=i,l.$containerProperty=s,l.$containerIndex=o}reviveReference(e,t,n,i,s){let o=i.$refText,l=i.$error;if(i.$ref){let u=this.getRefNode(n,i.$ref,s.uriConverter);if(le(u))return o||(o=this.nameProvider.getName(u)),{$refText:o??"",ref:u};l=u}if(l){let u={$refText:o??""};return u.error={container:e,property:t,message:l,reference:u},u}else return}getRefNode(e,t,n){try{let i=t.indexOf("#");if(i===0){let u=this.astNodeLocator.getAstNode(e,t.substring(1));return u||"Could not resolve path: "+t}if(i<0){let u=n?n(t):Ue.parse(t),c=this.langiumDocuments.getDocument(u);return c?c.parseResult.value:"Could not find document for URI: "+t}let s=n?n(t.substring(0,i)):Ue.parse(t.substring(0,i)),o=this.langiumDocuments.getDocument(s);if(!o)return"Could not find document for URI: "+t;if(i===t.length-1)return o.parseResult.value;let l=this.astNodeLocator.getAstNode(o.parseResult.value,t.substring(i+1));return l||"Could not resolve URI: "+t}catch(i){return String(i)}}};var ns=class{static{a(this,"DefaultServiceRegistry")}get map(){return this.fileExtensionMap}constructor(e){this.languageIdMap=new Map,this.fileExtensionMap=new Map,this.textDocuments=e?.workspace.TextDocuments}register(e){let t=e.LanguageMetaData;for(let n of t.fileExtensions)this.fileExtensionMap.has(n)&&console.warn(`The file extension ${n} is used by multiple languages. It is now assigned to '${t.languageId}'.`),this.fileExtensionMap.set(n,e);this.languageIdMap.set(t.languageId,e),this.languageIdMap.size===1?this.singleton=e:this.singleton=void 0}getServices(e){var t,n;if(this.singleton!==void 0)return this.singleton;if(this.languageIdMap.size===0)throw new Error("The service registry is empty. Use `register` to register the services of a language.");let i=(n=(t=this.textDocuments)===null||t===void 0?void 0:t.get(e))===null||n===void 0?void 0:n.languageId;if(i!==void 0){let l=this.languageIdMap.get(i);if(l)return l}let s=Be.extname(e),o=this.fileExtensionMap.get(s);if(!o)throw i?new Error(`The service registry contains no services for the extension '${s}' for language '${i}'.`):new Error(`The service registry contains no services for the extension '${s}'.`);return o}hasServices(e){try{return this.getServices(e),!0}catch{return!1}}get all(){return Array.from(this.languageIdMap.values())}};function Er(r){return{code:r}}a(Er,"diagnosticData");var Yn;(function(r){r.all=["fast","slow","built-in"]})(Yn||(Yn={}));var is=class{static{a(this,"ValidationRegistry")}constructor(e){this.entries=new st,this.entriesBefore=[],this.entriesAfter=[],this.reflection=e.shared.AstReflection}register(e,t=this,n="fast"){if(n==="built-in")throw new Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(let[i,s]of Object.entries(e)){let o=s;if(Array.isArray(o))for(let l of o){let u={check:this.wrapValidationException(l,t),category:n};this.addEntry(i,u)}else if(typeof o=="function"){let l={check:this.wrapValidationException(o,t),category:n};this.addEntry(i,l)}else ct(o)}}wrapValidationException(e,t){return async(n,i,s)=>{await this.handleException(()=>e.call(t,n,i,s),"An error occurred during validation",i,n)}}async handleException(e,t,n,i){try{await e()}catch(s){if(gt(s))throw s;console.error(`${t}:`,s),s instanceof Error&&s.stack&&console.error(s.stack);let o=s instanceof Error?s.message:String(s);n("error",`${t}: ${o}`,{node:i})}}addEntry(e,t){if(e==="AstNode"){this.entries.add("AstNode",t);return}for(let n of this.reflection.getAllSubTypes(e))this.entries.add(n,t)}getChecks(e,t){let n=j(this.entries.get(e)).concat(this.entries.get("AstNode"));return t&&(n=n.filter(i=>t.includes(i.category))),n.map(i=>i.check)}registerBeforeDocument(e,t=this){this.entriesBefore.push(this.wrapPreparationException(e,"An error occurred during set-up of the validation",t))}registerAfterDocument(e,t=this){this.entriesAfter.push(this.wrapPreparationException(e,"An error occurred during tear-down of the validation",t))}wrapPreparationException(e,t,n){return async(i,s,o,l)=>{await this.handleException(()=>e.call(n,i,s,o,l),t,s,i)}}get checksBefore(){return this.entriesBefore}get checksAfter(){return this.entriesAfter}};var ss=class{static{a(this,"DefaultDocumentValidator")}constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}async validateDocument(e,t={},n=S.CancellationToken.None){let i=e.parseResult,s=[];if(await ce(n),(!t.categories||t.categories.includes("built-in"))&&(this.processLexingErrors(i,s,t),t.stopAfterLexingErrors&&s.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===Qe.LexingError})||(this.processParsingErrors(i,s,t),t.stopAfterParsingErrors&&s.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===Qe.ParsingError}))||(this.processLinkingErrors(e,s,t),t.stopAfterLinkingErrors&&s.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===Qe.LinkingError}))))return s;try{s.push(...await this.validateAst(i.value,t,n))}catch(o){if(gt(o))throw o;console.error("An error occurred during validation:",o)}return await ce(n),s}processLexingErrors(e,t,n){var i,s,o;let l=[...e.lexerErrors,...(s=(i=e.lexerReport)===null||i===void 0?void 0:i.diagnostics)!==null&&s!==void 0?s:[]];for(let u of l){let c=(o=u.severity)!==null&&o!==void 0?o:"error",f={severity:Ba(c),range:{start:{line:u.line-1,character:u.column-1},end:{line:u.line-1,character:u.column+u.length-1}},message:u.message,data:rh(c),source:this.getSource()};t.push(f)}}processParsingErrors(e,t,n){for(let i of e.parserErrors){let s;if(isNaN(i.token.startOffset)){if("previousToken"in i){let o=i.previousToken;if(isNaN(o.startOffset)){let l={line:0,character:0};s={start:l,end:l}}else{let l={line:o.endLine-1,character:o.endColumn};s={start:l,end:l}}}}else s=Nr(i.token);if(s){let o={severity:Ba("error"),range:s,message:i.message,data:Er(Qe.ParsingError),source:this.getSource()};t.push(o)}}}processLinkingErrors(e,t,n){for(let i of e.references){let s=i.error;if(s){let o={node:s.container,property:s.property,index:s.index,data:{code:Qe.LinkingError,containerType:s.container.$type,property:s.property,refText:s.reference.$refText}};t.push(this.toDiagnostic("error",s.message,o))}}}async validateAst(e,t,n=S.CancellationToken.None){let i=[],s=a((o,l,u)=>{i.push(this.toDiagnostic(o,l,u))},"acceptor");return await this.validateAstBefore(e,t,s,n),await this.validateAstNodes(e,t,s,n),await this.validateAstAfter(e,t,s,n),i}async validateAstBefore(e,t,n,i=S.CancellationToken.None){var s;let o=this.validationRegistry.checksBefore;for(let l of o)await ce(i),await l(e,n,(s=t.categories)!==null&&s!==void 0?s:[],i)}async validateAstNodes(e,t,n,i=S.CancellationToken.None){await Promise.all(Je(e).map(async s=>{await ce(i);let o=this.validationRegistry.getChecks(s.$type,t.categories);for(let l of o)await l(s,n,i)}))}async validateAstAfter(e,t,n,i=S.CancellationToken.None){var s;let o=this.validationRegistry.checksAfter;for(let l of o)await ce(i),await l(e,n,(s=t.categories)!==null&&s!==void 0?s:[],i)}toDiagnostic(e,t,n){return{message:t,range:th(n),severity:Ba(e),code:n.code,codeDescription:n.codeDescription,tags:n.tags,relatedInformation:n.relatedInformation,data:n.data,source:this.getSource()}}getSource(){return this.metadata.languageId}};function th(r){if(r.range)return r.range;let e;return typeof r.property=="string"?e=gi(r.node.$cstNode,r.property,r.index):typeof r.keyword=="string"&&(e=Zo(r.node.$cstNode,r.keyword,r.index)),e??(e=r.node.$cstNode),e?e.range:{start:{line:0,character:0},end:{line:0,character:0}}}a(th,"getDiagnosticRange");function Ba(r){switch(r){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw new Error("Invalid diagnostic severity: "+r)}}a(Ba,"toDiagnosticSeverity");function rh(r){switch(r){case"error":return Er(Qe.LexingError);case"warning":return Er(Qe.LexingWarning);case"info":return Er(Qe.LexingInfo);case"hint":return Er(Qe.LexingHint);default:throw new Error("Invalid diagnostic severity: "+r)}}a(rh,"toDiagnosticData");var Qe;(function(r){r.LexingError="lexing-error",r.LexingWarning="lexing-warning",r.LexingInfo="lexing-info",r.LexingHint="lexing-hint",r.ParsingError="parsing-error",r.LinkingError="linking-error"})(Qe||(Qe={}));var as=class{static{a(this,"DefaultAstNodeDescriptionProvider")}constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,t,n){let i=n??we(e);t??(t=this.nameProvider.getName(e));let s=this.astNodeLocator.getAstNodePath(e);if(!t)throw new Error(`Node at path ${s} has no name.`);let o,l=a(()=>{var u;return o??(o=er((u=this.nameProvider.getNameNode(e))!==null&&u!==void 0?u:e.$cstNode))},"nameSegmentGetter");return{node:e,name:t,get nameSegment(){return l()},selectionSegment:er(e.$cstNode),type:e.$type,documentUri:i.uri,path:s}}},os=class{static{a(this,"DefaultReferenceDescriptionProvider")}constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}async createDescriptions(e,t=S.CancellationToken.None){let n=[],i=e.parseResult.value;for(let s of Je(i))await ce(t),pn(s).filter(o=>!Qt(o)).forEach(o=>{let l=this.createDescription(o);l&&n.push(l)});return n}createDescription(e){let t=e.reference.$nodeDescription,n=e.reference.$refNode;if(!t||!n)return;let i=we(e.container).uri;return{sourceUri:i,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:t.documentUri,targetPath:t.path,segment:er(n),local:Be.equals(t.documentUri,i)}}};var ls=class{static{a(this,"DefaultAstNodeLocator")}constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){let t=this.getAstNodePath(e.$container),n=this.getPathSegment(e);return t+this.segmentSeparator+n}return""}getPathSegment({$containerProperty:e,$containerIndex:t}){if(!e)throw new Error("Missing '$containerProperty' in AST node.");return t!==void 0?e+this.indexSeparator+t:e}getAstNode(e,t){return t.split(this.segmentSeparator).reduce((i,s)=>{if(!i||s.length===0)return i;let o=s.indexOf(this.indexSeparator);if(o>0){let l=s.substring(0,o),u=parseInt(s.substring(o+1)),c=i[l];return c?.[u]}return i[s]},e)}};var ne={};B(ne,Vu(lu(),1));var us=class{static{a(this,"DefaultConfigurationProvider")}constructor(e){this._ready=new Ge,this.settings={},this.workspaceConfig=!1,this.onConfigurationSectionUpdateEmitter=new ne.Emitter,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var t,n;this.workspaceConfig=(n=(t=e.capabilities.workspace)===null||t===void 0?void 0:t.configuration)!==null&&n!==void 0?n:!1}async initialized(e){if(this.workspaceConfig){if(e.register){let t=this.serviceRegistry.all;e.register({section:t.map(n=>this.toSectionName(n.LanguageMetaData.languageId))})}if(e.fetchConfiguration){let t=this.serviceRegistry.all.map(i=>({section:this.toSectionName(i.LanguageMetaData.languageId)})),n=await e.fetchConfiguration(t);t.forEach((i,s)=>{this.updateSectionConfiguration(i.section,n[s])})}}this._ready.resolve()}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach(t=>{let n=e.settings[t];this.updateSectionConfiguration(t,n),this.onConfigurationSectionUpdateEmitter.fire({section:t,configuration:n})})}updateSectionConfiguration(e,t){this.settings[e]=t}async getConfiguration(e,t){await this.ready;let n=this.toSectionName(e);if(this.settings[n])return this.settings[n][t]}toSectionName(e){return`${e}`}get onConfigurationSectionUpdate(){return this.onConfigurationSectionUpdateEmitter.event}};var jt;(function(r){function e(t){return{dispose:a(async()=>await t(),"dispose")}}a(e,"create"),r.create=e})(jt||(jt={}));var cs=class{static{a(this,"DefaultDocumentBuilder")}constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new st,this.documentPhaseListeners=new st,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=Y.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.textDocuments=e.workspace.TextDocuments,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}async build(e,t={},n=S.CancellationToken.None){var i,s;for(let o of e){let l=o.uri.toString();if(o.state===Y.Validated){if(typeof t.validation=="boolean"&&t.validation)o.state=Y.IndexedReferences,o.diagnostics=void 0,this.buildState.delete(l);else if(typeof t.validation=="object"){let u=this.buildState.get(l),c=(i=u?.result)===null||i===void 0?void 0:i.validationChecks;if(c){let d=((s=t.validation.categories)!==null&&s!==void 0?s:Yn.all).filter(h=>!c.includes(h));d.length>0&&(this.buildState.set(l,{completed:!1,options:{validation:Object.assign(Object.assign({},t.validation),{categories:d})},result:u.result}),o.state=Y.IndexedReferences)}}}else this.buildState.delete(l)}this.currentState=Y.Changed,await this.emitUpdate(e.map(o=>o.uri),[]),await this.buildDocuments(e,t,n)}async update(e,t,n=S.CancellationToken.None){this.currentState=Y.Changed;for(let o of t)this.langiumDocuments.deleteDocument(o),this.buildState.delete(o.toString()),this.indexManager.remove(o);for(let o of e){if(!this.langiumDocuments.invalidateDocument(o)){let u=this.langiumDocumentFactory.fromModel({$type:"INVALID"},o);u.state=Y.Changed,this.langiumDocuments.addDocument(u)}this.buildState.delete(o.toString())}let i=j(e).concat(t).map(o=>o.toString()).toSet();this.langiumDocuments.all.filter(o=>!i.has(o.uri.toString())&&this.shouldRelink(o,i)).forEach(o=>{this.serviceRegistry.getServices(o.uri).references.Linker.unlink(o),o.state=Math.min(o.state,Y.ComputedScopes),o.diagnostics=void 0}),await this.emitUpdate(e,t),await ce(n);let s=this.sortDocuments(this.langiumDocuments.all.filter(o=>{var l;return o.state<Y.Linked||!(!((l=this.buildState.get(o.uri.toString()))===null||l===void 0)&&l.completed)}).toArray());await this.buildDocuments(s,this.updateBuildOptions,n)}async emitUpdate(e,t){await Promise.all(this.updateListeners.map(n=>n(e,t)))}sortDocuments(e){let t=0,n=e.length-1;for(;t<n;){for(;t<e.length&&this.hasTextDocument(e[t]);)t++;for(;n>=0&&!this.hasTextDocument(e[n]);)n--;t<n&&([e[t],e[n]]=[e[n],e[t]])}return e}hasTextDocument(e){var t;return!!(!((t=this.textDocuments)===null||t===void 0)&&t.get(e.uri))}shouldRelink(e,t){return e.references.some(n=>n.error!==void 0)?!0:this.indexManager.isAffected(e,t)}onUpdate(e){return this.updateListeners.push(e),jt.create(()=>{let t=this.updateListeners.indexOf(e);t>=0&&this.updateListeners.splice(t,1)})}async buildDocuments(e,t,n){this.prepareBuild(e,t),await this.runCancelable(e,Y.Parsed,n,s=>this.langiumDocumentFactory.update(s,n)),await this.runCancelable(e,Y.IndexedContent,n,s=>this.indexManager.updateContent(s,n)),await this.runCancelable(e,Y.ComputedScopes,n,async s=>{let o=this.serviceRegistry.getServices(s.uri).references.ScopeComputation;s.precomputedScopes=await o.computeLocalScopes(s,n)}),await this.runCancelable(e,Y.Linked,n,s=>this.serviceRegistry.getServices(s.uri).references.Linker.link(s,n)),await this.runCancelable(e,Y.IndexedReferences,n,s=>this.indexManager.updateReferences(s,n));let i=e.filter(s=>this.shouldValidate(s));await this.runCancelable(i,Y.Validated,n,s=>this.validate(s,n));for(let s of e){let o=this.buildState.get(s.uri.toString());o&&(o.completed=!0)}}prepareBuild(e,t){for(let n of e){let i=n.uri.toString(),s=this.buildState.get(i);(!s||s.completed)&&this.buildState.set(i,{completed:!1,options:t,result:s?.result})}}async runCancelable(e,t,n,i){let s=e.filter(l=>l.state<t);for(let l of s)await ce(n),await i(l),l.state=t,await this.notifyDocumentPhase(l,t,n);let o=e.filter(l=>l.state===t);await this.notifyBuildPhase(o,t,n),this.currentState=t}onBuildPhase(e,t){return this.buildPhaseListeners.add(e,t),jt.create(()=>{this.buildPhaseListeners.delete(e,t)})}onDocumentPhase(e,t){return this.documentPhaseListeners.add(e,t),jt.create(()=>{this.documentPhaseListeners.delete(e,t)})}waitUntil(e,t,n){let i;if(t&&"path"in t?i=t:n=t,n??(n=S.CancellationToken.None),i){let s=this.langiumDocuments.getDocument(i);if(s&&s.state>e)return Promise.resolve(i)}return this.currentState>=e?Promise.resolve(void 0):n.isCancellationRequested?Promise.reject(mt):new Promise((s,o)=>{let l=this.onBuildPhase(e,()=>{if(l.dispose(),u.dispose(),i){let c=this.langiumDocuments.getDocument(i);s(c?.uri)}else s(void 0)}),u=n.onCancellationRequested(()=>{l.dispose(),u.dispose(),o(mt)})})}async notifyDocumentPhase(e,t,n){let s=this.documentPhaseListeners.get(t).slice();for(let o of s)try{await o(e,n)}catch(l){if(!gt(l))throw l}}async notifyBuildPhase(e,t,n){if(e.length===0)return;let s=this.buildPhaseListeners.get(t).slice();for(let o of s)await ce(n),await o(e,n)}shouldValidate(e){return!!this.getBuildOptions(e).validation}async validate(e,t){var n,i;let s=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,o=this.getBuildOptions(e).validation,l=typeof o=="object"?o:void 0,u=await s.validateDocument(e,l,t);e.diagnostics?e.diagnostics.push(...u):e.diagnostics=u;let c=this.buildState.get(e.uri.toString());if(c){(n=c.result)!==null&&n!==void 0||(c.result={});let f=(i=l?.categories)!==null&&i!==void 0?i:Yn.all;c.result.validationChecks?c.result.validationChecks.push(...f):c.result.validationChecks=[...f]}}getBuildOptions(e){var t,n;return(n=(t=this.buildState.get(e.uri.toString()))===null||t===void 0?void 0:t.options)!==null&&n!==void 0?n:{}}};var fs=class{static{a(this,"DefaultIndexManager")}constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new xr,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,t){let n=we(e).uri,i=[];return this.referenceIndex.forEach(s=>{s.forEach(o=>{Be.equals(o.targetUri,n)&&o.targetPath===t&&i.push(o)})}),j(i)}allElements(e,t){let n=j(this.symbolIndex.keys());return t&&(n=n.filter(i=>!t||t.has(i))),n.map(i=>this.getFileDescriptions(i,e)).flat()}getFileDescriptions(e,t){var n;return t?this.symbolByTypeIndex.get(e,t,()=>{var s;return((s=this.symbolIndex.get(e))!==null&&s!==void 0?s:[]).filter(l=>this.astReflection.isSubtype(l.type,t))}):(n=this.symbolIndex.get(e))!==null&&n!==void 0?n:[]}remove(e){let t=e.toString();this.symbolIndex.delete(t),this.symbolByTypeIndex.clear(t),this.referenceIndex.delete(t)}async updateContent(e,t=S.CancellationToken.None){let i=await this.serviceRegistry.getServices(e.uri).references.ScopeComputation.computeExports(e,t),s=e.uri.toString();this.symbolIndex.set(s,i),this.symbolByTypeIndex.clear(s)}async updateReferences(e,t=S.CancellationToken.None){let i=await this.serviceRegistry.getServices(e.uri).workspace.ReferenceDescriptionProvider.createDescriptions(e,t);this.referenceIndex.set(e.uri.toString(),i)}isAffected(e,t){let n=this.referenceIndex.get(e.uri.toString());return n?n.some(i=>!i.local&&t.has(i.targetUri.toString())):!1}};var ds=class{static{a(this,"DefaultWorkspaceManager")}constructor(e){this.initialBuildOptions={},this._ready=new Ge,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}get workspaceFolders(){return this.folders}initialize(e){var t;this.folders=(t=e.workspaceFolders)!==null&&t!==void 0?t:void 0}initialized(e){return this.mutex.write(t=>{var n;return this.initializeWorkspace((n=this.folders)!==null&&n!==void 0?n:[],t)})}async initializeWorkspace(e,t=S.CancellationToken.None){let n=await this.performStartup(e);await ce(t),await this.documentBuilder.build(n,this.initialBuildOptions,t)}async performStartup(e){let t=this.serviceRegistry.all.flatMap(s=>s.LanguageMetaData.fileExtensions),n=[],i=a(s=>{n.push(s),this.langiumDocuments.hasDocument(s.uri)||this.langiumDocuments.addDocument(s)},"collector");return await this.loadAdditionalDocuments(e,i),await Promise.all(e.map(s=>[s,this.getRootFolder(s)]).map(async s=>this.traverseFolder(...s,t,i))),this._ready.resolve(),n}loadAdditionalDocuments(e,t){return Promise.resolve()}getRootFolder(e){return Ue.parse(e.uri)}async traverseFolder(e,t,n,i){let s=await this.fileSystemProvider.readDirectory(t);await Promise.all(s.map(async o=>{if(this.includeEntry(e,o,n)){if(o.isDirectory)await this.traverseFolder(e,o.uri,n,i);else if(o.isFile){let l=await this.langiumDocuments.getOrCreateDocument(o.uri);i(l)}}}))}includeEntry(e,t,n){let i=Be.basename(t.uri);if(i.startsWith("."))return!1;if(t.isDirectory)return i!=="node_modules"&&i!=="out";if(t.isFile){let s=Be.extname(t.uri);return n.includes(s)}return!1}};var hs=class{static{a(this,"DefaultLexerErrorMessageProvider")}buildUnexpectedCharactersMessage(e,t,n,i,s){return In.buildUnexpectedCharactersMessage(e,t,n,i,s)}buildUnableToPopLexerModeMessage(e){return In.buildUnableToPopLexerModeMessage(e)}},Wa={mode:"full"},Ar=class{static{a(this,"DefaultLexer")}constructor(e){this.errorMessageProvider=e.parser.LexerErrorMessageProvider,this.tokenBuilder=e.parser.TokenBuilder;let t=this.tokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(t);let n=mu(t)?Object.values(t):t,i=e.LanguageMetaData.mode==="production";this.chevrotainLexer=new se(n,{positionTracking:"full",skipValidations:i,errorMessageProvider:this.errorMessageProvider})}get definition(){return this.tokenTypes}tokenize(e,t=Wa){var n,i,s;let o=this.chevrotainLexer.tokenize(e);return{tokens:o.tokens,errors:o.errors,hidden:(n=o.groups.hidden)!==null&&n!==void 0?n:[],report:(s=(i=this.tokenBuilder).flushLexingReport)===null||s===void 0?void 0:s.call(i,e)}}toTokenTypeDictionary(e){if(mu(e))return e;let t=gu(e)?Object.values(e.modes).flat():e,n={};return t.forEach(i=>n[i.name]=i),n}};function Ka(r){return Array.isArray(r)&&(r.length===0||"name"in r[0])}a(Ka,"isTokenTypeArray");function gu(r){return r&&"modes"in r&&"defaultMode"in r}a(gu,"isIMultiModeLexerDefinition");function mu(r){return!Ka(r)&&!gu(r)}a(mu,"isTokenTypeDictionary");function Ru(r,e,t){let n,i;typeof r=="string"?(i=e,n=t):(i=r.range.start,n=e),i||(i=W.create(0,0));let s=sh(r),o=Eu(n),l=hg({lines:s,position:i,options:o});return Tg({index:0,tokens:l,position:i})}a(Ru,"parseJSDoc");function xu(r,e){let t=Eu(e),n=sh(r);if(n.length===0)return!1;let i=n[0],s=n[n.length-1],o=t.start,l=t.end;return!!o?.exec(i)&&!!l?.exec(s)}a(xu,"isJSDoc");function sh(r){let e="";return typeof r=="string"?e=r:e=r.text,e.split(Ko)}a(sh,"getLines");var nh=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,dg=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu;function hg(r){var e,t,n;let i=[],s=r.position.line,o=r.position.character;for(let l=0;l<r.lines.length;l++){let u=l===0,c=l===r.lines.length-1,f=r.lines[l],d=0;if(u&&r.options.start){let p=(e=r.options.start)===null||e===void 0?void 0:e.exec(f);p&&(d=p.index+p[0].length)}else{let p=(t=r.options.line)===null||t===void 0?void 0:t.exec(f);p&&(d=p.index+p[0].length)}if(c){let p=(n=r.options.end)===null||n===void 0?void 0:n.exec(f);p&&(f=f.substring(0,p.index))}if(f=f.substring(0,yg(f)),Tu(f,d)>=f.length){if(i.length>0){let p=W.create(s,o);i.push({type:"break",content:"",range:U.create(p,p)})}}else{nh.lastIndex=d;let p=nh.exec(f);if(p){let g=p[0],y=p[1],v=W.create(s,o+d),x=W.create(s,o+d+g.length);i.push({type:"tag",content:y,range:U.create(v,x)}),d+=g.length,d=Tu(f,d)}if(d<f.length){let g=f.substring(d),y=Array.from(g.matchAll(dg));i.push(...pg(y,g,s,o+d))}}s++,o=0}return i.length>0&&i[i.length-1].type==="break"?i.slice(0,-1):i}a(hg,"tokenize");function pg(r,e,t,n){let i=[];if(r.length===0){let s=W.create(t,n),o=W.create(t,n+e.length);i.push({type:"text",content:e,range:U.create(s,o)})}else{let s=0;for(let l of r){let u=l.index,c=e.substring(s,u);c.length>0&&i.push({type:"text",content:e.substring(s,u),range:U.create(W.create(t,s+n),W.create(t,u+n))});let f=c.length+1,d=l[1];if(i.push({type:"inline-tag",content:d,range:U.create(W.create(t,s+f+n),W.create(t,s+f+d.length+n))}),f+=d.length,l.length===4){f+=l[2].length;let h=l[3];i.push({type:"text",content:h,range:U.create(W.create(t,s+f+n),W.create(t,s+f+h.length+n))})}else i.push({type:"text",content:"",range:U.create(W.create(t,s+f+n),W.create(t,s+f+n))});s=u+l[0].length}let o=e.substring(s);o.length>0&&i.push({type:"text",content:o,range:U.create(W.create(t,s+n),W.create(t,s+n+o.length))})}return i}a(pg,"buildInlineTokens");var mg=/\S/,gg=/\s*$/;function Tu(r,e){let t=r.substring(e).match(mg);return t?e+t.index:r.length}a(Tu,"skipWhitespace");function yg(r){let e=r.match(gg);if(e&&typeof e.index=="number")return e.index}a(yg,"lastCharacter");function Tg(r){var e,t,n,i;let s=W.create(r.position.line,r.position.character);if(r.tokens.length===0)return new Va([],U.create(s,s));let o=[];for(;r.index<r.tokens.length;){let c=Rg(r,o[o.length-1]);c&&o.push(c)}let l=(t=(e=o[0])===null||e===void 0?void 0:e.range.start)!==null&&t!==void 0?t:s,u=(i=(n=o[o.length-1])===null||n===void 0?void 0:n.range.end)!==null&&i!==void 0?i:s;return new Va(o,U.create(l,u))}a(Tg,"parseJSDocComment");function Rg(r,e){let t=r.tokens[r.index];if(t.type==="tag")return oh(r,!1);if(t.type==="text"||t.type==="inline-tag")return ah(r);xg(t,e),r.index++}a(Rg,"parseJSDocElement");function xg(r,e){if(e){let t=new ja("",r.range);"inlines"in e?e.inlines.push(t):e.content.inlines.push(t)}}a(xg,"appendEmptyLine");function ah(r){let e=r.tokens[r.index],t=e,n=e,i=[];for(;e&&e.type!=="break"&&e.type!=="tag";)i.push(Eg(r)),n=e,e=r.tokens[r.index];return new ms(i,U.create(t.range.start,n.range.end))}a(ah,"parseJSDocText");function Eg(r){return r.tokens[r.index].type==="inline-tag"?oh(r,!0):lh(r)}a(Eg,"parseJSDocInline");function oh(r,e){let t=r.tokens[r.index++],n=t.content.substring(1),i=r.tokens[r.index];if(i?.type==="text")if(e){let s=lh(r);return new ps(n,new ms([s],s.range),e,U.create(t.range.start,s.range.end))}else{let s=ah(r);return new ps(n,s,e,U.create(t.range.start,s.range.end))}else{let s=t.range;return new ps(n,new ms([],s),e,s)}}a(oh,"parseJSDocTag");function lh(r){let e=r.tokens[r.index++];return new ja(e.content,e.range)}a(lh,"parseJSDocLine");function Eu(r){if(!r)return Eu({start:"/**",end:"*/",line:"*"});let{start:e,end:t,line:n}=r;return{start:yu(e,!0),end:yu(t,!1),line:yu(n,!0)}}a(Eu,"normalizeOptions");function yu(r,e){if(typeof r=="string"||typeof r=="object"){let t=typeof r=="string"?lr(r):r.source;return e?new RegExp(`^\\s*${t}`):new RegExp(`\\s*${t}\\s*$`)}else return r}a(yu,"normalizeOption");var Va=class{static{a(this,"JSDocCommentImpl")}constructor(e,t){this.elements=e,this.range=t}getTag(e){return this.getAllTags().find(t=>t.name===e)}getTags(e){return this.getAllTags().filter(t=>t.name===e)}getAllTags(){return this.elements.filter(e=>"name"in e)}toString(){let e="";for(let t of this.elements)if(e.length===0)e=t.toString();else{let n=t.toString();e+=ih(e)+n}return e.trim()}toMarkdown(e){let t="";for(let n of this.elements)if(t.length===0)t=n.toMarkdown(e);else{let i=n.toMarkdown(e);t+=ih(t)+i}return t.trim()}},ps=class{static{a(this,"JSDocTagImpl")}constructor(e,t,n,i){this.name=e,this.content=t,this.inline=n,this.range=i}toString(){let e=`@${this.name}`,t=this.content.toString();return this.content.inlines.length===1?e=`${e} ${t}`:this.content.inlines.length>1&&(e=`${e}
${t}`),this.inline?`{${e}}`:e}toMarkdown(e){var t,n;return(n=(t=e?.renderTag)===null||t===void 0?void 0:t.call(e,this))!==null&&n!==void 0?n:this.toMarkdownDefault(e)}toMarkdownDefault(e){let t=this.content.toMarkdown(e);if(this.inline){let s=Ag(this.name,t,e??{});if(typeof s=="string")return s}let n="";e?.tag==="italic"||e?.tag===void 0?n="*":e?.tag==="bold"?n="**":e?.tag==="bold-italic"&&(n="***");let i=`${n}@${this.name}${n}`;return this.content.inlines.length===1?i=`${i} \u2014 ${t}`:this.content.inlines.length>1&&(i=`${i}
${t}`),this.inline?`{${i}}`:i}};function Ag(r,e,t){var n,i;if(r==="linkplain"||r==="linkcode"||r==="link"){let s=e.indexOf(" "),o=e;if(s>0){let u=Tu(e,s);o=e.substring(u),e=e.substring(0,s)}return(r==="linkcode"||r==="link"&&t.link==="code")&&(o=`\`${o}\``),(i=(n=t.renderLink)===null||n===void 0?void 0:n.call(t,e,o))!==null&&i!==void 0?i:vg(e,o)}}a(Ag,"renderInlineTag");function vg(r,e){try{return Ue.parse(r,!0),`[${e}](${r})`}catch{return r}}a(vg,"renderLinkDefault");var ms=class{static{a(this,"JSDocTextImpl")}constructor(e,t){this.inlines=e,this.range=t}toString(){let e="";for(let t=0;t<this.inlines.length;t++){let n=this.inlines[t],i=this.inlines[t+1];e+=n.toString(),i&&i.range.start.line>n.range.start.line&&(e+=`
`)}return e}toMarkdown(e){let t="";for(let n=0;n<this.inlines.length;n++){let i=this.inlines[n],s=this.inlines[n+1];t+=i.toMarkdown(e),s&&s.range.start.line>i.range.start.line&&(t+=`
`)}return t}},ja=class{static{a(this,"JSDocLineImpl")}constructor(e,t){this.text=e,this.range=t}toString(){return this.text}toMarkdown(){return this.text}};function ih(r){return r.endsWith(`
`)?`
`:`

`}a(ih,"fillNewlines");var gs=class{static{a(this,"JSDocDocumentationProvider")}constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){let t=this.commentProvider.getComment(e);if(t&&xu(t))return Ru(t).toMarkdown({renderLink:a((i,s)=>this.documentationLinkRenderer(e,i,s),"renderLink"),renderTag:a(i=>this.documentationTagRenderer(e,i),"renderTag")})}documentationLinkRenderer(e,t,n){var i;let s=(i=this.findNameInPrecomputedScopes(e,t))!==null&&i!==void 0?i:this.findNameInGlobalScope(e,t);if(s&&s.nameSegment){let o=s.nameSegment.range.start.line+1,l=s.nameSegment.range.start.character+1,u=s.documentUri.with({fragment:`L${o},${l}`});return`[${n}](${u.toString()})`}else return}documentationTagRenderer(e,t){}findNameInPrecomputedScopes(e,t){let i=we(e).precomputedScopes;if(!i)return;let s=e;do{let l=i.get(s).find(u=>u.name===t);if(l)return l;s=s.$container}while(s)}findNameInGlobalScope(e,t){return this.indexManager.allElements().find(i=>i.name===t)}};var ys=class{static{a(this,"DefaultCommentProvider")}constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var t;return pu(e)?e.$comment:(t=To(e.$cstNode,this.grammarConfig().multilineCommentRules))===null||t===void 0?void 0:t.text}};var Ts=class{static{a(this,"DefaultAsyncParser")}constructor(e){this.syncParser=e.parser.LangiumParser}parse(e,t){return Promise.resolve(this.syncParser.parse(e))}},Au=class{static{a(this,"AbstractThreadedAsyncParser")}constructor(e){this.threadCount=8,this.terminationDelay=200,this.workerPool=[],this.queue=[],this.hydrator=e.serializer.Hydrator}initializeWorkers(){for(;this.workerPool.length<this.threadCount;){let e=this.createWorker();e.onReady(()=>{if(this.queue.length>0){let t=this.queue.shift();t&&(e.lock(),t.resolve(e))}}),this.workerPool.push(e)}}async parse(e,t){let n=await this.acquireParserWorker(t),i=new Ge,s,o=t.onCancellationRequested(()=>{s=setTimeout(()=>{this.terminateWorker(n)},this.terminationDelay)});return n.parse(e).then(l=>{let u=this.hydrator.hydrate(l);i.resolve(u)}).catch(l=>{i.reject(l)}).finally(()=>{o.dispose(),clearTimeout(s)}),i.promise}terminateWorker(e){e.terminate();let t=this.workerPool.indexOf(e);t>=0&&this.workerPool.splice(t,1)}async acquireParserWorker(e){this.initializeWorkers();for(let n of this.workerPool)if(n.ready)return n.lock(),n;let t=new Ge;return e.onCancellationRequested(()=>{let n=this.queue.indexOf(t);n>=0&&this.queue.splice(n,1),t.reject(mt)}),this.queue.push(t),t.promise}},vu=class{static{a(this,"ParserWorker")}get ready(){return this._ready}get onReady(){return this.onReadyEmitter.event}constructor(e,t,n,i){this.onReadyEmitter=new ne.Emitter,this.deferred=new Ge,this._ready=!0,this._parsing=!1,this.sendMessage=e,this._terminate=i,t(s=>{let o=s;this.deferred.resolve(o),this.unlock()}),n(s=>{this.deferred.reject(s),this.unlock()})}terminate(){this.deferred.reject(mt),this._terminate()}lock(){this._ready=!1}unlock(){this._parsing=!1,this._ready=!0,this.onReadyEmitter.fire()}parse(e){if(this._parsing)throw new Error("Parser worker is busy");return this._parsing=!0,this.deferred=new Ge,this.sendMessage(e),this.deferred.promise}};var Rs=class{static{a(this,"DefaultWorkspaceLock")}constructor(){this.previousTokenSource=new S.CancellationTokenSource,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();let t=Fa();return this.previousTokenSource=t,this.enqueue(this.writeQueue,e,t.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,t,n=S.CancellationToken.None){let i=new Ge,s={action:t,deferred:i,cancellationToken:n};return e.push(s),this.performNextOperation(),i.promise}async performNextOperation(){if(!this.done)return;let e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else if(this.readQueue.length>0)e.push(...this.readQueue.splice(0,this.readQueue.length));else return;this.done=!1,await Promise.all(e.map(async({action:t,deferred:n,cancellationToken:i})=>{try{let s=await Promise.resolve().then(()=>t(i));n.resolve(s)}catch(s){gt(s)?n.resolve(void 0):n.reject(s)}})),this.done=!0,this.performNextOperation()}cancelWrite(){this.previousTokenSource.cancel()}};var xs=class{static{a(this,"DefaultHydrator")}constructor(e){this.grammarElementIdMap=new Rr,this.tokenTypeIdMap=new Rr,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport?this.dehydrateLexerReport(e.lexerReport):void 0,parserErrors:e.parserErrors.map(t=>Object.assign(Object.assign({},t),{message:t.message})),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}dehydrateLexerReport(e){return e}createDehyrationContext(e){let t=new Map,n=new Map;for(let i of Je(e))t.set(i,{});if(e.$cstNode)for(let i of Zt(e.$cstNode))n.set(i,{});return{astNodes:t,cstNodes:n}}dehydrateAstNode(e,t){let n=t.astNodes.get(e);n.$type=e.$type,n.$containerIndex=e.$containerIndex,n.$containerProperty=e.$containerProperty,e.$cstNode!==void 0&&(n.$cstNode=this.dehydrateCstNode(e.$cstNode,t));for(let[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){let o=[];n[i]=o;for(let l of s)le(l)?o.push(this.dehydrateAstNode(l,t)):Ee(l)?o.push(this.dehydrateReference(l,t)):o.push(l)}else le(s)?n[i]=this.dehydrateAstNode(s,t):Ee(s)?n[i]=this.dehydrateReference(s,t):s!==void 0&&(n[i]=s);return n}dehydrateReference(e,t){let n={};return n.$refText=e.$refText,e.$refNode&&(n.$refNode=t.cstNodes.get(e.$refNode)),n}dehydrateCstNode(e,t){let n=t.cstNodes.get(e);return Zn(e)?n.fullText=e.fullText:n.grammarSource=this.getGrammarElementId(e.grammarSource),n.hidden=e.hidden,n.astNode=t.astNodes.get(e.astNode),tt(e)?n.content=e.content.map(i=>this.dehydrateCstNode(i,t)):Gt(e)&&(n.tokenType=e.tokenType.name,n.offset=e.offset,n.length=e.length,n.startLine=e.range.start.line,n.startColumn=e.range.start.character,n.endLine=e.range.end.line,n.endColumn=e.range.end.character),n}hydrate(e){let t=e.value,n=this.createHydrationContext(t);return"$cstNode"in t&&this.hydrateCstNode(t.$cstNode,n),{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport,parserErrors:e.parserErrors,value:this.hydrateAstNode(t,n)}}createHydrationContext(e){let t=new Map,n=new Map;for(let s of Je(e))t.set(s,{});let i;if(e.$cstNode)for(let s of Zt(e.$cstNode)){let o;"fullText"in s?(o=new Bn(s.fullText),i=o):"content"in s?o=new gr:"tokenType"in s&&(o=this.hydrateCstLeafNode(s)),o&&(n.set(s,o),o.root=i)}return{astNodes:t,cstNodes:n}}hydrateAstNode(e,t){let n=t.astNodes.get(e);n.$type=e.$type,n.$containerIndex=e.$containerIndex,n.$containerProperty=e.$containerProperty,e.$cstNode&&(n.$cstNode=t.cstNodes.get(e.$cstNode));for(let[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){let o=[];n[i]=o;for(let l of s)le(l)?o.push(this.setParent(this.hydrateAstNode(l,t),n)):Ee(l)?o.push(this.hydrateReference(l,n,i,t)):o.push(l)}else le(s)?n[i]=this.setParent(this.hydrateAstNode(s,t),n):Ee(s)?n[i]=this.hydrateReference(s,n,i,t):s!==void 0&&(n[i]=s);return n}setParent(e,t){return e.$container=t,e}hydrateReference(e,t,n,i){return this.linker.buildReference(t,n,i.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,t,n=0){let i=t.cstNodes.get(e);if(typeof e.grammarSource=="number"&&(i.grammarSource=this.getGrammarElement(e.grammarSource)),i.astNode=t.astNodes.get(e.astNode),tt(i))for(let s of e.content){let o=this.hydrateCstNode(s,t,n++);i.content.push(o)}return i}hydrateCstLeafNode(e){let t=this.getTokenType(e.tokenType),n=e.offset,i=e.length,s=e.startLine,o=e.startColumn,l=e.endLine,u=e.endColumn,c=e.hidden;return new mr(n,i,{start:{line:s,character:o},end:{line:l,character:u}},t,c)}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){if(e)return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.getKey(e)}createGrammarElementIdMap(){let e=0;for(let t of Je(this.grammar))oi(t)&&this.grammarElementIdMap.set(t,e++)}};function ku(r){return{documentation:{CommentProvider:a(e=>new ys(e),"CommentProvider"),DocumentationProvider:a(e=>new gs(e),"DocumentationProvider")},parser:{AsyncParser:a(e=>new Ts(e),"AsyncParser"),GrammarConfig:a(e=>il(e),"GrammarConfig"),LangiumParser:a(e=>ru(e),"LangiumParser"),CompletionParser:a(e=>tu(e),"CompletionParser"),ValueConverter:a(()=>new Tr,"ValueConverter"),TokenBuilder:a(()=>new bt,"TokenBuilder"),Lexer:a(e=>new Ar(e),"Lexer"),ParserErrorMessageProvider:a(()=>new Wn,"ParserErrorMessageProvider"),LexerErrorMessageProvider:a(()=>new hs,"LexerErrorMessageProvider")},workspace:{AstNodeLocator:a(()=>new ls,"AstNodeLocator"),AstNodeDescriptionProvider:a(e=>new as(e),"AstNodeDescriptionProvider"),ReferenceDescriptionProvider:a(e=>new os(e),"ReferenceDescriptionProvider")},references:{Linker:a(e=>new Xi(e),"Linker"),NameProvider:a(()=>new Yi,"NameProvider"),ScopeProvider:a(e=>new ts(e),"ScopeProvider"),ScopeComputation:a(e=>new Qi(e),"ScopeComputation"),References:a(e=>new Ji(e),"References")},serializer:{Hydrator:a(e=>new xs(e),"Hydrator"),JsonSerializer:a(e=>new rs(e),"JsonSerializer")},validation:{DocumentValidator:a(e=>new ss(e),"DocumentValidator"),ValidationRegistry:a(e=>new is(e),"ValidationRegistry")},shared:a(()=>r.shared,"shared")}}a(ku,"createDefaultCoreModule");function $u(r){return{ServiceRegistry:a(e=>new ns(e),"ServiceRegistry"),workspace:{LangiumDocuments:a(e=>new qi(e),"LangiumDocuments"),LangiumDocumentFactory:a(e=>new zi(e),"LangiumDocumentFactory"),DocumentBuilder:a(e=>new cs(e),"DocumentBuilder"),IndexManager:a(e=>new fs(e),"IndexManager"),WorkspaceManager:a(e=>new ds(e),"WorkspaceManager"),FileSystemProvider:a(e=>r.fileSystemProvider(e),"FileSystemProvider"),WorkspaceLock:a(()=>new Rs,"WorkspaceLock"),ConfigurationProvider:a(e=>new us(e),"ConfigurationProvider")}}}a($u,"createDefaultSharedCoreModule");var Iu;(function(r){r.merge=(e,t)=>Ha(Ha({},e),t)})(Iu||(Iu={}));function za(r,e,t,n,i,s,o,l,u){let c=[r,e,t,n,i,s,o,l,u].reduce(Ha,{});return hh(c)}a(za,"inject");var fh=Symbol("isProxy");function dh(r){if(r&&r[fh])for(let e of Object.values(r))dh(e);return r}a(dh,"eagerLoad");function hh(r,e){let t=new Proxy({},{deleteProperty:a(()=>!1,"deleteProperty"),set:a(()=>{throw new Error("Cannot set property on injected service container")},"set"),get:a((n,i)=>i===fh?!0:ch(n,i,r,e||t),"get"),getOwnPropertyDescriptor:a((n,i)=>(ch(n,i,r,e||t),Object.getOwnPropertyDescriptor(n,i)),"getOwnPropertyDescriptor"),has:a((n,i)=>i in r,"has"),ownKeys:a(()=>[...Object.getOwnPropertyNames(r)],"ownKeys")});return t}a(hh,"_inject");var uh=Symbol();function ch(r,e,t,n){if(e in r){if(r[e]instanceof Error)throw new Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:r[e]});if(r[e]===uh)throw new Error('Cycle detected. Please make "'+String(e)+'" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies');return r[e]}else if(e in t){let i=t[e];r[e]=uh;try{r[e]=typeof i=="function"?i(n):hh(i,n)}catch(s){throw r[e]=s instanceof Error?s:void 0,s}return r[e]}else return}a(ch,"_resolve");function Ha(r,e){if(e){for(let[t,n]of Object.entries(e))if(n!==void 0){let i=r[t];i!==null&&n!==null&&typeof i=="object"&&typeof n=="object"?r[t]=Ha(i,n):r[t]=n}}return r}a(Ha,"_merge");var Nu={indentTokenName:"INDENT",dedentTokenName:"DEDENT",whitespaceTokenName:"WS",ignoreIndentationDelimiters:[]},vr;(function(r){r.REGULAR="indentation-sensitive",r.IGNORE_INDENTATION="ignore-indentation"})(vr||(vr={}));var qa=class extends bt{static{a(this,"IndentationAwareTokenBuilder")}constructor(e=Nu){super(),this.indentationStack=[0],this.whitespaceRegExp=/[ \t]+/y,this.options=Object.assign(Object.assign({},Nu),e),this.indentTokenType=Bt({name:this.options.indentTokenName,pattern:this.indentMatcher.bind(this),line_breaks:!1}),this.dedentTokenType=Bt({name:this.options.dedentTokenName,pattern:this.dedentMatcher.bind(this),line_breaks:!1})}buildTokens(e,t){let n=super.buildTokens(e,t);if(!Ka(n))throw new Error("Invalid tokens built by default builder");let{indentTokenName:i,dedentTokenName:s,whitespaceTokenName:o,ignoreIndentationDelimiters:l}=this.options,u,c,f,d=[];for(let h of n){for(let[p,g]of l)h.name===p?h.PUSH_MODE=vr.IGNORE_INDENTATION:h.name===g&&(h.POP_MODE=!0);h.name===s?u=h:h.name===i?c=h:h.name===o?f=h:d.push(h)}if(!u||!c||!f)throw new Error("Some indentation/whitespace tokens not found!");return l.length>0?{modes:{[vr.REGULAR]:[u,c,...d,f],[vr.IGNORE_INDENTATION]:[...d,f]},defaultMode:vr.REGULAR}:[u,c,f,...d]}flushLexingReport(e){let t=super.flushLexingReport(e);return Object.assign(Object.assign({},t),{remainingDedents:this.flushRemainingDedents(e)})}isStartOfLine(e,t){return t===0||`\r
`.includes(e[t-1])}matchWhitespace(e,t,n,i){var s;this.whitespaceRegExp.lastIndex=t;let o=this.whitespaceRegExp.exec(e);return{currIndentLevel:(s=o?.[0].length)!==null&&s!==void 0?s:0,prevIndentLevel:this.indentationStack.at(-1),match:o}}createIndentationTokenInstance(e,t,n,i){let s=this.getLineNumber(t,i);return _t(e,n,i,i+n.length,s,s,1,n.length)}getLineNumber(e,t){return e.substring(0,t).split(/\r\n|\r|\n/).length}indentMatcher(e,t,n,i){if(!this.isStartOfLine(e,t))return null;let{currIndentLevel:s,prevIndentLevel:o,match:l}=this.matchWhitespace(e,t,n,i);return s<=o?null:(this.indentationStack.push(s),l)}dedentMatcher(e,t,n,i){var s,o,l,u;if(!this.isStartOfLine(e,t))return null;let{currIndentLevel:c,prevIndentLevel:f,match:d}=this.matchWhitespace(e,t,n,i);if(c>=f)return null;let h=this.indentationStack.lastIndexOf(c);if(h===-1)return this.diagnostics.push({severity:"error",message:`Invalid dedent level ${c} at offset: ${t}. Current indentation stack: ${this.indentationStack}`,offset:t,length:(o=(s=d?.[0])===null||s===void 0?void 0:s.length)!==null&&o!==void 0?o:0,line:this.getLineNumber(e,t),column:1}),null;let p=this.indentationStack.length-h-1,g=(u=(l=e.substring(0,t).match(/[\r\n]+$/))===null||l===void 0?void 0:l[0].length)!==null&&u!==void 0?u:1;for(let y=0;y<p;y++){let v=this.createIndentationTokenInstance(this.dedentTokenType,e,"",t-(g-1));n.push(v),this.indentationStack.pop()}return null}buildTerminalToken(e){let t=super.buildTerminalToken(e),{indentTokenName:n,dedentTokenName:i,whitespaceTokenName:s}=this.options;return t.name===n?this.indentTokenType:t.name===i?this.dedentTokenType:t.name===s?Bt({name:s,pattern:this.whitespaceRegExp,group:se.SKIPPED}):t}flushRemainingDedents(e){let t=[];for(;this.indentationStack.length>1;)t.push(this.createIndentationTokenInstance(this.dedentTokenType,e,"",e.length)),this.indentationStack.pop();return this.indentationStack=[0],t}},Cu=class extends Ar{static{a(this,"IndentationAwareLexer")}constructor(e){if(super(e),e.parser.TokenBuilder instanceof qa)this.indentationTokenBuilder=e.parser.TokenBuilder;else throw new Error("IndentationAwareLexer requires an accompanying IndentationAwareTokenBuilder")}tokenize(e,t=Wa){let n=super.tokenize(e),i=n.report;t?.mode==="full"&&n.tokens.push(...i.remainingDedents),i.remainingDedents=[];let{indentTokenType:s,dedentTokenType:o}=this.indentationTokenBuilder,l=s.tokenTypeIdx,u=o.tokenTypeIdx,c=[],f=n.tokens.length-1;for(let d=0;d<f;d++){let h=n.tokens[d],p=n.tokens[d+1];if(h.tokenTypeIdx===l&&p.tokenTypeIdx===u){d++;continue}c.push(h)}return f>=0&&c.push(n.tokens[f]),n.tokens=c,n}};var K={};qt(K,{AstUtils:()=>Fs,BiMap:()=>Rr,Cancellation:()=>S,ContextCache:()=>xr,CstUtils:()=>Ss,DONE_RESULT:()=>Ce,Deferred:()=>Ge,Disposable:()=>jt,DisposableCache:()=>qn,DocumentCache:()=>Ua,EMPTY_STREAM:()=>ei,ErrorWithLocation:()=>tr,GrammarUtils:()=>Ks,MultiMap:()=>st,OperationCancelled:()=>mt,Reduction:()=>Ir,RegExpUtils:()=>Bs,SimpleCache:()=>es,StreamImpl:()=>je,TreeStreamImpl:()=>lt,URI:()=>Ue,UriUtils:()=>Be,WorkspaceCache:()=>Xn,assertUnreachable:()=>ct,delayNextTick:()=>fu,interruptAndCheck:()=>ce,isOperationCancelled:()=>gt,loadGrammarFromJson:()=>yt,setInterruptionPeriod:()=>qd,startCancelableOperation:()=>Fa,stream:()=>j});B(K,ne);var Xa=class{static{a(this,"EmptyFileSystemProvider")}readFile(){throw new Error("No file system is available.")}async readDirectory(){return[]}},Su={fileSystemProvider:a(()=>new Xa,"fileSystemProvider")};var kg={Grammar:a(()=>{},"Grammar"),LanguageMetaData:a(()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"}),"LanguageMetaData")},$g={AstReflection:a(()=>new hn,"AstReflection")};function Ig(){let r=za($u(Su),$g),e=za(ku({shared:r}),kg);return r.ServiceRegistry.register(e),e}a(Ig,"createMinimalGrammarServices");function yt(r){var e;let t=Ig(),n=t.serializer.JsonSerializer.deserialize(r);return t.shared.workspace.LangiumDocumentFactory.fromModel(n,Ue.parse(`memory://${(e=n.name)!==null&&e!==void 0?e:"grammar"}.langium`)),n}a(yt,"loadGrammarFromJson");B(fe,K);var Ng=Object.defineProperty,F=a((r,e)=>Ng(r,"name",{value:e,configurable:!0}),"__name"),ph="Statement",no="Architecture";function Cg(r){return at.isInstance(r,no)}a(Cg,"isArchitecture");F(Cg,"isArchitecture");var Ya="Axis",Es="Branch";function Sg(r){return at.isInstance(r,Es)}a(Sg,"isBranch");F(Sg,"isBranch");var Ja="Checkout",Qa="CherryPicking",wu="ClassDefStatement",As="Commit";function wg(r){return at.isInstance(r,As)}a(wg,"isCommit");F(wg,"isCommit");var _u="Curve",Lu="Edge",Ou="Entry",vs="GitGraph";function _g(r){return at.isInstance(r,vs)}a(_g,"isGitGraph");F(_g,"isGitGraph");var Pu="Group",io="Info";function Lg(r){return at.isInstance(r,io)}a(Lg,"isInfo");F(Lg,"isInfo");var Za="Item",bu="Junction",ks="Merge";function Og(r){return at.isInstance(r,ks)}a(Og,"isMerge");F(Og,"isMerge");var Mu="Option",so="Packet";function Pg(r){return at.isInstance(r,so)}a(Pg,"isPacket");F(Pg,"isPacket");var ao="PacketBlock";function bg(r){return at.isInstance(r,ao)}a(bg,"isPacketBlock");F(bg,"isPacketBlock");var oo="Pie";function Mg(r){return at.isInstance(r,oo)}a(Mg,"isPie");F(Mg,"isPie");var lo="PieSection";function Dg(r){return at.isInstance(r,lo)}a(Dg,"isPieSection");F(Dg,"isPieSection");var Du="Radar",Fu="Service",uo="Treemap";function Fg(r){return at.isInstance(r,uo)}a(Fg,"isTreemap");F(Fg,"isTreemap");var Gu="TreemapRow",eo="Direction",to="Leaf",ro="Section",Ah=class extends Jt{static{a(this,"MermaidAstReflection")}static{F(this,"MermaidAstReflection")}getAllTypes(){return[no,Ya,Es,Ja,Qa,wu,As,_u,eo,Lu,Ou,vs,Pu,io,Za,bu,to,ks,Mu,so,ao,oo,lo,Du,ro,Fu,ph,uo,Gu]}computeIsSubtype(r,e){switch(r){case Es:case Ja:case Qa:case As:case ks:return this.isSubtype(ph,e);case eo:return this.isSubtype(vs,e);case to:case ro:return this.isSubtype(Za,e);default:return!1}}getReferenceType(r){let e=`${r.container.$type}:${r.property}`;switch(e){case"Entry:axis":return Ya;default:throw new Error(`${e} is not a valid reference id.`)}}getTypeMetaData(r){switch(r){case no:return{name:no,properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case Ya:return{name:Ya,properties:[{name:"label"},{name:"name"}]};case Es:return{name:Es,properties:[{name:"name"},{name:"order"}]};case Ja:return{name:Ja,properties:[{name:"branch"}]};case Qa:return{name:Qa,properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case wu:return{name:wu,properties:[{name:"className"},{name:"styleText"}]};case As:return{name:As,properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case _u:return{name:_u,properties:[{name:"entries",defaultValue:[]},{name:"label"},{name:"name"}]};case Lu:return{name:Lu,properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case Ou:return{name:Ou,properties:[{name:"axis"},{name:"value"}]};case vs:return{name:vs,properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case Pu:return{name:Pu,properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case io:return{name:io,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case Za:return{name:Za,properties:[{name:"classSelector"},{name:"name"}]};case bu:return{name:bu,properties:[{name:"id"},{name:"in"}]};case ks:return{name:ks,properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case Mu:return{name:Mu,properties:[{name:"name"},{name:"value",defaultValue:!1}]};case so:return{name:so,properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case ao:return{name:ao,properties:[{name:"bits"},{name:"end"},{name:"label"},{name:"start"}]};case oo:return{name:oo,properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case lo:return{name:lo,properties:[{name:"label"},{name:"value"}]};case Du:return{name:Du,properties:[{name:"accDescr"},{name:"accTitle"},{name:"axes",defaultValue:[]},{name:"curves",defaultValue:[]},{name:"options",defaultValue:[]},{name:"title"}]};case Fu:return{name:Fu,properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case uo:return{name:uo,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"},{name:"TreemapRows",defaultValue:[]}]};case Gu:return{name:Gu,properties:[{name:"indent"},{name:"item"}]};case eo:return{name:eo,properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};case to:return{name:to,properties:[{name:"classSelector"},{name:"name"},{name:"value"}]};case ro:return{name:ro,properties:[{name:"classSelector"},{name:"name"}]};default:return{name:r,properties:[]}}}},at=new Ah,mh,Gg=F(()=>mh??(mh=yt(`{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Info","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@7"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"InfoGrammar"),gh,Ug=F(()=>gh??(gh=yt(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Packet","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"packet"},{"$type":"Keyword","value":"packet-beta"}]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}],"cardinality":"?"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"+"},{"$type":"Assignment","feature":"bits","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]}]},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@9"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"PacketGrammar"),yh,Bg=F(()=>yh??(yh=yt(`{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Pie","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"FLOAT_PIE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/-?[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT_PIE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/-?(0|[1-9][0-9]*)(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER_PIE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@2"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@3"}}]},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@11"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@12"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"PieGrammar"),Th,Wg=F(()=>Th??(Th=yt(`{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Architecture","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"LeftPort","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"RightPort","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Arrow","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@28"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@28"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@18"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@19"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"ArchitectureGrammar"),Rh,Kg=F(()=>Rh??(Rh=yt(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"GitGraph","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@14"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@15"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"TerminalRule","name":"REFERENCE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"GitGraphGrammar"),xh,Vg=F(()=>xh??(xh=yt(`{"$type":"Grammar","isDeclared":true,"name":"Radar","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Radar","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":"radar-beta:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},{"$type":"Group","elements":[{"$type":"Keyword","value":"axis"},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"curve"},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Label","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"["},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}},{"$type":"Keyword","value":"]"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Axis","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Curve","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"},{"$type":"Keyword","value":"{"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Keyword","value":"}"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Entries","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"DetailedEntry","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"axis","operator":"=","terminal":{"$type":"CrossReference","type":{"$ref":"#/rules@2"},"terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},"deprecatedSyntax":false}},{"$type":"Keyword","value":":","cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"NumberEntry","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Option","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"showLegend"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"ticks"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"max"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"min"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"graticule"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"GRATICULE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"circle"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"polygon"}}]},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@15"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@16"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"interfaces":[{"$type":"Interface","name":"Entry","attributes":[{"$type":"TypeAttribute","name":"axis","isOptional":true,"type":{"$type":"ReferenceType","referenceType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@2"}}}},{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}],"superTypes":[]}],"definesHiddenTokens":false,"hiddenTokens":[],"types":[],"usedGrammars":[]}`)),"RadarGrammar"),Eh,jg=F(()=>Eh??(Eh=yt(`{"$type":"Grammar","isDeclared":true,"name":"Treemap","rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"ParserRule","entry":true,"name":"Treemap","returnType":{"$ref":"#/interfaces@4"},"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"TreemapRows","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]}}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"TREEMAP_KEYWORD","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"treemap-beta"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"treemap"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"CLASS_DEF","definition":{"$type":"RegexToken","regex":"/classDef\\\\s+([a-zA-Z_][a-zA-Z0-9_]+)(?:\\\\s+([^;\\\\r\\\\n]*))?(?:;)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STYLE_SEPARATOR","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":":::"}},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"SEPARATOR","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":":"}},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"COMMA","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":","}},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WS","definition":{"$type":"RegexToken","regex":"/[ \\\\t]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"ML_COMMENT","definition":{"$type":"RegexToken","regex":"/\\\\%\\\\%[^\\\\n]*/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"NL","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false},{"$type":"ParserRule","name":"TreemapRow","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"indent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"item","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"ClassDef","dataType":"string","definition":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Item","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Section","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]},{"$type":"Assignment","feature":"classSelector","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Leaf","returnType":{"$ref":"#/interfaces@2"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]},{"$type":"Assignment","feature":"classSelector","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INDENTATION","definition":{"$type":"RegexToken","regex":"/[ \\\\t]{1,}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID2","definition":{"$type":"RegexToken","regex":"/[a-zA-Z_][a-zA-Z0-9_]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER2","definition":{"$type":"RegexToken","regex":"/[0-9_\\\\.\\\\,]+/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"MyNumber","dataType":"number","definition":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"STRING2","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"interfaces":[{"$type":"Interface","name":"Item","attributes":[{"$type":"TypeAttribute","name":"name","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false},{"$type":"TypeAttribute","name":"classSelector","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]},{"$type":"Interface","name":"Section","superTypes":[{"$ref":"#/interfaces@0"}],"attributes":[]},{"$type":"Interface","name":"Leaf","superTypes":[{"$ref":"#/interfaces@0"}],"attributes":[{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}]},{"$type":"Interface","name":"ClassDefStatement","attributes":[{"$type":"TypeAttribute","name":"className","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false},{"$type":"TypeAttribute","name":"styleText","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false}],"superTypes":[]},{"$type":"Interface","name":"Treemap","attributes":[{"$type":"TypeAttribute","name":"TreemapRows","type":{"$type":"ArrayType","elementType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@14"}}},"isOptional":false},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[],"$comment":"/**\\n * Treemap grammar for Langium\\n * Converted from mindmap grammar\\n *\\n * The ML_COMMENT and NL hidden terminals handle whitespace, comments, and newlines\\n * before the treemap keyword, allowing for empty lines and comments before the\\n * treemap declaration.\\n */"}`)),"TreemapGrammar"),Hg={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},zg={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},qg={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Xg={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Yg={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Jg={languageId:"radar",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Qg={languageId:"treemap",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},JN={AstReflection:F(()=>new Ah,"AstReflection")},QN={Grammar:F(()=>Gg(),"Grammar"),LanguageMetaData:F(()=>Hg,"LanguageMetaData"),parser:{}},ZN={Grammar:F(()=>Ug(),"Grammar"),LanguageMetaData:F(()=>zg,"LanguageMetaData"),parser:{}},eC={Grammar:F(()=>Bg(),"Grammar"),LanguageMetaData:F(()=>qg,"LanguageMetaData"),parser:{}},tC={Grammar:F(()=>Wg(),"Grammar"),LanguageMetaData:F(()=>Xg,"LanguageMetaData"),parser:{}},rC={Grammar:F(()=>Kg(),"Grammar"),LanguageMetaData:F(()=>Yg,"LanguageMetaData"),parser:{}},nC={Grammar:F(()=>Vg(),"Grammar"),LanguageMetaData:F(()=>Jg,"LanguageMetaData"),parser:{}},iC={Grammar:F(()=>jg(),"Grammar"),LanguageMetaData:F(()=>Qg,"LanguageMetaData"),parser:{}},Zg=/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,ey=/accTitle[\t ]*:([^\n\r]*)/,ty=/title([\t ][^\n\r]*|)/,ry={ACC_DESCR:Zg,ACC_TITLE:ey,TITLE:ty},ny=class extends Tr{static{a(this,"AbstractMermaidValueConverter")}static{F(this,"AbstractMermaidValueConverter")}runConverter(r,e,t){let n=this.runCommonConverter(r,e,t);return n===void 0&&(n=this.runCustomConverter(r,e,t)),n===void 0?super.runConverter(r,e,t):n}runCommonConverter(r,e,t){let n=ry[r.name];if(n===void 0)return;let i=n.exec(e);if(i!==null){if(i[1]!==void 0)return i[1].trim().replace(/[\t ]{2,}/gm," ");if(i[2]!==void 0)return i[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,`
`)}}},aC=class extends ny{static{a(this,"CommonValueConverter")}static{F(this,"CommonValueConverter")}runCustomConverter(r,e,t){}},iy=class extends bt{static{a(this,"AbstractMermaidTokenBuilder")}static{F(this,"AbstractMermaidTokenBuilder")}constructor(r){super(),this.keywords=new Set(r)}buildKeywordTokens(r,e,t){let n=super.buildKeywordTokens(r,e,t);return n.forEach(i=>{this.keywords.has(i.name)&&i.PATTERN!==void 0&&(i.PATTERN=new RegExp(i.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))}),n}},lC=class extends iy{static{a(this,"CommonTokenBuilder")}static{F(this,"CommonTokenBuilder")}};export{ku as a,$u as b,za as c,Su as d,fe as e,F as f,JN as g,QN as h,ZN as i,eC as j,tC as k,rC as l,nC as m,iC as n,ny as o,aC as p,iy as q};
