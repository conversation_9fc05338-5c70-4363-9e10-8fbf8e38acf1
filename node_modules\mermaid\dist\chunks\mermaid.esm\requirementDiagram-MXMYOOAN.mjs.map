{"version": 3, "sources": ["../../../src/diagrams/requirement/parser/requirementDiagram.jison", "../../../src/diagrams/requirement/requirementDb.ts", "../../../src/diagrams/requirement/styles.js", "../../../src/diagrams/requirement/requirementRenderer.ts", "../../../src/diagrams/requirement/requirementDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,3],$V1=[1,4],$V2=[1,5],$V3=[1,6],$V4=[5,6,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,77,89,90],$V5=[1,22],$V6=[2,7],$V7=[1,26],$V8=[1,27],$V9=[1,28],$Va=[1,29],$Vb=[1,33],$Vc=[1,34],$Vd=[1,35],$Ve=[1,36],$Vf=[1,37],$Vg=[1,38],$Vh=[1,24],$Vi=[1,31],$Vj=[1,32],$Vk=[1,30],$Vl=[1,39],$Vm=[1,40],$Vn=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,77,89,90],$Vo=[1,61],$Vp=[89,90],$Vq=[5,8,9,11,13,21,22,23,24,27,29,41,42,43,44,45,46,54,61,63,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],$Vr=[27,29],$Vs=[1,70],$Vt=[1,71],$Vu=[1,72],$Vv=[1,73],$Vw=[1,74],$Vx=[1,75],$Vy=[1,76],$Vz=[1,83],$VA=[1,80],$VB=[1,84],$VC=[1,85],$VD=[1,86],$VE=[1,87],$VF=[1,88],$VG=[1,89],$VH=[1,90],$VI=[1,91],$VJ=[1,92],$VK=[5,8,9,11,13,21,22,23,24,27,41,42,43,44,45,46,54,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],$VL=[63,64],$VM=[1,101],$VN=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,76,77,89,90],$VO=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],$VP=[1,110],$VQ=[1,106],$VR=[1,107],$VS=[1,108],$VT=[1,109],$VU=[1,111],$VV=[1,116],$VW=[1,117],$VX=[1,114],$VY=[1,115];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"directive\":4,\"NEWLINE\":5,\"RD\":6,\"diagram\":7,\"EOF\":8,\"acc_title\":9,\"acc_title_value\":10,\"acc_descr\":11,\"acc_descr_value\":12,\"acc_descr_multiline_value\":13,\"requirementDef\":14,\"elementDef\":15,\"relationshipDef\":16,\"direction\":17,\"styleStatement\":18,\"classDefStatement\":19,\"classStatement\":20,\"direction_tb\":21,\"direction_bt\":22,\"direction_rl\":23,\"direction_lr\":24,\"requirementType\":25,\"requirementName\":26,\"STRUCT_START\":27,\"requirementBody\":28,\"STYLE_SEPARATOR\":29,\"idList\":30,\"ID\":31,\"COLONSEP\":32,\"id\":33,\"TEXT\":34,\"text\":35,\"RISK\":36,\"riskLevel\":37,\"VERIFYMTHD\":38,\"verifyType\":39,\"STRUCT_STOP\":40,\"REQUIREMENT\":41,\"FUNCTIONAL_REQUIREMENT\":42,\"INTERFACE_REQUIREMENT\":43,\"PERFORMANCE_REQUIREMENT\":44,\"PHYSICAL_REQUIREMENT\":45,\"DESIGN_CONSTRAINT\":46,\"LOW_RISK\":47,\"MED_RISK\":48,\"HIGH_RISK\":49,\"VERIFY_ANALYSIS\":50,\"VERIFY_DEMONSTRATION\":51,\"VERIFY_INSPECTION\":52,\"VERIFY_TEST\":53,\"ELEMENT\":54,\"elementName\":55,\"elementBody\":56,\"TYPE\":57,\"type\":58,\"DOCREF\":59,\"ref\":60,\"END_ARROW_L\":61,\"relationship\":62,\"LINE\":63,\"END_ARROW_R\":64,\"CONTAINS\":65,\"COPIES\":66,\"DERIVES\":67,\"SATISFIES\":68,\"VERIFIES\":69,\"REFINES\":70,\"TRACES\":71,\"CLASSDEF\":72,\"stylesOpt\":73,\"CLASS\":74,\"ALPHA\":75,\"COMMA\":76,\"STYLE\":77,\"style\":78,\"styleComponent\":79,\"NUM\":80,\"COLON\":81,\"UNIT\":82,\"SPACE\":83,\"BRKT\":84,\"PCT\":85,\"MINUS\":86,\"LABEL\":87,\"SEMICOLON\":88,\"unqString\":89,\"qString\":90,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",5:\"NEWLINE\",6:\"RD\",8:\"EOF\",9:\"acc_title\",10:\"acc_title_value\",11:\"acc_descr\",12:\"acc_descr_value\",13:\"acc_descr_multiline_value\",21:\"direction_tb\",22:\"direction_bt\",23:\"direction_rl\",24:\"direction_lr\",27:\"STRUCT_START\",29:\"STYLE_SEPARATOR\",31:\"ID\",32:\"COLONSEP\",34:\"TEXT\",36:\"RISK\",38:\"VERIFYMTHD\",40:\"STRUCT_STOP\",41:\"REQUIREMENT\",42:\"FUNCTIONAL_REQUIREMENT\",43:\"INTERFACE_REQUIREMENT\",44:\"PERFORMANCE_REQUIREMENT\",45:\"PHYSICAL_REQUIREMENT\",46:\"DESIGN_CONSTRAINT\",47:\"LOW_RISK\",48:\"MED_RISK\",49:\"HIGH_RISK\",50:\"VERIFY_ANALYSIS\",51:\"VERIFY_DEMONSTRATION\",52:\"VERIFY_INSPECTION\",53:\"VERIFY_TEST\",54:\"ELEMENT\",57:\"TYPE\",59:\"DOCREF\",61:\"END_ARROW_L\",63:\"LINE\",64:\"END_ARROW_R\",65:\"CONTAINS\",66:\"COPIES\",67:\"DERIVES\",68:\"SATISFIES\",69:\"VERIFIES\",70:\"REFINES\",71:\"TRACES\",72:\"CLASSDEF\",74:\"CLASS\",75:\"ALPHA\",76:\"COMMA\",77:\"STYLE\",80:\"NUM\",81:\"COLON\",82:\"UNIT\",83:\"SPACE\",84:\"BRKT\",85:\"PCT\",86:\"MINUS\",87:\"LABEL\",88:\"SEMICOLON\",89:\"unqString\",90:\"qString\"},\nproductions_: [0,[3,3],[3,2],[3,4],[4,2],[4,2],[4,1],[7,0],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[17,1],[17,1],[17,1],[17,1],[14,5],[14,7],[28,5],[28,5],[28,5],[28,5],[28,2],[28,1],[25,1],[25,1],[25,1],[25,1],[25,1],[25,1],[37,1],[37,1],[37,1],[39,1],[39,1],[39,1],[39,1],[15,5],[15,7],[56,5],[56,5],[56,2],[56,1],[16,5],[16,5],[62,1],[62,1],[62,1],[62,1],[62,1],[62,1],[62,1],[19,3],[20,3],[20,3],[30,1],[30,3],[30,1],[30,3],[18,3],[73,1],[73,3],[78,1],[78,2],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[26,1],[26,1],[33,1],[33,1],[35,1],[35,1],[55,1],[55,1],[58,1],[58,1],[60,1],[60,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 4:\n this.$=$$[$0].trim();yy.setAccTitle(this.$); \nbreak;\ncase 5: case 6:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 7:\n this.$ = [] \nbreak;\ncase 17:\n yy.setDirection('TB');\nbreak;\ncase 18:\n yy.setDirection('BT');\nbreak;\ncase 19:\n yy.setDirection('RL');\nbreak;\ncase 20:\n yy.setDirection('LR');\nbreak;\ncase 21:\n yy.addRequirement($$[$0-3], $$[$0-4]) \nbreak;\ncase 22:\n yy.addRequirement($$[$0-5], $$[$0-6]); yy.setClass([$$[$0-5]], $$[$0-3]); \nbreak;\ncase 23:\n yy.setNewReqId($$[$0-2]); \nbreak;\ncase 24:\n yy.setNewReqText($$[$0-2]); \nbreak;\ncase 25:\n yy.setNewReqRisk($$[$0-2]); \nbreak;\ncase 26:\n yy.setNewReqVerifyMethod($$[$0-2]); \nbreak;\ncase 29:\n this.$=yy.RequirementType.REQUIREMENT;\nbreak;\ncase 30:\n this.$=yy.RequirementType.FUNCTIONAL_REQUIREMENT;\nbreak;\ncase 31:\n this.$=yy.RequirementType.INTERFACE_REQUIREMENT;\nbreak;\ncase 32:\n this.$=yy.RequirementType.PERFORMANCE_REQUIREMENT;\nbreak;\ncase 33:\n this.$=yy.RequirementType.PHYSICAL_REQUIREMENT;\nbreak;\ncase 34:\n this.$=yy.RequirementType.DESIGN_CONSTRAINT;\nbreak;\ncase 35:\n this.$=yy.RiskLevel.LOW_RISK;\nbreak;\ncase 36:\n this.$=yy.RiskLevel.MED_RISK;\nbreak;\ncase 37:\n this.$=yy.RiskLevel.HIGH_RISK;\nbreak;\ncase 38:\n this.$=yy.VerifyType.VERIFY_ANALYSIS;\nbreak;\ncase 39:\n this.$=yy.VerifyType.VERIFY_DEMONSTRATION;\nbreak;\ncase 40:\n this.$=yy.VerifyType.VERIFY_INSPECTION;\nbreak;\ncase 41:\n this.$=yy.VerifyType.VERIFY_TEST;\nbreak;\ncase 42:\n yy.addElement($$[$0-3]) \nbreak;\ncase 43:\n yy.addElement($$[$0-5]); yy.setClass([$$[$0-5]], $$[$0-3]); \nbreak;\ncase 44:\n yy.setNewElementType($$[$0-2]); \nbreak;\ncase 45:\n yy.setNewElementDocRef($$[$0-2]); \nbreak;\ncase 48:\n  yy.addRelationship($$[$0-2], $$[$0], $$[$0-4]) \nbreak;\ncase 49:\n yy.addRelationship($$[$0-2], $$[$0-4], $$[$0]) \nbreak;\ncase 50:\n this.$=yy.Relationships.CONTAINS;\nbreak;\ncase 51:\n this.$=yy.Relationships.COPIES;\nbreak;\ncase 52:\n this.$=yy.Relationships.DERIVES;\nbreak;\ncase 53:\n this.$=yy.Relationships.SATISFIES;\nbreak;\ncase 54:\n this.$=yy.Relationships.VERIFIES;\nbreak;\ncase 55:\n this.$=yy.Relationships.REFINES;\nbreak;\ncase 56:\n this.$=yy.Relationships.TRACES;\nbreak;\ncase 57:\nthis.$ = $$[$0-2];yy.defineClass($$[$0-1],$$[$0]);\nbreak;\ncase 58:\nyy.setClass($$[$0-1], $$[$0]);\nbreak;\ncase 59:\nyy.setClass([$$[$0-2]], $$[$0]);\nbreak;\ncase 60: case 62:\n this.$ = [$$[$0]]; \nbreak;\ncase 61: case 63:\n this.$ = $$[$0-2].concat([$$[$0]]); \nbreak;\ncase 64:\nthis.$ = $$[$0-2];yy.setCssStyle($$[$0-1],$$[$0]);\nbreak;\ncase 65:\nthis.$ = [$$[$0]]\nbreak;\ncase 66:\n$$[$0-2].push($$[$0]);this.$ = $$[$0-2];\nbreak;\ncase 68:\nthis.$ = $$[$0-1] + $$[$0];\nbreak;\n}\n},\ntable: [{3:1,4:2,6:$V0,9:$V1,11:$V2,13:$V3},{1:[3]},{3:8,4:2,5:[1,7],6:$V0,9:$V1,11:$V2,13:$V3},{5:[1,9]},{10:[1,10]},{12:[1,11]},o($V4,[2,6]),{3:12,4:2,6:$V0,9:$V1,11:$V2,13:$V3},{1:[2,2]},{4:17,5:$V5,7:13,8:$V6,9:$V1,11:$V2,13:$V3,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:$V7,22:$V8,23:$V9,24:$Va,25:23,33:25,41:$Vb,42:$Vc,43:$Vd,44:$Ve,45:$Vf,46:$Vg,54:$Vh,72:$Vi,74:$Vj,77:$Vk,89:$Vl,90:$Vm},o($V4,[2,4]),o($V4,[2,5]),{1:[2,1]},{8:[1,41]},{4:17,5:$V5,7:42,8:$V6,9:$V1,11:$V2,13:$V3,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:$V7,22:$V8,23:$V9,24:$Va,25:23,33:25,41:$Vb,42:$Vc,43:$Vd,44:$Ve,45:$Vf,46:$Vg,54:$Vh,72:$Vi,74:$Vj,77:$Vk,89:$Vl,90:$Vm},{4:17,5:$V5,7:43,8:$V6,9:$V1,11:$V2,13:$V3,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:$V7,22:$V8,23:$V9,24:$Va,25:23,33:25,41:$Vb,42:$Vc,43:$Vd,44:$Ve,45:$Vf,46:$Vg,54:$Vh,72:$Vi,74:$Vj,77:$Vk,89:$Vl,90:$Vm},{4:17,5:$V5,7:44,8:$V6,9:$V1,11:$V2,13:$V3,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:$V7,22:$V8,23:$V9,24:$Va,25:23,33:25,41:$Vb,42:$Vc,43:$Vd,44:$Ve,45:$Vf,46:$Vg,54:$Vh,72:$Vi,74:$Vj,77:$Vk,89:$Vl,90:$Vm},{4:17,5:$V5,7:45,8:$V6,9:$V1,11:$V2,13:$V3,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:$V7,22:$V8,23:$V9,24:$Va,25:23,33:25,41:$Vb,42:$Vc,43:$Vd,44:$Ve,45:$Vf,46:$Vg,54:$Vh,72:$Vi,74:$Vj,77:$Vk,89:$Vl,90:$Vm},{4:17,5:$V5,7:46,8:$V6,9:$V1,11:$V2,13:$V3,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:$V7,22:$V8,23:$V9,24:$Va,25:23,33:25,41:$Vb,42:$Vc,43:$Vd,44:$Ve,45:$Vf,46:$Vg,54:$Vh,72:$Vi,74:$Vj,77:$Vk,89:$Vl,90:$Vm},{4:17,5:$V5,7:47,8:$V6,9:$V1,11:$V2,13:$V3,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:$V7,22:$V8,23:$V9,24:$Va,25:23,33:25,41:$Vb,42:$Vc,43:$Vd,44:$Ve,45:$Vf,46:$Vg,54:$Vh,72:$Vi,74:$Vj,77:$Vk,89:$Vl,90:$Vm},{4:17,5:$V5,7:48,8:$V6,9:$V1,11:$V2,13:$V3,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:$V7,22:$V8,23:$V9,24:$Va,25:23,33:25,41:$Vb,42:$Vc,43:$Vd,44:$Ve,45:$Vf,46:$Vg,54:$Vh,72:$Vi,74:$Vj,77:$Vk,89:$Vl,90:$Vm},{4:17,5:$V5,7:49,8:$V6,9:$V1,11:$V2,13:$V3,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:$V7,22:$V8,23:$V9,24:$Va,25:23,33:25,41:$Vb,42:$Vc,43:$Vd,44:$Ve,45:$Vf,46:$Vg,54:$Vh,72:$Vi,74:$Vj,77:$Vk,89:$Vl,90:$Vm},{4:17,5:$V5,7:50,8:$V6,9:$V1,11:$V2,13:$V3,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:$V7,22:$V8,23:$V9,24:$Va,25:23,33:25,41:$Vb,42:$Vc,43:$Vd,44:$Ve,45:$Vf,46:$Vg,54:$Vh,72:$Vi,74:$Vj,77:$Vk,89:$Vl,90:$Vm},{26:51,89:[1,52],90:[1,53]},{55:54,89:[1,55],90:[1,56]},{29:[1,59],61:[1,57],63:[1,58]},o($Vn,[2,17]),o($Vn,[2,18]),o($Vn,[2,19]),o($Vn,[2,20]),{30:60,33:62,75:$Vo,89:$Vl,90:$Vm},{30:63,33:62,75:$Vo,89:$Vl,90:$Vm},{30:64,33:62,75:$Vo,89:$Vl,90:$Vm},o($Vp,[2,29]),o($Vp,[2,30]),o($Vp,[2,31]),o($Vp,[2,32]),o($Vp,[2,33]),o($Vp,[2,34]),o($Vq,[2,81]),o($Vq,[2,82]),{1:[2,3]},{8:[2,8]},{8:[2,9]},{8:[2,10]},{8:[2,11]},{8:[2,12]},{8:[2,13]},{8:[2,14]},{8:[2,15]},{8:[2,16]},{27:[1,65],29:[1,66]},o($Vr,[2,79]),o($Vr,[2,80]),{27:[1,67],29:[1,68]},o($Vr,[2,85]),o($Vr,[2,86]),{62:69,65:$Vs,66:$Vt,67:$Vu,68:$Vv,69:$Vw,70:$Vx,71:$Vy},{62:77,65:$Vs,66:$Vt,67:$Vu,68:$Vv,69:$Vw,70:$Vx,71:$Vy},{30:78,33:62,75:$Vo,89:$Vl,90:$Vm},{73:79,75:$Vz,76:$VA,78:81,79:82,80:$VB,81:$VC,82:$VD,83:$VE,84:$VF,85:$VG,86:$VH,87:$VI,88:$VJ},o($VK,[2,60]),o($VK,[2,62]),{73:93,75:$Vz,76:$VA,78:81,79:82,80:$VB,81:$VC,82:$VD,83:$VE,84:$VF,85:$VG,86:$VH,87:$VI,88:$VJ},{30:94,33:62,75:$Vo,76:$VA,89:$Vl,90:$Vm},{5:[1,95]},{30:96,33:62,75:$Vo,89:$Vl,90:$Vm},{5:[1,97]},{30:98,33:62,75:$Vo,89:$Vl,90:$Vm},{63:[1,99]},o($VL,[2,50]),o($VL,[2,51]),o($VL,[2,52]),o($VL,[2,53]),o($VL,[2,54]),o($VL,[2,55]),o($VL,[2,56]),{64:[1,100]},o($Vn,[2,59],{76:$VA}),o($Vn,[2,64],{76:$VM}),{33:103,75:[1,102],89:$Vl,90:$Vm},o($VN,[2,65],{79:104,75:$Vz,80:$VB,81:$VC,82:$VD,83:$VE,84:$VF,85:$VG,86:$VH,87:$VI,88:$VJ}),o($VO,[2,67]),o($VO,[2,69]),o($VO,[2,70]),o($VO,[2,71]),o($VO,[2,72]),o($VO,[2,73]),o($VO,[2,74]),o($VO,[2,75]),o($VO,[2,76]),o($VO,[2,77]),o($VO,[2,78]),o($Vn,[2,57],{76:$VM}),o($Vn,[2,58],{76:$VA}),{5:$VP,28:105,31:$VQ,34:$VR,36:$VS,38:$VT,40:$VU},{27:[1,112],76:$VA},{5:$VV,40:$VW,56:113,57:$VX,59:$VY},{27:[1,118],76:$VA},{33:119,89:$Vl,90:$Vm},{33:120,89:$Vl,90:$Vm},{75:$Vz,78:121,79:82,80:$VB,81:$VC,82:$VD,83:$VE,84:$VF,85:$VG,86:$VH,87:$VI,88:$VJ},o($VK,[2,61]),o($VK,[2,63]),o($VO,[2,68]),o($Vn,[2,21]),{32:[1,122]},{32:[1,123]},{32:[1,124]},{32:[1,125]},{5:$VP,28:126,31:$VQ,34:$VR,36:$VS,38:$VT,40:$VU},o($Vn,[2,28]),{5:[1,127]},o($Vn,[2,42]),{32:[1,128]},{32:[1,129]},{5:$VV,40:$VW,56:130,57:$VX,59:$VY},o($Vn,[2,47]),{5:[1,131]},o($Vn,[2,48]),o($Vn,[2,49]),o($VN,[2,66],{79:104,75:$Vz,80:$VB,81:$VC,82:$VD,83:$VE,84:$VF,85:$VG,86:$VH,87:$VI,88:$VJ}),{33:132,89:$Vl,90:$Vm},{35:133,89:[1,134],90:[1,135]},{37:136,47:[1,137],48:[1,138],49:[1,139]},{39:140,50:[1,141],51:[1,142],52:[1,143],53:[1,144]},o($Vn,[2,27]),{5:$VP,28:145,31:$VQ,34:$VR,36:$VS,38:$VT,40:$VU},{58:146,89:[1,147],90:[1,148]},{60:149,89:[1,150],90:[1,151]},o($Vn,[2,46]),{5:$VV,40:$VW,56:152,57:$VX,59:$VY},{5:[1,153]},{5:[1,154]},{5:[2,83]},{5:[2,84]},{5:[1,155]},{5:[2,35]},{5:[2,36]},{5:[2,37]},{5:[1,156]},{5:[2,38]},{5:[2,39]},{5:[2,40]},{5:[2,41]},o($Vn,[2,22]),{5:[1,157]},{5:[2,87]},{5:[2,88]},{5:[1,158]},{5:[2,89]},{5:[2,90]},o($Vn,[2,43]),{5:$VP,28:159,31:$VQ,34:$VR,36:$VS,38:$VT,40:$VU},{5:$VP,28:160,31:$VQ,34:$VR,36:$VS,38:$VT,40:$VU},{5:$VP,28:161,31:$VQ,34:$VR,36:$VS,38:$VT,40:$VU},{5:$VP,28:162,31:$VQ,34:$VR,36:$VS,38:$VT,40:$VU},{5:$VV,40:$VW,56:163,57:$VX,59:$VY},{5:$VV,40:$VW,56:164,57:$VX,59:$VY},o($Vn,[2,23]),o($Vn,[2,24]),o($Vn,[2,25]),o($Vn,[2,26]),o($Vn,[2,44]),o($Vn,[2,45])],\ndefaultActions: {8:[2,2],12:[2,1],41:[2,3],42:[2,8],43:[2,9],44:[2,10],45:[2,11],46:[2,12],47:[2,13],48:[2,14],49:[2,15],50:[2,16],134:[2,83],135:[2,84],137:[2,35],138:[2,36],139:[2,37],141:[2,38],142:[2,39],143:[2,40],144:[2,41],147:[2,87],148:[2,88],150:[2,89],151:[2,90]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:return 'title';\nbreak;\ncase 1: this.begin(\"acc_title\");return 9; \nbreak;\ncase 2: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 3: this.begin(\"acc_descr\");return 11; \nbreak;\ncase 4: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 5: this.begin(\"acc_descr_multiline\");\nbreak;\ncase 6: this.popState(); \nbreak;\ncase 7:return \"acc_descr_multiline_value\";\nbreak;\ncase 8:return 21;\nbreak;\ncase 9:return 22;\nbreak;\ncase 10:return 23;\nbreak;\ncase 11:return 24;\nbreak;\ncase 12:return 5;\nbreak;\ncase 13:/* skip all whitespace */\nbreak;\ncase 14:/* skip comments */\nbreak;\ncase 15:/* skip comments */\nbreak;\ncase 16:return 8;\nbreak;\ncase 17:return 6;\nbreak;\ncase 18:return 27;\nbreak;\ncase 19:return 40;\nbreak;\ncase 20:return 29;\nbreak;\ncase 21:return 32;\nbreak;\ncase 22:return 31;\nbreak;\ncase 23:return 34;\nbreak;\ncase 24:return 36;\nbreak;\ncase 25:return 38;\nbreak;\ncase 26:return 41;\nbreak;\ncase 27:return 42;\nbreak;\ncase 28:return 43;\nbreak;\ncase 29:return 44;\nbreak;\ncase 30:return 45;\nbreak;\ncase 31:return 46;\nbreak;\ncase 32:return 47;\nbreak;\ncase 33:return 48;\nbreak;\ncase 34:return 49;\nbreak;\ncase 35:return 50;\nbreak;\ncase 36:return 51;\nbreak;\ncase 37:return 52;\nbreak;\ncase 38:return 53;\nbreak;\ncase 39:return 54;\nbreak;\ncase 40:return 65;\nbreak;\ncase 41:return 66;\nbreak;\ncase 42:return 67;\nbreak;\ncase 43:return 68;\nbreak;\ncase 44:return 69;\nbreak;\ncase 45:return 70;\nbreak;\ncase 46:return 71;\nbreak;\ncase 47:return 57;\nbreak;\ncase 48:return 59;\nbreak;\ncase 49: this.begin(\"style\"); return 77; \nbreak;\ncase 50:return 75;\nbreak;\ncase 51:return 81;\nbreak;\ncase 52:return 88;\nbreak;\ncase 53:return 'PERCENT';\nbreak;\ncase 54:return 86;\nbreak;\ncase 55:return 84;\nbreak;\ncase 56:/* skip spaces */\nbreak;\ncase 57: this.begin(\"string\"); \nbreak;\ncase 58: this.popState(); \nbreak;\ncase 59: this.begin(\"style\"); return 72; \nbreak;\ncase 60: this.begin(\"style\"); return 74; \nbreak;\ncase 61:return 61;\nbreak;\ncase 62:return 64;\nbreak;\ncase 63:return 63;\nbreak;\ncase 64: this.begin(\"string\"); \nbreak;\ncase 65: this.popState(); \nbreak;\ncase 66: return \"qString\"; \nbreak;\ncase 67: yy_.yytext = yy_.yytext.trim(); return 89;\nbreak;\ncase 68:return 75;\nbreak;\ncase 69:return 80;\nbreak;\ncase 70:return 76;\nbreak;\n}\n},\nrules: [/^(?:title\\s[^#\\n;]+)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:[\\}])/i,/^(?:[^\\}]*)/i,/^(?:.*direction\\s+TB[^\\n]*)/i,/^(?:.*direction\\s+BT[^\\n]*)/i,/^(?:.*direction\\s+RL[^\\n]*)/i,/^(?:.*direction\\s+LR[^\\n]*)/i,/^(?:(\\r?\\n)+)/i,/^(?:\\s+)/i,/^(?:#[^\\n]*)/i,/^(?:%[^\\n]*)/i,/^(?:$)/i,/^(?:requirementDiagram\\b)/i,/^(?:\\{)/i,/^(?:\\})/i,/^(?::{3})/i,/^(?::)/i,/^(?:id\\b)/i,/^(?:text\\b)/i,/^(?:risk\\b)/i,/^(?:verifyMethod\\b)/i,/^(?:requirement\\b)/i,/^(?:functionalRequirement\\b)/i,/^(?:interfaceRequirement\\b)/i,/^(?:performanceRequirement\\b)/i,/^(?:physicalRequirement\\b)/i,/^(?:designConstraint\\b)/i,/^(?:low\\b)/i,/^(?:medium\\b)/i,/^(?:high\\b)/i,/^(?:analysis\\b)/i,/^(?:demonstration\\b)/i,/^(?:inspection\\b)/i,/^(?:test\\b)/i,/^(?:element\\b)/i,/^(?:contains\\b)/i,/^(?:copies\\b)/i,/^(?:derives\\b)/i,/^(?:satisfies\\b)/i,/^(?:verifies\\b)/i,/^(?:refines\\b)/i,/^(?:traces\\b)/i,/^(?:type\\b)/i,/^(?:docref\\b)/i,/^(?:style\\b)/i,/^(?:\\w+)/i,/^(?::)/i,/^(?:;)/i,/^(?:%)/i,/^(?:-)/i,/^(?:#)/i,/^(?: )/i,/^(?:[\"])/i,/^(?:\\n)/i,/^(?:classDef\\b)/i,/^(?:class\\b)/i,/^(?:<-)/i,/^(?:->)/i,/^(?:-)/i,/^(?:[\"])/i,/^(?:[\"])/i,/^(?:[^\"]*)/i,/^(?:[\\w][^:,\\r\\n\\{\\<\\>\\-\\=]*)/i,/^(?:\\w+)/i,/^(?:[0-9]+)/i,/^(?:,)/i],\nconditions: {\"acc_descr_multiline\":{\"rules\":[6,7,68,69,70],\"inclusive\":false},\"acc_descr\":{\"rules\":[4,68,69,70],\"inclusive\":false},\"acc_title\":{\"rules\":[2,68,69,70],\"inclusive\":false},\"style\":{\"rules\":[50,51,52,53,54,55,56,57,58,68,69,70],\"inclusive\":false},\"unqString\":{\"rules\":[68,69,70],\"inclusive\":false},\"token\":{\"rules\":[68,69,70],\"inclusive\":false},\"string\":{\"rules\":[65,66,68,69,70],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,3,5,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,67,68,69,70],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { DiagramDB } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport type { Node, Edge } from '../../rendering-util/types.js';\n\nimport {\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  clear as commonClear,\n  setDiagramTitle,\n  getDiagramTitle,\n} from '../common/commonDb.js';\nimport type {\n  Element,\n  Relation,\n  RelationshipType,\n  Requirement,\n  RequirementClass,\n  RequirementType,\n  RiskLevel,\n  VerifyType,\n} from './types.js';\n\nexport class RequirementDB implements DiagramDB {\n  private relations: Relation[] = [];\n  private latestRequirement: Requirement = this.getInitialRequirement();\n  private requirements = new Map<string, Requirement>();\n  private latestElement: Element = this.getInitialElement();\n  private elements = new Map<string, Element>();\n  private classes = new Map<string, RequirementClass>();\n  private direction = 'TB';\n\n  private RequirementType = {\n    REQUIREMENT: 'Requirement',\n    FUNCTIONAL_REQUIREMENT: 'Functional Requirement',\n    INTERFACE_REQUIREMENT: 'Interface Requirement',\n    PERFORMANCE_REQUIREMENT: 'Performance Requirement',\n    PHYSICAL_REQUIREMENT: 'Physical Requirement',\n    DESIGN_CONSTRAINT: 'Design Constraint',\n  };\n\n  private RiskLevel = {\n    LOW_RISK: 'Low',\n    MED_RISK: 'Medium',\n    HIGH_RISK: 'High',\n  };\n\n  private VerifyType = {\n    VERIFY_ANALYSIS: 'Analysis',\n    VERIFY_DEMONSTRATION: 'Demonstration',\n    VERIFY_INSPECTION: 'Inspection',\n    VERIFY_TEST: 'Test',\n  };\n\n  private Relationships = {\n    CONTAINS: 'contains',\n    COPIES: 'copies',\n    DERIVES: 'derives',\n    SATISFIES: 'satisfies',\n    VERIFIES: 'verifies',\n    REFINES: 'refines',\n    TRACES: 'traces',\n  };\n\n  constructor() {\n    this.clear();\n\n    // Needed for JISON since it only supports direct properties\n    this.setDirection = this.setDirection.bind(this);\n    this.addRequirement = this.addRequirement.bind(this);\n    this.setNewReqId = this.setNewReqId.bind(this);\n    this.setNewReqRisk = this.setNewReqRisk.bind(this);\n    this.setNewReqText = this.setNewReqText.bind(this);\n    this.setNewReqVerifyMethod = this.setNewReqVerifyMethod.bind(this);\n    this.addElement = this.addElement.bind(this);\n    this.setNewElementType = this.setNewElementType.bind(this);\n    this.setNewElementDocRef = this.setNewElementDocRef.bind(this);\n    this.addRelationship = this.addRelationship.bind(this);\n    this.setCssStyle = this.setCssStyle.bind(this);\n    this.setClass = this.setClass.bind(this);\n    this.defineClass = this.defineClass.bind(this);\n    this.setAccTitle = this.setAccTitle.bind(this);\n    this.setAccDescription = this.setAccDescription.bind(this);\n  }\n\n  public getDirection() {\n    return this.direction;\n  }\n  public setDirection(dir: string) {\n    this.direction = dir;\n  }\n\n  private resetLatestRequirement() {\n    this.latestRequirement = this.getInitialRequirement();\n  }\n\n  private resetLatestElement() {\n    this.latestElement = this.getInitialElement();\n  }\n\n  private getInitialRequirement(): Requirement {\n    return {\n      requirementId: '',\n      text: '',\n      risk: '' as RiskLevel,\n      verifyMethod: '' as VerifyType,\n      name: '',\n      type: '' as RequirementType,\n      cssStyles: [],\n      classes: ['default'],\n    };\n  }\n\n  private getInitialElement(): Element {\n    return {\n      name: '',\n      type: '',\n      docRef: '',\n      cssStyles: [],\n      classes: ['default'],\n    };\n  }\n\n  public addRequirement(name: string, type: RequirementType) {\n    if (!this.requirements.has(name)) {\n      this.requirements.set(name, {\n        name,\n        type,\n        requirementId: this.latestRequirement.requirementId,\n        text: this.latestRequirement.text,\n        risk: this.latestRequirement.risk,\n        verifyMethod: this.latestRequirement.verifyMethod,\n        cssStyles: [],\n        classes: ['default'],\n      });\n    }\n    this.resetLatestRequirement();\n\n    return this.requirements.get(name);\n  }\n\n  public getRequirements() {\n    return this.requirements;\n  }\n\n  public setNewReqId(id: string) {\n    if (this.latestRequirement !== undefined) {\n      this.latestRequirement.requirementId = id;\n    }\n  }\n\n  public setNewReqText(text: string) {\n    if (this.latestRequirement !== undefined) {\n      this.latestRequirement.text = text;\n    }\n  }\n\n  public setNewReqRisk(risk: RiskLevel) {\n    if (this.latestRequirement !== undefined) {\n      this.latestRequirement.risk = risk;\n    }\n  }\n\n  public setNewReqVerifyMethod(verifyMethod: VerifyType) {\n    if (this.latestRequirement !== undefined) {\n      this.latestRequirement.verifyMethod = verifyMethod;\n    }\n  }\n\n  public addElement(name: string) {\n    if (!this.elements.has(name)) {\n      this.elements.set(name, {\n        name,\n        type: this.latestElement.type,\n        docRef: this.latestElement.docRef,\n        cssStyles: [],\n        classes: ['default'],\n      });\n      log.info('Added new element: ', name);\n    }\n    this.resetLatestElement();\n\n    return this.elements.get(name);\n  }\n\n  public getElements() {\n    return this.elements;\n  }\n\n  public setNewElementType(type: string) {\n    if (this.latestElement !== undefined) {\n      this.latestElement.type = type;\n    }\n  }\n\n  public setNewElementDocRef(docRef: string) {\n    if (this.latestElement !== undefined) {\n      this.latestElement.docRef = docRef;\n    }\n  }\n\n  public addRelationship(type: RelationshipType, src: string, dst: string) {\n    this.relations.push({\n      type,\n      src,\n      dst,\n    });\n  }\n\n  public getRelationships() {\n    return this.relations;\n  }\n\n  public clear() {\n    this.relations = [];\n    this.resetLatestRequirement();\n    this.requirements = new Map();\n    this.resetLatestElement();\n    this.elements = new Map();\n    this.classes = new Map();\n    commonClear();\n  }\n\n  public setCssStyle(ids: string[], styles: string[]) {\n    for (const id of ids) {\n      const node = this.requirements.get(id) ?? this.elements.get(id);\n      if (!styles || !node) {\n        return;\n      }\n      for (const s of styles) {\n        if (s.includes(',')) {\n          node.cssStyles.push(...s.split(','));\n        } else {\n          node.cssStyles.push(s);\n        }\n      }\n    }\n  }\n\n  public setClass(ids: string[], classNames: string[]) {\n    for (const id of ids) {\n      const node = this.requirements.get(id) ?? this.elements.get(id);\n      if (node) {\n        for (const _class of classNames) {\n          node.classes.push(_class);\n          const styles = this.classes.get(_class)?.styles;\n          if (styles) {\n            node.cssStyles.push(...styles);\n          }\n        }\n      }\n    }\n  }\n\n  public defineClass(ids: string[], style: string[]) {\n    for (const id of ids) {\n      let styleClass = this.classes.get(id);\n      if (styleClass === undefined) {\n        styleClass = { id, styles: [], textStyles: [] };\n        this.classes.set(id, styleClass);\n      }\n\n      if (style) {\n        style.forEach(function (s) {\n          if (/color/.exec(s)) {\n            const newStyle = s.replace('fill', 'bgFill'); // .replace('color', 'fill');\n            styleClass.textStyles.push(newStyle);\n          }\n          styleClass.styles.push(s);\n        });\n      }\n\n      this.requirements.forEach((value) => {\n        if (value.classes.includes(id)) {\n          value.cssStyles.push(...style.flatMap((s) => s.split(',')));\n        }\n      });\n      this.elements.forEach((value) => {\n        if (value.classes.includes(id)) {\n          value.cssStyles.push(...style.flatMap((s) => s.split(',')));\n        }\n      });\n    }\n  }\n\n  public getClasses() {\n    return this.classes;\n  }\n\n  public getData() {\n    const config = getConfig();\n    const nodes: Node[] = [];\n    const edges: Edge[] = [];\n    for (const requirement of this.requirements.values()) {\n      const node = requirement as unknown as Node;\n      node.id = requirement.name;\n      node.cssStyles = requirement.cssStyles;\n      node.cssClasses = requirement.classes.join(' ');\n      node.shape = 'requirementBox';\n      node.look = config.look;\n      nodes.push(node);\n    }\n\n    for (const element of this.elements.values()) {\n      const node = element as unknown as Node;\n      node.shape = 'requirementBox';\n      node.look = config.look;\n      node.id = element.name;\n      node.cssStyles = element.cssStyles;\n      node.cssClasses = element.classes.join(' ');\n\n      nodes.push(node);\n    }\n\n    for (const relation of this.relations) {\n      let counter = 0;\n      const isContains = relation.type === this.Relationships.CONTAINS;\n      const edge: Edge = {\n        id: `${relation.src}-${relation.dst}-${counter}`,\n        start: this.requirements.get(relation.src)?.name ?? this.elements.get(relation.src)?.name,\n        end: this.requirements.get(relation.dst)?.name ?? this.elements.get(relation.dst)?.name,\n        label: `&lt;&lt;${relation.type}&gt;&gt;`,\n        classes: 'relationshipLine',\n        style: ['fill:none', isContains ? '' : 'stroke-dasharray: 10,7'],\n        labelpos: 'c',\n        thickness: 'normal',\n        type: 'normal',\n        pattern: isContains ? 'normal' : 'dashed',\n        arrowTypeStart: isContains ? 'requirement_contains' : '',\n        arrowTypeEnd: isContains ? '' : 'requirement_arrow',\n        look: config.look,\n      };\n\n      edges.push(edge);\n      counter++;\n    }\n\n    return { nodes, edges, other: {}, config, direction: this.getDirection() };\n  }\n\n  public setAccTitle = setAccTitle;\n  public getAccTitle = getAccTitle;\n  public setAccDescription = setAccDescription;\n  public getAccDescription = getAccDescription;\n  public setDiagramTitle = setDiagramTitle;\n  public getDiagramTitle = getDiagramTitle;\n  public getConfig = () => getConfig().requirement;\n}\n", "const getStyles = (options) => `\n\n  marker {\n    fill: ${options.relationColor};\n    stroke: ${options.relationColor};\n  }\n\n  marker.cross {\n    stroke: ${options.lineColor};\n  }\n\n  svg {\n    font-family: ${options.fontFamily};\n    font-size: ${options.fontSize};\n  }\n\n  .reqBox {\n    fill: ${options.requirementBackground};\n    fill-opacity: 1.0;\n    stroke: ${options.requirementBorderColor};\n    stroke-width: ${options.requirementBorderSize};\n  }\n  \n  .reqTitle, .reqLabel{\n    fill:  ${options.requirementTextColor};\n  }\n  .reqLabelBox {\n    fill: ${options.relationLabelBackground};\n    fill-opacity: 1.0;\n  }\n\n  .req-title-line {\n    stroke: ${options.requirementBorderColor};\n    stroke-width: ${options.requirementBorderSize};\n  }\n  .relationshipLine {\n    stroke: ${options.relationColor};\n    stroke-width: 1;\n  }\n  .relationshipLabel {\n    fill: ${options.relationLabelColor};\n  }\n  .divider {\n    stroke: ${options.nodeBorder};\n    stroke-width: 1;\n  }\n  .label {\n    font-family: ${options.fontFamily};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n  .label text,span {\n    fill: ${options.nodeTextColor || options.textColor};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n  .labelBkg {\n    background-color: ${options.edgeLabelBackground};\n  }\n\n`;\n// fill', conf.rect_fill)\nexport default getStyles;\n", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { log } from '../../logger.js';\nimport { getDiagramElement } from '../../rendering-util/insertElementsForSize.js';\nimport { getRegisteredLayoutAlgorithm, render } from '../../rendering-util/render.js';\nimport { setupViewPortForSVG } from '../../rendering-util/setupViewPortForSVG.js';\nimport type { LayoutData } from '../../rendering-util/types.js';\nimport utils from '../../utils.js';\n\nexport const draw = async function (text: string, id: string, _version: string, diag: any) {\n  log.info('REF0:');\n  log.info('Drawing requirement diagram (unified)', id);\n  const { securityLevel, state: conf, layout } = getConfig();\n\n  const data4Layout = diag.db.getData() as LayoutData;\n\n  // Create the root SVG - the element is the div containing the SVG element\n  const svg = getDiagramElement(id, securityLevel);\n\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = getRegisteredLayoutAlgorithm(layout);\n\n  data4Layout.nodeSpacing = conf?.nodeSpacing ?? 50;\n  data4Layout.rankSpacing = conf?.rankSpacing ?? 50;\n  data4Layout.markers = ['requirement_contains', 'requirement_arrow'];\n  data4Layout.diagramId = id;\n  await render(data4Layout, svg);\n  const padding = 8;\n  utils.insertTitle(\n    svg,\n    'requirementDiagramTitleText',\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n\n  setupViewPortForSVG(svg, padding, 'requirementDiagram', conf?.useMaxWidth ?? true);\n};\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: JISON doesn't support types\nimport parser from './parser/requirementDiagram.jison';\nimport { RequirementDB } from './requirementDb.js';\nimport styles from './styles.js';\nimport * as renderer from './requirementRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  get db() {\n    return new RequirementDB();\n  },\n  renderer,\n  styles,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEA,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAG,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAG,GAAE,MAAI,CAAC,GAAE,GAAG,GAAE,MAAI,CAAC,GAAE,GAAG,GAAE,MAAI,CAAC,GAAE,GAAG,GAAE,MAAI,CAAC,GAAE,GAAG,GAAE,MAAI,CAAC,GAAE,GAAG,GAAE,MAAI,CAAC,GAAE,GAAG,GAAE,MAAI,CAAC,GAAE,GAAG,GAAE,MAAI,CAAC,GAAE,GAAG,GAAE,MAAI,CAAC,GAAE,GAAG;AAC/qC,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,SAAQ,GAAE,aAAY,GAAE,WAAU,GAAE,MAAK,GAAE,WAAU,GAAE,OAAM,GAAE,aAAY,GAAE,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,kBAAiB,IAAG,cAAa,IAAG,mBAAkB,IAAG,aAAY,IAAG,kBAAiB,IAAG,qBAAoB,IAAG,kBAAiB,IAAG,gBAAe,IAAG,gBAAe,IAAG,gBAAe,IAAG,gBAAe,IAAG,mBAAkB,IAAG,mBAAkB,IAAG,gBAAe,IAAG,mBAAkB,IAAG,mBAAkB,IAAG,UAAS,IAAG,MAAK,IAAG,YAAW,IAAG,MAAK,IAAG,QAAO,IAAG,QAAO,IAAG,QAAO,IAAG,aAAY,IAAG,cAAa,IAAG,cAAa,IAAG,eAAc,IAAG,eAAc,IAAG,0BAAyB,IAAG,yBAAwB,IAAG,2BAA0B,IAAG,wBAAuB,IAAG,qBAAoB,IAAG,YAAW,IAAG,YAAW,IAAG,aAAY,IAAG,mBAAkB,IAAG,wBAAuB,IAAG,qBAAoB,IAAG,eAAc,IAAG,WAAU,IAAG,eAAc,IAAG,eAAc,IAAG,QAAO,IAAG,QAAO,IAAG,UAAS,IAAG,OAAM,IAAG,eAAc,IAAG,gBAAe,IAAG,QAAO,IAAG,eAAc,IAAG,YAAW,IAAG,UAAS,IAAG,WAAU,IAAG,aAAY,IAAG,YAAW,IAAG,WAAU,IAAG,UAAS,IAAG,YAAW,IAAG,aAAY,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,kBAAiB,IAAG,OAAM,IAAG,SAAQ,IAAG,QAAO,IAAG,SAAQ,IAAG,QAAO,IAAG,OAAM,IAAG,SAAQ,IAAG,SAAQ,IAAG,aAAY,IAAG,aAAY,IAAG,WAAU,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IAC33C,YAAY,EAAC,GAAE,SAAQ,GAAE,WAAU,GAAE,MAAK,GAAE,OAAM,GAAE,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,gBAAe,IAAG,gBAAe,IAAG,gBAAe,IAAG,gBAAe,IAAG,gBAAe,IAAG,mBAAkB,IAAG,MAAK,IAAG,YAAW,IAAG,QAAO,IAAG,QAAO,IAAG,cAAa,IAAG,eAAc,IAAG,eAAc,IAAG,0BAAyB,IAAG,yBAAwB,IAAG,2BAA0B,IAAG,wBAAuB,IAAG,qBAAoB,IAAG,YAAW,IAAG,YAAW,IAAG,aAAY,IAAG,mBAAkB,IAAG,wBAAuB,IAAG,qBAAoB,IAAG,eAAc,IAAG,WAAU,IAAG,QAAO,IAAG,UAAS,IAAG,eAAc,IAAG,QAAO,IAAG,eAAc,IAAG,YAAW,IAAG,UAAS,IAAG,WAAU,IAAG,aAAY,IAAG,YAAW,IAAG,WAAU,IAAG,UAAS,IAAG,YAAW,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,OAAM,IAAG,SAAQ,IAAG,QAAO,IAAG,SAAQ,IAAG,QAAO,IAAG,OAAM,IAAG,SAAQ,IAAG,SAAQ,IAAG,aAAY,IAAG,aAAY,IAAG,UAAS;AAAA,IAC58B,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,IACtnB,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AACJ,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,YAAY,KAAK,CAAC;AAC3C;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AACZ,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,kBAAkB,KAAK,CAAC;AACjD;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,CAAC;AACX;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AACrB;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AACrB;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AACrB;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AACrB;AAAA,QACA,KAAK;AACJ,aAAG,eAAe,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AACrC;AAAA,QACA,KAAK;AACJ,aAAG,eAAe,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAAG,aAAG,SAAS,CAAC,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AACxE;AAAA,QACA,KAAK;AACJ,aAAG,YAAY,GAAG,KAAG,CAAC,CAAC;AACxB;AAAA,QACA,KAAK;AACJ,aAAG,cAAc,GAAG,KAAG,CAAC,CAAC;AAC1B;AAAA,QACA,KAAK;AACJ,aAAG,cAAc,GAAG,KAAG,CAAC,CAAC;AAC1B;AAAA,QACA,KAAK;AACJ,aAAG,sBAAsB,GAAG,KAAG,CAAC,CAAC;AAClC;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,gBAAgB;AAC3B;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,gBAAgB;AAC3B;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,gBAAgB;AAC3B;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,gBAAgB;AAC3B;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,gBAAgB;AAC3B;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,gBAAgB;AAC3B;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,UAAU;AACrB;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,UAAU;AACrB;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,UAAU;AACrB;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,WAAW;AACtB;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,WAAW;AACtB;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,WAAW;AACtB;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,WAAW;AACtB;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,GAAG,KAAG,CAAC,CAAC;AACvB;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,GAAG,KAAG,CAAC,CAAC;AAAG,aAAG,SAAS,CAAC,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAC1D;AAAA,QACA,KAAK;AACJ,aAAG,kBAAkB,GAAG,KAAG,CAAC,CAAC;AAC9B;AAAA,QACA,KAAK;AACJ,aAAG,oBAAoB,GAAG,KAAG,CAAC,CAAC;AAChC;AAAA,QACA,KAAK;AACH,aAAG,gBAAgB,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,KAAG,CAAC,CAAC;AAC/C;AAAA,QACA,KAAK;AACJ,aAAG,gBAAgB,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAC9C;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,cAAc;AACzB;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,cAAc;AACzB;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,cAAc;AACzB;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,cAAc;AACzB;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,cAAc;AACzB;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,cAAc;AACzB;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,cAAc;AACzB;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC;AAAE,aAAG,YAAY,GAAG,KAAG,CAAC,GAAE,GAAG,EAAE,CAAC;AAChD;AAAA,QACA,KAAK;AACL,aAAG,SAAS,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAC5B;AAAA,QACA,KAAK;AACL,aAAG,SAAS,CAAC,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;AAC9B;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AACjB;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAI,GAAG,KAAG,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;AAClC;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC;AAAE,aAAG,YAAY,GAAG,KAAG,CAAC,GAAE,GAAG,EAAE,CAAC;AAChD;AAAA,QACA,KAAK;AACL,eAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AAChB;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AAAE,eAAK,IAAI,GAAG,KAAG,CAAC;AACtC;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC,IAAI,GAAG,EAAE;AACzB;AAAA,MACA;AAAA,IACA,GAtJe;AAAA,IAuJf,OAAO,CAAC,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,GAAE,IAAG,GAAE,GAAE,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,IAAG,GAAE,KAAI,GAAE,IAAG,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,IAAG,GAAE,KAAI,GAAE,IAAG,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,IAAG,GAAE,KAAI,GAAE,IAAG,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,IAAG,GAAE,KAAI,GAAE,IAAG,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,IAAG,GAAE,KAAI,GAAE,IAAG,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,IAAG,GAAE,KAAI,GAAE,IAAG,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,IAAG,GAAE,KAAI,GAAE,IAAG,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,IAAG,GAAE,KAAI,GAAE,IAAG,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,IAAG,GAAE,KAAI,GAAE,IAAG,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,IAAG,GAAE,KAAI,GAAE,IAAG,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,CAAC,GAAE,EAAC,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,CAAC,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IACr3K,gBAAgB,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,EAAC;AAAA,IACjR,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAGD,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AAClB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAE,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,EAAC,oBAAmB,KAAI;AAAA,MACjC,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAC7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,qBAAqB;AACxC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,QAAQ;AAC5B;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,QAAQ;AAC5B;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,gBAAI,SAAS,IAAI,OAAO,KAAK;AAAG,mBAAO;AAChD;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,QACA;AAAA,MACA,GAlJe;AAAA,MAmJf,OAAO,CAAC,yBAAwB,yBAAwB,yBAAwB,yBAAwB,yBAAwB,0BAAyB,cAAa,gBAAe,gCAA+B,gCAA+B,gCAA+B,gCAA+B,kBAAiB,aAAY,iBAAgB,iBAAgB,WAAU,8BAA6B,YAAW,YAAW,cAAa,WAAU,cAAa,gBAAe,gBAAe,wBAAuB,uBAAsB,iCAAgC,gCAA+B,kCAAiC,+BAA8B,4BAA2B,eAAc,kBAAiB,gBAAe,oBAAmB,yBAAwB,sBAAqB,gBAAe,mBAAkB,oBAAmB,kBAAiB,mBAAkB,qBAAoB,oBAAmB,mBAAkB,kBAAiB,gBAAe,kBAAiB,iBAAgB,aAAY,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,aAAY,YAAW,oBAAmB,iBAAgB,YAAW,YAAW,WAAU,aAAY,aAAY,eAAc,kCAAiC,aAAY,gBAAe,SAAS;AAAA,MAC/vC,YAAY,EAAC,uBAAsB,EAAC,SAAQ,CAAC,GAAE,GAAE,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,UAAS,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,EAAC;AAAA,IAClmB;AACA,WAAOA;AAAA,EACP,EAAG;AACH,EAAAD,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,6BAAQ;;;AC50BT,IAAM,gBAAN,MAAyC;AAAA,EAyC9C,cAAc;AAxCd,SAAQ,YAAwB,CAAC;AACjC,SAAQ,oBAAiC,KAAK,sBAAsB;AACpE,SAAQ,eAAe,oBAAI,IAAyB;AACpD,SAAQ,gBAAyB,KAAK,kBAAkB;AACxD,SAAQ,WAAW,oBAAI,IAAqB;AAC5C,SAAQ,UAAU,oBAAI,IAA8B;AACpD,SAAQ,YAAY;AAEpB,SAAQ,kBAAkB;AAAA,MACxB,aAAa;AAAA,MACb,wBAAwB;AAAA,MACxB,uBAAuB;AAAA,MACvB,yBAAyB;AAAA,MACzB,sBAAsB;AAAA,MACtB,mBAAmB;AAAA,IACrB;AAEA,SAAQ,YAAY;AAAA,MAClB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAEA,SAAQ,aAAa;AAAA,MACnB,iBAAiB;AAAA,MACjB,sBAAsB;AAAA,MACtB,mBAAmB;AAAA,MACnB,aAAa;AAAA,IACf;AAEA,SAAQ,gBAAgB;AAAA,MACtB,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAsRA,SAAO,cAAc;AACrB,SAAO,cAAc;AACrB,SAAO,oBAAoB;AAC3B,SAAO,oBAAoB;AAC3B,SAAO,kBAAkB;AACzB,SAAO,kBAAkB;AACzB,SAAO,YAAY,6BAAM,UAAU,EAAE,aAAlB;AAzRjB,SAAK,MAAM;AAGX,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,SAAK,wBAAwB,KAAK,sBAAsB,KAAK,IAAI;AACjE,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,SAAK,sBAAsB,KAAK,oBAAoB,KAAK,IAAI;AAC7D,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AAAA,EAC3D;AAAA,EArFF,OAyBgD;AAAA;AAAA;AAAA,EA8DvC,eAAe;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACO,aAAa,KAAa;AAC/B,SAAK,YAAY;AAAA,EACnB;AAAA,EAEQ,yBAAyB;AAC/B,SAAK,oBAAoB,KAAK,sBAAsB;AAAA,EACtD;AAAA,EAEQ,qBAAqB;AAC3B,SAAK,gBAAgB,KAAK,kBAAkB;AAAA,EAC9C;AAAA,EAEQ,wBAAqC;AAC3C,WAAO;AAAA,MACL,eAAe;AAAA,MACf,MAAM;AAAA,MACN,MAAM;AAAA,MACN,cAAc;AAAA,MACd,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW,CAAC;AAAA,MACZ,SAAS,CAAC,SAAS;AAAA,IACrB;AAAA,EACF;AAAA,EAEQ,oBAA6B;AACnC,WAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW,CAAC;AAAA,MACZ,SAAS,CAAC,SAAS;AAAA,IACrB;AAAA,EACF;AAAA,EAEO,eAAe,MAAc,MAAuB;AACzD,QAAI,CAAC,KAAK,aAAa,IAAI,IAAI,GAAG;AAChC,WAAK,aAAa,IAAI,MAAM;AAAA,QAC1B;AAAA,QACA;AAAA,QACA,eAAe,KAAK,kBAAkB;AAAA,QACtC,MAAM,KAAK,kBAAkB;AAAA,QAC7B,MAAM,KAAK,kBAAkB;AAAA,QAC7B,cAAc,KAAK,kBAAkB;AAAA,QACrC,WAAW,CAAC;AAAA,QACZ,SAAS,CAAC,SAAS;AAAA,MACrB,CAAC;AAAA,IACH;AACA,SAAK,uBAAuB;AAE5B,WAAO,KAAK,aAAa,IAAI,IAAI;AAAA,EACnC;AAAA,EAEO,kBAAkB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,YAAY,IAAY;AAC7B,QAAI,KAAK,sBAAsB,QAAW;AACxC,WAAK,kBAAkB,gBAAgB;AAAA,IACzC;AAAA,EACF;AAAA,EAEO,cAAc,MAAc;AACjC,QAAI,KAAK,sBAAsB,QAAW;AACxC,WAAK,kBAAkB,OAAO;AAAA,IAChC;AAAA,EACF;AAAA,EAEO,cAAc,MAAiB;AACpC,QAAI,KAAK,sBAAsB,QAAW;AACxC,WAAK,kBAAkB,OAAO;AAAA,IAChC;AAAA,EACF;AAAA,EAEO,sBAAsB,cAA0B;AACrD,QAAI,KAAK,sBAAsB,QAAW;AACxC,WAAK,kBAAkB,eAAe;AAAA,IACxC;AAAA,EACF;AAAA,EAEO,WAAW,MAAc;AAC9B,QAAI,CAAC,KAAK,SAAS,IAAI,IAAI,GAAG;AAC5B,WAAK,SAAS,IAAI,MAAM;AAAA,QACtB;AAAA,QACA,MAAM,KAAK,cAAc;AAAA,QACzB,QAAQ,KAAK,cAAc;AAAA,QAC3B,WAAW,CAAC;AAAA,QACZ,SAAS,CAAC,SAAS;AAAA,MACrB,CAAC;AACD,UAAI,KAAK,uBAAuB,IAAI;AAAA,IACtC;AACA,SAAK,mBAAmB;AAExB,WAAO,KAAK,SAAS,IAAI,IAAI;AAAA,EAC/B;AAAA,EAEO,cAAc;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,kBAAkB,MAAc;AACrC,QAAI,KAAK,kBAAkB,QAAW;AACpC,WAAK,cAAc,OAAO;AAAA,IAC5B;AAAA,EACF;AAAA,EAEO,oBAAoB,QAAgB;AACzC,QAAI,KAAK,kBAAkB,QAAW;AACpC,WAAK,cAAc,SAAS;AAAA,IAC9B;AAAA,EACF;AAAA,EAEO,gBAAgB,MAAwB,KAAa,KAAa;AACvE,SAAK,UAAU,KAAK;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEO,mBAAmB;AACxB,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,QAAQ;AACb,SAAK,YAAY,CAAC;AAClB,SAAK,uBAAuB;AAC5B,SAAK,eAAe,oBAAI,IAAI;AAC5B,SAAK,mBAAmB;AACxB,SAAK,WAAW,oBAAI,IAAI;AACxB,SAAK,UAAU,oBAAI,IAAI;AACvB,UAAY;AAAA,EACd;AAAA,EAEO,YAAY,KAAe,QAAkB;AAClD,eAAW,MAAM,KAAK;AACpB,YAAM,OAAO,KAAK,aAAa,IAAI,EAAE,KAAK,KAAK,SAAS,IAAI,EAAE;AAC9D,UAAI,CAAC,UAAU,CAAC,MAAM;AACpB;AAAA,MACF;AACA,iBAAW,KAAK,QAAQ;AACtB,YAAI,EAAE,SAAS,GAAG,GAAG;AACnB,eAAK,UAAU,KAAK,GAAG,EAAE,MAAM,GAAG,CAAC;AAAA,QACrC,OAAO;AACL,eAAK,UAAU,KAAK,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEO,SAAS,KAAe,YAAsB;AACnD,eAAW,MAAM,KAAK;AACpB,YAAM,OAAO,KAAK,aAAa,IAAI,EAAE,KAAK,KAAK,SAAS,IAAI,EAAE;AAC9D,UAAI,MAAM;AACR,mBAAW,UAAU,YAAY;AAC/B,eAAK,QAAQ,KAAK,MAAM;AACxB,gBAAM,SAAS,KAAK,QAAQ,IAAI,MAAM,GAAG;AACzC,cAAI,QAAQ;AACV,iBAAK,UAAU,KAAK,GAAG,MAAM;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEO,YAAY,KAAe,OAAiB;AACjD,eAAW,MAAM,KAAK;AACpB,UAAI,aAAa,KAAK,QAAQ,IAAI,EAAE;AACpC,UAAI,eAAe,QAAW;AAC5B,qBAAa,EAAE,IAAI,QAAQ,CAAC,GAAG,YAAY,CAAC,EAAE;AAC9C,aAAK,QAAQ,IAAI,IAAI,UAAU;AAAA,MACjC;AAEA,UAAI,OAAO;AACT,cAAM,QAAQ,SAAU,GAAG;AACzB,cAAI,QAAQ,KAAK,CAAC,GAAG;AACnB,kBAAM,WAAW,EAAE,QAAQ,QAAQ,QAAQ;AAC3C,uBAAW,WAAW,KAAK,QAAQ;AAAA,UACrC;AACA,qBAAW,OAAO,KAAK,CAAC;AAAA,QAC1B,CAAC;AAAA,MACH;AAEA,WAAK,aAAa,QAAQ,CAAC,UAAU;AACnC,YAAI,MAAM,QAAQ,SAAS,EAAE,GAAG;AAC9B,gBAAM,UAAU,KAAK,GAAG,MAAM,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC;AAAA,QAC5D;AAAA,MACF,CAAC;AACD,WAAK,SAAS,QAAQ,CAAC,UAAU;AAC/B,YAAI,MAAM,QAAQ,SAAS,EAAE,GAAG;AAC9B,gBAAM,UAAU,KAAK,GAAG,MAAM,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC;AAAA,QAC5D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEO,aAAa;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,UAAU;AACf,UAAM,SAAS,UAAU;AACzB,UAAM,QAAgB,CAAC;AACvB,UAAM,QAAgB,CAAC;AACvB,eAAW,eAAe,KAAK,aAAa,OAAO,GAAG;AACpD,YAAM,OAAO;AACb,WAAK,KAAK,YAAY;AACtB,WAAK,YAAY,YAAY;AAC7B,WAAK,aAAa,YAAY,QAAQ,KAAK,GAAG;AAC9C,WAAK,QAAQ;AACb,WAAK,OAAO,OAAO;AACnB,YAAM,KAAK,IAAI;AAAA,IACjB;AAEA,eAAW,WAAW,KAAK,SAAS,OAAO,GAAG;AAC5C,YAAM,OAAO;AACb,WAAK,QAAQ;AACb,WAAK,OAAO,OAAO;AACnB,WAAK,KAAK,QAAQ;AAClB,WAAK,YAAY,QAAQ;AACzB,WAAK,aAAa,QAAQ,QAAQ,KAAK,GAAG;AAE1C,YAAM,KAAK,IAAI;AAAA,IACjB;AAEA,eAAW,YAAY,KAAK,WAAW;AACrC,UAAI,UAAU;AACd,YAAM,aAAa,SAAS,SAAS,KAAK,cAAc;AACxD,YAAM,OAAa;AAAA,QACjB,IAAI,GAAG,SAAS,GAAG,IAAI,SAAS,GAAG,IAAI,OAAO;AAAA,QAC9C,OAAO,KAAK,aAAa,IAAI,SAAS,GAAG,GAAG,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,GAAG;AAAA,QACrF,KAAK,KAAK,aAAa,IAAI,SAAS,GAAG,GAAG,QAAQ,KAAK,SAAS,IAAI,SAAS,GAAG,GAAG;AAAA,QACnF,OAAO,WAAW,SAAS,IAAI;AAAA,QAC/B,SAAS;AAAA,QACT,OAAO,CAAC,aAAa,aAAa,KAAK,wBAAwB;AAAA,QAC/D,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS,aAAa,WAAW;AAAA,QACjC,gBAAgB,aAAa,yBAAyB;AAAA,QACtD,cAAc,aAAa,KAAK;AAAA,QAChC,MAAM,OAAO;AAAA,MACf;AAEA,YAAM,KAAK,IAAI;AACf;AAAA,IACF;AAEA,WAAO,EAAE,OAAO,OAAO,OAAO,CAAC,GAAG,QAAQ,WAAW,KAAK,aAAa,EAAE;AAAA,EAC3E;AASF;;;AC7VA,IAAM,YAAY,wBAAC,YAAY;AAAA;AAAA;AAAA,YAGnB,QAAQ,aAAa;AAAA,cACnB,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA,cAIrB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA,mBAIZ,QAAQ,UAAU;AAAA,iBACpB,QAAQ,QAAQ;AAAA;AAAA;AAAA;AAAA,YAIrB,QAAQ,qBAAqB;AAAA;AAAA,cAE3B,QAAQ,sBAAsB;AAAA,oBACxB,QAAQ,qBAAqB;AAAA;AAAA;AAAA;AAAA,aAIpC,QAAQ,oBAAoB;AAAA;AAAA;AAAA,YAG7B,QAAQ,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,cAK7B,QAAQ,sBAAsB;AAAA,oBACxB,QAAQ,qBAAqB;AAAA;AAAA;AAAA,cAGnC,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA,YAIvB,QAAQ,kBAAkB;AAAA;AAAA;AAAA,cAGxB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,mBAIb,QAAQ,UAAU;AAAA,aACxB,QAAQ,iBAAiB,QAAQ,SAAS;AAAA;AAAA;AAAA,YAG3C,QAAQ,iBAAiB,QAAQ,SAAS;AAAA,aACzC,QAAQ,iBAAiB,QAAQ,SAAS;AAAA;AAAA;AAAA,wBAG/B,QAAQ,mBAAmB;AAAA;AAAA;AAAA,GAvDjC;AA4DlB,IAAO,iBAAQ;;;AC5Df;AAAA;AAAA;AAAA;AAQO,IAAM,OAAO,sCAAgB,MAAc,IAAY,UAAkB,MAAW;AACzF,MAAI,KAAK,OAAO;AAChB,MAAI,KAAK,yCAAyC,EAAE;AACpD,QAAM,EAAE,eAAe,OAAO,MAAM,OAAO,IAAI,UAAU;AAEzD,QAAM,cAAc,KAAK,GAAG,QAAQ;AAGpC,QAAM,MAAM,kBAAkB,IAAI,aAAa;AAE/C,cAAY,OAAO,KAAK;AACxB,cAAY,kBAAkB,6BAA6B,MAAM;AAEjE,cAAY,cAAc,MAAM,eAAe;AAC/C,cAAY,cAAc,MAAM,eAAe;AAC/C,cAAY,UAAU,CAAC,wBAAwB,mBAAmB;AAClE,cAAY,YAAY;AACxB,QAAM,OAAO,aAAa,GAAG;AAC7B,QAAM,UAAU;AAChB,gBAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM,kBAAkB;AAAA,IACxB,KAAK,GAAG,gBAAgB;AAAA,EAC1B;AAEA,sBAAoB,KAAK,SAAS,sBAAsB,MAAM,eAAe,IAAI;AACnF,GA3BoB;;;ACDb,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA,IAAI,KAAK;AACP,WAAO,IAAI,cAAc;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AACF;", "names": ["o", "parser", "lexer"]}