{"version": 3, "sources": ["../../../src/diagrams/sequence/parser/sequenceDiagram.jison", "../../../src/diagrams/sequence/sequenceDb.ts", "../../../src/diagrams/sequence/styles.js", "../../../src/diagrams/sequence/svgDraw.js", "../../../src/diagrams/sequence/sequenceRenderer.ts", "../../../src/diagrams/sequence/sequenceDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,2],$V1=[1,3],$V2=[1,4],$V3=[2,4],$V4=[1,9],$V5=[1,11],$V6=[1,13],$V7=[1,14],$V8=[1,16],$V9=[1,17],$Va=[1,18],$Vb=[1,24],$Vc=[1,25],$Vd=[1,26],$Ve=[1,27],$Vf=[1,28],$Vg=[1,29],$Vh=[1,30],$Vi=[1,31],$Vj=[1,32],$Vk=[1,33],$Vl=[1,34],$Vm=[1,35],$Vn=[1,36],$Vo=[1,37],$Vp=[1,38],$Vq=[1,39],$Vr=[1,41],$Vs=[1,42],$Vt=[1,43],$Vu=[1,44],$Vv=[1,45],$Vw=[1,46],$Vx=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],$Vy=[4,5,16,50,52,53],$Vz=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],$VA=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],$VB=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],$VC=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],$VD=[68,69,70],$VE=[1,122];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"SPACE\":4,\"NEWLINE\":5,\"SD\":6,\"document\":7,\"line\":8,\"statement\":9,\"box_section\":10,\"box_line\":11,\"participant_statement\":12,\"create\":13,\"box\":14,\"restOfLine\":15,\"end\":16,\"signal\":17,\"autonumber\":18,\"NUM\":19,\"off\":20,\"activate\":21,\"actor\":22,\"deactivate\":23,\"note_statement\":24,\"links_statement\":25,\"link_statement\":26,\"properties_statement\":27,\"details_statement\":28,\"title\":29,\"legacy_title\":30,\"acc_title\":31,\"acc_title_value\":32,\"acc_descr\":33,\"acc_descr_value\":34,\"acc_descr_multiline_value\":35,\"loop\":36,\"rect\":37,\"opt\":38,\"alt\":39,\"else_sections\":40,\"par\":41,\"par_sections\":42,\"par_over\":43,\"critical\":44,\"option_sections\":45,\"break\":46,\"option\":47,\"and\":48,\"else\":49,\"participant\":50,\"AS\":51,\"participant_actor\":52,\"destroy\":53,\"note\":54,\"placement\":55,\"text2\":56,\"over\":57,\"actor_pair\":58,\"links\":59,\"link\":60,\"properties\":61,\"details\":62,\"spaceList\":63,\",\":64,\"left_of\":65,\"right_of\":66,\"signaltype\":67,\"+\":68,\"-\":69,\"ACTOR\":70,\"SOLID_OPEN_ARROW\":71,\"DOTTED_OPEN_ARROW\":72,\"SOLID_ARROW\":73,\"BIDIRECTIONAL_SOLID_ARROW\":74,\"DOTTED_ARROW\":75,\"BIDIRECTIONAL_DOTTED_ARROW\":76,\"SOLID_CROSS\":77,\"DOTTED_CROSS\":78,\"SOLID_POINT\":79,\"DOTTED_POINT\":80,\"TXT\":81,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"SPACE\",5:\"NEWLINE\",6:\"SD\",13:\"create\",14:\"box\",15:\"restOfLine\",16:\"end\",18:\"autonumber\",19:\"NUM\",20:\"off\",21:\"activate\",23:\"deactivate\",29:\"title\",30:\"legacy_title\",31:\"acc_title\",32:\"acc_title_value\",33:\"acc_descr\",34:\"acc_descr_value\",35:\"acc_descr_multiline_value\",36:\"loop\",37:\"rect\",38:\"opt\",39:\"alt\",41:\"par\",43:\"par_over\",44:\"critical\",46:\"break\",47:\"option\",48:\"and\",49:\"else\",50:\"participant\",51:\"AS\",52:\"participant_actor\",53:\"destroy\",54:\"note\",57:\"over\",59:\"links\",60:\"link\",61:\"properties\",62:\"details\",64:\",\",65:\"left_of\",66:\"right_of\",68:\"+\",69:\"-\",70:\"ACTOR\",71:\"SOLID_OPEN_ARROW\",72:\"DOTTED_OPEN_ARROW\",73:\"SOLID_ARROW\",74:\"BIDIRECTIONAL_SOLID_ARROW\",75:\"DOTTED_ARROW\",76:\"BIDIRECTIONAL_DOTTED_ARROW\",77:\"SOLID_CROSS\",78:\"DOTTED_CROSS\",79:\"SOLID_POINT\",80:\"DOTTED_POINT\",81:\"TXT\"},\nproductions_: [0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 3:\n yy.apply($$[$0]);return $$[$0]; \nbreak;\ncase 4: case 9:\n this.$ = [] \nbreak;\ncase 5: case 10:\n$$[$0-1].push($$[$0]);this.$ = $$[$0-1]\nbreak;\ncase 6: case 7: case 11: case 12:\n this.$ = $$[$0] \nbreak;\ncase 8: case 13:\n this.$=[]; \nbreak;\ncase 15:\n$$[$0].type='createParticipant'; this.$=$$[$0];\nbreak;\ncase 16:\n\n\t\t$$[$0-1].unshift({type: 'boxStart', boxData:yy.parseBoxData($$[$0-2]) });\n\t\t$$[$0-1].push({type: 'boxEnd', boxText:$$[$0-2]});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 18:\n this.$= {type:'sequenceIndex',sequenceIndex: Number($$[$0-2]), sequenceIndexStep:Number($$[$0-1]), sequenceVisible:true, signalType:yy.LINETYPE.AUTONUMBER};\nbreak;\ncase 19:\n this.$ = {type:'sequenceIndex',sequenceIndex: Number($$[$0-1]), sequenceIndexStep:1, sequenceVisible:true, signalType:yy.LINETYPE.AUTONUMBER};\nbreak;\ncase 20:\n this.$ = {type:'sequenceIndex', sequenceVisible:false, signalType:yy.LINETYPE.AUTONUMBER};\nbreak;\ncase 21:\nthis.$ = {type:'sequenceIndex', sequenceVisible:true, signalType:yy.LINETYPE.AUTONUMBER}; \nbreak;\ncase 22:\nthis.$={type: 'activeStart', signalType: yy.LINETYPE.ACTIVE_START, actor: $$[$0-1].actor};\nbreak;\ncase 23:\nthis.$={type: 'activeEnd', signalType: yy.LINETYPE.ACTIVE_END, actor: $$[$0-1].actor};\nbreak;\ncase 29:\nyy.setDiagramTitle($$[$0].substring(6));this.$=$$[$0].substring(6);\nbreak;\ncase 30:\nyy.setDiagramTitle($$[$0].substring(7));this.$=$$[$0].substring(7);\nbreak;\ncase 31:\n this.$=$$[$0].trim();yy.setAccTitle(this.$); \nbreak;\ncase 32: case 33:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 34:\n\n\t\t$$[$0-1].unshift({type: 'loopStart', loopText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.LOOP_START});\n\t\t$$[$0-1].push({type: 'loopEnd', loopText:$$[$0-2], signalType: yy.LINETYPE.LOOP_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 35:\n\n\t\t$$[$0-1].unshift({type: 'rectStart', color:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.RECT_START });\n\t\t$$[$0-1].push({type: 'rectEnd', color:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.RECT_END });\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 36:\n\n\t\t$$[$0-1].unshift({type: 'optStart', optText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.OPT_START});\n\t\t$$[$0-1].push({type: 'optEnd', optText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.OPT_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 37:\n\n\t\t// Alt start\n\t\t$$[$0-1].unshift({type: 'altStart', altText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.ALT_START});\n\t\t// Content in alt is already in $$[$0-1]\n\t\t// End\n\t\t$$[$0-1].push({type: 'altEnd', signalType: yy.LINETYPE.ALT_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 38:\n\n\t\t// Parallel start\n\t\t$$[$0-1].unshift({type: 'parStart', parText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.PAR_START});\n\t\t// Content in par is already in $$[$0-1]\n\t\t// End\n\t\t$$[$0-1].push({type: 'parEnd', signalType: yy.LINETYPE.PAR_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 39:\n\n\t\t// Parallel (overlapped) start\n\t\t$$[$0-1].unshift({type: 'parStart', parText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.PAR_OVER_START});\n\t\t// Content in par is already in $$[$0-1]\n\t\t// End\n\t\t$$[$0-1].push({type: 'parEnd', signalType: yy.LINETYPE.PAR_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 40:\n\n\t\t// critical start\n\t\t$$[$0-1].unshift({type: 'criticalStart', criticalText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.CRITICAL_START});\n\t\t// Content in critical is already in $$[$0-1]\n\t\t// critical end\n\t\t$$[$0-1].push({type: 'criticalEnd', signalType: yy.LINETYPE.CRITICAL_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 41:\n\n\t\t$$[$0-1].unshift({type: 'breakStart', breakText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.BREAK_START});\n\t\t$$[$0-1].push({type: 'breakEnd', optText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.BREAK_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 43:\n this.$ = $$[$0-3].concat([{type: 'option', optionText:yy.parseMessage($$[$0-1]), signalType: yy.LINETYPE.CRITICAL_OPTION}, $$[$0]]); \nbreak;\ncase 45:\n this.$ = $$[$0-3].concat([{type: 'and', parText:yy.parseMessage($$[$0-1]), signalType: yy.LINETYPE.PAR_AND}, $$[$0]]); \nbreak;\ncase 47:\n this.$ = $$[$0-3].concat([{type: 'else', altText:yy.parseMessage($$[$0-1]), signalType: yy.LINETYPE.ALT_ELSE}, $$[$0]]); \nbreak;\ncase 48:\n$$[$0-3].draw='participant'; $$[$0-3].type='addParticipant';$$[$0-3].description=yy.parseMessage($$[$0-1]); this.$=$$[$0-3];\nbreak;\ncase 49:\n$$[$0-1].draw='participant'; $$[$0-1].type='addParticipant';this.$=$$[$0-1];\nbreak;\ncase 50:\n$$[$0-3].draw='actor'; $$[$0-3].type='addParticipant';$$[$0-3].description=yy.parseMessage($$[$0-1]); this.$=$$[$0-3];\nbreak;\ncase 51:\n$$[$0-1].draw='actor'; $$[$0-1].type='addParticipant'; this.$=$$[$0-1];\nbreak;\ncase 52:\n$$[$0-1].type='destroyParticipant'; this.$=$$[$0-1];\nbreak;\ncase 53:\n\n\t\tthis.$ = [$$[$0-1], {type:'addNote', placement:$$[$0-2], actor:$$[$0-1].actor, text:$$[$0]}];\nbreak;\ncase 54:\n\n\t\t// Coerce actor_pair into a [to, from, ...] array\n\t\t$$[$0-2] = [].concat($$[$0-1], $$[$0-1]).slice(0, 2);\n\t\t$$[$0-2][0] = $$[$0-2][0].actor;\n\t\t$$[$0-2][1] = $$[$0-2][1].actor;\n\t\tthis.$ = [$$[$0-1], {type:'addNote', placement:yy.PLACEMENT.OVER, actor:$$[$0-2].slice(0, 2), text:$$[$0]}];\nbreak;\ncase 55:\n\n\t\tthis.$ = [$$[$0-1], {type:'addLinks', actor:$$[$0-1].actor, text:$$[$0]}];\n  \nbreak;\ncase 56:\n\n\t\tthis.$ = [$$[$0-1], {type:'addALink', actor:$$[$0-1].actor, text:$$[$0]}];\n  \nbreak;\ncase 57:\n\n\t\tthis.$ = [$$[$0-1], {type:'addProperties', actor:$$[$0-1].actor, text:$$[$0]}];\n  \nbreak;\ncase 58:\n\n\t\tthis.$ = [$$[$0-1], {type:'addDetails', actor:$$[$0-1].actor, text:$$[$0]}];\n  \nbreak;\ncase 61:\n this.$ = [$$[$0-2], $$[$0]]; \nbreak;\ncase 62:\n this.$ = $$[$0]; \nbreak;\ncase 63:\n this.$ = yy.PLACEMENT.LEFTOF; \nbreak;\ncase 64:\n this.$ = yy.PLACEMENT.RIGHTOF; \nbreak;\ncase 65:\n this.$ = [$$[$0-4],$$[$0-1],{type: 'addMessage', from:$$[$0-4].actor, to:$$[$0-1].actor, signalType:$$[$0-3], msg:$$[$0], activate: true},\n\t              {type: 'activeStart', signalType: yy.LINETYPE.ACTIVE_START, actor: $$[$0-1].actor}\n\t             ]\nbreak;\ncase 66:\n this.$ = [$$[$0-4],$$[$0-1],{type: 'addMessage', from:$$[$0-4].actor, to:$$[$0-1].actor, signalType:$$[$0-3], msg:$$[$0]},\n\t             {type: 'activeEnd', signalType: yy.LINETYPE.ACTIVE_END, actor: $$[$0-4].actor}\n\t             ]\nbreak;\ncase 67:\n this.$ = [$$[$0-3],$$[$0-1],{type: 'addMessage', from:$$[$0-3].actor, to:$$[$0-1].actor, signalType:$$[$0-2], msg:$$[$0]}]\nbreak;\ncase 68:\nthis.$={ type: 'addParticipant', actor:$$[$0]}\nbreak;\ncase 69:\n this.$ = yy.LINETYPE.SOLID_OPEN; \nbreak;\ncase 70:\n this.$ = yy.LINETYPE.DOTTED_OPEN; \nbreak;\ncase 71:\n this.$ = yy.LINETYPE.SOLID; \nbreak;\ncase 72:\n this.$ = yy.LINETYPE.BIDIRECTIONAL_SOLID; \nbreak;\ncase 73:\n this.$ = yy.LINETYPE.DOTTED; \nbreak;\ncase 74:\n this.$ = yy.LINETYPE.BIDIRECTIONAL_DOTTED; \nbreak;\ncase 75:\n this.$ = yy.LINETYPE.SOLID_CROSS; \nbreak;\ncase 76:\n this.$ = yy.LINETYPE.DOTTED_CROSS; \nbreak;\ncase 77:\n this.$ = yy.LINETYPE.SOLID_POINT; \nbreak;\ncase 78:\n this.$ = yy.LINETYPE.DOTTED_POINT; \nbreak;\ncase 79:\nthis.$ = yy.parseMessage($$[$0].trim().substring(1)) \nbreak;\n}\n},\ntable: [{3:1,4:$V0,5:$V1,6:$V2},{1:[3]},{3:5,4:$V0,5:$V1,6:$V2},{3:6,4:$V0,5:$V1,6:$V2},o([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],$V3,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},o($Vx,[2,5]),{9:47,12:12,13:$V6,14:$V7,17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},o($Vx,[2,7]),o($Vx,[2,8]),o($Vx,[2,14]),{12:48,50:$Vo,52:$Vp,53:$Vq},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:$Vw},{22:55,70:$Vw},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},o($Vx,[2,29]),o($Vx,[2,30]),{32:[1,61]},{34:[1,62]},o($Vx,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:$Vw},{22:72,70:$Vw},{22:73,70:$Vw},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82],79:[1,83],80:[1,84]},{55:85,57:[1,86],65:[1,87],66:[1,88]},{22:89,70:$Vw},{22:90,70:$Vw},{22:91,70:$Vw},{22:92,70:$Vw},o([5,51,64,71,72,73,74,75,76,77,78,79,80,81],[2,68]),o($Vx,[2,6]),o($Vx,[2,15]),o($Vy,[2,9],{10:93}),o($Vx,[2,17]),{5:[1,95],19:[1,94]},{5:[1,96]},o($Vx,[2,21]),{5:[1,97]},{5:[1,98]},o($Vx,[2,24]),o($Vx,[2,25]),o($Vx,[2,26]),o($Vx,[2,27]),o($Vx,[2,28]),o($Vx,[2,31]),o($Vx,[2,32]),o($Vz,$V3,{7:99}),o($Vz,$V3,{7:100}),o($Vz,$V3,{7:101}),o($VA,$V3,{40:102,7:103}),o($VB,$V3,{42:104,7:105}),o($VB,$V3,{7:105,42:106}),o($VC,$V3,{45:107,7:108}),o($Vz,$V3,{7:109}),{5:[1,111],51:[1,110]},{5:[1,113],51:[1,112]},{5:[1,114]},{22:117,68:[1,115],69:[1,116],70:$Vw},o($VD,[2,69]),o($VD,[2,70]),o($VD,[2,71]),o($VD,[2,72]),o($VD,[2,73]),o($VD,[2,74]),o($VD,[2,75]),o($VD,[2,76]),o($VD,[2,77]),o($VD,[2,78]),{22:118,70:$Vw},{22:120,58:119,70:$Vw},{70:[2,63]},{70:[2,64]},{56:121,81:$VE},{56:123,81:$VE},{56:124,81:$VE},{56:125,81:$VE},{4:[1,128],5:[1,130],11:127,12:129,16:[1,126],50:$Vo,52:$Vp,53:$Vq},{5:[1,131]},o($Vx,[2,19]),o($Vx,[2,20]),o($Vx,[2,22]),o($Vx,[2,23]),{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[1,132],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[1,133],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[1,134],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{16:[1,135]},{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[2,46],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,49:[1,136],50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{16:[1,137]},{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[2,44],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,48:[1,138],50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{16:[1,139]},{16:[1,140]},{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[2,42],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,47:[1,141],50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[1,142],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{15:[1,143]},o($Vx,[2,49]),{15:[1,144]},o($Vx,[2,51]),o($Vx,[2,52]),{22:145,70:$Vw},{22:146,70:$Vw},{56:147,81:$VE},{56:148,81:$VE},{56:149,81:$VE},{64:[1,150],81:[2,62]},{5:[2,55]},{5:[2,79]},{5:[2,56]},{5:[2,57]},{5:[2,58]},o($Vx,[2,16]),o($Vy,[2,10]),{12:151,50:$Vo,52:$Vp,53:$Vq},o($Vy,[2,12]),o($Vy,[2,13]),o($Vx,[2,18]),o($Vx,[2,34]),o($Vx,[2,35]),o($Vx,[2,36]),o($Vx,[2,37]),{15:[1,152]},o($Vx,[2,38]),{15:[1,153]},o($Vx,[2,39]),o($Vx,[2,40]),{15:[1,154]},o($Vx,[2,41]),{5:[1,155]},{5:[1,156]},{56:157,81:$VE},{56:158,81:$VE},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:159,70:$Vw},o($Vy,[2,11]),o($VA,$V3,{7:103,40:160}),o($VB,$V3,{7:105,42:161}),o($VC,$V3,{7:108,45:162}),o($Vx,[2,48]),o($Vx,[2,50]),{5:[2,65]},{5:[2,66]},{81:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],\ndefaultActions: {5:[2,1],6:[2,2],87:[2,63],88:[2,64],121:[2,55],122:[2,79],123:[2,56],124:[2,57],125:[2,58],147:[2,67],148:[2,53],149:[2,54],157:[2,65],158:[2,66],159:[2,61],160:[2,47],161:[2,45],162:[2,43]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:return 5;\nbreak;\ncase 1:/* skip all whitespace */\nbreak;\ncase 2:/* skip same-line whitespace */\nbreak;\ncase 3:/* skip comments */\nbreak;\ncase 4:/* skip comments */\nbreak;\ncase 5:/* skip comments */\nbreak;\ncase 6:return 19;\nbreak;\ncase 7: this.begin('LINE'); return 14; \nbreak;\ncase 8: this.begin('ID'); return 50; \nbreak;\ncase 9: this.begin('ID'); return 52; \nbreak;\ncase 10:return 13;\nbreak;\ncase 11: this.begin('ID'); return 53; \nbreak;\ncase 12: yy_.yytext = yy_.yytext.trim(); this.begin('ALIAS'); return 70; \nbreak;\ncase 13: this.popState(); this.popState(); this.begin('LINE'); return 51; \nbreak;\ncase 14: this.popState(); this.popState(); return 5; \nbreak;\ncase 15: this.begin('LINE'); return 36; \nbreak;\ncase 16: this.begin('LINE'); return 37; \nbreak;\ncase 17: this.begin('LINE'); return 38; \nbreak;\ncase 18: this.begin('LINE'); return 39; \nbreak;\ncase 19: this.begin('LINE'); return 49; \nbreak;\ncase 20: this.begin('LINE'); return 41; \nbreak;\ncase 21: this.begin('LINE'); return 43; \nbreak;\ncase 22: this.begin('LINE'); return 48; \nbreak;\ncase 23: this.begin('LINE'); return 44; \nbreak;\ncase 24: this.begin('LINE'); return 47; \nbreak;\ncase 25: this.begin('LINE'); return 46; \nbreak;\ncase 26: this.popState(); return 15; \nbreak;\ncase 27:return 16;\nbreak;\ncase 28:return 65;\nbreak;\ncase 29:return 66;\nbreak;\ncase 30:return 59;\nbreak;\ncase 31:return 60;\nbreak;\ncase 32:return 61;\nbreak;\ncase 33:return 62;\nbreak;\ncase 34:return 57;\nbreak;\ncase 35:return 54;\nbreak;\ncase 36: this.begin('ID'); return 21; \nbreak;\ncase 37: this.begin('ID'); return 23; \nbreak;\ncase 38:return 29;\nbreak;\ncase 39:return 30;\nbreak;\ncase 40: this.begin(\"acc_title\");return 31; \nbreak;\ncase 41: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 42: this.begin(\"acc_descr\");return 33; \nbreak;\ncase 43: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 44: this.begin(\"acc_descr_multiline\");\nbreak;\ncase 45: this.popState(); \nbreak;\ncase 46:return \"acc_descr_multiline_value\";\nbreak;\ncase 47:return 6;\nbreak;\ncase 48:return 18;\nbreak;\ncase 49:return 20;\nbreak;\ncase 50:return 64;\nbreak;\ncase 51:return 5;\nbreak;\ncase 52: yy_.yytext = yy_.yytext.trim(); return 70; \nbreak;\ncase 53:return 73;\nbreak;\ncase 54:return 74;\nbreak;\ncase 55:return 75;\nbreak;\ncase 56:return 76;\nbreak;\ncase 57:return 71;\nbreak;\ncase 58:return 72;\nbreak;\ncase 59:return 77;\nbreak;\ncase 60:return 78;\nbreak;\ncase 61:return 79;\nbreak;\ncase 62:return 80;\nbreak;\ncase 63:return 81;\nbreak;\ncase 64:return 81;\nbreak;\ncase 65:return 68;\nbreak;\ncase 66:return 69;\nbreak;\ncase 67:return 5;\nbreak;\ncase 68:return 'INVALID';\nbreak;\n}\n},\nrules: [/^(?:[\\n]+)/i,/^(?:\\s+)/i,/^(?:((?!\\n)\\s)+)/i,/^(?:#[^\\n]*)/i,/^(?:%(?!\\{)[^\\n]*)/i,/^(?:[^\\}]%%[^\\n]*)/i,/^(?:[0-9]+(?=[ \\n]+))/i,/^(?:box\\b)/i,/^(?:participant\\b)/i,/^(?:actor\\b)/i,/^(?:create\\b)/i,/^(?:destroy\\b)/i,/^(?:[^<\\->\\->:\\n,;]+?([\\-]*[^<\\->\\->:\\n,;]+?)*?(?=((?!\\n)\\s)+as(?!\\n)\\s|[#\\n;]|$))/i,/^(?:as\\b)/i,/^(?:(?:))/i,/^(?:loop\\b)/i,/^(?:rect\\b)/i,/^(?:opt\\b)/i,/^(?:alt\\b)/i,/^(?:else\\b)/i,/^(?:par\\b)/i,/^(?:par_over\\b)/i,/^(?:and\\b)/i,/^(?:critical\\b)/i,/^(?:option\\b)/i,/^(?:break\\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\\n;]*)/i,/^(?:end\\b)/i,/^(?:left of\\b)/i,/^(?:right of\\b)/i,/^(?:links\\b)/i,/^(?:link\\b)/i,/^(?:properties\\b)/i,/^(?:details\\b)/i,/^(?:over\\b)/i,/^(?:note\\b)/i,/^(?:activate\\b)/i,/^(?:deactivate\\b)/i,/^(?:title\\s[^#\\n;]+)/i,/^(?:title:\\s[^#\\n;]+)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:[\\}])/i,/^(?:[^\\}]*)/i,/^(?:sequenceDiagram\\b)/i,/^(?:autonumber\\b)/i,/^(?:off\\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^+<\\->\\->:\\n,;]+((?!(-x|--x|-\\)|--\\)))[\\-]*[^\\+<\\->\\->:\\n,;]+)*)/i,/^(?:->>)/i,/^(?:<<->>)/i,/^(?:-->>)/i,/^(?:<<-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\\)])/i,/^(?:--[\\)])/i,/^(?::(?:(?:no)?wrap)?[^#\\n;]*)/i,/^(?::)/i,/^(?:\\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],\nconditions: {\"acc_descr_multiline\":{\"rules\":[45,46],\"inclusive\":false},\"acc_descr\":{\"rules\":[43],\"inclusive\":false},\"acc_title\":{\"rules\":[41],\"inclusive\":false},\"ID\":{\"rules\":[2,3,12],\"inclusive\":false},\"ALIAS\":{\"rules\":[2,3,13,14],\"inclusive\":false},\"LINE\":{\"rules\":[2,3,26],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { DiagramDB } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { ImperativeState } from '../../utils/imperativeState.js';\nimport { sanitizeText } from '../common/common.js';\nimport {\n  clear as commonClear,\n  getAccDescription,\n  getAccTitle,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n} from '../common/commonDb.js';\nimport type { Actor, AddMessageParams, Box, Message, Note } from './types.js';\n\ninterface SequenceState {\n  prevActor?: string;\n  actors: Map<string, Actor>;\n  createdActors: Map<string, number>;\n  destroyedActors: Map<string, number>;\n  boxes: Box[];\n  messages: Message[];\n  notes: Note[];\n  sequenceNumbersEnabled: boolean;\n  wrapEnabled?: boolean;\n  currentBox?: Box;\n  lastCreated?: Actor;\n  lastDestroyed?: Actor;\n}\n\nconst LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25,\n  AUTONUMBER: 26,\n  CRITICAL_START: 27,\n  CRITICAL_OPTION: 28,\n  CRITICAL_END: 29,\n  BREAK_START: 30,\n  BREAK_END: 31,\n  PAR_OVER_START: 32,\n  BIDIRECTIONAL_SOLID: 33,\n  BIDIRECTIONAL_DOTTED: 34,\n} as const;\n\nconst ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1,\n} as const;\n\nconst PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2,\n} as const;\n\nexport class SequenceDB implements DiagramDB {\n  private readonly state = new ImperativeState<SequenceState>(() => ({\n    prevActor: undefined,\n    actors: new Map(),\n    createdActors: new Map(),\n    destroyedActors: new Map(),\n    boxes: [],\n    messages: [],\n    notes: [],\n    sequenceNumbersEnabled: false,\n    wrapEnabled: undefined,\n    currentBox: undefined,\n    lastCreated: undefined,\n    lastDestroyed: undefined,\n  }));\n\n  constructor() {\n    // Needed for JISON since it only supports direct properties\n    this.apply = this.apply.bind(this);\n    this.parseBoxData = this.parseBoxData.bind(this);\n    this.parseMessage = this.parseMessage.bind(this);\n\n    this.clear();\n\n    this.setWrap(getConfig().wrap);\n    this.LINETYPE = LINETYPE;\n    this.ARROWTYPE = ARROWTYPE;\n    this.PLACEMENT = PLACEMENT;\n  }\n\n  public addBox(data: { text: string; color: string; wrap: boolean }) {\n    this.state.records.boxes.push({\n      name: data.text,\n      wrap: data.wrap ?? this.autoWrap(),\n      fill: data.color,\n      actorKeys: [],\n    });\n    this.state.records.currentBox = this.state.records.boxes.slice(-1)[0];\n  }\n\n  public addActor(\n    id: string,\n    name: string,\n    description: { text: string; wrap?: boolean | null; type: string },\n    type: string\n  ) {\n    let assignedBox = this.state.records.currentBox;\n    const old = this.state.records.actors.get(id);\n    if (old) {\n      // If already set and trying to set to a new one throw error\n      if (this.state.records.currentBox && old.box && this.state.records.currentBox !== old.box) {\n        throw new Error(\n          `A same participant should only be defined in one Box: ${old.name} can't be in '${old.box.name}' and in '${this.state.records.currentBox.name}' at the same time.`\n        );\n      }\n\n      // Don't change the box if already\n      assignedBox = old.box ? old.box : this.state.records.currentBox;\n      old.box = assignedBox;\n\n      // Don't allow description nulling\n      if (old && name === old.name && description == null) {\n        return;\n      }\n    }\n\n    // Don't allow null descriptions, either\n    if (description?.text == null) {\n      description = { text: name, type };\n    }\n    if (type == null || description.text == null) {\n      description = { text: name, type };\n    }\n\n    this.state.records.actors.set(id, {\n      box: assignedBox,\n      name: name,\n      description: description.text,\n      wrap: description.wrap ?? this.autoWrap(),\n      prevActor: this.state.records.prevActor,\n      links: {},\n      properties: {},\n      actorCnt: null,\n      rectData: null,\n      type: type ?? 'participant',\n    });\n    if (this.state.records.prevActor) {\n      const prevActorInRecords = this.state.records.actors.get(this.state.records.prevActor);\n      if (prevActorInRecords) {\n        prevActorInRecords.nextActor = id;\n      }\n    }\n\n    if (this.state.records.currentBox) {\n      this.state.records.currentBox.actorKeys.push(id);\n    }\n    this.state.records.prevActor = id;\n  }\n\n  private activationCount(part: string) {\n    let i;\n    let count = 0;\n    if (!part) {\n      return 0;\n    }\n    for (i = 0; i < this.state.records.messages.length; i++) {\n      if (\n        this.state.records.messages[i].type === this.LINETYPE.ACTIVE_START &&\n        this.state.records.messages[i].from === part\n      ) {\n        count++;\n      }\n      if (\n        this.state.records.messages[i].type === this.LINETYPE.ACTIVE_END &&\n        this.state.records.messages[i].from === part\n      ) {\n        count--;\n      }\n    }\n    return count;\n  }\n\n  public addMessage(\n    idFrom: Message['from'],\n    idTo: Message['to'],\n    message: { text: string; wrap?: boolean },\n    answer: Message['answer']\n  ) {\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: idFrom,\n      to: idTo,\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap(),\n      answer: answer,\n    });\n  }\n\n  public addSignal(\n    idFrom?: Message['from'],\n    idTo?: Message['to'],\n    message?: { text: string; wrap: boolean },\n    messageType?: number,\n    activate = false\n  ) {\n    if (messageType === this.LINETYPE.ACTIVE_END) {\n      const cnt = this.activationCount(idFrom ?? '');\n      if (cnt < 1) {\n        // Bail out as there is an activation signal from an inactive participant\n        const error = new Error('Trying to inactivate an inactive participant (' + idFrom + ')');\n\n        // @ts-ignore: we are passing hash param to the error object, however we should define our own custom error class to make it type safe\n        error.hash = {\n          text: '->>-',\n          token: '->>-',\n          line: '1',\n          loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n          expected: [\"'ACTIVE_PARTICIPANT'\"],\n        };\n        throw error;\n      }\n    }\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: idFrom,\n      to: idTo,\n      message: message?.text ?? '',\n      wrap: message?.wrap ?? this.autoWrap(),\n      type: messageType,\n      activate,\n    });\n    return true;\n  }\n\n  public hasAtLeastOneBox() {\n    return this.state.records.boxes.length > 0;\n  }\n\n  public hasAtLeastOneBoxWithTitle() {\n    return this.state.records.boxes.some((b) => b.name);\n  }\n\n  public getMessages() {\n    return this.state.records.messages;\n  }\n\n  public getBoxes() {\n    return this.state.records.boxes;\n  }\n  public getActors() {\n    return this.state.records.actors;\n  }\n  public getCreatedActors() {\n    return this.state.records.createdActors;\n  }\n  public getDestroyedActors() {\n    return this.state.records.destroyedActors;\n  }\n  public getActor(id: string) {\n    // TODO: do we ever use this function in a way that it might return undefined?\n    return this.state.records.actors.get(id)!;\n  }\n  public getActorKeys() {\n    return [...this.state.records.actors.keys()];\n  }\n  public enableSequenceNumbers() {\n    this.state.records.sequenceNumbersEnabled = true;\n  }\n  public disableSequenceNumbers() {\n    this.state.records.sequenceNumbersEnabled = false;\n  }\n  public showSequenceNumbers() {\n    return this.state.records.sequenceNumbersEnabled;\n  }\n\n  public setWrap(wrapSetting?: boolean) {\n    this.state.records.wrapEnabled = wrapSetting;\n  }\n\n  private extractWrap(text?: string): { cleanedText?: string; wrap?: boolean } {\n    if (text === undefined) {\n      return {};\n    }\n    text = text.trim();\n    const wrap =\n      /^:?wrap:/.exec(text) !== null ? true : /^:?nowrap:/.exec(text) !== null ? false : undefined;\n    const cleanedText = (wrap === undefined ? text : text.replace(/^:?(?:no)?wrap:/, '')).trim();\n    return { cleanedText, wrap };\n  }\n\n  public autoWrap() {\n    // if setWrap has been called, use that value, otherwise use the value from the config\n    // TODO: refactor, always use the config value let setWrap update the config value\n    if (this.state.records.wrapEnabled !== undefined) {\n      return this.state.records.wrapEnabled;\n    }\n    return getConfig().sequence?.wrap ?? false;\n  }\n\n  public clear() {\n    this.state.reset();\n    commonClear();\n  }\n\n  public parseMessage(str: string) {\n    const trimmedStr = str.trim();\n    const { wrap, cleanedText } = this.extractWrap(trimmedStr);\n    const message = {\n      text: cleanedText,\n      wrap,\n    };\n    log.debug(`parseMessage: ${JSON.stringify(message)}`);\n    return message;\n  }\n\n  // We expect the box statement to be color first then description\n  // The color can be rgb,rgba,hsl,hsla, or css code names  #hex codes are not supported for now because of the way the char # is handled\n  // We extract first segment as color, the rest of the line is considered as text\n  public parseBoxData(str: string) {\n    const match = /^((?:rgba?|hsla?)\\s*\\(.*\\)|\\w*)(.*)$/.exec(str);\n    let color = match?.[1] ? match[1].trim() : 'transparent';\n    let title = match?.[2] ? match[2].trim() : undefined;\n\n    // check that the string is a color\n    if (window?.CSS) {\n      if (!window.CSS.supports('color', color)) {\n        color = 'transparent';\n        title = str.trim();\n      }\n    } else {\n      const style = new Option().style;\n      style.color = color;\n      if (style.color !== color) {\n        color = 'transparent';\n        title = str.trim();\n      }\n    }\n    const { wrap, cleanedText } = this.extractWrap(title);\n    return {\n      text: cleanedText ? sanitizeText(cleanedText, getConfig()) : undefined,\n      color,\n      wrap,\n    };\n  }\n\n  public readonly LINETYPE: typeof LINETYPE;\n  public readonly ARROWTYPE: typeof ARROWTYPE;\n  public readonly PLACEMENT: typeof PLACEMENT;\n\n  public addNote(\n    actor: { actor: string },\n    placement: Message['placement'],\n    message: { text: string; wrap?: boolean }\n  ) {\n    const note: Note = {\n      actor: actor,\n      placement: placement,\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap(),\n    };\n\n    //@ts-ignore: Coerce actor into a [to, from, ...] array\n    // eslint-disable-next-line unicorn/prefer-spread\n    const actors = [].concat(actor, actor);\n    this.state.records.notes.push(note);\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: actors[0],\n      to: actors[1],\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap(),\n      type: this.LINETYPE.NOTE,\n      placement: placement,\n    });\n  }\n\n  public addLinks(actorId: string, text: { text: string }) {\n    // find the actor\n    const actor = this.getActor(actorId);\n    // JSON.parse the text\n    try {\n      let sanitizedText = sanitizeText(text.text, getConfig());\n      sanitizedText = sanitizedText.replace(/&equals;/g, '=');\n      sanitizedText = sanitizedText.replace(/&amp;/g, '&');\n      const links = JSON.parse(sanitizedText);\n      // add the deserialized text to the actor's links field.\n      this.insertLinks(actor, links);\n    } catch (e) {\n      log.error('error while parsing actor link text', e);\n    }\n  }\n\n  public addALink(actorId: string, text: { text: string }) {\n    // find the actor\n    const actor = this.getActor(actorId);\n    try {\n      const links: Record<string, string> = {};\n      let sanitizedText = sanitizeText(text.text, getConfig());\n      const sep = sanitizedText.indexOf('@');\n      sanitizedText = sanitizedText.replace(/&equals;/g, '=');\n      sanitizedText = sanitizedText.replace(/&amp;/g, '&');\n      const label = sanitizedText.slice(0, sep - 1).trim();\n      const link = sanitizedText.slice(sep + 1).trim();\n\n      links[label] = link;\n      // add the deserialized text to the actor's links field.\n      this.insertLinks(actor, links);\n    } catch (e) {\n      log.error('error while parsing actor link text', e);\n    }\n  }\n\n  private insertLinks(actor: Actor, links: Record<string, string>) {\n    if (actor.links == null) {\n      actor.links = links;\n    } else {\n      for (const key in links) {\n        actor.links[key] = links[key];\n      }\n    }\n  }\n\n  public addProperties(actorId: string, text: { text: string }) {\n    // find the actor\n    const actor = this.getActor(actorId);\n    // JSON.parse the text\n    try {\n      const sanitizedText = sanitizeText(text.text, getConfig());\n      const properties: Record<string, unknown> = JSON.parse(sanitizedText);\n      // add the deserialized text to the actor's property field.\n      this.insertProperties(actor, properties);\n    } catch (e) {\n      log.error('error while parsing actor properties text', e);\n    }\n  }\n\n  private insertProperties(actor: Actor, properties: Record<string, unknown>) {\n    if (actor.properties == null) {\n      actor.properties = properties;\n    } else {\n      for (const key in properties) {\n        actor.properties[key] = properties[key];\n      }\n    }\n  }\n\n  private boxEnd() {\n    this.state.records.currentBox = undefined;\n  }\n\n  public addDetails(actorId: string, text: { text: string }) {\n    // find the actor\n    const actor = this.getActor(actorId);\n    const elem = document.getElementById(text.text)!;\n\n    // JSON.parse the text\n    try {\n      const text = elem.innerHTML;\n      const details = JSON.parse(text);\n      // add the deserialized text to the actor's property field.\n      if (details.properties) {\n        this.insertProperties(actor, details.properties);\n      }\n\n      if (details.links) {\n        this.insertLinks(actor, details.links);\n      }\n    } catch (e) {\n      log.error('error while parsing actor details text', e);\n    }\n  }\n\n  public getActorProperty(actor: Actor, key: string) {\n    if (actor?.properties !== undefined) {\n      return actor.properties[key];\n    }\n\n    return undefined;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-redundant-type-constituents\n  public apply(param: any | AddMessageParams | AddMessageParams[]) {\n    if (Array.isArray(param)) {\n      param.forEach((item) => {\n        this.apply(item);\n      });\n    } else {\n      switch (param.type) {\n        case 'sequenceIndex':\n          this.state.records.messages.push({\n            id: this.state.records.messages.length.toString(),\n            from: undefined,\n            to: undefined,\n            message: {\n              start: param.sequenceIndex,\n              step: param.sequenceIndexStep,\n              visible: param.sequenceVisible,\n            },\n            wrap: false,\n            type: param.signalType,\n          });\n          break;\n        case 'addParticipant':\n          this.addActor(param.actor, param.actor, param.description, param.draw);\n          break;\n        case 'createParticipant':\n          if (this.state.records.actors.has(param.actor)) {\n            throw new Error(\n              \"It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior\"\n            );\n          }\n          this.state.records.lastCreated = param.actor;\n          this.addActor(param.actor, param.actor, param.description, param.draw);\n          this.state.records.createdActors.set(param.actor, this.state.records.messages.length);\n          break;\n        case 'destroyParticipant':\n          this.state.records.lastDestroyed = param.actor;\n          this.state.records.destroyedActors.set(param.actor, this.state.records.messages.length);\n          break;\n        case 'activeStart':\n          this.addSignal(param.actor, undefined, undefined, param.signalType);\n          break;\n        case 'activeEnd':\n          this.addSignal(param.actor, undefined, undefined, param.signalType);\n          break;\n        case 'addNote':\n          this.addNote(param.actor, param.placement, param.text);\n          break;\n        case 'addLinks':\n          this.addLinks(param.actor, param.text);\n          break;\n        case 'addALink':\n          this.addALink(param.actor, param.text);\n          break;\n        case 'addProperties':\n          this.addProperties(param.actor, param.text);\n          break;\n        case 'addDetails':\n          this.addDetails(param.actor, param.text);\n          break;\n        case 'addMessage':\n          if (this.state.records.lastCreated) {\n            if (param.to !== this.state.records.lastCreated) {\n              throw new Error(\n                'The created participant ' +\n                  this.state.records.lastCreated.name +\n                  ' does not have an associated creating message after its declaration. Please check the sequence diagram.'\n              );\n            } else {\n              this.state.records.lastCreated = undefined;\n            }\n          } else if (this.state.records.lastDestroyed) {\n            if (\n              param.to !== this.state.records.lastDestroyed &&\n              param.from !== this.state.records.lastDestroyed\n            ) {\n              throw new Error(\n                'The destroyed participant ' +\n                  this.state.records.lastDestroyed.name +\n                  ' does not have an associated destroying message after its declaration. Please check the sequence diagram.'\n              );\n            } else {\n              this.state.records.lastDestroyed = undefined;\n            }\n          }\n          this.addSignal(param.from, param.to, param.msg, param.signalType, param.activate);\n          break;\n        case 'boxStart':\n          this.addBox(param.boxData);\n          break;\n        case 'boxEnd':\n          this.boxEnd();\n          break;\n        case 'loopStart':\n          this.addSignal(undefined, undefined, param.loopText, param.signalType);\n          break;\n        case 'loopEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n        case 'rectStart':\n          this.addSignal(undefined, undefined, param.color, param.signalType);\n          break;\n        case 'rectEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n        case 'optStart':\n          this.addSignal(undefined, undefined, param.optText, param.signalType);\n          break;\n        case 'optEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n        case 'altStart':\n          this.addSignal(undefined, undefined, param.altText, param.signalType);\n          break;\n        case 'else':\n          this.addSignal(undefined, undefined, param.altText, param.signalType);\n          break;\n        case 'altEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n        case 'setAccTitle':\n          setAccTitle(param.text);\n          break;\n        case 'parStart':\n          this.addSignal(undefined, undefined, param.parText, param.signalType);\n          break;\n        case 'and':\n          this.addSignal(undefined, undefined, param.parText, param.signalType);\n          break;\n        case 'parEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n        case 'criticalStart':\n          this.addSignal(undefined, undefined, param.criticalText, param.signalType);\n          break;\n        case 'option':\n          this.addSignal(undefined, undefined, param.optionText, param.signalType);\n          break;\n        case 'criticalEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n        case 'breakStart':\n          this.addSignal(undefined, undefined, param.breakText, param.signalType);\n          break;\n        case 'breakEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n      }\n    }\n  }\n\n  public setAccTitle = setAccTitle;\n  public setAccDescription = setAccDescription;\n  public setDiagramTitle = setDiagramTitle;\n  public getAccTitle = getAccTitle;\n  public getAccDescription = getAccDescription;\n  public getDiagramTitle = getDiagramTitle;\n  public getConfig() {\n    return getConfig().sequence;\n  }\n}\n", "const getStyles = (options) =>\n  `.actor {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n  }\n\n  text.actor > tspan {\n    fill: ${options.actorTextColor};\n    stroke: none;\n  }\n\n  .actor-line {\n    stroke: ${options.actorLineColor};\n  }\n\n  .messageLine0 {\n    stroke-width: 1.5;\n    stroke-dasharray: none;\n    stroke: ${options.signalColor};\n  }\n\n  .messageLine1 {\n    stroke-width: 1.5;\n    stroke-dasharray: 2, 2;\n    stroke: ${options.signalColor};\n  }\n\n  #arrowhead path {\n    fill: ${options.signalColor};\n    stroke: ${options.signalColor};\n  }\n\n  .sequenceNumber {\n    fill: ${options.sequenceNumberColor};\n  }\n\n  #sequencenumber {\n    fill: ${options.signalColor};\n  }\n\n  #crosshead path {\n    fill: ${options.signalColor};\n    stroke: ${options.signalColor};\n  }\n\n  .messageText {\n    fill: ${options.signalTextColor};\n    stroke: none;\n  }\n\n  .labelBox {\n    stroke: ${options.labelBoxBorderColor};\n    fill: ${options.labelBoxBkgColor};\n  }\n\n  .labelText, .labelText > tspan {\n    fill: ${options.labelTextColor};\n    stroke: none;\n  }\n\n  .loopText, .loopText > tspan {\n    fill: ${options.loopTextColor};\n    stroke: none;\n  }\n\n  .loopLine {\n    stroke-width: 2px;\n    stroke-dasharray: 2, 2;\n    stroke: ${options.labelBoxBorderColor};\n    fill: ${options.labelBoxBorderColor};\n  }\n\n  .note {\n    //stroke: #decc93;\n    stroke: ${options.noteBorderColor};\n    fill: ${options.noteBkgColor};\n  }\n\n  .noteText, .noteText > tspan {\n    fill: ${options.noteTextColor};\n    stroke: none;\n  }\n\n  .activation0 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .activation1 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .activation2 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .actorPopupMenu {\n    position: absolute;\n  }\n\n  .actorPopupMenuPanel {\n    position: absolute;\n    fill: ${options.actorBkg};\n    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\n    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));\n}\n  .actor-man line {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n  }\n  .actor-man circle, line {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n    stroke-width: 2px;\n  }\n`;\n\nexport default getStyles;\n", "import { sanitizeUrl } from '@braintree/sanitize-url';\nimport * as configApi from '../../config.js';\nimport { ZERO_WIDTH_SPACE, parseFontSize } from '../../utils.js';\nimport common, {\n  calculateMathMLDimensions,\n  hasKatex,\n  renderKatexSanitized,\n} from '../common/common.js';\nimport * as svgDrawCommon from '../common/svgDrawCommon.js';\n\nexport const ACTOR_TYPE_WIDTH = 18 * 2;\nconst TOP_ACTOR_CLASS = 'actor-top';\nconst BOTTOM_ACTOR_CLASS = 'actor-bottom';\nconst ACTOR_BOX_CLASS = 'actor-box';\nconst ACTOR_MAN_FIGURE_CLASS = 'actor-man';\n\nexport const drawRect = function (elem, rectData) {\n  return svgDrawCommon.drawRect(elem, rectData);\n};\n\nexport const drawPopup = function (elem, actor, minMenuWidth, textAttrs, forceMenus) {\n  if (actor.links === undefined || actor.links === null || Object.keys(actor.links).length === 0) {\n    return { height: 0, width: 0 };\n  }\n\n  const links = actor.links;\n  const actorCnt = actor.actorCnt;\n  const rectData = actor.rectData;\n\n  var displayValue = 'none';\n  if (forceMenus) {\n    displayValue = 'block !important';\n  }\n\n  const g = elem.append('g');\n  g.attr('id', 'actor' + actorCnt + '_popup');\n  g.attr('class', 'actorPopupMenu');\n  g.attr('display', displayValue);\n  var actorClass = '';\n  if (rectData.class !== undefined) {\n    actorClass = ' ' + rectData.class;\n  }\n\n  let menuWidth = rectData.width > minMenuWidth ? rectData.width : minMenuWidth;\n\n  const rectElem = g.append('rect');\n  rectElem.attr('class', 'actorPopupMenuPanel' + actorClass);\n  rectElem.attr('x', rectData.x);\n  rectElem.attr('y', rectData.height);\n  rectElem.attr('fill', rectData.fill);\n  rectElem.attr('stroke', rectData.stroke);\n  rectElem.attr('width', menuWidth);\n  rectElem.attr('height', rectData.height);\n  rectElem.attr('rx', rectData.rx);\n  rectElem.attr('ry', rectData.ry);\n  if (links != null) {\n    var linkY = 20;\n    for (let key in links) {\n      var linkElem = g.append('a');\n      var sanitizedLink = sanitizeUrl(links[key]);\n      linkElem.attr('xlink:href', sanitizedLink);\n      linkElem.attr('target', '_blank');\n\n      _drawMenuItemTextCandidateFunc(textAttrs)(\n        key,\n        linkElem,\n        rectData.x + 10,\n        rectData.height + linkY,\n        menuWidth,\n        20,\n        { class: 'actor' },\n        textAttrs\n      );\n\n      linkY += 30;\n    }\n  }\n\n  rectElem.attr('height', linkY);\n\n  return { height: rectData.height + linkY, width: menuWidth };\n};\n\nconst popupMenuToggle = function (popId) {\n  return (\n    \"var pu = document.getElementById('\" +\n    popId +\n    \"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }\"\n  );\n};\n\nexport const drawKatex = async function (elem, textData, msgModel = null) {\n  let textElem = elem.append('foreignObject');\n  const linesSanitized = await renderKatexSanitized(textData.text, configApi.getConfig());\n\n  const divElem = textElem\n    .append('xhtml:div')\n    .attr('style', 'width: fit-content;')\n    .attr('xmlns', 'http://www.w3.org/1999/xhtml')\n    .html(linesSanitized);\n  const dim = divElem.node().getBoundingClientRect();\n\n  textElem.attr('height', Math.round(dim.height)).attr('width', Math.round(dim.width));\n\n  if (textData.class === 'noteText') {\n    const rectElem = elem.node().firstChild;\n\n    rectElem.setAttribute('height', dim.height + 2 * textData.textMargin);\n    const rectDim = rectElem.getBBox();\n\n    textElem\n      .attr('x', Math.round(rectDim.x + rectDim.width / 2 - dim.width / 2))\n      .attr('y', Math.round(rectDim.y + rectDim.height / 2 - dim.height / 2));\n  } else if (msgModel) {\n    let { startx, stopx, starty } = msgModel;\n    if (startx > stopx) {\n      const temp = startx;\n      startx = stopx;\n      stopx = temp;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n    textElem.attr('x', Math.round(startx + Math.abs(startx - stopx) / 2 - dim.width / 2));\n    if (textData.class === 'loopText') {\n      textElem.attr('y', Math.round(starty));\n    } else {\n      textElem.attr('y', Math.round(starty - dim.height));\n    }\n  }\n\n  return [textElem];\n};\n\nexport const drawText = function (elem, textData) {\n  let prevTextHeight = 0;\n  let textHeight = 0;\n  const lines = textData.text.split(common.lineBreakRegex);\n\n  const [_textFontSize, _textFontSizePx] = parseFontSize(textData.fontSize);\n\n  let textElems = [];\n  let dy = 0;\n  let yfunc = () => textData.y;\n  if (\n    textData.valign !== undefined &&\n    textData.textMargin !== undefined &&\n    textData.textMargin > 0\n  ) {\n    switch (textData.valign) {\n      case 'top':\n      case 'start':\n        yfunc = () => Math.round(textData.y + textData.textMargin);\n        break;\n      case 'middle':\n      case 'center':\n        yfunc = () =>\n          Math.round(textData.y + (prevTextHeight + textHeight + textData.textMargin) / 2);\n        break;\n      case 'bottom':\n      case 'end':\n        yfunc = () =>\n          Math.round(\n            textData.y +\n              (prevTextHeight + textHeight + 2 * textData.textMargin) -\n              textData.textMargin\n          );\n        break;\n    }\n  }\n\n  if (\n    textData.anchor !== undefined &&\n    textData.textMargin !== undefined &&\n    textData.width !== undefined\n  ) {\n    switch (textData.anchor) {\n      case 'left':\n      case 'start':\n        textData.x = Math.round(textData.x + textData.textMargin);\n        textData.anchor = 'start';\n        textData.dominantBaseline = 'middle';\n        textData.alignmentBaseline = 'middle';\n        break;\n      case 'middle':\n      case 'center':\n        textData.x = Math.round(textData.x + textData.width / 2);\n        textData.anchor = 'middle';\n        textData.dominantBaseline = 'middle';\n        textData.alignmentBaseline = 'middle';\n        break;\n      case 'right':\n      case 'end':\n        textData.x = Math.round(textData.x + textData.width - textData.textMargin);\n        textData.anchor = 'end';\n        textData.dominantBaseline = 'middle';\n        textData.alignmentBaseline = 'middle';\n        break;\n    }\n  }\n\n  for (let [i, line] of lines.entries()) {\n    if (\n      textData.textMargin !== undefined &&\n      textData.textMargin === 0 &&\n      _textFontSize !== undefined\n    ) {\n      dy = i * _textFontSize;\n    }\n\n    const textElem = elem.append('text');\n    textElem.attr('x', textData.x);\n    textElem.attr('y', yfunc());\n    if (textData.anchor !== undefined) {\n      textElem\n        .attr('text-anchor', textData.anchor)\n        .attr('dominant-baseline', textData.dominantBaseline)\n        .attr('alignment-baseline', textData.alignmentBaseline);\n    }\n    if (textData.fontFamily !== undefined) {\n      textElem.style('font-family', textData.fontFamily);\n    }\n    if (_textFontSizePx !== undefined) {\n      textElem.style('font-size', _textFontSizePx);\n    }\n    if (textData.fontWeight !== undefined) {\n      textElem.style('font-weight', textData.fontWeight);\n    }\n    if (textData.fill !== undefined) {\n      textElem.attr('fill', textData.fill);\n    }\n    if (textData.class !== undefined) {\n      textElem.attr('class', textData.class);\n    }\n    if (textData.dy !== undefined) {\n      textElem.attr('dy', textData.dy);\n    } else if (dy !== 0) {\n      textElem.attr('dy', dy);\n    }\n\n    const text = line || ZERO_WIDTH_SPACE;\n    if (textData.tspan) {\n      const span = textElem.append('tspan');\n      span.attr('x', textData.x);\n      if (textData.fill !== undefined) {\n        span.attr('fill', textData.fill);\n      }\n      span.text(text);\n    } else {\n      textElem.text(text);\n    }\n    if (\n      textData.valign !== undefined &&\n      textData.textMargin !== undefined &&\n      textData.textMargin > 0\n    ) {\n      textHeight += (textElem._groups || textElem)[0][0].getBBox().height;\n      prevTextHeight = textHeight;\n    }\n\n    textElems.push(textElem);\n  }\n\n  return textElems;\n};\n\nexport const drawLabel = function (elem, txtObject) {\n  /**\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} cut\n   * @returns {any}\n   */\n  function genPoints(x, y, width, height, cut) {\n    return (\n      x +\n      ',' +\n      y +\n      ' ' +\n      (x + width) +\n      ',' +\n      y +\n      ' ' +\n      (x + width) +\n      ',' +\n      (y + height - cut) +\n      ' ' +\n      (x + width - cut * 1.2) +\n      ',' +\n      (y + height) +\n      ' ' +\n      x +\n      ',' +\n      (y + height)\n    );\n  }\n  const polygon = elem.append('polygon');\n  polygon.attr('points', genPoints(txtObject.x, txtObject.y, txtObject.width, txtObject.height, 7));\n  polygon.attr('class', 'labelBox');\n\n  txtObject.y = txtObject.y + txtObject.height / 2;\n\n  drawText(elem, txtObject);\n  return polygon;\n};\n\nlet actorCnt = -1;\n\nexport const fixLifeLineHeights = (diagram, actors, actorKeys, conf) => {\n  if (!diagram.select) {\n    return;\n  }\n  actorKeys.forEach((actorKey) => {\n    const actor = actors.get(actorKey);\n    const actorDOM = diagram.select('#actor' + actor.actorCnt);\n    if (!conf.mirrorActors && actor.stopy) {\n      actorDOM.attr('y2', actor.stopy + actor.height / 2);\n    } else if (conf.mirrorActors) {\n      actorDOM.attr('y2', actor.stopy);\n    }\n  });\n};\n\n/**\n * Draws an actor in the diagram with the attached line\n *\n * @param {any} elem - The diagram we'll draw to.\n * @param {any} actor - The actor to draw.\n * @param {any} conf - DrawText implementation discriminator object\n * @param {boolean} isFooter - If the actor is the footer one\n */\nconst drawActorTypeParticipant = function (elem, actor, conf, isFooter) {\n  const actorY = isFooter ? actor.stopy : actor.starty;\n  const center = actor.x + actor.width / 2;\n  const centerY = actorY + actor.height;\n\n  const boxplusLineGroup = elem.append('g').lower();\n  var g = boxplusLineGroup;\n\n  if (!isFooter) {\n    actorCnt++;\n    if (Object.keys(actor.links || {}).length && !conf.forceMenus) {\n      g.attr('onclick', popupMenuToggle(`actor${actorCnt}_popup`)).attr('cursor', 'pointer');\n    }\n    g.append('line')\n      .attr('id', 'actor' + actorCnt)\n      .attr('x1', center)\n      .attr('y1', centerY)\n      .attr('x2', center)\n      .attr('y2', 2000)\n      .attr('class', 'actor-line 200')\n      .attr('stroke-width', '0.5px')\n      .attr('stroke', '#999')\n      .attr('name', actor.name);\n\n    g = boxplusLineGroup.append('g');\n    actor.actorCnt = actorCnt;\n\n    if (actor.links != null) {\n      g.attr('id', 'root-' + actorCnt);\n    }\n  }\n\n  const rect = svgDrawCommon.getNoteRect();\n  var cssclass = 'actor';\n  if (actor.properties?.class) {\n    cssclass = actor.properties.class;\n  } else {\n    rect.fill = '#eaeaea';\n  }\n  if (isFooter) {\n    cssclass += ` ${BOTTOM_ACTOR_CLASS}`;\n  } else {\n    cssclass += ` ${TOP_ACTOR_CLASS}`;\n  }\n  rect.x = actor.x;\n  rect.y = actorY;\n  rect.width = actor.width;\n  rect.height = actor.height;\n  rect.class = cssclass;\n  rect.rx = 3;\n  rect.ry = 3;\n  rect.name = actor.name;\n  const rectElem = drawRect(g, rect);\n  actor.rectData = rect;\n\n  if (actor.properties?.icon) {\n    const iconSrc = actor.properties.icon.trim();\n    if (iconSrc.charAt(0) === '@') {\n      svgDrawCommon.drawEmbeddedImage(g, rect.x + rect.width - 20, rect.y + 10, iconSrc.substr(1));\n    } else {\n      svgDrawCommon.drawImage(g, rect.x + rect.width - 20, rect.y + 10, iconSrc);\n    }\n  }\n\n  _drawTextCandidateFunc(conf, hasKatex(actor.description))(\n    actor.description,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: `actor ${ACTOR_BOX_CLASS}` },\n    conf\n  );\n\n  let height = actor.height;\n  if (rectElem.node) {\n    const bounds = rectElem.node().getBBox();\n    actor.height = bounds.height;\n    height = bounds.height;\n  }\n\n  return height;\n};\n\nconst drawActorTypeActor = function (elem, actor, conf, isFooter) {\n  const actorY = isFooter ? actor.stopy : actor.starty;\n  const center = actor.x + actor.width / 2;\n  const centerY = actorY + 80;\n\n  const line = elem.append('g').lower();\n\n  if (!isFooter) {\n    actorCnt++;\n    line\n      .append('line')\n      .attr('id', 'actor' + actorCnt)\n      .attr('x1', center)\n      .attr('y1', centerY)\n      .attr('x2', center)\n      .attr('y2', 2000)\n      .attr('class', 'actor-line 200')\n      .attr('stroke-width', '0.5px')\n      .attr('stroke', '#999')\n      .attr('name', actor.name);\n\n    actor.actorCnt = actorCnt;\n  }\n  const actElem = elem.append('g');\n  let cssClass = ACTOR_MAN_FIGURE_CLASS;\n  if (isFooter) {\n    cssClass += ` ${BOTTOM_ACTOR_CLASS}`;\n  } else {\n    cssClass += ` ${TOP_ACTOR_CLASS}`;\n  }\n  actElem.attr('class', cssClass);\n  actElem.attr('name', actor.name);\n\n  const rect = svgDrawCommon.getNoteRect();\n  rect.x = actor.x;\n  rect.y = actorY;\n  rect.fill = '#eaeaea';\n  rect.width = actor.width;\n  rect.height = actor.height;\n  rect.class = 'actor';\n  rect.rx = 3;\n  rect.ry = 3;\n\n  actElem\n    .append('line')\n    .attr('id', 'actor-man-torso' + actorCnt)\n    .attr('x1', center)\n    .attr('y1', actorY + 25)\n    .attr('x2', center)\n    .attr('y2', actorY + 45);\n\n  actElem\n    .append('line')\n    .attr('id', 'actor-man-arms' + actorCnt)\n    .attr('x1', center - ACTOR_TYPE_WIDTH / 2)\n    .attr('y1', actorY + 33)\n    .attr('x2', center + ACTOR_TYPE_WIDTH / 2)\n    .attr('y2', actorY + 33);\n  actElem\n    .append('line')\n    .attr('x1', center - ACTOR_TYPE_WIDTH / 2)\n    .attr('y1', actorY + 60)\n    .attr('x2', center)\n    .attr('y2', actorY + 45);\n  actElem\n    .append('line')\n    .attr('x1', center)\n    .attr('y1', actorY + 45)\n    .attr('x2', center + ACTOR_TYPE_WIDTH / 2 - 2)\n    .attr('y2', actorY + 60);\n\n  const circle = actElem.append('circle');\n  circle.attr('cx', actor.x + actor.width / 2);\n  circle.attr('cy', actorY + 10);\n  circle.attr('r', 15);\n  circle.attr('width', actor.width);\n  circle.attr('height', actor.height);\n\n  const bounds = actElem.node().getBBox();\n  actor.height = bounds.height;\n\n  _drawTextCandidateFunc(conf, hasKatex(actor.description))(\n    actor.description,\n    actElem,\n    rect.x,\n    rect.y + 35,\n    rect.width,\n    rect.height,\n    { class: `actor ${ACTOR_MAN_FIGURE_CLASS}` },\n    conf\n  );\n\n  return actor.height;\n};\n\nexport const drawActor = async function (elem, actor, conf, isFooter) {\n  switch (actor.type) {\n    case 'actor':\n      return await drawActorTypeActor(elem, actor, conf, isFooter);\n    case 'participant':\n      return await drawActorTypeParticipant(elem, actor, conf, isFooter);\n  }\n};\n\nexport const drawBox = function (elem, box, conf) {\n  const boxplusTextGroup = elem.append('g');\n  const g = boxplusTextGroup;\n  drawBackgroundRect(g, box);\n  if (box.name) {\n    _drawTextCandidateFunc(conf)(\n      box.name,\n      g,\n      box.x,\n      box.y + conf.boxTextMargin + (box.textMaxHeight || 0) / 2,\n      box.width,\n      0,\n      { class: 'text' },\n      conf\n    );\n  }\n  g.lower();\n};\n\nexport const anchorElement = function (elem) {\n  return elem.append('g');\n};\n\n/**\n * Draws an activation in the diagram\n *\n * @param {any} elem - Element to append activation rect.\n * @param {any} bounds - Activation box bounds.\n * @param {any} verticalPos - Precise y coordinate of bottom activation box edge.\n * @param {any} conf - Sequence diagram config object.\n * @param {any} actorActivations - Number of activations on the actor.\n */\nexport const drawActivation = function (elem, bounds, verticalPos, conf, actorActivations) {\n  const rect = svgDrawCommon.getNoteRect();\n  const g = bounds.anchored;\n  rect.x = bounds.startx;\n  rect.y = bounds.starty;\n  rect.class = 'activation' + (actorActivations % 3); // Will evaluate to 0, 1 or 2\n  rect.width = bounds.stopx - bounds.startx;\n  rect.height = verticalPos - bounds.starty;\n  drawRect(g, rect);\n};\n\n/**\n * Draws a loop in the diagram\n *\n * @param {any} elem - Element to append the loop to.\n * @param {any} loopModel - LoopModel of the given loop.\n * @param {any} labelText - Text within the loop.\n * @param {any} conf - Diagram configuration\n * @returns {any}\n */\nexport const drawLoop = async function (elem, loopModel, labelText, conf) {\n  const {\n    boxMargin,\n    boxTextMargin,\n    labelBoxHeight,\n    labelBoxWidth,\n    messageFontFamily: fontFamily,\n    messageFontSize: fontSize,\n    messageFontWeight: fontWeight,\n  } = conf;\n  const g = elem.append('g');\n  const drawLoopLine = function (startx, starty, stopx, stopy) {\n    return g\n      .append('line')\n      .attr('x1', startx)\n      .attr('y1', starty)\n      .attr('x2', stopx)\n      .attr('y2', stopy)\n      .attr('class', 'loopLine');\n  };\n  drawLoopLine(loopModel.startx, loopModel.starty, loopModel.stopx, loopModel.starty);\n  drawLoopLine(loopModel.stopx, loopModel.starty, loopModel.stopx, loopModel.stopy);\n  drawLoopLine(loopModel.startx, loopModel.stopy, loopModel.stopx, loopModel.stopy);\n  drawLoopLine(loopModel.startx, loopModel.starty, loopModel.startx, loopModel.stopy);\n  if (loopModel.sections !== undefined) {\n    loopModel.sections.forEach(function (item) {\n      drawLoopLine(loopModel.startx, item.y, loopModel.stopx, item.y).style(\n        'stroke-dasharray',\n        '3, 3'\n      );\n    });\n  }\n\n  let txt = svgDrawCommon.getTextObj();\n  txt.text = labelText;\n  txt.x = loopModel.startx;\n  txt.y = loopModel.starty;\n  txt.fontFamily = fontFamily;\n  txt.fontSize = fontSize;\n  txt.fontWeight = fontWeight;\n  txt.anchor = 'middle';\n  txt.valign = 'middle';\n  txt.tspan = false;\n  txt.width = labelBoxWidth || 50;\n  txt.height = labelBoxHeight || 20;\n  txt.textMargin = boxTextMargin;\n  txt.class = 'labelText';\n\n  drawLabel(g, txt);\n  txt = getTextObj();\n  txt.text = loopModel.title;\n  txt.x = loopModel.startx + labelBoxWidth / 2 + (loopModel.stopx - loopModel.startx) / 2;\n  txt.y = loopModel.starty + boxMargin + boxTextMargin;\n  txt.anchor = 'middle';\n  txt.valign = 'middle';\n  txt.textMargin = boxTextMargin;\n  txt.class = 'loopText';\n  txt.fontFamily = fontFamily;\n  txt.fontSize = fontSize;\n  txt.fontWeight = fontWeight;\n  txt.wrap = true;\n\n  let textElem = hasKatex(txt.text) ? await drawKatex(g, txt, loopModel) : drawText(g, txt);\n\n  if (loopModel.sectionTitles !== undefined) {\n    for (const [idx, item] of Object.entries(loopModel.sectionTitles)) {\n      if (item.message) {\n        txt.text = item.message;\n        txt.x = loopModel.startx + (loopModel.stopx - loopModel.startx) / 2;\n        txt.y = loopModel.sections[idx].y + boxMargin + boxTextMargin;\n        txt.class = 'loopText';\n        txt.anchor = 'middle';\n        txt.valign = 'middle';\n        txt.tspan = false;\n        txt.fontFamily = fontFamily;\n        txt.fontSize = fontSize;\n        txt.fontWeight = fontWeight;\n        txt.wrap = loopModel.wrap;\n\n        if (hasKatex(txt.text)) {\n          loopModel.starty = loopModel.sections[idx].y;\n          await drawKatex(g, txt, loopModel);\n        } else {\n          drawText(g, txt);\n        }\n        let sectionHeight = Math.round(\n          textElem\n            .map((te) => (te._groups || te)[0][0].getBBox().height)\n            .reduce((acc, curr) => acc + curr)\n        );\n        loopModel.sections[idx].height += sectionHeight - (boxMargin + boxTextMargin);\n      }\n    }\n  }\n\n  loopModel.height = Math.round(loopModel.stopy - loopModel.starty);\n  return g;\n};\n\n/**\n * Draws a background rectangle\n *\n * @param {any} elem Diagram (reference for bounds)\n * @param {any} bounds Shape of the rectangle\n */\nexport const drawBackgroundRect = function (elem, bounds) {\n  svgDrawCommon.drawBackgroundRect(elem, bounds);\n};\n\nexport const insertDatabaseIcon = function (elem) {\n  elem\n    .append('defs')\n    .append('symbol')\n    .attr('id', 'database')\n    .attr('fill-rule', 'evenodd')\n    .attr('clip-rule', 'evenodd')\n    .append('path')\n    .attr('transform', 'scale(.5)')\n    .attr(\n      'd',\n      'M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z'\n    );\n};\n\nexport const insertComputerIcon = function (elem) {\n  elem\n    .append('defs')\n    .append('symbol')\n    .attr('id', 'computer')\n    .attr('width', '24')\n    .attr('height', '24')\n    .append('path')\n    .attr('transform', 'scale(.5)')\n    .attr(\n      'd',\n      'M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z'\n    );\n};\n\nexport const insertClockIcon = function (elem) {\n  elem\n    .append('defs')\n    .append('symbol')\n    .attr('id', 'clock')\n    .attr('width', '24')\n    .attr('height', '24')\n    .append('path')\n    .attr('transform', 'scale(.5)')\n    .attr(\n      'd',\n      'M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z'\n    );\n};\n\n/**\n * Setup arrow head and define the marker. The result is appended to the svg.\n *\n * @param elem\n */\nexport const insertArrowHead = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'arrowhead')\n    .attr('refX', 7.9)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 12)\n    .attr('markerHeight', 12)\n    .attr('orient', 'auto-start-reverse')\n    .append('path')\n    .attr('d', 'M -1 0 L 10 5 L 0 10 z'); // this is actual shape for arrowhead\n};\n\n/**\n * Setup arrow head and define the marker. The result is appended to the svg.\n *\n * @param {any} elem\n */\nexport const insertArrowFilledHead = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'filled-head')\n    .attr('refX', 15.5)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L14,7 L9,1 Z');\n};\n\n/**\n * Setup node number. The result is appended to the svg.\n *\n * @param {any} elem\n */\nexport const insertSequenceNumber = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'sequencenumber')\n    .attr('refX', 15)\n    .attr('refY', 15)\n    .attr('markerWidth', 60)\n    .attr('markerHeight', 40)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('cx', 15)\n    .attr('cy', 15)\n    .attr('r', 6);\n  // .style(\"fill\", '#f00');\n};\n\n/**\n * Setup cross head and define the marker. The result is appended to the svg.\n *\n * @param {any} elem\n */\nexport const insertArrowCrossHead = function (elem) {\n  const defs = elem.append('defs');\n  const marker = defs\n    .append('marker')\n    .attr('id', 'crosshead')\n    .attr('markerWidth', 15)\n    .attr('markerHeight', 8)\n    .attr('orient', 'auto')\n    .attr('refX', 4)\n    .attr('refY', 4.5);\n  // The cross\n  marker\n    .append('path')\n    .attr('fill', 'none')\n    .attr('stroke', '#000000')\n    .style('stroke-dasharray', '0, 0')\n    .attr('stroke-width', '1pt')\n    .attr('d', 'M 1,2 L 6,7 M 6,2 L 1,7');\n  // this is actual shape for arrowhead\n};\n\nexport const getTextObj = function () {\n  return {\n    x: 0,\n    y: 0,\n    fill: undefined,\n    anchor: undefined,\n    style: '#666',\n    width: undefined,\n    height: undefined,\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true,\n    valign: undefined,\n  };\n};\n\nexport const getNoteRect = function () {\n  return {\n    x: 0,\n    y: 0,\n    fill: '#EDF2AE',\n    stroke: '#666',\n    width: 100,\n    anchor: 'start',\n    height: 100,\n    rx: 0,\n    ry: 0,\n  };\n};\n\nconst _drawTextCandidateFunc = (function () {\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   */\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g\n      .append('text')\n      .attr('x', x + width / 2)\n      .attr('y', y + height / 2 + 5)\n      .style('text-anchor', 'middle')\n      .text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byTspan(content, g, x, y, width, height, textAttrs, conf) {\n    const { actorFontSize, actorFontFamily, actorFontWeight } = conf;\n\n    const [_actorFontSize, _actorFontSizePx] = parseFontSize(actorFontSize);\n\n    const lines = content.split(common.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * _actorFontSize - (_actorFontSize * (lines.length - 1)) / 2;\n      const text = g\n        .append('text')\n        .attr('x', x + width / 2)\n        .attr('y', y)\n        .style('text-anchor', 'middle')\n        .style('font-size', _actorFontSizePx)\n        .style('font-weight', actorFontWeight)\n        .style('font-family', actorFontFamily);\n      text\n        .append('tspan')\n        .attr('x', x + width / 2)\n        .attr('dy', dy)\n        .text(lines[i]);\n\n      text\n        .attr('y', y + height / 2.0)\n        .attr('dominant-baseline', 'central')\n        .attr('alignment-baseline', 'central');\n\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const s = g.append('switch');\n    const f = s\n      .append('foreignObject')\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height);\n\n    const text = f\n      .append('xhtml:div')\n      .style('display', 'table')\n      .style('height', '100%')\n      .style('width', '100%');\n\n    text\n      .append('div')\n      .style('display', 'table-cell')\n      .style('text-align', 'center')\n      .style('vertical-align', 'middle')\n      .text(content);\n\n    byTspan(content, s, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   *\n   * @param content\n   * @param g\n   * @param x\n   * @param y\n   * @param width\n   * @param height\n   * @param textAttrs\n   * @param conf\n   */\n  async function byKatex(content, g, x, y, width, height, textAttrs, conf) {\n    // TODO duplicate render calls, optimize\n\n    const dim = await calculateMathMLDimensions(content, configApi.getConfig());\n    const s = g.append('switch');\n    const f = s\n      .append('foreignObject')\n      .attr('x', x + width / 2 - dim.width / 2)\n      .attr('y', y + height / 2 - dim.height / 2)\n      .attr('width', dim.width)\n      .attr('height', dim.height);\n\n    const text = f.append('xhtml:div').style('height', '100%').style('width', '100%');\n\n    text\n      .append('div')\n      .style('text-align', 'center')\n      .style('vertical-align', 'middle')\n      .html(await renderKatexSanitized(content, configApi.getConfig()));\n\n    byTspan(content, s, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} toText\n   * @param {any} fromTextAttrsDict\n   */\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n\n  return function (conf, hasKatex = false) {\n    if (hasKatex) {\n      return byKatex;\n    }\n    return conf.textPlacement === 'fo' ? byFo : conf.textPlacement === 'old' ? byText : byTspan;\n  };\n})();\n\nconst _drawMenuItemTextCandidateFunc = (function () {\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   */\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g\n      .append('text')\n      .attr('x', x)\n      .attr('y', y)\n      .style('text-anchor', 'start')\n      .text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byTspan(content, g, x, y, width, height, textAttrs, conf) {\n    const { actorFontSize, actorFontFamily, actorFontWeight } = conf;\n\n    const lines = content.split(common.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * actorFontSize - (actorFontSize * (lines.length - 1)) / 2;\n      const text = g\n        .append('text')\n        .attr('x', x)\n        .attr('y', y)\n        .style('text-anchor', 'start')\n        .style('font-size', actorFontSize)\n        .style('font-weight', actorFontWeight)\n        .style('font-family', actorFontFamily);\n      text.append('tspan').attr('x', x).attr('dy', dy).text(lines[i]);\n\n      text\n        .attr('y', y + height / 2.0)\n        .attr('dominant-baseline', 'central')\n        .attr('alignment-baseline', 'central');\n\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const s = g.append('switch');\n    const f = s\n      .append('foreignObject')\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height);\n\n    const text = f\n      .append('xhtml:div')\n      .style('display', 'table')\n      .style('height', '100%')\n      .style('width', '100%');\n\n    text\n      .append('div')\n      .style('display', 'table-cell')\n      .style('text-align', 'center')\n      .style('vertical-align', 'middle')\n      .text(content);\n\n    byTspan(content, s, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} toText\n   * @param {any} fromTextAttrsDict\n   */\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n\n  return function (conf) {\n    return conf.textPlacement === 'fo' ? byFo : conf.textPlacement === 'old' ? byText : byTspan;\n  };\n})();\n\nexport default {\n  drawRect,\n  drawText,\n  drawLabel,\n  drawActor,\n  drawBox,\n  drawPopup,\n  anchorElement,\n  drawActivation,\n  drawLoop,\n  drawBackgroundRect,\n  insertArrowHead,\n  insertArrowFilledHead,\n  insertSequenceNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon,\n  getTextObj,\n  getNoteRect,\n  fixLifeLineHeights,\n  sanitizeUrl,\n};\n", "// @ts-nocheck TODO: fix file\nimport { select } from 'd3';\nimport svgDraw, { drawKatex, ACTOR_TYPE_WIDTH, drawText, fixLifeLineHeights } from './svgDraw.js';\nimport { log } from '../../logger.js';\nimport common, { calculateMathMLDimensions, hasKatex } from '../common/common.js';\nimport { getUrl } from '../common/common.js';\nimport * as svgDrawCommon from '../common/svgDrawCommon.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport assignWithDepth from '../../assignWithDepth.js';\nimport utils from '../../utils.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport type { Diagram } from '../../Diagram.js';\n\nlet conf = {};\n\nexport const bounds = {\n  data: {\n    startx: undefined,\n    stopx: undefined,\n    starty: undefined,\n    stopy: undefined,\n  },\n  verticalPos: 0,\n  sequenceItems: [],\n  activations: [],\n  models: {\n    getHeight: function () {\n      return (\n        Math.max.apply(\n          null,\n          this.actors.length === 0 ? [0] : this.actors.map((actor) => actor.height || 0)\n        ) +\n        (this.loops.length === 0\n          ? 0\n          : this.loops.map((it) => it.height || 0).reduce((acc, h) => acc + h)) +\n        (this.messages.length === 0\n          ? 0\n          : this.messages.map((it) => it.height || 0).reduce((acc, h) => acc + h)) +\n        (this.notes.length === 0\n          ? 0\n          : this.notes.map((it) => it.height || 0).reduce((acc, h) => acc + h))\n      );\n    },\n    clear: function () {\n      this.actors = [];\n      this.boxes = [];\n      this.loops = [];\n      this.messages = [];\n      this.notes = [];\n    },\n    addBox: function (boxModel) {\n      this.boxes.push(boxModel);\n    },\n    addActor: function (actorModel) {\n      this.actors.push(actorModel);\n    },\n    addLoop: function (loopModel) {\n      this.loops.push(loopModel);\n    },\n    addMessage: function (msgModel) {\n      this.messages.push(msgModel);\n    },\n    addNote: function (noteModel) {\n      this.notes.push(noteModel);\n    },\n    lastActor: function () {\n      return this.actors[this.actors.length - 1];\n    },\n    lastLoop: function () {\n      return this.loops[this.loops.length - 1];\n    },\n    lastMessage: function () {\n      return this.messages[this.messages.length - 1];\n    },\n    lastNote: function () {\n      return this.notes[this.notes.length - 1];\n    },\n    actors: [],\n    boxes: [],\n    loops: [],\n    messages: [],\n    notes: [],\n  },\n  init: function () {\n    this.sequenceItems = [];\n    this.activations = [];\n    this.models.clear();\n    this.data = {\n      startx: undefined,\n      stopx: undefined,\n      starty: undefined,\n      stopy: undefined,\n    };\n    this.verticalPos = 0;\n    setConf(getConfig());\n  },\n  updateVal: function (obj, key, val, fun) {\n    if (obj[key] === undefined) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  },\n  updateBounds: function (startx, starty, stopx, stopy) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const _self = this;\n    let cnt = 0;\n    /** @param type - Either `activation` or `undefined` */\n    function updateFn(type?: 'activation') {\n      return function updateItemBounds(item) {\n        cnt++;\n        // The loop sequenceItems is a stack so the biggest margins in the beginning of the sequenceItems\n        const n = _self.sequenceItems.length - cnt + 1;\n\n        _self.updateVal(item, 'starty', starty - n * conf.boxMargin, Math.min);\n        _self.updateVal(item, 'stopy', stopy + n * conf.boxMargin, Math.max);\n\n        _self.updateVal(bounds.data, 'startx', startx - n * conf.boxMargin, Math.min);\n        _self.updateVal(bounds.data, 'stopx', stopx + n * conf.boxMargin, Math.max);\n\n        if (!(type === 'activation')) {\n          _self.updateVal(item, 'startx', startx - n * conf.boxMargin, Math.min);\n          _self.updateVal(item, 'stopx', stopx + n * conf.boxMargin, Math.max);\n\n          _self.updateVal(bounds.data, 'starty', starty - n * conf.boxMargin, Math.min);\n          _self.updateVal(bounds.data, 'stopy', stopy + n * conf.boxMargin, Math.max);\n        }\n      };\n    }\n\n    this.sequenceItems.forEach(updateFn());\n    this.activations.forEach(updateFn('activation'));\n  },\n  insert: function (startx, starty, stopx, stopy) {\n    const _startx = common.getMin(startx, stopx);\n    const _stopx = common.getMax(startx, stopx);\n    const _starty = common.getMin(starty, stopy);\n    const _stopy = common.getMax(starty, stopy);\n\n    this.updateVal(bounds.data, 'startx', _startx, Math.min);\n    this.updateVal(bounds.data, 'starty', _starty, Math.min);\n    this.updateVal(bounds.data, 'stopx', _stopx, Math.max);\n    this.updateVal(bounds.data, 'stopy', _stopy, Math.max);\n\n    this.updateBounds(_startx, _starty, _stopx, _stopy);\n  },\n  newActivation: function (message, diagram, actors) {\n    const actorRect = actors.get(message.from);\n    const stackedSize = actorActivations(message.from).length || 0;\n    const x = actorRect.x + actorRect.width / 2 + ((stackedSize - 1) * conf.activationWidth) / 2;\n    this.activations.push({\n      startx: x,\n      starty: this.verticalPos + 2,\n      stopx: x + conf.activationWidth,\n      stopy: undefined,\n      actor: message.from,\n      anchored: svgDraw.anchorElement(diagram),\n    });\n  },\n  endActivation: function (message) {\n    // find most recent activation for given actor\n    const lastActorActivationIdx = this.activations\n      .map(function (activation) {\n        return activation.actor;\n      })\n      .lastIndexOf(message.from);\n    return this.activations.splice(lastActorActivationIdx, 1)[0];\n  },\n  createLoop: function (title = { message: undefined, wrap: false, width: undefined }, fill) {\n    return {\n      startx: undefined,\n      starty: this.verticalPos,\n      stopx: undefined,\n      stopy: undefined,\n      title: title.message,\n      wrap: title.wrap,\n      width: title.width,\n      height: 0,\n      fill: fill,\n    };\n  },\n  newLoop: function (title = { message: undefined, wrap: false, width: undefined }, fill) {\n    this.sequenceItems.push(this.createLoop(title, fill));\n  },\n  endLoop: function () {\n    return this.sequenceItems.pop();\n  },\n  isLoopOverlap: function () {\n    return this.sequenceItems.length\n      ? this.sequenceItems[this.sequenceItems.length - 1].overlap\n      : false;\n  },\n  addSectionToLoop: function (message) {\n    const loop = this.sequenceItems.pop();\n    loop.sections = loop.sections || [];\n    loop.sectionTitles = loop.sectionTitles || [];\n    loop.sections.push({ y: bounds.getVerticalPos(), height: 0 });\n    loop.sectionTitles.push(message);\n    this.sequenceItems.push(loop);\n  },\n  saveVerticalPos: function () {\n    if (this.isLoopOverlap()) {\n      this.savedVerticalPos = this.verticalPos;\n    }\n  },\n  resetVerticalPos: function () {\n    if (this.isLoopOverlap()) {\n      this.verticalPos = this.savedVerticalPos;\n    }\n  },\n  bumpVerticalPos: function (bump) {\n    this.verticalPos = this.verticalPos + bump;\n    this.data.stopy = common.getMax(this.data.stopy, this.verticalPos);\n  },\n  getVerticalPos: function () {\n    return this.verticalPos;\n  },\n  getBounds: function () {\n    return { bounds: this.data, models: this.models };\n  },\n};\n\n/** Options for drawing a note in {@link drawNote} */\ninterface NoteModel {\n  /** x axis start position */\n  startx: number;\n  /** y axis position */\n  starty: number;\n  /** the message to be shown */\n  message: string;\n  /** Set this with a custom width to override the default configured width. */\n  width: number;\n}\n\n/**\n * Draws a note in the diagram with the attached line\n *\n * @param elem - The diagram to draw to.\n * @param noteModel - Note model options.\n */\nconst drawNote = async function (elem: any, noteModel: NoteModel) {\n  bounds.bumpVerticalPos(conf.boxMargin);\n  noteModel.height = conf.boxMargin;\n  noteModel.starty = bounds.getVerticalPos();\n  const rect = svgDrawCommon.getNoteRect();\n  rect.x = noteModel.startx;\n  rect.y = noteModel.starty;\n  rect.width = noteModel.width || conf.width;\n  rect.class = 'note';\n\n  const g = elem.append('g');\n  const rectElem = svgDraw.drawRect(g, rect);\n  const textObj = svgDrawCommon.getTextObj();\n  textObj.x = noteModel.startx;\n  textObj.y = noteModel.starty;\n  textObj.width = rect.width;\n  textObj.dy = '1em';\n  textObj.text = noteModel.message;\n  textObj.class = 'noteText';\n  textObj.fontFamily = conf.noteFontFamily;\n  textObj.fontSize = conf.noteFontSize;\n  textObj.fontWeight = conf.noteFontWeight;\n  textObj.anchor = conf.noteAlign;\n  textObj.textMargin = conf.noteMargin;\n  textObj.valign = 'center';\n\n  const textElem = hasKatex(textObj.text) ? await drawKatex(g, textObj) : drawText(g, textObj);\n\n  const textHeight = Math.round(\n    textElem\n      .map((te) => (te._groups || te)[0][0].getBBox().height)\n      .reduce((acc, curr) => acc + curr)\n  );\n\n  rectElem.attr('height', textHeight + 2 * conf.noteMargin);\n  noteModel.height += textHeight + 2 * conf.noteMargin;\n  bounds.bumpVerticalPos(textHeight + 2 * conf.noteMargin);\n  noteModel.stopy = noteModel.starty + textHeight + 2 * conf.noteMargin;\n  noteModel.stopx = noteModel.startx + rect.width;\n  bounds.insert(noteModel.startx, noteModel.starty, noteModel.stopx, noteModel.stopy);\n  bounds.models.addNote(noteModel);\n};\n\nconst messageFont = (cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight,\n  };\n};\nconst noteFont = (cnf) => {\n  return {\n    fontFamily: cnf.noteFontFamily,\n    fontSize: cnf.noteFontSize,\n    fontWeight: cnf.noteFontWeight,\n  };\n};\nconst actorFont = (cnf) => {\n  return {\n    fontFamily: cnf.actorFontFamily,\n    fontSize: cnf.actorFontSize,\n    fontWeight: cnf.actorFontWeight,\n  };\n};\n\n/**\n * Process a message by adding its dimensions to the bound. It returns the Y coordinate of the\n * message so it can be drawn later. We do not draw the message at this point so the arrowhead can\n * be on top of the activation box.\n *\n * @param _diagram - The parent of the message element.\n * @param msgModel - The model containing fields describing a message\n * @returns `lineStartY` - The Y coordinate at which the message line starts\n */\nasync function boundMessage(_diagram, msgModel): Promise<number> {\n  bounds.bumpVerticalPos(10);\n  const { startx, stopx, message } = msgModel;\n  const lines = common.splitBreaks(message).length;\n  const isKatexMsg = hasKatex(message);\n  const textDims = isKatexMsg\n    ? await calculateMathMLDimensions(message, getConfig())\n    : utils.calculateTextDimensions(message, messageFont(conf));\n\n  if (!isKatexMsg) {\n    const lineHeight = textDims.height / lines;\n    msgModel.height += lineHeight;\n    bounds.bumpVerticalPos(lineHeight);\n  }\n\n  let lineStartY;\n  let totalOffset = textDims.height - 10;\n  const textWidth = textDims.width;\n\n  if (startx === stopx) {\n    lineStartY = bounds.getVerticalPos() + totalOffset;\n    if (!conf.rightAngles) {\n      totalOffset += conf.boxMargin;\n      lineStartY = bounds.getVerticalPos() + totalOffset;\n    }\n    totalOffset += 30;\n    const dx = common.getMax(textWidth / 2, conf.width / 2);\n    bounds.insert(\n      startx - dx,\n      bounds.getVerticalPos() - 10 + totalOffset,\n      stopx + dx,\n      bounds.getVerticalPos() + 30 + totalOffset\n    );\n  } else {\n    totalOffset += conf.boxMargin;\n    lineStartY = bounds.getVerticalPos() + totalOffset;\n    bounds.insert(startx, lineStartY - 10, stopx, lineStartY);\n  }\n  bounds.bumpVerticalPos(totalOffset);\n  msgModel.height += totalOffset;\n  msgModel.stopy = msgModel.starty + msgModel.height;\n  bounds.insert(msgModel.fromBounds, msgModel.starty, msgModel.toBounds, msgModel.stopy);\n\n  return lineStartY;\n}\n\n/**\n * Draws a message. Note that the bounds have previously been updated by boundMessage.\n *\n * @param diagram - The parent of the message element\n * @param msgModel - The model containing fields describing a message\n * @param lineStartY - The Y coordinate at which the message line starts\n * @param diagObj - The diagram object.\n */\nconst drawMessage = async function (diagram, msgModel, lineStartY: number, diagObj: Diagram) {\n  const { startx, stopx, starty, message, type, sequenceIndex, sequenceVisible } = msgModel;\n  const textDims = utils.calculateTextDimensions(message, messageFont(conf));\n  const textObj = svgDrawCommon.getTextObj();\n  textObj.x = startx;\n  textObj.y = starty + 10;\n  textObj.width = stopx - startx;\n  textObj.class = 'messageText';\n  textObj.dy = '1em';\n  textObj.text = message;\n  textObj.fontFamily = conf.messageFontFamily;\n  textObj.fontSize = conf.messageFontSize;\n  textObj.fontWeight = conf.messageFontWeight;\n  textObj.anchor = conf.messageAlign;\n  textObj.valign = 'center';\n  textObj.textMargin = conf.wrapPadding;\n  textObj.tspan = false;\n\n  if (hasKatex(textObj.text)) {\n    await drawKatex(diagram, textObj, { startx, stopx, starty: lineStartY });\n  } else {\n    drawText(diagram, textObj);\n  }\n\n  const textWidth = textDims.width;\n\n  let line;\n  if (startx === stopx) {\n    if (conf.rightAngles) {\n      line = diagram\n        .append('path')\n        .attr(\n          'd',\n          `M  ${startx},${lineStartY} H ${\n            startx + common.getMax(conf.width / 2, textWidth / 2)\n          } V ${lineStartY + 25} H ${startx}`\n        );\n    } else {\n      line = diagram\n        .append('path')\n        .attr(\n          'd',\n          'M ' +\n            startx +\n            ',' +\n            lineStartY +\n            ' C ' +\n            (startx + 60) +\n            ',' +\n            (lineStartY - 10) +\n            ' ' +\n            (startx + 60) +\n            ',' +\n            (lineStartY + 30) +\n            ' ' +\n            startx +\n            ',' +\n            (lineStartY + 20)\n        );\n    }\n  } else {\n    line = diagram.append('line');\n    line.attr('x1', startx);\n    line.attr('y1', lineStartY);\n    line.attr('x2', stopx);\n    line.attr('y2', lineStartY);\n  }\n  // Make an SVG Container\n  // Draw the line\n  if (\n    type === diagObj.db.LINETYPE.DOTTED ||\n    type === diagObj.db.LINETYPE.DOTTED_CROSS ||\n    type === diagObj.db.LINETYPE.DOTTED_POINT ||\n    type === diagObj.db.LINETYPE.DOTTED_OPEN ||\n    type === diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED\n  ) {\n    line.style('stroke-dasharray', '3, 3');\n    line.attr('class', 'messageLine1');\n  } else {\n    line.attr('class', 'messageLine0');\n  }\n\n  let url = '';\n  if (conf.arrowMarkerAbsolute) {\n    url = getUrl(true);\n  }\n\n  line.attr('stroke-width', 2);\n  line.attr('stroke', 'none'); // handled by theme/css anyway\n  line.style('fill', 'none'); // remove any fill colour\n  if (type === diagObj.db.LINETYPE.SOLID || type === diagObj.db.LINETYPE.DOTTED) {\n    line.attr('marker-end', 'url(' + url + '#arrowhead)');\n  }\n  if (\n    type === diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID ||\n    type === diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED\n  ) {\n    line.attr('marker-start', 'url(' + url + '#arrowhead)');\n    line.attr('marker-end', 'url(' + url + '#arrowhead)');\n  }\n  if (type === diagObj.db.LINETYPE.SOLID_POINT || type === diagObj.db.LINETYPE.DOTTED_POINT) {\n    line.attr('marker-end', 'url(' + url + '#filled-head)');\n  }\n\n  if (type === diagObj.db.LINETYPE.SOLID_CROSS || type === diagObj.db.LINETYPE.DOTTED_CROSS) {\n    line.attr('marker-end', 'url(' + url + '#crosshead)');\n  }\n\n  // add node number\n  if (sequenceVisible || conf.showSequenceNumbers) {\n    line.attr('marker-start', 'url(' + url + '#sequencenumber)');\n    diagram\n      .append('text')\n      .attr('x', startx)\n      .attr('y', lineStartY + 4)\n      .attr('font-family', 'sans-serif')\n      .attr('font-size', '12px')\n      .attr('text-anchor', 'middle')\n      .attr('class', 'sequenceNumber')\n      .text(sequenceIndex);\n  }\n};\n\nconst addActorRenderingData = function (\n  diagram,\n  actors,\n  createdActors: Map<string, any>,\n  actorKeys,\n  verticalPos,\n  messages,\n  isFooter\n) {\n  let prevWidth = 0;\n  let prevMargin = 0;\n  let prevBox = undefined;\n  let maxHeight = 0;\n\n  for (const actorKey of actorKeys) {\n    const actor = actors.get(actorKey);\n    const box = actor.box;\n\n    // end of box\n    if (prevBox && prevBox != box) {\n      if (!isFooter) {\n        bounds.models.addBox(prevBox);\n      }\n      prevMargin += conf.boxMargin + prevBox.margin;\n    }\n\n    // new box\n    if (box && box != prevBox) {\n      if (!isFooter) {\n        box.x = prevWidth + prevMargin;\n        box.y = verticalPos;\n      }\n      prevMargin += box.margin;\n    }\n\n    // Add some rendering data to the object\n    actor.width = actor.width || conf.width;\n    actor.height = common.getMax(actor.height || conf.height, conf.height);\n    actor.margin = actor.margin || conf.actorMargin;\n\n    maxHeight = common.getMax(maxHeight, actor.height);\n\n    // if the actor is created by a message, widen margin\n    if (createdActors.get(actor.name)) {\n      prevMargin += actor.width / 2;\n    }\n\n    actor.x = prevWidth + prevMargin;\n    actor.starty = bounds.getVerticalPos();\n\n    bounds.insert(actor.x, verticalPos, actor.x + actor.width, actor.height);\n\n    prevWidth += actor.width + prevMargin;\n    if (actor.box) {\n      actor.box.width = prevWidth + box.margin - actor.box.x;\n    }\n    prevMargin = actor.margin;\n    prevBox = actor.box;\n    bounds.models.addActor(actor);\n  }\n\n  // end of box\n  if (prevBox && !isFooter) {\n    bounds.models.addBox(prevBox);\n  }\n\n  // Add a margin between the actor boxes and the first arrow\n  bounds.bumpVerticalPos(maxHeight);\n};\n\nexport const drawActors = async function (diagram, actors, actorKeys, isFooter) {\n  if (!isFooter) {\n    for (const actorKey of actorKeys) {\n      const actor = actors.get(actorKey);\n      // Draw the box with the attached line\n      await svgDraw.drawActor(diagram, actor, conf, false);\n    }\n  } else {\n    let maxHeight = 0;\n    bounds.bumpVerticalPos(conf.boxMargin * 2);\n    for (const actorKey of actorKeys) {\n      const actor = actors.get(actorKey);\n      if (!actor.stopy) {\n        actor.stopy = bounds.getVerticalPos();\n      }\n      const height = await svgDraw.drawActor(diagram, actor, conf, true);\n      maxHeight = common.getMax(maxHeight, height);\n    }\n    bounds.bumpVerticalPos(maxHeight + conf.boxMargin);\n  }\n};\n\nexport const drawActorsPopup = function (diagram, actors, actorKeys, doc) {\n  let maxHeight = 0;\n  let maxWidth = 0;\n  for (const actorKey of actorKeys) {\n    const actor = actors.get(actorKey);\n    const minMenuWidth = getRequiredPopupWidth(actor);\n    const menuDimensions = svgDraw.drawPopup(\n      diagram,\n      actor,\n      minMenuWidth,\n      conf,\n      conf.forceMenus,\n      doc\n    );\n    if (menuDimensions.height > maxHeight) {\n      maxHeight = menuDimensions.height;\n    }\n    if (menuDimensions.width + actor.x > maxWidth) {\n      maxWidth = menuDimensions.width + actor.x;\n    }\n  }\n\n  return { maxHeight: maxHeight, maxWidth: maxWidth };\n};\n\nexport const setConf = function (cnf) {\n  assignWithDepth(conf, cnf);\n\n  if (cnf.fontFamily) {\n    conf.actorFontFamily = conf.noteFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.actorFontSize = conf.noteFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.actorFontWeight = conf.noteFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n};\n\nconst actorActivations = function (actor) {\n  return bounds.activations.filter(function (activation) {\n    return activation.actor === actor;\n  });\n};\n\nconst activationBounds = function (actor, actors) {\n  // handle multiple stacked activations for same actor\n  const actorObj = actors.get(actor);\n  const activations = actorActivations(actor);\n\n  const left = activations.reduce(\n    function (acc, activation) {\n      return common.getMin(acc, activation.startx);\n    },\n    actorObj.x + actorObj.width / 2 - 1\n  );\n  const right = activations.reduce(\n    function (acc, activation) {\n      return common.getMax(acc, activation.stopx);\n    },\n    actorObj.x + actorObj.width / 2 + 1\n  );\n  return [left, right];\n};\n\nfunction adjustLoopHeightForWrap(loopWidths, msg, preMargin, postMargin, addLoopFn) {\n  bounds.bumpVerticalPos(preMargin);\n  let heightAdjust = postMargin;\n  if (msg.id && msg.message && loopWidths[msg.id]) {\n    const loopWidth = loopWidths[msg.id].width;\n    const textConf = messageFont(conf);\n    msg.message = utils.wrapLabel(`[${msg.message}]`, loopWidth - 2 * conf.wrapPadding, textConf);\n    msg.width = loopWidth;\n    msg.wrap = true;\n\n    // const lines = common.splitBreaks(msg.message).length;\n    const textDims = utils.calculateTextDimensions(msg.message, textConf);\n    const totalOffset = common.getMax(textDims.height, conf.labelBoxHeight);\n    heightAdjust = postMargin + totalOffset;\n    log.debug(`${totalOffset} - ${msg.message}`);\n  }\n  addLoopFn(msg);\n  bounds.bumpVerticalPos(heightAdjust);\n}\n\n/**\n * Adjust the msgModel and the actor for the rendering in case the latter is created or destroyed by the msg\n * @param msg - the potentially creating or destroying message\n * @param msgModel - the model associated with the message\n * @param lineStartY - the y position of the message line\n * @param index - the index of the current actor under consideration\n * @param actors - the array of all actors\n * @param createdActors - the array of actors created in the diagram\n * @param destroyedActors - the array of actors destroyed in the diagram\n */\nfunction adjustCreatedDestroyedData(\n  msg,\n  msgModel,\n  lineStartY,\n  index,\n  actors,\n  createdActors,\n  destroyedActors\n) {\n  function receiverAdjustment(actor, adjustment) {\n    if (actor.x < actors.get(msg.from).x) {\n      bounds.insert(\n        msgModel.stopx - adjustment,\n        msgModel.starty,\n        msgModel.startx,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.stopx = msgModel.stopx + adjustment;\n    } else {\n      bounds.insert(\n        msgModel.startx,\n        msgModel.starty,\n        msgModel.stopx + adjustment,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.stopx = msgModel.stopx - adjustment;\n    }\n  }\n\n  function senderAdjustment(actor, adjustment) {\n    if (actor.x < actors.get(msg.to).x) {\n      bounds.insert(\n        msgModel.startx - adjustment,\n        msgModel.starty,\n        msgModel.stopx,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.startx = msgModel.startx + adjustment;\n    } else {\n      bounds.insert(\n        msgModel.stopx,\n        msgModel.starty,\n        msgModel.startx + adjustment,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.startx = msgModel.startx - adjustment;\n    }\n  }\n\n  // if it is a create message\n  if (createdActors.get(msg.to) == index) {\n    const actor = actors.get(msg.to);\n    const adjustment = actor.type == 'actor' ? ACTOR_TYPE_WIDTH / 2 + 3 : actor.width / 2 + 3;\n    receiverAdjustment(actor, adjustment);\n    actor.starty = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  }\n  // if it is a destroy sender message\n  else if (destroyedActors.get(msg.from) == index) {\n    const actor = actors.get(msg.from);\n    if (conf.mirrorActors) {\n      const adjustment = actor.type == 'actor' ? ACTOR_TYPE_WIDTH / 2 : actor.width / 2;\n      senderAdjustment(actor, adjustment);\n    }\n    actor.stopy = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  }\n  // if it is a destroy receiver message\n  else if (destroyedActors.get(msg.to) == index) {\n    const actor = actors.get(msg.to);\n    if (conf.mirrorActors) {\n      const adjustment = actor.type == 'actor' ? ACTOR_TYPE_WIDTH / 2 + 3 : actor.width / 2 + 3;\n      receiverAdjustment(actor, adjustment);\n    }\n    actor.stopy = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  }\n}\n\n/**\n * Draws a sequenceDiagram in the tag with id: id based on the graph definition in text.\n *\n * @param _text - The text of the diagram\n * @param id - The id of the diagram which will be used as a DOM element id¨\n * @param _version - Mermaid version from package.json\n * @param diagObj - A standard diagram containing the db and the text and type etc of the diagram\n */\nexport const draw = async function (_text: string, id: string, _version: string, diagObj: Diagram) {\n  const { securityLevel, sequence } = getConfig();\n  conf = sequence;\n  // Handle root and Document for when rendering in sandbox mode\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n  const doc = securityLevel === 'sandbox' ? sandboxElement.nodes()[0].contentDocument : document;\n  bounds.init();\n  log.debug(diagObj.db);\n\n  const diagram =\n    securityLevel === 'sandbox' ? root.select(`[id=\"${id}\"]`) : select(`[id=\"${id}\"]`);\n\n  // Fetch data from the parsing\n  const actors = diagObj.db.getActors();\n  const createdActors = diagObj.db.getCreatedActors();\n  const destroyedActors = diagObj.db.getDestroyedActors();\n  const boxes = diagObj.db.getBoxes();\n  let actorKeys = diagObj.db.getActorKeys();\n  const messages = diagObj.db.getMessages();\n  const title = diagObj.db.getDiagramTitle();\n  const hasBoxes = diagObj.db.hasAtLeastOneBox();\n  const hasBoxTitles = diagObj.db.hasAtLeastOneBoxWithTitle();\n  const maxMessageWidthPerActor = await getMaxMessageWidthPerActor(actors, messages, diagObj);\n  conf.height = await calculateActorMargins(actors, maxMessageWidthPerActor, boxes);\n\n  svgDraw.insertComputerIcon(diagram);\n  svgDraw.insertDatabaseIcon(diagram);\n  svgDraw.insertClockIcon(diagram);\n\n  if (hasBoxes) {\n    bounds.bumpVerticalPos(conf.boxMargin);\n    if (hasBoxTitles) {\n      bounds.bumpVerticalPos(boxes[0].textMaxHeight);\n    }\n  }\n\n  if (conf.hideUnusedParticipants === true) {\n    const newActors = new Set();\n    messages.forEach((message) => {\n      newActors.add(message.from);\n      newActors.add(message.to);\n    });\n    actorKeys = actorKeys.filter((actorKey) => newActors.has(actorKey));\n  }\n\n  addActorRenderingData(diagram, actors, createdActors, actorKeys, 0, messages, false);\n  const loopWidths = await calculateLoopBounds(messages, actors, maxMessageWidthPerActor, diagObj);\n\n  // The arrow head definition is attached to the svg once\n  svgDraw.insertArrowHead(diagram);\n  svgDraw.insertArrowCrossHead(diagram);\n  svgDraw.insertArrowFilledHead(diagram);\n  svgDraw.insertSequenceNumber(diagram);\n\n  /**\n   * @param msg - The message to draw.\n   * @param verticalPos - The vertical position of the message.\n   */\n  function activeEnd(msg: any, verticalPos: number) {\n    const activationData = bounds.endActivation(msg);\n    if (activationData.starty + 18 > verticalPos) {\n      activationData.starty = verticalPos - 6;\n      verticalPos += 12;\n    }\n    svgDraw.drawActivation(\n      diagram,\n      activationData,\n      verticalPos,\n      conf,\n      actorActivations(msg.from).length\n    );\n\n    bounds.insert(activationData.startx, verticalPos - 10, activationData.stopx, verticalPos);\n  }\n\n  // Draw the messages/signals\n  let sequenceIndex = 1;\n  let sequenceIndexStep = 1;\n  const messagesToDraw = [];\n  const backgrounds = [];\n  let index = 0;\n  for (const msg of messages) {\n    let loopModel, noteModel, msgModel;\n\n    switch (msg.type) {\n      case diagObj.db.LINETYPE.NOTE:\n        bounds.resetVerticalPos();\n        noteModel = msg.noteModel;\n        await drawNote(diagram, noteModel);\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_START:\n        bounds.newActivation(msg, diagram, actors);\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_END:\n        activeEnd(msg, bounds.getVerticalPos());\n        break;\n      case diagObj.db.LINETYPE.LOOP_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.LOOP_END:\n        loopModel = bounds.endLoop();\n        await svgDraw.drawLoop(diagram, loopModel, 'loop', conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.RECT_START:\n        adjustLoopHeightForWrap(loopWidths, msg, conf.boxMargin, conf.boxMargin, (message) =>\n          bounds.newLoop(undefined, message.message)\n        );\n        break;\n      case diagObj.db.LINETYPE.RECT_END:\n        loopModel = bounds.endLoop();\n        backgrounds.push(loopModel);\n        bounds.models.addLoop(loopModel);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        break;\n      case diagObj.db.LINETYPE.OPT_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.OPT_END:\n        loopModel = bounds.endLoop();\n        await svgDraw.drawLoop(diagram, loopModel, 'opt', conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.ALT_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.ALT_ELSE:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.ALT_END:\n        loopModel = bounds.endLoop();\n        await svgDraw.drawLoop(diagram, loopModel, 'alt', conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.PAR_START:\n      case diagObj.db.LINETYPE.PAR_OVER_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        bounds.saveVerticalPos();\n        break;\n      case diagObj.db.LINETYPE.PAR_AND:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.PAR_END:\n        loopModel = bounds.endLoop();\n        await svgDraw.drawLoop(diagram, loopModel, 'par', conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.AUTONUMBER:\n        sequenceIndex = msg.message.start || sequenceIndex;\n        sequenceIndexStep = msg.message.step || sequenceIndexStep;\n        if (msg.message.visible) {\n          diagObj.db.enableSequenceNumbers();\n        } else {\n          diagObj.db.disableSequenceNumbers();\n        }\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_OPTION:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_END:\n        loopModel = bounds.endLoop();\n        await svgDraw.drawLoop(diagram, loopModel, 'critical', conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.BREAK_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.BREAK_END:\n        loopModel = bounds.endLoop();\n        await svgDraw.drawLoop(diagram, loopModel, 'break', conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      default:\n        try {\n          msgModel = msg.msgModel;\n          msgModel.starty = bounds.getVerticalPos();\n          msgModel.sequenceIndex = sequenceIndex;\n          msgModel.sequenceVisible = diagObj.db.showSequenceNumbers();\n          const lineStartY = await boundMessage(diagram, msgModel);\n          adjustCreatedDestroyedData(\n            msg,\n            msgModel,\n            lineStartY,\n            index,\n            actors,\n            createdActors,\n            destroyedActors\n          );\n          messagesToDraw.push({ messageModel: msgModel, lineStartY: lineStartY });\n          bounds.models.addMessage(msgModel);\n        } catch (e) {\n          log.error('error while drawing message', e);\n        }\n    }\n\n    // Increment sequence counter if msg.type is a line (and not another event like activation or note, etc)\n    if (\n      [\n        diagObj.db.LINETYPE.SOLID_OPEN,\n        diagObj.db.LINETYPE.DOTTED_OPEN,\n        diagObj.db.LINETYPE.SOLID,\n        diagObj.db.LINETYPE.DOTTED,\n        diagObj.db.LINETYPE.SOLID_CROSS,\n        diagObj.db.LINETYPE.DOTTED_CROSS,\n        diagObj.db.LINETYPE.SOLID_POINT,\n        diagObj.db.LINETYPE.DOTTED_POINT,\n        diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID,\n        diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED,\n      ].includes(msg.type)\n    ) {\n      sequenceIndex = sequenceIndex + sequenceIndexStep;\n    }\n    index++;\n  }\n\n  log.debug('createdActors', createdActors);\n  log.debug('destroyedActors', destroyedActors);\n  await drawActors(diagram, actors, actorKeys, false);\n\n  for (const e of messagesToDraw) {\n    await drawMessage(diagram, e.messageModel, e.lineStartY, diagObj);\n  }\n  if (conf.mirrorActors) {\n    await drawActors(diagram, actors, actorKeys, true);\n  }\n  backgrounds.forEach((e) => svgDraw.drawBackgroundRect(diagram, e));\n  fixLifeLineHeights(diagram, actors, actorKeys, conf);\n\n  for (const box of bounds.models.boxes) {\n    box.height = bounds.getVerticalPos() - box.y;\n    bounds.insert(box.x, box.y, box.x + box.width, box.height);\n    box.startx = box.x;\n    box.starty = box.y;\n    box.stopx = box.startx + box.width;\n    box.stopy = box.starty + box.height;\n    box.stroke = 'rgb(0,0,0, 0.5)';\n    svgDraw.drawBox(diagram, box, conf);\n  }\n\n  if (hasBoxes) {\n    bounds.bumpVerticalPos(conf.boxMargin);\n  }\n\n  // only draw popups for the top row of actors.\n  const requiredBoxSize = drawActorsPopup(diagram, actors, actorKeys, doc);\n\n  const { bounds: box } = bounds.getBounds();\n\n  if (box.startx === undefined) {\n    box.startx = 0;\n  }\n  if (box.starty === undefined) {\n    box.starty = 0;\n  }\n  if (box.stopx === undefined) {\n    box.stopx = 0;\n  }\n  if (box.stopy === undefined) {\n    box.stopy = 0;\n  }\n\n  // Make sure the height of the diagram supports long menus.\n  let boxHeight = box.stopy - box.starty;\n  if (boxHeight < requiredBoxSize.maxHeight) {\n    boxHeight = requiredBoxSize.maxHeight;\n  }\n\n  let height = boxHeight + 2 * conf.diagramMarginY;\n  if (conf.mirrorActors) {\n    height = height - conf.boxMargin + conf.bottomMarginAdj;\n  }\n\n  // Make sure the width of the diagram supports wide menus.\n  let boxWidth = box.stopx - box.startx;\n  if (boxWidth < requiredBoxSize.maxWidth) {\n    boxWidth = requiredBoxSize.maxWidth;\n  }\n  const width = boxWidth + 2 * conf.diagramMarginX;\n\n  if (title) {\n    diagram\n      .append('text')\n      .text(title)\n      .attr('x', (box.stopx - box.startx) / 2 - 2 * conf.diagramMarginX)\n      .attr('y', -25);\n  }\n\n  configureSvgSize(diagram, height, width, conf.useMaxWidth);\n\n  const extraVertForTitle = title ? 40 : 0;\n  diagram.attr(\n    'viewBox',\n    box.startx -\n      conf.diagramMarginX +\n      ' -' +\n      (conf.diagramMarginY + extraVertForTitle) +\n      ' ' +\n      width +\n      ' ' +\n      (height + extraVertForTitle)\n  );\n\n  log.debug(`models:`, bounds.models);\n};\n\n/**\n * Retrieves the max message width of each actor, supports signals (messages, loops) and notes.\n *\n * It will enumerate each given message, and will determine its text width, in relation to the actor\n * it originates from, and destined to.\n *\n * @param actors - The actors map\n * @param messages - A list of message objects to iterate\n * @param diagObj - The diagram object.\n * @returns The max message width of each actor.\n */\nasync function getMaxMessageWidthPerActor(\n  actors: Map<string, any>,\n  messages: any[],\n  diagObj: Diagram\n): Promise<Record<string, number>> {\n  const maxMessageWidthPerActor = {};\n\n  for (const msg of messages) {\n    if (actors.get(msg.to) && actors.get(msg.from)) {\n      const actor = actors.get(msg.to);\n\n      // If this is the first actor, and the message is left of it, no need to calculate the margin\n      if (msg.placement === diagObj.db.PLACEMENT.LEFTOF && !actor.prevActor) {\n        continue;\n      }\n\n      // If this is the last actor, and the message is right of it, no need to calculate the margin\n      if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF && !actor.nextActor) {\n        continue;\n      }\n\n      const isNote = msg.placement !== undefined;\n      const isMessage = !isNote;\n\n      const textFont = isNote ? noteFont(conf) : messageFont(conf);\n      const wrappedMessage = msg.wrap\n        ? utils.wrapLabel(msg.message, conf.width - 2 * conf.wrapPadding, textFont)\n        : msg.message;\n      const messageDimensions = hasKatex(wrappedMessage)\n        ? await calculateMathMLDimensions(msg.message, getConfig())\n        : utils.calculateTextDimensions(wrappedMessage, textFont);\n      const messageWidth = messageDimensions.width + 2 * conf.wrapPadding;\n\n      /*\n       * The following scenarios should be supported:\n       *\n       * - There's a message (non-note) between fromActor and toActor\n       *   - If fromActor is on the right and toActor is on the left, we should\n       *     define the toActor's margin\n       *   - If fromActor is on the left and toActor is on the right, we should\n       *     define the fromActor's margin\n       * - There's a note, in which case fromActor == toActor\n       *   - If the note is to the left of the actor, we should define the previous actor\n       *     margin\n       *   - If the note is on the actor, we should define both the previous and next actor\n       *     margins, each being the half of the note size\n       *   - If the note is on the right of the actor, we should define the current actor\n       *     margin\n       */\n      if (isMessage && msg.from === actor.nextActor) {\n        maxMessageWidthPerActor[msg.to] = common.getMax(\n          maxMessageWidthPerActor[msg.to] || 0,\n          messageWidth\n        );\n      } else if (isMessage && msg.from === actor.prevActor) {\n        maxMessageWidthPerActor[msg.from] = common.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth\n        );\n      } else if (isMessage && msg.from === msg.to) {\n        maxMessageWidthPerActor[msg.from] = common.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth / 2\n        );\n\n        maxMessageWidthPerActor[msg.to] = common.getMax(\n          maxMessageWidthPerActor[msg.to] || 0,\n          messageWidth / 2\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF) {\n        maxMessageWidthPerActor[msg.from] = common.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.LEFTOF) {\n        maxMessageWidthPerActor[actor.prevActor] = common.getMax(\n          maxMessageWidthPerActor[actor.prevActor] || 0,\n          messageWidth\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.OVER) {\n        if (actor.prevActor) {\n          maxMessageWidthPerActor[actor.prevActor] = common.getMax(\n            maxMessageWidthPerActor[actor.prevActor] || 0,\n            messageWidth / 2\n          );\n        }\n\n        if (actor.nextActor) {\n          maxMessageWidthPerActor[msg.from] = common.getMax(\n            maxMessageWidthPerActor[msg.from] || 0,\n            messageWidth / 2\n          );\n        }\n      }\n    }\n  }\n\n  log.debug('maxMessageWidthPerActor:', maxMessageWidthPerActor);\n  return maxMessageWidthPerActor;\n}\n\nconst getRequiredPopupWidth = function (actor) {\n  let requiredPopupWidth = 0;\n  const textFont = actorFont(conf);\n  for (const key in actor.links) {\n    const labelDimensions = utils.calculateTextDimensions(key, textFont);\n    const labelWidth = labelDimensions.width + 2 * conf.wrapPadding + 2 * conf.boxMargin;\n    if (requiredPopupWidth < labelWidth) {\n      requiredPopupWidth = labelWidth;\n    }\n  }\n\n  return requiredPopupWidth;\n};\n\n/**\n * This will calculate the optimal margin for each given actor,\n * for a given actor → messageWidth map.\n *\n * An actor's margin is determined by the width of the actor, the width of the largest message that\n * originates from it, and the configured conf.actorMargin.\n *\n * @param actors - The actors map to calculate margins for\n * @param actorToMessageWidth - A map of actor key → max message width it holds\n * @param boxes - The boxes around the actors if any\n */\nasync function calculateActorMargins(\n  actors: Map<string, any>,\n  actorToMessageWidth: Awaited<ReturnType<typeof getMaxMessageWidthPerActor>>,\n  boxes\n) {\n  let maxHeight = 0;\n  for (const prop of actors.keys()) {\n    const actor = actors.get(prop);\n    if (actor.wrap) {\n      actor.description = utils.wrapLabel(\n        actor.description,\n        conf.width - 2 * conf.wrapPadding,\n        actorFont(conf)\n      );\n    }\n    const actDims = hasKatex(actor.description)\n      ? await calculateMathMLDimensions(actor.description, getConfig())\n      : utils.calculateTextDimensions(actor.description, actorFont(conf));\n\n    actor.width = actor.wrap\n      ? conf.width\n      : common.getMax(conf.width, actDims.width + 2 * conf.wrapPadding);\n\n    actor.height = actor.wrap ? common.getMax(actDims.height, conf.height) : conf.height;\n    maxHeight = common.getMax(maxHeight, actor.height);\n  }\n\n  for (const actorKey in actorToMessageWidth) {\n    const actor = actors.get(actorKey);\n\n    if (!actor) {\n      continue;\n    }\n\n    const nextActor = actors.get(actor.nextActor);\n\n    // No need to space out an actor that doesn't have a next link\n    if (!nextActor) {\n      const messageWidth = actorToMessageWidth[actorKey];\n      const actorWidth = messageWidth + conf.actorMargin - actor.width / 2;\n      actor.margin = common.getMax(actorWidth, conf.actorMargin);\n      continue;\n    }\n\n    const messageWidth = actorToMessageWidth[actorKey];\n    const actorWidth = messageWidth + conf.actorMargin - actor.width / 2 - nextActor.width / 2;\n\n    actor.margin = common.getMax(actorWidth, conf.actorMargin);\n  }\n\n  let maxBoxHeight = 0;\n  boxes.forEach((box) => {\n    const textFont = messageFont(conf);\n    let totalWidth = box.actorKeys.reduce((total, aKey) => {\n      return (total += actors.get(aKey).width + (actors.get(aKey).margin || 0));\n    }, 0);\n\n    totalWidth -= 2 * conf.boxTextMargin;\n    if (box.wrap) {\n      box.name = utils.wrapLabel(box.name, totalWidth - 2 * conf.wrapPadding, textFont);\n    }\n\n    const boxMsgDimensions = utils.calculateTextDimensions(box.name, textFont);\n    maxBoxHeight = common.getMax(boxMsgDimensions.height, maxBoxHeight);\n    const minWidth = common.getMax(totalWidth, boxMsgDimensions.width + 2 * conf.wrapPadding);\n    box.margin = conf.boxTextMargin;\n    if (totalWidth < minWidth) {\n      const missing = (minWidth - totalWidth) / 2;\n      box.margin += missing;\n    }\n  });\n  boxes.forEach((box) => (box.textMaxHeight = maxBoxHeight));\n\n  return common.getMax(maxHeight, conf.height);\n}\n\nconst buildNoteModel = async function (msg, actors, diagObj) {\n  const fromActor = actors.get(msg.from);\n  const toActor = actors.get(msg.to);\n  const startx = fromActor.x;\n  const stopx = toActor.x;\n  const shouldWrap = msg.wrap && msg.message;\n\n  let textDimensions: { width: number; height: number; lineHeight?: number } = hasKatex(msg.message)\n    ? await calculateMathMLDimensions(msg.message, getConfig())\n    : utils.calculateTextDimensions(\n        shouldWrap ? utils.wrapLabel(msg.message, conf.width, noteFont(conf)) : msg.message,\n        noteFont(conf)\n      );\n  const noteModel = {\n    width: shouldWrap\n      ? conf.width\n      : common.getMax(conf.width, textDimensions.width + 2 * conf.noteMargin),\n    height: 0,\n    startx: fromActor.x,\n    stopx: 0,\n    starty: 0,\n    stopy: 0,\n    message: msg.message,\n  };\n  if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF) {\n    noteModel.width = shouldWrap\n      ? common.getMax(conf.width, textDimensions.width)\n      : common.getMax(\n          fromActor.width / 2 + toActor.width / 2,\n          textDimensions.width + 2 * conf.noteMargin\n        );\n    noteModel.startx = startx + (fromActor.width + conf.actorMargin) / 2;\n  } else if (msg.placement === diagObj.db.PLACEMENT.LEFTOF) {\n    noteModel.width = shouldWrap\n      ? common.getMax(conf.width, textDimensions.width + 2 * conf.noteMargin)\n      : common.getMax(\n          fromActor.width / 2 + toActor.width / 2,\n          textDimensions.width + 2 * conf.noteMargin\n        );\n    noteModel.startx = startx - noteModel.width + (fromActor.width - conf.actorMargin) / 2;\n  } else if (msg.to === msg.from) {\n    textDimensions = utils.calculateTextDimensions(\n      shouldWrap\n        ? utils.wrapLabel(msg.message, common.getMax(conf.width, fromActor.width), noteFont(conf))\n        : msg.message,\n      noteFont(conf)\n    );\n    noteModel.width = shouldWrap\n      ? common.getMax(conf.width, fromActor.width)\n      : common.getMax(fromActor.width, conf.width, textDimensions.width + 2 * conf.noteMargin);\n    noteModel.startx = startx + (fromActor.width - noteModel.width) / 2;\n  } else {\n    noteModel.width =\n      Math.abs(startx + fromActor.width / 2 - (stopx + toActor.width / 2)) + conf.actorMargin;\n    noteModel.startx =\n      startx < stopx\n        ? startx + fromActor.width / 2 - conf.actorMargin / 2\n        : stopx + toActor.width / 2 - conf.actorMargin / 2;\n  }\n  if (shouldWrap) {\n    noteModel.message = utils.wrapLabel(\n      msg.message,\n      noteModel.width - 2 * conf.wrapPadding,\n      noteFont(conf)\n    );\n  }\n  log.debug(\n    `NM:[${noteModel.startx},${noteModel.stopx},${noteModel.starty},${noteModel.stopy}:${noteModel.width},${noteModel.height}=${msg.message}]`\n  );\n  return noteModel;\n};\n\nconst buildMessageModel = function (msg, actors, diagObj) {\n  if (\n    ![\n      diagObj.db.LINETYPE.SOLID_OPEN,\n      diagObj.db.LINETYPE.DOTTED_OPEN,\n      diagObj.db.LINETYPE.SOLID,\n      diagObj.db.LINETYPE.DOTTED,\n      diagObj.db.LINETYPE.SOLID_CROSS,\n      diagObj.db.LINETYPE.DOTTED_CROSS,\n      diagObj.db.LINETYPE.SOLID_POINT,\n      diagObj.db.LINETYPE.DOTTED_POINT,\n      diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID,\n      diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED,\n    ].includes(msg.type)\n  ) {\n    return {};\n  }\n  const [fromLeft, fromRight] = activationBounds(msg.from, actors);\n  const [toLeft, toRight] = activationBounds(msg.to, actors);\n  const isArrowToRight = fromLeft <= toLeft;\n  let startx = isArrowToRight ? fromRight : fromLeft;\n  let stopx = isArrowToRight ? toLeft : toRight;\n\n  // As the line width is considered, the left and right values will be off by 2.\n  const isArrowToActivation = Math.abs(toLeft - toRight) > 2;\n\n  /**\n   * Adjust the value based on the arrow direction\n   * @param value - The value to adjust\n   * @returns The adjustment with correct sign to be added to the actual value.\n   */\n  const adjustValue = (value: number) => {\n    return isArrowToRight ? -value : value;\n  };\n\n  if (msg.from === msg.to) {\n    // This is a self reference, so we need to make sure the arrow is drawn correctly\n    // There are many checks in the downstream rendering that checks for equality.\n    // The lines on loops will be off by few pixels, but that's fine for now.\n    stopx = startx;\n  } else {\n    /**\n     * This is an edge case for the first activation.\n     * Proper fix would require significant changes.\n     * So, we set an activate flag in the message, and cross check that with isToActivation\n     * In cases where the message is to an activation that was properly detected, we don't want to move the arrow head\n     * The activation will not be detected on the first message, so we need to move the arrow head\n     */\n    if (msg.activate && !isArrowToActivation) {\n      stopx += adjustValue(conf.activationWidth / 2 - 1);\n    }\n\n    /**\n     * Shorten the length of arrow at the end and move the marker forward (using refX) to have a clean arrowhead\n     * This is not required for open arrows that don't have arrowheads\n     */\n    if (![diagObj.db.LINETYPE.SOLID_OPEN, diagObj.db.LINETYPE.DOTTED_OPEN].includes(msg.type)) {\n      stopx += adjustValue(3);\n    }\n\n    /**\n     * Shorten start position of bidirectional arrow to accommodate for second arrowhead\n     */\n    if (\n      [diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID, diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(\n        msg.type\n      )\n    ) {\n      startx -= adjustValue(3);\n    }\n  }\n\n  const allBounds = [fromLeft, fromRight, toLeft, toRight];\n  const boundedWidth = Math.abs(startx - stopx);\n  if (msg.wrap && msg.message) {\n    msg.message = utils.wrapLabel(\n      msg.message,\n      common.getMax(boundedWidth + 2 * conf.wrapPadding, conf.width),\n      messageFont(conf)\n    );\n  }\n  const msgDims = utils.calculateTextDimensions(msg.message, messageFont(conf));\n\n  return {\n    width: common.getMax(\n      msg.wrap ? 0 : msgDims.width + 2 * conf.wrapPadding,\n      boundedWidth + 2 * conf.wrapPadding,\n      conf.width\n    ),\n    height: 0,\n    startx,\n    stopx,\n    starty: 0,\n    stopy: 0,\n    message: msg.message,\n    type: msg.type,\n    wrap: msg.wrap,\n    fromBounds: Math.min.apply(null, allBounds),\n    toBounds: Math.max.apply(null, allBounds),\n  };\n};\n\nconst calculateLoopBounds = async function (messages, actors, _maxWidthPerActor, diagObj) {\n  const loops = {};\n  const stack = [];\n  let current, noteModel, msgModel;\n\n  for (const msg of messages) {\n    switch (msg.type) {\n      case diagObj.db.LINETYPE.LOOP_START:\n      case diagObj.db.LINETYPE.ALT_START:\n      case diagObj.db.LINETYPE.OPT_START:\n      case diagObj.db.LINETYPE.PAR_START:\n      case diagObj.db.LINETYPE.PAR_OVER_START:\n      case diagObj.db.LINETYPE.CRITICAL_START:\n      case diagObj.db.LINETYPE.BREAK_START:\n        stack.push({\n          id: msg.id,\n          msg: msg.message,\n          from: Number.MAX_SAFE_INTEGER,\n          to: Number.MIN_SAFE_INTEGER,\n          width: 0,\n        });\n        break;\n      case diagObj.db.LINETYPE.ALT_ELSE:\n      case diagObj.db.LINETYPE.PAR_AND:\n      case diagObj.db.LINETYPE.CRITICAL_OPTION:\n        if (msg.message) {\n          current = stack.pop();\n          loops[current.id] = current;\n          loops[msg.id] = current;\n          stack.push(current);\n        }\n        break;\n      case diagObj.db.LINETYPE.LOOP_END:\n      case diagObj.db.LINETYPE.ALT_END:\n      case diagObj.db.LINETYPE.OPT_END:\n      case diagObj.db.LINETYPE.PAR_END:\n      case diagObj.db.LINETYPE.CRITICAL_END:\n      case diagObj.db.LINETYPE.BREAK_END:\n        current = stack.pop();\n        loops[current.id] = current;\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_START:\n        {\n          const actorRect = actors.get(msg.from ? msg.from : msg.to.actor);\n          const stackedSize = actorActivations(msg.from ? msg.from : msg.to.actor).length;\n          const x =\n            actorRect.x + actorRect.width / 2 + ((stackedSize - 1) * conf.activationWidth) / 2;\n          const toAdd = {\n            startx: x,\n            stopx: x + conf.activationWidth,\n            actor: msg.from,\n            enabled: true,\n          };\n          bounds.activations.push(toAdd);\n        }\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_END:\n        {\n          const lastActorActivationIdx = bounds.activations\n            .map((a) => a.actor)\n            .lastIndexOf(msg.from);\n          bounds.activations.splice(lastActorActivationIdx, 1).splice(0, 1);\n        }\n        break;\n    }\n    const isNote = msg.placement !== undefined;\n    if (isNote) {\n      noteModel = await buildNoteModel(msg, actors, diagObj);\n      msg.noteModel = noteModel;\n      stack.forEach((stk) => {\n        current = stk;\n        current.from = common.getMin(current.from, noteModel.startx);\n        current.to = common.getMax(current.to, noteModel.startx + noteModel.width);\n        current.width =\n          common.getMax(current.width, Math.abs(current.from - current.to)) - conf.labelBoxWidth;\n      });\n    } else {\n      msgModel = buildMessageModel(msg, actors, diagObj);\n      msg.msgModel = msgModel;\n      if (msgModel.startx && msgModel.stopx && stack.length > 0) {\n        stack.forEach((stk) => {\n          current = stk;\n          if (msgModel.startx === msgModel.stopx) {\n            const from = actors.get(msg.from);\n            const to = actors.get(msg.to);\n            current.from = common.getMin(\n              from.x - msgModel.width / 2,\n              from.x - from.width / 2,\n              current.from\n            );\n            current.to = common.getMax(\n              to.x + msgModel.width / 2,\n              to.x + from.width / 2,\n              current.to\n            );\n            current.width =\n              common.getMax(current.width, Math.abs(current.to - current.from)) -\n              conf.labelBoxWidth;\n          } else {\n            current.from = common.getMin(msgModel.startx, current.from);\n            current.to = common.getMax(msgModel.stopx, current.to);\n            current.width = common.getMax(current.width, msgModel.width) - conf.labelBoxWidth;\n          }\n        });\n      }\n    }\n  }\n  bounds.activations = [];\n  log.debug('Loop type widths:', loops);\n  return loops;\n};\n\nexport default {\n  bounds,\n  drawActors,\n  drawActorsPopup,\n  setConf,\n  draw,\n};\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: JISON doesn't support types\nimport parser from './parser/sequenceDiagram.jison';\nimport { SequenceDB } from './sequenceDb.js';\nimport styles from './styles.js';\nimport { setConfig } from '../../diagram-api/diagramAPI.js';\nimport renderer from './sequenceRenderer.js';\nimport type { MermaidConfig } from '../../config.type.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  get db() {\n    return new SequenceDB();\n  },\n  renderer,\n  styles,\n  init: (cnf: MermaidConfig) => {\n    if (!cnf.sequence) {\n      cnf.sequence = {};\n    }\n    if (cnf.wrap) {\n      cnf.sequence.wrap = cnf.wrap;\n      setConfig({ sequence: { wrap: cnf.wrap } });\n    }\n  },\n};\n"], "mappings": "6eAyEA,IAAIA,GAAU,UAAU,CACxB,IAAIC,EAAEC,EAAA,SAASC,GAAEC,EAAEH,EAAEI,EAAE,CAAC,IAAIJ,EAAEA,GAAG,CAAC,EAAEI,EAAEF,GAAE,OAAOE,IAAIJ,EAAEE,GAAEE,CAAC,CAAC,EAAED,EAAE,CAAC,OAAOH,CAAC,EAAhE,KAAkEK,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,GAAI,CAAC,GAAG,GAAG,EAAE,EAAEC,GAAI,CAAC,EAAE,GAAG,EACn8B9C,GAAS,CAAC,MAAOE,EAAA,UAAkB,CAAE,EAApB,SACrB,GAAI,CAAC,EACL,SAAU,CAAC,MAAQ,EAAE,MAAQ,EAAE,MAAQ,EAAE,QAAU,EAAE,GAAK,EAAE,SAAW,EAAE,KAAO,EAAE,UAAY,EAAE,YAAc,GAAG,SAAW,GAAG,sBAAwB,GAAG,OAAS,GAAG,IAAM,GAAG,WAAa,GAAG,IAAM,GAAG,OAAS,GAAG,WAAa,GAAG,IAAM,GAAG,IAAM,GAAG,SAAW,GAAG,MAAQ,GAAG,WAAa,GAAG,eAAiB,GAAG,gBAAkB,GAAG,eAAiB,GAAG,qBAAuB,GAAG,kBAAoB,GAAG,MAAQ,GAAG,aAAe,GAAG,UAAY,GAAG,gBAAkB,GAAG,UAAY,GAAG,gBAAkB,GAAG,0BAA4B,GAAG,KAAO,GAAG,KAAO,GAAG,IAAM,GAAG,IAAM,GAAG,cAAgB,GAAG,IAAM,GAAG,aAAe,GAAG,SAAW,GAAG,SAAW,GAAG,gBAAkB,GAAG,MAAQ,GAAG,OAAS,GAAG,IAAM,GAAG,KAAO,GAAG,YAAc,GAAG,GAAK,GAAG,kBAAoB,GAAG,QAAU,GAAG,KAAO,GAAG,UAAY,GAAG,MAAQ,GAAG,KAAO,GAAG,WAAa,GAAG,MAAQ,GAAG,KAAO,GAAG,WAAa,GAAG,QAAU,GAAG,UAAY,GAAG,IAAI,GAAG,QAAU,GAAG,SAAW,GAAG,WAAa,GAAG,IAAI,GAAG,IAAI,GAAG,MAAQ,GAAG,iBAAmB,GAAG,kBAAoB,GAAG,YAAc,GAAG,0BAA4B,GAAG,aAAe,GAAG,2BAA6B,GAAG,YAAc,GAAG,aAAe,GAAG,YAAc,GAAG,aAAe,GAAG,IAAM,GAAG,QAAU,EAAE,KAAO,CAAC,EACzrC,WAAY,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,GAAG,SAAS,GAAG,MAAM,GAAG,aAAa,GAAG,MAAM,GAAG,aAAa,GAAG,MAAM,GAAG,MAAM,GAAG,WAAW,GAAG,aAAa,GAAG,QAAQ,GAAG,eAAe,GAAG,YAAY,GAAG,kBAAkB,GAAG,YAAY,GAAG,kBAAkB,GAAG,4BAA4B,GAAG,OAAO,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,WAAW,GAAG,WAAW,GAAG,QAAQ,GAAG,SAAS,GAAG,MAAM,GAAG,OAAO,GAAG,cAAc,GAAG,KAAK,GAAG,oBAAoB,GAAG,UAAU,GAAG,OAAO,GAAG,OAAO,GAAG,QAAQ,GAAG,OAAO,GAAG,aAAa,GAAG,UAAU,GAAG,IAAI,GAAG,UAAU,GAAG,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,QAAQ,GAAG,mBAAmB,GAAG,oBAAoB,GAAG,cAAc,GAAG,4BAA4B,GAAG,eAAe,GAAG,6BAA6B,GAAG,cAAc,GAAG,eAAe,GAAG,cAAc,GAAG,eAAe,GAAG,KAAK,EACtzB,aAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EACrhB,cAAeA,EAAA,SAAmB6C,EAAQC,EAAQC,EAAUC,EAAIC,EAAyBC,EAAiBC,GAAiB,CAG3H,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAS,CACjB,IAAK,GACJ,OAAAD,EAAG,MAAME,EAAGE,CAAE,CAAC,EAASF,EAAGE,CAAE,EAC9B,MACA,IAAK,GAAG,IAAK,GACZ,KAAK,EAAI,CAAC,EACX,MACA,IAAK,GAAG,IAAK,IACbF,EAAGE,EAAG,CAAC,EAAE,KAAKF,EAAGE,CAAE,CAAC,EAAE,KAAK,EAAIF,EAAGE,EAAG,CAAC,EACtC,MACA,IAAK,GAAG,IAAK,GAAG,IAAK,IAAI,IAAK,IAC7B,KAAK,EAAIF,EAAGE,CAAE,EACf,MACA,IAAK,GAAG,IAAK,IACZ,KAAK,EAAE,CAAC,EACT,MACA,IAAK,IACLF,EAAGE,CAAE,EAAE,KAAK,oBAAqB,KAAK,EAAEF,EAAGE,CAAE,EAC7C,MACA,IAAK,IAEHF,EAAGE,EAAG,CAAC,EAAE,QAAQ,CAAC,KAAM,WAAY,QAAQJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,CAAE,CAAC,EACvEF,EAAGE,EAAG,CAAC,EAAE,KAAK,CAAC,KAAM,SAAU,QAAQF,EAAGE,EAAG,CAAC,CAAC,CAAC,EAChD,KAAK,EAAEF,EAAGE,EAAG,CAAC,EAChB,MACA,IAAK,IACJ,KAAK,EAAG,CAAC,KAAK,gBAAgB,cAAe,OAAOF,EAAGE,EAAG,CAAC,CAAC,EAAG,kBAAkB,OAAOF,EAAGE,EAAG,CAAC,CAAC,EAAG,gBAAgB,GAAM,WAAWJ,EAAG,SAAS,UAAU,EAC3J,MACA,IAAK,IACJ,KAAK,EAAI,CAAC,KAAK,gBAAgB,cAAe,OAAOE,EAAGE,EAAG,CAAC,CAAC,EAAG,kBAAkB,EAAG,gBAAgB,GAAM,WAAWJ,EAAG,SAAS,UAAU,EAC7I,MACA,IAAK,IACJ,KAAK,EAAI,CAAC,KAAK,gBAAiB,gBAAgB,GAAO,WAAWA,EAAG,SAAS,UAAU,EACzF,MACA,IAAK,IACL,KAAK,EAAI,CAAC,KAAK,gBAAiB,gBAAgB,GAAM,WAAWA,EAAG,SAAS,UAAU,EACvF,MACA,IAAK,IACL,KAAK,EAAE,CAAC,KAAM,cAAe,WAAYA,EAAG,SAAS,aAAc,MAAOE,EAAGE,EAAG,CAAC,EAAE,KAAK,EACxF,MACA,IAAK,IACL,KAAK,EAAE,CAAC,KAAM,YAAa,WAAYJ,EAAG,SAAS,WAAY,MAAOE,EAAGE,EAAG,CAAC,EAAE,KAAK,EACpF,MACA,IAAK,IACLJ,EAAG,gBAAgBE,EAAGE,CAAE,EAAE,UAAU,CAAC,CAAC,EAAE,KAAK,EAAEF,EAAGE,CAAE,EAAE,UAAU,CAAC,EACjE,MACA,IAAK,IACLJ,EAAG,gBAAgBE,EAAGE,CAAE,EAAE,UAAU,CAAC,CAAC,EAAE,KAAK,EAAEF,EAAGE,CAAE,EAAE,UAAU,CAAC,EACjE,MACA,IAAK,IACJ,KAAK,EAAEF,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,YAAY,KAAK,CAAC,EAC3C,MACA,IAAK,IAAI,IAAK,IACb,KAAK,EAAEE,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,kBAAkB,KAAK,CAAC,EACjD,MACA,IAAK,IAEHE,EAAGE,EAAG,CAAC,EAAE,QAAQ,CAAC,KAAM,YAAa,SAASJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,UAAU,CAAC,EAC5GE,EAAGE,EAAG,CAAC,EAAE,KAAK,CAAC,KAAM,UAAW,SAASF,EAAGE,EAAG,CAAC,EAAG,WAAYJ,EAAG,SAAS,QAAQ,CAAC,EACpF,KAAK,EAAEE,EAAGE,EAAG,CAAC,EAChB,MACA,IAAK,IAEHF,EAAGE,EAAG,CAAC,EAAE,QAAQ,CAAC,KAAM,YAAa,MAAMJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,UAAW,CAAC,EAC1GE,EAAGE,EAAG,CAAC,EAAE,KAAK,CAAC,KAAM,UAAW,MAAMJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,QAAS,CAAC,EACnG,KAAK,EAAEE,EAAGE,EAAG,CAAC,EAChB,MACA,IAAK,IAEHF,EAAGE,EAAG,CAAC,EAAE,QAAQ,CAAC,KAAM,WAAY,QAAQJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,SAAS,CAAC,EACzGE,EAAGE,EAAG,CAAC,EAAE,KAAK,CAAC,KAAM,SAAU,QAAQJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,OAAO,CAAC,EAClG,KAAK,EAAEE,EAAGE,EAAG,CAAC,EAChB,MACA,IAAK,IAGHF,EAAGE,EAAG,CAAC,EAAE,QAAQ,CAAC,KAAM,WAAY,QAAQJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,SAAS,CAAC,EAGzGE,EAAGE,EAAG,CAAC,EAAE,KAAK,CAAC,KAAM,SAAU,WAAYJ,EAAG,SAAS,OAAO,CAAC,EAC/D,KAAK,EAAEE,EAAGE,EAAG,CAAC,EAChB,MACA,IAAK,IAGHF,EAAGE,EAAG,CAAC,EAAE,QAAQ,CAAC,KAAM,WAAY,QAAQJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,SAAS,CAAC,EAGzGE,EAAGE,EAAG,CAAC,EAAE,KAAK,CAAC,KAAM,SAAU,WAAYJ,EAAG,SAAS,OAAO,CAAC,EAC/D,KAAK,EAAEE,EAAGE,EAAG,CAAC,EAChB,MACA,IAAK,IAGHF,EAAGE,EAAG,CAAC,EAAE,QAAQ,CAAC,KAAM,WAAY,QAAQJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,cAAc,CAAC,EAG9GE,EAAGE,EAAG,CAAC,EAAE,KAAK,CAAC,KAAM,SAAU,WAAYJ,EAAG,SAAS,OAAO,CAAC,EAC/D,KAAK,EAAEE,EAAGE,EAAG,CAAC,EAChB,MACA,IAAK,IAGHF,EAAGE,EAAG,CAAC,EAAE,QAAQ,CAAC,KAAM,gBAAiB,aAAaJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,cAAc,CAAC,EAGxHE,EAAGE,EAAG,CAAC,EAAE,KAAK,CAAC,KAAM,cAAe,WAAYJ,EAAG,SAAS,YAAY,CAAC,EACzE,KAAK,EAAEE,EAAGE,EAAG,CAAC,EAChB,MACA,IAAK,IAEHF,EAAGE,EAAG,CAAC,EAAE,QAAQ,CAAC,KAAM,aAAc,UAAUJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,WAAW,CAAC,EAC/GE,EAAGE,EAAG,CAAC,EAAE,KAAK,CAAC,KAAM,WAAY,QAAQJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,SAAS,CAAC,EACtG,KAAK,EAAEE,EAAGE,EAAG,CAAC,EAChB,MACA,IAAK,IACJ,KAAK,EAAIF,EAAGE,EAAG,CAAC,EAAE,OAAO,CAAC,CAAC,KAAM,SAAU,WAAWJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,eAAe,EAAGE,EAAGE,CAAE,CAAC,CAAC,EACnI,MACA,IAAK,IACJ,KAAK,EAAIF,EAAGE,EAAG,CAAC,EAAE,OAAO,CAAC,CAAC,KAAM,MAAO,QAAQJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,OAAO,EAAGE,EAAGE,CAAE,CAAC,CAAC,EACrH,MACA,IAAK,IACJ,KAAK,EAAIF,EAAGE,EAAG,CAAC,EAAE,OAAO,CAAC,CAAC,KAAM,OAAQ,QAAQJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,WAAYJ,EAAG,SAAS,QAAQ,EAAGE,EAAGE,CAAE,CAAC,CAAC,EACvH,MACA,IAAK,IACLF,EAAGE,EAAG,CAAC,EAAE,KAAK,cAAeF,EAAGE,EAAG,CAAC,EAAE,KAAK,iBAAiBF,EAAGE,EAAG,CAAC,EAAE,YAAYJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,KAAK,EAAEF,EAAGE,EAAG,CAAC,EAC1H,MACA,IAAK,IACLF,EAAGE,EAAG,CAAC,EAAE,KAAK,cAAeF,EAAGE,EAAG,CAAC,EAAE,KAAK,iBAAiB,KAAK,EAAEF,EAAGE,EAAG,CAAC,EAC1E,MACA,IAAK,IACLF,EAAGE,EAAG,CAAC,EAAE,KAAK,QAASF,EAAGE,EAAG,CAAC,EAAE,KAAK,iBAAiBF,EAAGE,EAAG,CAAC,EAAE,YAAYJ,EAAG,aAAaE,EAAGE,EAAG,CAAC,CAAC,EAAG,KAAK,EAAEF,EAAGE,EAAG,CAAC,EACpH,MACA,IAAK,IACLF,EAAGE,EAAG,CAAC,EAAE,KAAK,QAASF,EAAGE,EAAG,CAAC,EAAE,KAAK,iBAAkB,KAAK,EAAEF,EAAGE,EAAG,CAAC,EACrE,MACA,IAAK,IACLF,EAAGE,EAAG,CAAC,EAAE,KAAK,qBAAsB,KAAK,EAAEF,EAAGE,EAAG,CAAC,EAClD,MACA,IAAK,IAEH,KAAK,EAAI,CAACF,EAAGE,EAAG,CAAC,EAAG,CAAC,KAAK,UAAW,UAAUF,EAAGE,EAAG,CAAC,EAAG,MAAMF,EAAGE,EAAG,CAAC,EAAE,MAAO,KAAKF,EAAGE,CAAE,CAAC,CAAC,EAC7F,MACA,IAAK,IAGHF,EAAGE,EAAG,CAAC,EAAI,CAAC,EAAE,OAAOF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,EAAG,CAAC,CAAC,EAAE,MAAM,EAAG,CAAC,EACnDF,EAAGE,EAAG,CAAC,EAAE,CAAC,EAAIF,EAAGE,EAAG,CAAC,EAAE,CAAC,EAAE,MAC1BF,EAAGE,EAAG,CAAC,EAAE,CAAC,EAAIF,EAAGE,EAAG,CAAC,EAAE,CAAC,EAAE,MAC1B,KAAK,EAAI,CAACF,EAAGE,EAAG,CAAC,EAAG,CAAC,KAAK,UAAW,UAAUJ,EAAG,UAAU,KAAM,MAAME,EAAGE,EAAG,CAAC,EAAE,MAAM,EAAG,CAAC,EAAG,KAAKF,EAAGE,CAAE,CAAC,CAAC,EAC5G,MACA,IAAK,IAEH,KAAK,EAAI,CAACF,EAAGE,EAAG,CAAC,EAAG,CAAC,KAAK,WAAY,MAAMF,EAAGE,EAAG,CAAC,EAAE,MAAO,KAAKF,EAAGE,CAAE,CAAC,CAAC,EAE1E,MACA,IAAK,IAEH,KAAK,EAAI,CAACF,EAAGE,EAAG,CAAC,EAAG,CAAC,KAAK,WAAY,MAAMF,EAAGE,EAAG,CAAC,EAAE,MAAO,KAAKF,EAAGE,CAAE,CAAC,CAAC,EAE1E,MACA,IAAK,IAEH,KAAK,EAAI,CAACF,EAAGE,EAAG,CAAC,EAAG,CAAC,KAAK,gBAAiB,MAAMF,EAAGE,EAAG,CAAC,EAAE,MAAO,KAAKF,EAAGE,CAAE,CAAC,CAAC,EAE/E,MACA,IAAK,IAEH,KAAK,EAAI,CAACF,EAAGE,EAAG,CAAC,EAAG,CAAC,KAAK,aAAc,MAAMF,EAAGE,EAAG,CAAC,EAAE,MAAO,KAAKF,EAAGE,CAAE,CAAC,CAAC,EAE5E,MACA,IAAK,IACJ,KAAK,EAAI,CAACF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAC3B,MACA,IAAK,IACJ,KAAK,EAAIF,EAAGE,CAAE,EACf,MACA,IAAK,IACJ,KAAK,EAAIJ,EAAG,UAAU,OACvB,MACA,IAAK,IACJ,KAAK,EAAIA,EAAG,UAAU,QACvB,MACA,IAAK,IACJ,KAAK,EAAI,CAACE,EAAGE,EAAG,CAAC,EAAEF,EAAGE,EAAG,CAAC,EAAE,CAAC,KAAM,aAAc,KAAKF,EAAGE,EAAG,CAAC,EAAE,MAAO,GAAGF,EAAGE,EAAG,CAAC,EAAE,MAAO,WAAWF,EAAGE,EAAG,CAAC,EAAG,IAAIF,EAAGE,CAAE,EAAG,SAAU,EAAI,EAC1H,CAAC,KAAM,cAAe,WAAYJ,EAAG,SAAS,aAAc,MAAOE,EAAGE,EAAG,CAAC,EAAE,KAAK,CAClF,EACd,MACA,IAAK,IACJ,KAAK,EAAI,CAACF,EAAGE,EAAG,CAAC,EAAEF,EAAGE,EAAG,CAAC,EAAE,CAAC,KAAM,aAAc,KAAKF,EAAGE,EAAG,CAAC,EAAE,MAAO,GAAGF,EAAGE,EAAG,CAAC,EAAE,MAAO,WAAWF,EAAGE,EAAG,CAAC,EAAG,IAAIF,EAAGE,CAAE,CAAC,EAC3G,CAAC,KAAM,YAAa,WAAYJ,EAAG,SAAS,WAAY,MAAOE,EAAGE,EAAG,CAAC,EAAE,KAAK,CAC7E,EACd,MACA,IAAK,IACJ,KAAK,EAAI,CAACF,EAAGE,EAAG,CAAC,EAAEF,EAAGE,EAAG,CAAC,EAAE,CAAC,KAAM,aAAc,KAAKF,EAAGE,EAAG,CAAC,EAAE,MAAO,GAAGF,EAAGE,EAAG,CAAC,EAAE,MAAO,WAAWF,EAAGE,EAAG,CAAC,EAAG,IAAIF,EAAGE,CAAE,CAAC,CAAC,EAC1H,MACA,IAAK,IACL,KAAK,EAAE,CAAE,KAAM,iBAAkB,MAAMF,EAAGE,CAAE,CAAC,EAC7C,MACA,IAAK,IACJ,KAAK,EAAIJ,EAAG,SAAS,WACtB,MACA,IAAK,IACJ,KAAK,EAAIA,EAAG,SAAS,YACtB,MACA,IAAK,IACJ,KAAK,EAAIA,EAAG,SAAS,MACtB,MACA,IAAK,IACJ,KAAK,EAAIA,EAAG,SAAS,oBACtB,MACA,IAAK,IACJ,KAAK,EAAIA,EAAG,SAAS,OACtB,MACA,IAAK,IACJ,KAAK,EAAIA,EAAG,SAAS,qBACtB,MACA,IAAK,IACJ,KAAK,EAAIA,EAAG,SAAS,YACtB,MACA,IAAK,IACJ,KAAK,EAAIA,EAAG,SAAS,aACtB,MACA,IAAK,IACJ,KAAK,EAAIA,EAAG,SAAS,YACtB,MACA,IAAK,IACJ,KAAK,EAAIA,EAAG,SAAS,aACtB,MACA,IAAK,IACL,KAAK,EAAIA,EAAG,aAAaE,EAAGE,CAAE,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,EACnD,KACA,CACA,EA7Oe,aA8Of,MAAO,CAAC,CAAC,EAAE,EAAE,EAAEhD,EAAI,EAAEC,EAAI,EAAEC,CAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAEF,EAAI,EAAEC,EAAI,EAAEC,CAAG,EAAE,CAAC,EAAE,EAAE,EAAEF,EAAI,EAAEC,EAAI,EAAEC,CAAG,EAAEP,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEQ,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAEC,EAAI,EAAEC,EAAI,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,CAAG,EAAErC,EAAEsC,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG3B,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,CAAG,EAAErC,EAAEsC,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAGT,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAGM,CAAG,EAAE,CAAC,GAAG,GAAG,GAAGA,CAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAErC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAGD,CAAG,EAAE,CAAC,GAAG,GAAG,GAAGA,CAAG,EAAE,CAAC,GAAG,GAAG,GAAGA,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAGA,CAAG,EAAE,CAAC,GAAG,GAAG,GAAGA,CAAG,EAAE,CAAC,GAAG,GAAG,GAAGA,CAAG,EAAE,CAAC,GAAG,GAAG,GAAGA,CAAG,EAAErC,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAEA,EAAEsC,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEuC,EAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAEvC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEwC,EAAIhC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAER,EAAEwC,EAAIhC,EAAI,CAAC,EAAE,GAAG,CAAC,EAAER,EAAEwC,EAAIhC,EAAI,CAAC,EAAE,GAAG,CAAC,EAAER,EAAEyC,GAAIjC,EAAI,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,EAAER,EAAE0C,EAAIlC,EAAI,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,EAAER,EAAE0C,EAAIlC,EAAI,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,EAAER,EAAE2C,GAAInC,EAAI,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,EAAER,EAAEwC,EAAIhC,EAAI,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG6B,CAAG,EAAErC,EAAE4C,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE5C,EAAE4C,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE5C,EAAE4C,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE5C,EAAE4C,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE5C,EAAE4C,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE5C,EAAE4C,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE5C,EAAE4C,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE5C,EAAE4C,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE5C,EAAE4C,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE5C,EAAE4C,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGP,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,GAAGA,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGQ,EAAG,EAAE,CAAC,GAAG,IAAI,GAAGA,EAAG,EAAE,CAAC,GAAG,IAAI,GAAGA,EAAG,EAAE,CAAC,GAAG,IAAI,GAAGA,EAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,GAAGhB,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE/B,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE7B,EAAI,EAAEC,EAAI,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,CAAG,EAAE,CAAC,EAAE5B,EAAI,EAAEC,EAAI,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,CAAG,EAAE,CAAC,EAAE5B,EAAI,EAAEC,EAAI,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE5B,EAAI,EAAEC,EAAI,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAG,CAAC,EAAE,GAAG,EAAE,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE5B,EAAI,EAAEC,EAAI,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAG,CAAC,EAAE,GAAG,EAAE,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE5B,EAAI,EAAEC,EAAI,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAG,CAAC,EAAE,GAAG,EAAE,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,CAAG,EAAE,CAAC,EAAE5B,EAAI,EAAEC,EAAI,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAErC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGD,CAAG,EAAE,CAAC,GAAG,IAAI,GAAGA,CAAG,EAAE,CAAC,GAAG,IAAI,GAAGQ,EAAG,EAAE,CAAC,GAAG,IAAI,GAAGA,EAAG,EAAE,CAAC,GAAG,IAAI,GAAGA,EAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE7C,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEuC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGV,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE/B,EAAEuC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvC,EAAEuC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGO,EAAG,EAAE,CAAC,GAAG,IAAI,GAAGA,EAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGR,CAAG,EAAErC,EAAEuC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvC,EAAEyC,GAAIjC,EAAI,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,EAAER,EAAE0C,EAAIlC,EAAI,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,EAAER,EAAE2C,GAAInC,EAAI,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,EAAER,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEtC,EAAEsC,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EACz8J,eAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,EAC9M,WAAYrC,EAAA,SAAqBqD,EAAKC,EAAM,CACxC,GAAIA,EAAK,YACL,KAAK,MAAMD,CAAG,MACX,CACH,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACV,CACJ,EARY,cASZ,MAAOvD,EAAA,SAAewD,EAAO,CACzB,IAAIC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAC,EAAGC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAC,EAAGC,GAAQ,KAAK,MAAOjB,EAAS,GAAIE,GAAW,EAAGD,GAAS,EAAGiB,GAAa,EAAGC,GAAS,EAAGC,GAAM,EAClKC,GAAOL,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCM,EAAQ,OAAO,OAAO,KAAK,KAAK,EAChCC,GAAc,CAAE,GAAI,CAAC,CAAE,EAC3B,QAASnE,MAAK,KAAK,GACX,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,EAAC,IAC/CmE,GAAY,GAAGnE,EAAC,EAAI,KAAK,GAAGA,EAAC,GAGrCkE,EAAM,SAASX,EAAOY,GAAY,EAAE,EACpCA,GAAY,GAAG,MAAQD,EACvBC,GAAY,GAAG,OAAS,KACpB,OAAOD,EAAM,OAAU,MACvBA,EAAM,OAAS,CAAC,GAEpB,IAAIE,GAAQF,EAAM,OAClBN,EAAO,KAAKQ,EAAK,EACjB,IAAIC,GAASH,EAAM,SAAWA,EAAM,QAAQ,OACxC,OAAOC,GAAY,GAAG,YAAe,WACrC,KAAK,WAAaA,GAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAElD,SAASG,GAASC,EAAG,CACjBd,EAAM,OAASA,EAAM,OAAS,EAAIc,EAClCZ,EAAO,OAASA,EAAO,OAASY,EAChCX,EAAO,OAASA,EAAO,OAASW,CACpC,CAJSxE,EAAAuE,GAAA,YAKD,SAASE,IAAM,CACf,IAAIC,EACJ,OAAAA,EAAQf,EAAO,IAAI,GAAKQ,EAAM,IAAI,GAAKF,GACnC,OAAOS,GAAU,WACbA,aAAiB,QACjBf,EAASe,EACTA,EAAQf,EAAO,IAAI,GAEvBe,EAAQjB,EAAK,SAASiB,CAAK,GAAKA,GAE7BA,CACX,CAXa1E,EAAAyE,GAAA,OAajB,QADIE,EAAQC,GAAgBC,GAAOC,GAAQC,GAAGC,GAAGC,GAAQ,CAAC,EAAGC,GAAGC,GAAKC,GAAUC,KAClE,CAUT,GATAR,GAAQnB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAemB,EAAK,EACzBC,GAAS,KAAK,eAAeD,EAAK,IAE9BF,IAAW,MAAQ,OAAOA,EAAU,OACpCA,EAASF,GAAI,GAEjBK,GAAShB,GAAMe,EAAK,GAAKf,GAAMe,EAAK,EAAEF,CAAM,GAE5C,OAAOG,GAAW,KAAe,CAACA,GAAO,QAAU,CAACA,GAAO,CAAC,EAAG,CAC/D,IAAIQ,GAAS,GACbD,GAAW,CAAC,EACZ,IAAKH,MAAKpB,GAAMe,EAAK,EACb,KAAK,WAAWK,EAAC,GAAKA,GAAIlB,IAC1BqB,GAAS,KAAK,IAAO,KAAK,WAAWH,EAAC,EAAI,GAAI,EAGlDf,EAAM,aACNmB,GAAS,wBAA0BvC,GAAW,GAAK;AAAA,EAAQoB,EAAM,aAAa,EAAI;AAAA,YAAiBkB,GAAS,KAAK,IAAI,EAAI,WAAc,KAAK,WAAWV,CAAM,GAAKA,GAAU,IAE5KW,GAAS,wBAA0BvC,GAAW,GAAK,iBAAmB4B,GAAUV,GAAM,eAAiB,KAAQ,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAExJ,KAAK,WAAWW,GAAQ,CACpB,KAAMnB,EAAM,MACZ,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAM,SACZ,IAAKE,GACL,SAAUgB,EACd,CAAC,CACL,CACA,GAAIP,GAAO,CAAC,YAAa,OAASA,GAAO,OAAS,EAC9C,MAAM,IAAI,MAAM,oDAAsDD,GAAQ,YAAcF,CAAM,EAEtG,OAAQG,GAAO,CAAC,EAAG,CACnB,IAAK,GACDpB,EAAM,KAAKiB,CAAM,EACjBf,EAAO,KAAKO,EAAM,MAAM,EACxBN,EAAO,KAAKM,EAAM,MAAM,EACxBT,EAAM,KAAKoB,GAAO,CAAC,CAAC,EACpBH,EAAS,KACJC,IASDD,EAASC,GACTA,GAAiB,OATjB9B,GAASqB,EAAM,OACftB,EAASsB,EAAM,OACfpB,GAAWoB,EAAM,SACjBE,GAAQF,EAAM,OACVJ,GAAa,GACbA,MAMR,MACJ,IAAK,GAwBD,GAvBAoB,GAAM,KAAK,aAAaL,GAAO,CAAC,CAAC,EAAE,CAAC,EACpCG,GAAM,EAAIrB,EAAOA,EAAO,OAASuB,EAAG,EACpCF,GAAM,GAAK,CACP,WAAYpB,EAAOA,EAAO,QAAUsB,IAAO,EAAE,EAAE,WAC/C,UAAWtB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUsB,IAAO,EAAE,EAAE,aACjD,YAAatB,EAAOA,EAAO,OAAS,CAAC,EAAE,WAC3C,EACIS,KACAW,GAAM,GAAG,MAAQ,CACbpB,EAAOA,EAAO,QAAUsB,IAAO,EAAE,EAAE,MAAM,CAAC,EAC1CtB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACrC,GAEJmB,GAAI,KAAK,cAAc,MAAMC,GAAO,CAChCpC,EACAC,GACAC,GACAqB,GAAY,GACZU,GAAO,CAAC,EACRlB,EACAC,CACJ,EAAE,OAAOK,EAAI,CAAC,EACV,OAAOc,GAAM,IACb,OAAOA,GAEPG,KACAzB,EAAQA,EAAM,MAAM,EAAG,GAAKyB,GAAM,CAAC,EACnCvB,EAASA,EAAO,MAAM,EAAG,GAAKuB,EAAG,EACjCtB,EAASA,EAAO,MAAM,EAAG,GAAKsB,EAAG,GAErCzB,EAAM,KAAK,KAAK,aAAaoB,GAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ClB,EAAO,KAAKqB,GAAM,CAAC,EACnBpB,EAAO,KAAKoB,GAAM,EAAE,EACpBG,GAAWtB,GAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAK0B,EAAQ,EACnB,MACJ,IAAK,GACD,MAAO,EACX,CACJ,CACA,MAAO,EACX,EA3IO,QA2IN,EAGGjB,GAAS,UAAU,CACvB,IAAIA,GAAS,CAEb,IAAI,EAEJ,WAAWnE,EAAA,SAAoBqD,EAAKC,EAAM,CAClC,GAAI,KAAK,GAAG,OACR,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAE3B,EANO,cASX,SAASrD,EAAA,SAAUwD,EAAOR,EAAI,CACtB,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAC,EAC5B,KAAK,OAASQ,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACV,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACjB,EACI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,EAAE,CAAC,GAE5B,KAAK,OAAS,EACP,IACX,EAlBK,YAqBT,MAAMxD,EAAA,UAAY,CACV,IAAIuF,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACA,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEZ,KAAK,QAAQ,QACb,KAAK,OAAO,MAAM,CAAC,IAGvB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACX,EApBE,SAuBN,MAAMvF,EAAA,SAAUuF,EAAI,CACZ,IAAIJ,EAAMI,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EAEpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASJ,CAAG,EAE5D,KAAK,QAAUA,EACf,IAAIM,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EAEzDD,EAAM,OAAS,IACf,KAAK,UAAYA,EAAM,OAAS,GAEpC,IAAIR,EAAI,KAAK,OAAO,MAEpB,YAAK,OAAS,CACV,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaQ,GACRA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAC5DA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAChE,KAAK,OAAO,aAAeL,CACjC,EAEI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAEvD,KAAK,OAAS,KAAK,OAAO,OACnB,IACX,EAhCE,SAmCN,KAAKnF,EAAA,UAAY,CACT,YAAK,MAAQ,GACN,IACX,EAHC,QAML,OAAOA,EAAA,UAAY,CACX,GAAI,KAAK,QAAQ,gBACb,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,aAAa,EAAG,CAC9N,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,EAGL,OAAO,IACX,EAZG,UAeP,KAAKA,EAAA,SAAUwE,EAAG,CACV,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAClC,EAFC,QAKL,UAAUxE,EAAA,UAAY,CACd,IAAI0F,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAM,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC7E,EAHM,aAMV,cAAc1F,EAAA,UAAY,CAClB,IAAI2F,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KACdA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAGA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAE,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CAClF,EANU,iBASd,aAAa3F,EAAA,UAAY,CACjB,IAAI4F,EAAM,KAAK,UAAU,EACrBC,EAAI,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAc,EAAI;AAAA,EAAOC,EAAI,GACnD,EAJS,gBAOb,WAAW7F,EAAA,SAAS8F,EAAOC,EAAc,CACjC,IAAIrB,EACAc,EACAQ,EAwDJ,GAtDI,KAAK,QAAQ,kBAEbA,EAAS,CACL,SAAU,KAAK,SACf,OAAQ,CACJ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC7B,EACA,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACf,EACI,KAAK,QAAQ,SACbA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAIvDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACA,KAAK,UAAYA,EAAM,QAE3B,KAAK,OAAS,CACV,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EACAA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAC5E,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MACpD,EACA,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAEhE,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBpB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMqB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SAClB,KAAK,KAAO,IAEZrB,EACA,OAAOA,EACJ,GAAI,KAAK,WAAY,CAExB,QAASzE,KAAK+F,EACV,KAAK/F,CAAC,EAAI+F,EAAO/F,CAAC,EAEtB,MAAO,EACX,CACA,MAAO,EACX,EArEO,cAwEX,KAAKD,EAAA,UAAY,CACT,GAAI,KAAK,KACL,OAAO,KAAK,IAEX,KAAK,SACN,KAAK,KAAO,IAGhB,IAAI0E,EACAoB,EACAG,EACAC,EACC,KAAK,QACN,KAAK,OAAS,GACd,KAAK,MAAQ,IAGjB,QADIC,EAAQ,KAAK,cAAc,EACtBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAE9B,GADAH,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMC,CAAC,CAAC,CAAC,EAC9CH,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGvD,GAFAA,EAAQG,EACRC,EAAQE,EACJ,KAAK,QAAQ,gBAAiB,CAE9B,GADA1B,EAAQ,KAAK,WAAWuB,EAAWE,EAAMC,CAAC,CAAC,EACvC1B,IAAU,GACV,OAAOA,EACJ,GAAI,KAAK,WAAY,CACxBoB,EAAQ,GACR,QACJ,KAEI,OAAO,EAEf,SAAW,CAAC,KAAK,QAAQ,KACrB,MAIZ,OAAIA,GACApB,EAAQ,KAAK,WAAWoB,EAAOK,EAAMD,CAAK,CAAC,EACvCxB,IAAU,GACHA,EAGJ,IAEP,KAAK,SAAW,GACT,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,aAAa,EAAG,CACpH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,CAET,EAvDC,QA0DL,IAAI1E,EAAA,UAAgB,CACZ,IAAIgF,EAAI,KAAK,KAAK,EAClB,OAAIA,GAGO,KAAK,IAAI,CAExB,EAPA,OAUJ,MAAMhF,EAAA,SAAgBqG,EAAW,CACzB,KAAK,eAAe,KAAKA,CAAS,CACtC,EAFE,SAKN,SAASrG,EAAA,UAAqB,CACtB,IAAIwE,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACG,KAAK,eAAe,IAAI,EAExB,KAAK,eAAe,CAAC,CAEpC,EAPK,YAUT,cAAcxE,EAAA,UAA0B,CAChC,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EACzE,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAE1C,EANU,iBASd,SAASA,EAAA,SAAmBwE,EAAG,CAEvB,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACE,KAAK,eAAeA,CAAC,EAErB,SAEf,EAPK,YAUT,UAAUxE,EAAA,SAAoBqG,EAAW,CACjC,KAAK,MAAMA,CAAS,CACxB,EAFM,aAKV,eAAerG,EAAA,UAA0B,CACjC,OAAO,KAAK,eAAe,MAC/B,EAFW,kBAGf,QAAS,CAAC,mBAAmB,EAAI,EACjC,cAAeA,EAAA,SAAmBgD,EAAGsD,EAAIC,EAA0BC,EAAU,CAC7E,IAAIC,EAAQD,EACZ,OAAOD,EAA2B,CAClC,IAAK,GAAE,MAAO,GAEd,IAAK,GACL,MACA,IAAK,GACL,MACA,IAAK,GACL,MACA,IAAK,GACL,MACA,IAAK,GACL,MACA,IAAK,GAAE,MAAO,IAEd,IAAK,GAAG,YAAK,MAAM,MAAM,EAAU,GACnC,MACA,IAAK,GAAG,YAAK,MAAM,IAAI,EAAU,GACjC,MACA,IAAK,GAAG,YAAK,MAAM,IAAI,EAAU,GACjC,MACA,IAAK,IAAG,MAAO,IAEf,IAAK,IAAI,YAAK,MAAM,IAAI,EAAU,GAClC,MACA,IAAK,IAAI,OAAAD,EAAI,OAASA,EAAI,OAAO,KAAK,EAAG,KAAK,MAAM,OAAO,EAAU,GACrE,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,SAAS,EAAG,KAAK,MAAM,MAAM,EAAU,GACtE,MACA,IAAK,IAAI,YAAK,SAAS,EAAG,KAAK,SAAS,EAAU,EAClD,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,GACjC,MACA,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAI,YAAK,MAAM,IAAI,EAAU,GAClC,MACA,IAAK,IAAI,YAAK,MAAM,IAAI,EAAU,GAClC,MACA,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAI,YAAK,MAAM,WAAW,EAAS,GACxC,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,kBACjC,MACA,IAAK,IAAI,YAAK,MAAM,WAAW,EAAS,GACxC,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,kBACjC,MACA,IAAK,IAAI,KAAK,MAAM,qBAAqB,EACzC,MACA,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAG,MAAO,4BAEf,IAAK,IAAG,MAAO,GAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,GAEf,IAAK,IAAI,OAAAA,EAAI,OAASA,EAAI,OAAO,KAAK,EAAU,GAChD,MACA,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,GAEf,IAAK,IAAG,MAAO,SAEf,CACA,EA9Ie,aA+If,MAAO,CAAC,cAAc,YAAY,oBAAoB,gBAAgB,sBAAsB,sBAAsB,yBAAyB,cAAc,sBAAsB,gBAAgB,iBAAiB,kBAAkB,sFAAsF,aAAa,aAAa,eAAe,eAAe,cAAc,cAAc,eAAe,cAAc,mBAAmB,cAAc,mBAAmB,iBAAiB,gBAAgB,qCAAqC,cAAc,kBAAkB,mBAAmB,gBAAgB,eAAe,qBAAqB,kBAAkB,eAAe,eAAe,mBAAmB,qBAAqB,wBAAwB,yBAAyB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,yBAAyB,aAAa,eAAe,0BAA0B,qBAAqB,cAAc,UAAU,UAAU,yEAAyE,YAAY,cAAc,aAAa,eAAe,WAAW,YAAY,aAAa,cAAc,cAAc,eAAe,kCAAkC,UAAU,WAAW,UAAU,UAAU,SAAS,EAC3xC,WAAY,CAAC,oBAAsB,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,GAAK,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,KAAO,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,UAAY,EAAK,EAAE,QAAU,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAI,CAAC,CACxf,EACA,OAAOnC,EACP,EAAG,EACHrE,GAAO,MAAQqE,GACf,SAASuC,IAAU,CACjB,KAAK,GAAK,CAAC,CACb,CAFS,OAAA1G,EAAA0G,GAAA,UAGTA,GAAO,UAAY5G,GAAOA,GAAO,OAAS4G,GACnC,IAAIA,EACX,EAAG,EACF5G,GAAO,OAASA,GAEhB,IAAO6G,GAAQC,GCz5BhB,IAAMC,GAAW,CACf,MAAO,EACP,OAAQ,EACR,KAAM,EACN,YAAa,EACb,aAAc,EACd,WAAY,EACZ,YAAa,EACb,WAAY,GACZ,SAAU,GACV,UAAW,GACX,SAAU,GACV,QAAS,GACT,UAAW,GACX,QAAS,GACT,aAAc,GACd,WAAY,GACZ,UAAW,GACX,QAAS,GACT,QAAS,GACT,WAAY,GACZ,SAAU,GACV,YAAa,GACb,aAAc,GACd,WAAY,GACZ,eAAgB,GAChB,gBAAiB,GACjB,aAAc,GACd,YAAa,GACb,UAAW,GACX,eAAgB,GAChB,oBAAqB,GACrB,qBAAsB,EACxB,EAEMC,GAAY,CAChB,OAAQ,EACR,KAAM,CACR,EAEMC,GAAY,CAChB,OAAQ,EACR,QAAS,EACT,KAAM,CACR,EAEaC,GAAN,KAAsC,CAgB3C,aAAc,CAfd,KAAiB,MAAQ,IAAIC,GAA+B,KAAO,CACjE,UAAW,OACX,OAAQ,IAAI,IACZ,cAAe,IAAI,IACnB,gBAAiB,IAAI,IACrB,MAAO,CAAC,EACR,SAAU,CAAC,EACX,MAAO,CAAC,EACR,uBAAwB,GACxB,YAAa,OACb,WAAY,OACZ,YAAa,OACb,cAAe,MACjB,EAAE,EA6iBF,KAAO,YAAcC,GACrB,KAAO,kBAAoBC,GAC3B,KAAO,gBAAkBC,GACzB,KAAO,YAAcC,GACrB,KAAO,kBAAoBC,GAC3B,KAAO,gBAAkBC,GA9iBvB,KAAK,MAAQ,KAAK,MAAM,KAAK,IAAI,EACjC,KAAK,aAAe,KAAK,aAAa,KAAK,IAAI,EAC/C,KAAK,aAAe,KAAK,aAAa,KAAK,IAAI,EAE/C,KAAK,MAAM,EAEX,KAAK,QAAQC,EAAU,EAAE,IAAI,EAC7B,KAAK,SAAWX,GAChB,KAAK,UAAYC,GACjB,KAAK,UAAYC,EACnB,CAzGF,MA6E6C,CAAAU,EAAA,mBA8BpC,OAAOC,EAAsD,CAClE,KAAK,MAAM,QAAQ,MAAM,KAAK,CAC5B,KAAMA,EAAK,KACX,KAAMA,EAAK,MAAQ,KAAK,SAAS,EACjC,KAAMA,EAAK,MACX,UAAW,CAAC,CACd,CAAC,EACD,KAAK,MAAM,QAAQ,WAAa,KAAK,MAAM,QAAQ,MAAM,MAAM,EAAE,EAAE,CAAC,CACtE,CAEO,SACLC,EACAC,EACAC,EACAC,EACA,CACA,IAAIC,EAAc,KAAK,MAAM,QAAQ,WAC/BC,EAAM,KAAK,MAAM,QAAQ,OAAO,IAAIL,CAAE,EAC5C,GAAIK,EAAK,CAEP,GAAI,KAAK,MAAM,QAAQ,YAAcA,EAAI,KAAO,KAAK,MAAM,QAAQ,aAAeA,EAAI,IACpF,MAAM,IAAI,MACR,yDAAyDA,EAAI,IAAI,iBAAiBA,EAAI,IAAI,IAAI,aAAa,KAAK,MAAM,QAAQ,WAAW,IAAI,qBAC/I,EAQF,GAJAD,EAAcC,EAAI,IAAMA,EAAI,IAAM,KAAK,MAAM,QAAQ,WACrDA,EAAI,IAAMD,EAGNC,GAAOJ,IAASI,EAAI,MAAQH,GAAe,KAC7C,MAEJ,CAsBA,GAnBIA,GAAa,MAAQ,OACvBA,EAAc,CAAE,KAAMD,EAAM,KAAAE,CAAK,IAE/BA,GAAQ,MAAQD,EAAY,MAAQ,QACtCA,EAAc,CAAE,KAAMD,EAAM,KAAAE,CAAK,GAGnC,KAAK,MAAM,QAAQ,OAAO,IAAIH,EAAI,CAChC,IAAKI,EACL,KAAMH,EACN,YAAaC,EAAY,KACzB,KAAMA,EAAY,MAAQ,KAAK,SAAS,EACxC,UAAW,KAAK,MAAM,QAAQ,UAC9B,MAAO,CAAC,EACR,WAAY,CAAC,EACb,SAAU,KACV,SAAU,KACV,KAAMC,GAAQ,aAChB,CAAC,EACG,KAAK,MAAM,QAAQ,UAAW,CAChC,IAAMG,EAAqB,KAAK,MAAM,QAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,SAAS,EACjFA,IACFA,EAAmB,UAAYN,EAEnC,CAEI,KAAK,MAAM,QAAQ,YACrB,KAAK,MAAM,QAAQ,WAAW,UAAU,KAAKA,CAAE,EAEjD,KAAK,MAAM,QAAQ,UAAYA,CACjC,CAEQ,gBAAgBO,EAAc,CACpC,IAAIC,EACAC,EAAQ,EACZ,GAAI,CAACF,EACH,MAAO,GAET,IAAKC,EAAI,EAAGA,EAAI,KAAK,MAAM,QAAQ,SAAS,OAAQA,IAEhD,KAAK,MAAM,QAAQ,SAASA,CAAC,EAAE,OAAS,KAAK,SAAS,cACtD,KAAK,MAAM,QAAQ,SAASA,CAAC,EAAE,OAASD,GAExCE,IAGA,KAAK,MAAM,QAAQ,SAASD,CAAC,EAAE,OAAS,KAAK,SAAS,YACtD,KAAK,MAAM,QAAQ,SAASA,CAAC,EAAE,OAASD,GAExCE,IAGJ,OAAOA,CACT,CAEO,WACLC,EACAC,EACAC,EACAC,EACA,CACA,KAAK,MAAM,QAAQ,SAAS,KAAK,CAC/B,GAAI,KAAK,MAAM,QAAQ,SAAS,OAAO,SAAS,EAChD,KAAMH,EACN,GAAIC,EACJ,QAASC,EAAQ,KACjB,KAAMA,EAAQ,MAAQ,KAAK,SAAS,EACpC,OAAQC,CACV,CAAC,CACH,CAEO,UACLH,EACAC,EACAC,EACAE,EACAC,EAAW,GACX,CACA,GAAID,IAAgB,KAAK,SAAS,YACpB,KAAK,gBAAgBJ,GAAU,EAAE,EACnC,EAAG,CAEX,IAAMM,EAAQ,IAAI,MAAM,iDAAmDN,EAAS,GAAG,EAGvF,MAAAM,EAAM,KAAO,CACX,KAAM,OACN,MAAO,OACP,KAAM,IACN,IAAK,CAAE,WAAY,EAAG,UAAW,EAAG,aAAc,EAAG,YAAa,CAAE,EACpE,SAAU,CAAC,sBAAsB,CACnC,EACMA,CACR,CAEF,YAAK,MAAM,QAAQ,SAAS,KAAK,CAC/B,GAAI,KAAK,MAAM,QAAQ,SAAS,OAAO,SAAS,EAChD,KAAMN,EACN,GAAIC,EACJ,QAASC,GAAS,MAAQ,GAC1B,KAAMA,GAAS,MAAQ,KAAK,SAAS,EACrC,KAAME,EACN,SAAAC,CACF,CAAC,EACM,EACT,CAEO,kBAAmB,CACxB,OAAO,KAAK,MAAM,QAAQ,MAAM,OAAS,CAC3C,CAEO,2BAA4B,CACjC,OAAO,KAAK,MAAM,QAAQ,MAAM,KAAME,GAAMA,EAAE,IAAI,CACpD,CAEO,aAAc,CACnB,OAAO,KAAK,MAAM,QAAQ,QAC5B,CAEO,UAAW,CAChB,OAAO,KAAK,MAAM,QAAQ,KAC5B,CACO,WAAY,CACjB,OAAO,KAAK,MAAM,QAAQ,MAC5B,CACO,kBAAmB,CACxB,OAAO,KAAK,MAAM,QAAQ,aAC5B,CACO,oBAAqB,CAC1B,OAAO,KAAK,MAAM,QAAQ,eAC5B,CACO,SAASjB,EAAY,CAE1B,OAAO,KAAK,MAAM,QAAQ,OAAO,IAAIA,CAAE,CACzC,CACO,cAAe,CACpB,MAAO,CAAC,GAAG,KAAK,MAAM,QAAQ,OAAO,KAAK,CAAC,CAC7C,CACO,uBAAwB,CAC7B,KAAK,MAAM,QAAQ,uBAAyB,EAC9C,CACO,wBAAyB,CAC9B,KAAK,MAAM,QAAQ,uBAAyB,EAC9C,CACO,qBAAsB,CAC3B,OAAO,KAAK,MAAM,QAAQ,sBAC5B,CAEO,QAAQkB,EAAuB,CACpC,KAAK,MAAM,QAAQ,YAAcA,CACnC,CAEQ,YAAYC,EAAyD,CAC3E,GAAIA,IAAS,OACX,MAAO,CAAC,EAEVA,EAAOA,EAAK,KAAK,EACjB,IAAMC,EACJ,WAAW,KAAKD,CAAI,IAAM,KAAO,GAAO,aAAa,KAAKA,CAAI,IAAM,KAAO,GAAQ,OAErF,MAAO,CAAE,aADYC,IAAS,OAAYD,EAAOA,EAAK,QAAQ,kBAAmB,EAAE,GAAG,KAAK,EACrE,KAAAC,CAAK,CAC7B,CAEO,UAAW,CAGhB,OAAI,KAAK,MAAM,QAAQ,cAAgB,OAC9B,KAAK,MAAM,QAAQ,YAErBvB,EAAU,EAAE,UAAU,MAAQ,EACvC,CAEO,OAAQ,CACb,KAAK,MAAM,MAAM,EACjBwB,GAAY,CACd,CAEO,aAAaC,EAAa,CAC/B,IAAMC,EAAaD,EAAI,KAAK,EACtB,CAAE,KAAAF,EAAM,YAAAI,CAAY,EAAI,KAAK,YAAYD,CAAU,EACnDX,EAAU,CACd,KAAMY,EACN,KAAAJ,CACF,EACA,OAAAK,EAAI,MAAM,iBAAiB,KAAK,UAAUb,CAAO,CAAC,EAAE,EAC7CA,CACT,CAKO,aAAaU,EAAa,CAC/B,IAAMI,EAAQ,uCAAuC,KAAKJ,CAAG,EACzDK,EAAQD,IAAQ,CAAC,EAAIA,EAAM,CAAC,EAAE,KAAK,EAAI,cACvCE,EAAQF,IAAQ,CAAC,EAAIA,EAAM,CAAC,EAAE,KAAK,EAAI,OAG3C,GAAI,QAAQ,IACL,OAAO,IAAI,SAAS,QAASC,CAAK,IACrCA,EAAQ,cACRC,EAAQN,EAAI,KAAK,OAEd,CACL,IAAMO,EAAQ,IAAI,OAAO,EAAE,MAC3BA,EAAM,MAAQF,EACVE,EAAM,QAAUF,IAClBA,EAAQ,cACRC,EAAQN,EAAI,KAAK,EAErB,CACA,GAAM,CAAE,KAAAF,EAAM,YAAAI,CAAY,EAAI,KAAK,YAAYI,CAAK,EACpD,MAAO,CACL,KAAMJ,EAAcM,GAAaN,EAAa3B,EAAU,CAAC,EAAI,OAC7D,MAAA8B,EACA,KAAAP,CACF,CACF,CAMO,QACLW,EACAC,EACApB,EACA,CACA,IAAMqB,EAAa,CACjB,MAAOF,EACP,UAAWC,EACX,QAASpB,EAAQ,KACjB,KAAMA,EAAQ,MAAQ,KAAK,SAAS,CACtC,EAIMsB,EAAS,CAAC,EAAE,OAAOH,EAAOA,CAAK,EACrC,KAAK,MAAM,QAAQ,MAAM,KAAKE,CAAI,EAClC,KAAK,MAAM,QAAQ,SAAS,KAAK,CAC/B,GAAI,KAAK,MAAM,QAAQ,SAAS,OAAO,SAAS,EAChD,KAAMC,EAAO,CAAC,EACd,GAAIA,EAAO,CAAC,EACZ,QAAStB,EAAQ,KACjB,KAAMA,EAAQ,MAAQ,KAAK,SAAS,EACpC,KAAM,KAAK,SAAS,KACpB,UAAWoB,CACb,CAAC,CACH,CAEO,SAASG,EAAiBhB,EAAwB,CAEvD,IAAMY,EAAQ,KAAK,SAASI,CAAO,EAEnC,GAAI,CACF,IAAIC,EAAgBN,GAAaX,EAAK,KAAMtB,EAAU,CAAC,EACvDuC,EAAgBA,EAAc,QAAQ,YAAa,GAAG,EACtDA,EAAgBA,EAAc,QAAQ,SAAU,GAAG,EACnD,IAAMC,EAAQ,KAAK,MAAMD,CAAa,EAEtC,KAAK,YAAYL,EAAOM,CAAK,CAC/B,OAASC,EAAG,CACVb,EAAI,MAAM,sCAAuCa,CAAC,CACpD,CACF,CAEO,SAASH,EAAiBhB,EAAwB,CAEvD,IAAMY,EAAQ,KAAK,SAASI,CAAO,EACnC,GAAI,CACF,IAAME,EAAgC,CAAC,EACnCD,EAAgBN,GAAaX,EAAK,KAAMtB,EAAU,CAAC,EACjD0C,EAAMH,EAAc,QAAQ,GAAG,EACrCA,EAAgBA,EAAc,QAAQ,YAAa,GAAG,EACtDA,EAAgBA,EAAc,QAAQ,SAAU,GAAG,EACnD,IAAMI,EAAQJ,EAAc,MAAM,EAAGG,EAAM,CAAC,EAAE,KAAK,EAC7CE,EAAOL,EAAc,MAAMG,EAAM,CAAC,EAAE,KAAK,EAE/CF,EAAMG,CAAK,EAAIC,EAEf,KAAK,YAAYV,EAAOM,CAAK,CAC/B,OAASC,EAAG,CACVb,EAAI,MAAM,sCAAuCa,CAAC,CACpD,CACF,CAEQ,YAAYP,EAAcM,EAA+B,CAC/D,GAAIN,EAAM,OAAS,KACjBA,EAAM,MAAQM,MAEd,SAAWK,KAAOL,EAChBN,EAAM,MAAMW,CAAG,EAAIL,EAAMK,CAAG,CAGlC,CAEO,cAAcP,EAAiBhB,EAAwB,CAE5D,IAAMY,EAAQ,KAAK,SAASI,CAAO,EAEnC,GAAI,CACF,IAAMC,EAAgBN,GAAaX,EAAK,KAAMtB,EAAU,CAAC,EACnD8C,EAAsC,KAAK,MAAMP,CAAa,EAEpE,KAAK,iBAAiBL,EAAOY,CAAU,CACzC,OAASL,EAAG,CACVb,EAAI,MAAM,4CAA6Ca,CAAC,CAC1D,CACF,CAEQ,iBAAiBP,EAAcY,EAAqC,CAC1E,GAAIZ,EAAM,YAAc,KACtBA,EAAM,WAAaY,MAEnB,SAAWD,KAAOC,EAChBZ,EAAM,WAAWW,CAAG,EAAIC,EAAWD,CAAG,CAG5C,CAEQ,QAAS,CACf,KAAK,MAAM,QAAQ,WAAa,MAClC,CAEO,WAAWP,EAAiBhB,EAAwB,CAEzD,IAAMY,EAAQ,KAAK,SAASI,CAAO,EAC7BS,EAAO,SAAS,eAAezB,EAAK,IAAI,EAG9C,GAAI,CACF,IAAMA,EAAOyB,EAAK,UACZC,EAAU,KAAK,MAAM1B,CAAI,EAE3B0B,EAAQ,YACV,KAAK,iBAAiBd,EAAOc,EAAQ,UAAU,EAG7CA,EAAQ,OACV,KAAK,YAAYd,EAAOc,EAAQ,KAAK,CAEzC,OAASP,EAAG,CACVb,EAAI,MAAM,yCAA0Ca,CAAC,CACvD,CACF,CAEO,iBAAiBP,EAAcW,EAAa,CACjD,GAAIX,GAAO,aAAe,OACxB,OAAOA,EAAM,WAAWW,CAAG,CAI/B,CAGO,MAAMI,EAAoD,CAC/D,GAAI,MAAM,QAAQA,CAAK,EACrBA,EAAM,QAASC,GAAS,CACtB,KAAK,MAAMA,CAAI,CACjB,CAAC,MAED,QAAQD,EAAM,KAAM,CAClB,IAAK,gBACH,KAAK,MAAM,QAAQ,SAAS,KAAK,CAC/B,GAAI,KAAK,MAAM,QAAQ,SAAS,OAAO,SAAS,EAChD,KAAM,OACN,GAAI,OACJ,QAAS,CACP,MAAOA,EAAM,cACb,KAAMA,EAAM,kBACZ,QAASA,EAAM,eACjB,EACA,KAAM,GACN,KAAMA,EAAM,UACd,CAAC,EACD,MACF,IAAK,iBACH,KAAK,SAASA,EAAM,MAAOA,EAAM,MAAOA,EAAM,YAAaA,EAAM,IAAI,EACrE,MACF,IAAK,oBACH,GAAI,KAAK,MAAM,QAAQ,OAAO,IAAIA,EAAM,KAAK,EAC3C,MAAM,IAAI,MACR,oJACF,EAEF,KAAK,MAAM,QAAQ,YAAcA,EAAM,MACvC,KAAK,SAASA,EAAM,MAAOA,EAAM,MAAOA,EAAM,YAAaA,EAAM,IAAI,EACrE,KAAK,MAAM,QAAQ,cAAc,IAAIA,EAAM,MAAO,KAAK,MAAM,QAAQ,SAAS,MAAM,EACpF,MACF,IAAK,qBACH,KAAK,MAAM,QAAQ,cAAgBA,EAAM,MACzC,KAAK,MAAM,QAAQ,gBAAgB,IAAIA,EAAM,MAAO,KAAK,MAAM,QAAQ,SAAS,MAAM,EACtF,MACF,IAAK,cACH,KAAK,UAAUA,EAAM,MAAO,OAAW,OAAWA,EAAM,UAAU,EAClE,MACF,IAAK,YACH,KAAK,UAAUA,EAAM,MAAO,OAAW,OAAWA,EAAM,UAAU,EAClE,MACF,IAAK,UACH,KAAK,QAAQA,EAAM,MAAOA,EAAM,UAAWA,EAAM,IAAI,EACrD,MACF,IAAK,WACH,KAAK,SAASA,EAAM,MAAOA,EAAM,IAAI,EACrC,MACF,IAAK,WACH,KAAK,SAASA,EAAM,MAAOA,EAAM,IAAI,EACrC,MACF,IAAK,gBACH,KAAK,cAAcA,EAAM,MAAOA,EAAM,IAAI,EAC1C,MACF,IAAK,aACH,KAAK,WAAWA,EAAM,MAAOA,EAAM,IAAI,EACvC,MACF,IAAK,aACH,GAAI,KAAK,MAAM,QAAQ,YAAa,CAClC,GAAIA,EAAM,KAAO,KAAK,MAAM,QAAQ,YAClC,MAAM,IAAI,MACR,2BACE,KAAK,MAAM,QAAQ,YAAY,KAC/B,yGACJ,EAEA,KAAK,MAAM,QAAQ,YAAc,MAErC,SAAW,KAAK,MAAM,QAAQ,cAAe,CAC3C,GACEA,EAAM,KAAO,KAAK,MAAM,QAAQ,eAChCA,EAAM,OAAS,KAAK,MAAM,QAAQ,cAElC,MAAM,IAAI,MACR,6BACE,KAAK,MAAM,QAAQ,cAAc,KACjC,2GACJ,EAEA,KAAK,MAAM,QAAQ,cAAgB,MAEvC,CACA,KAAK,UAAUA,EAAM,KAAMA,EAAM,GAAIA,EAAM,IAAKA,EAAM,WAAYA,EAAM,QAAQ,EAChF,MACF,IAAK,WACH,KAAK,OAAOA,EAAM,OAAO,EACzB,MACF,IAAK,SACH,KAAK,OAAO,EACZ,MACF,IAAK,YACH,KAAK,UAAU,OAAW,OAAWA,EAAM,SAAUA,EAAM,UAAU,EACrE,MACF,IAAK,UACH,KAAK,UAAU,OAAW,OAAW,OAAWA,EAAM,UAAU,EAChE,MACF,IAAK,YACH,KAAK,UAAU,OAAW,OAAWA,EAAM,MAAOA,EAAM,UAAU,EAClE,MACF,IAAK,UACH,KAAK,UAAU,OAAW,OAAW,OAAWA,EAAM,UAAU,EAChE,MACF,IAAK,WACH,KAAK,UAAU,OAAW,OAAWA,EAAM,QAASA,EAAM,UAAU,EACpE,MACF,IAAK,SACH,KAAK,UAAU,OAAW,OAAW,OAAWA,EAAM,UAAU,EAChE,MACF,IAAK,WACH,KAAK,UAAU,OAAW,OAAWA,EAAM,QAASA,EAAM,UAAU,EACpE,MACF,IAAK,OACH,KAAK,UAAU,OAAW,OAAWA,EAAM,QAASA,EAAM,UAAU,EACpE,MACF,IAAK,SACH,KAAK,UAAU,OAAW,OAAW,OAAWA,EAAM,UAAU,EAChE,MACF,IAAK,cACHvD,GAAYuD,EAAM,IAAI,EACtB,MACF,IAAK,WACH,KAAK,UAAU,OAAW,OAAWA,EAAM,QAASA,EAAM,UAAU,EACpE,MACF,IAAK,MACH,KAAK,UAAU,OAAW,OAAWA,EAAM,QAASA,EAAM,UAAU,EACpE,MACF,IAAK,SACH,KAAK,UAAU,OAAW,OAAW,OAAWA,EAAM,UAAU,EAChE,MACF,IAAK,gBACH,KAAK,UAAU,OAAW,OAAWA,EAAM,aAAcA,EAAM,UAAU,EACzE,MACF,IAAK,SACH,KAAK,UAAU,OAAW,OAAWA,EAAM,WAAYA,EAAM,UAAU,EACvE,MACF,IAAK,cACH,KAAK,UAAU,OAAW,OAAW,OAAWA,EAAM,UAAU,EAChE,MACF,IAAK,aACH,KAAK,UAAU,OAAW,OAAWA,EAAM,UAAWA,EAAM,UAAU,EACtE,MACF,IAAK,WACH,KAAK,UAAU,OAAW,OAAW,OAAWA,EAAM,UAAU,EAChE,KACJ,CAEJ,CAQO,WAAY,CACjB,OAAOjD,EAAU,EAAE,QACrB,CACF,ECjpBA,IAAMmD,GAAYC,EAACC,GACjB;AAAA,cACYA,EAAQ,WAAW;AAAA,YACrBA,EAAQ,QAAQ;AAAA;AAAA;AAAA;AAAA,YAIhBA,EAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,cAKpBA,EAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMtBA,EAAQ,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMnBA,EAAQ,WAAW;AAAA;AAAA;AAAA;AAAA,YAIrBA,EAAQ,WAAW;AAAA,cACjBA,EAAQ,WAAW;AAAA;AAAA;AAAA;AAAA,YAIrBA,EAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA,YAI3BA,EAAQ,WAAW;AAAA;AAAA;AAAA;AAAA,YAInBA,EAAQ,WAAW;AAAA,cACjBA,EAAQ,WAAW;AAAA;AAAA;AAAA;AAAA,YAIrBA,EAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,cAKrBA,EAAQ,mBAAmB;AAAA,YAC7BA,EAAQ,gBAAgB;AAAA;AAAA;AAAA;AAAA,YAIxBA,EAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,YAKtBA,EAAQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOnBA,EAAQ,mBAAmB;AAAA,YAC7BA,EAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,cAKzBA,EAAQ,eAAe;AAAA,YACzBA,EAAQ,YAAY;AAAA;AAAA;AAAA;AAAA,YAIpBA,EAAQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,YAKrBA,EAAQ,kBAAkB;AAAA,cACxBA,EAAQ,qBAAqB;AAAA;AAAA;AAAA;AAAA,YAI/BA,EAAQ,kBAAkB;AAAA,cACxBA,EAAQ,qBAAqB;AAAA;AAAA;AAAA;AAAA,YAI/BA,EAAQ,kBAAkB;AAAA,cACxBA,EAAQ,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAS/BA,EAAQ,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,cAKdA,EAAQ,WAAW;AAAA,YACrBA,EAAQ,QAAQ;AAAA;AAAA;AAAA,cAGdA,EAAQ,WAAW;AAAA,YACrBA,EAAQ,QAAQ;AAAA;AAAA;AAAA,EAlHV,aAuHXC,GAAQH,GCvHf,IAAAI,GAA4B,WAUrB,IAAMC,GAAmB,GAAK,EAC/BC,GAAkB,YAClBC,GAAqB,eACrBC,GAAkB,YAClBC,GAAyB,YAElBC,GAAWC,EAAA,SAAUC,EAAMC,EAAU,CAChD,OAAqBH,GAASE,EAAMC,CAAQ,CAC9C,EAFwB,YAIXC,GAAYH,EAAA,SAAUC,EAAMG,EAAOC,EAAcC,EAAWC,EAAY,CACnF,GAAIH,EAAM,QAAU,QAAaA,EAAM,QAAU,MAAQ,OAAO,KAAKA,EAAM,KAAK,EAAE,SAAW,EAC3F,MAAO,CAAE,OAAQ,EAAG,MAAO,CAAE,EAG/B,IAAMI,EAAQJ,EAAM,MACdK,EAAWL,EAAM,SACjBF,EAAWE,EAAM,SAEvB,IAAIM,EAAe,OACfH,IACFG,EAAe,oBAGjB,IAAMC,EAAIV,EAAK,OAAO,GAAG,EACzBU,EAAE,KAAK,KAAM,QAAUF,EAAW,QAAQ,EAC1CE,EAAE,KAAK,QAAS,gBAAgB,EAChCA,EAAE,KAAK,UAAWD,CAAY,EAC9B,IAAIE,EAAa,GACbV,EAAS,QAAU,SACrBU,EAAa,IAAMV,EAAS,OAG9B,IAAIW,EAAYX,EAAS,MAAQG,EAAeH,EAAS,MAAQG,EAE3DS,EAAWH,EAAE,OAAO,MAAM,EAUhC,GATAG,EAAS,KAAK,QAAS,sBAAwBF,CAAU,EACzDE,EAAS,KAAK,IAAKZ,EAAS,CAAC,EAC7BY,EAAS,KAAK,IAAKZ,EAAS,MAAM,EAClCY,EAAS,KAAK,OAAQZ,EAAS,IAAI,EACnCY,EAAS,KAAK,SAAUZ,EAAS,MAAM,EACvCY,EAAS,KAAK,QAASD,CAAS,EAChCC,EAAS,KAAK,SAAUZ,EAAS,MAAM,EACvCY,EAAS,KAAK,KAAMZ,EAAS,EAAE,EAC/BY,EAAS,KAAK,KAAMZ,EAAS,EAAE,EAC3BM,GAAS,KAAM,CACjB,IAAIO,EAAQ,GACZ,QAASC,KAAOR,EAAO,CACrB,IAAIS,EAAWN,EAAE,OAAO,GAAG,EACvBO,KAAgB,gBAAYV,EAAMQ,CAAG,CAAC,EAC1CC,EAAS,KAAK,aAAcC,CAAa,EACzCD,EAAS,KAAK,SAAU,QAAQ,EAEhCE,GAA+Bb,CAAS,EACtCU,EACAC,EACAf,EAAS,EAAI,GACbA,EAAS,OAASa,EAClBF,EACA,GACA,CAAE,MAAO,OAAQ,EACjBP,CACF,EAEAS,GAAS,EACX,CACF,CAEA,OAAAD,EAAS,KAAK,SAAUC,CAAK,EAEtB,CAAE,OAAQb,EAAS,OAASa,EAAO,MAAOF,CAAU,CAC7D,EA7DyB,aA+DnBO,GAAkBpB,EAAA,SAAUqB,EAAO,CACvC,MACE,qCACAA,EACA,4FAEJ,EANwB,mBAQXC,GAAYtB,EAAA,eAAgBC,EAAMsB,EAAUC,EAAW,KAAM,CACxE,IAAIC,EAAWxB,EAAK,OAAO,eAAe,EACpCyB,EAAiB,MAAMC,GAAqBJ,EAAS,KAAgBK,GAAU,CAAC,EAOhFC,EALUJ,EACb,OAAO,WAAW,EAClB,KAAK,QAAS,qBAAqB,EACnC,KAAK,QAAS,8BAA8B,EAC5C,KAAKC,CAAc,EACF,KAAK,EAAE,sBAAsB,EAIjD,GAFAD,EAAS,KAAK,SAAU,KAAK,MAAMI,EAAI,MAAM,CAAC,EAAE,KAAK,QAAS,KAAK,MAAMA,EAAI,KAAK,CAAC,EAE/EN,EAAS,QAAU,WAAY,CACjC,IAAMT,EAAWb,EAAK,KAAK,EAAE,WAE7Ba,EAAS,aAAa,SAAUe,EAAI,OAAS,EAAIN,EAAS,UAAU,EACpE,IAAMO,EAAUhB,EAAS,QAAQ,EAEjCW,EACG,KAAK,IAAK,KAAK,MAAMK,EAAQ,EAAIA,EAAQ,MAAQ,EAAID,EAAI,MAAQ,CAAC,CAAC,EACnE,KAAK,IAAK,KAAK,MAAMC,EAAQ,EAAIA,EAAQ,OAAS,EAAID,EAAI,OAAS,CAAC,CAAC,CAC1E,SAAWL,EAAU,CACnB,GAAI,CAAE,OAAAO,EAAQ,MAAAC,EAAO,OAAAC,CAAO,EAAIT,EAChC,GAAIO,EAASC,EAAO,CAClB,IAAME,EAAOH,EACbA,EAASC,EACTA,EAAQE,CACV,CAGAT,EAAS,KAAK,IAAK,KAAK,MAAMM,EAAS,KAAK,IAAIA,EAASC,CAAK,EAAI,EAAIH,EAAI,MAAQ,CAAC,CAAC,EAChFN,EAAS,QAAU,WACrBE,EAAS,KAAK,IAAK,KAAK,MAAMQ,CAAM,CAAC,EAErCR,EAAS,KAAK,IAAK,KAAK,MAAMQ,EAASJ,EAAI,MAAM,CAAC,CAEtD,CAEA,MAAO,CAACJ,CAAQ,CAClB,EAxCyB,aA0CZU,GAAWnC,EAAA,SAAUC,EAAMsB,EAAU,CAChD,IAAIa,EAAiB,EACjBC,EAAa,EACXC,EAAQf,EAAS,KAAK,MAAMgB,EAAO,cAAc,EAEjD,CAACC,EAAeC,CAAe,EAAIC,GAAcnB,EAAS,QAAQ,EAEpEoB,EAAY,CAAC,EACbC,EAAK,EACLC,EAAQ7C,EAAA,IAAMuB,EAAS,EAAf,SACZ,GACEA,EAAS,SAAW,QACpBA,EAAS,aAAe,QACxBA,EAAS,WAAa,EAEtB,OAAQA,EAAS,OAAQ,CACvB,IAAK,MACL,IAAK,QACHsB,EAAQ7C,EAAA,IAAM,KAAK,MAAMuB,EAAS,EAAIA,EAAS,UAAU,EAAjD,SACR,MACF,IAAK,SACL,IAAK,SACHsB,EAAQ7C,EAAA,IACN,KAAK,MAAMuB,EAAS,GAAKa,EAAiBC,EAAad,EAAS,YAAc,CAAC,EADzE,SAER,MACF,IAAK,SACL,IAAK,MACHsB,EAAQ7C,EAAA,IACN,KAAK,MACHuB,EAAS,GACNa,EAAiBC,EAAa,EAAId,EAAS,YAC5CA,EAAS,UACb,EALM,SAMR,KACJ,CAGF,GACEA,EAAS,SAAW,QACpBA,EAAS,aAAe,QACxBA,EAAS,QAAU,OAEnB,OAAQA,EAAS,OAAQ,CACvB,IAAK,OACL,IAAK,QACHA,EAAS,EAAI,KAAK,MAAMA,EAAS,EAAIA,EAAS,UAAU,EACxDA,EAAS,OAAS,QAClBA,EAAS,iBAAmB,SAC5BA,EAAS,kBAAoB,SAC7B,MACF,IAAK,SACL,IAAK,SACHA,EAAS,EAAI,KAAK,MAAMA,EAAS,EAAIA,EAAS,MAAQ,CAAC,EACvDA,EAAS,OAAS,SAClBA,EAAS,iBAAmB,SAC5BA,EAAS,kBAAoB,SAC7B,MACF,IAAK,QACL,IAAK,MACHA,EAAS,EAAI,KAAK,MAAMA,EAAS,EAAIA,EAAS,MAAQA,EAAS,UAAU,EACzEA,EAAS,OAAS,MAClBA,EAAS,iBAAmB,SAC5BA,EAAS,kBAAoB,SAC7B,KACJ,CAGF,OAAS,CAACuB,EAAGC,CAAI,IAAKT,EAAM,QAAQ,EAAG,CAEnCf,EAAS,aAAe,QACxBA,EAAS,aAAe,GACxBiB,IAAkB,SAElBI,EAAKE,EAAIN,GAGX,IAAMf,EAAWxB,EAAK,OAAO,MAAM,EACnCwB,EAAS,KAAK,IAAKF,EAAS,CAAC,EAC7BE,EAAS,KAAK,IAAKoB,EAAM,CAAC,EACtBtB,EAAS,SAAW,QACtBE,EACG,KAAK,cAAeF,EAAS,MAAM,EACnC,KAAK,oBAAqBA,EAAS,gBAAgB,EACnD,KAAK,qBAAsBA,EAAS,iBAAiB,EAEtDA,EAAS,aAAe,QAC1BE,EAAS,MAAM,cAAeF,EAAS,UAAU,EAE/CkB,IAAoB,QACtBhB,EAAS,MAAM,YAAagB,CAAe,EAEzClB,EAAS,aAAe,QAC1BE,EAAS,MAAM,cAAeF,EAAS,UAAU,EAE/CA,EAAS,OAAS,QACpBE,EAAS,KAAK,OAAQF,EAAS,IAAI,EAEjCA,EAAS,QAAU,QACrBE,EAAS,KAAK,QAASF,EAAS,KAAK,EAEnCA,EAAS,KAAO,OAClBE,EAAS,KAAK,KAAMF,EAAS,EAAE,EACtBqB,IAAO,GAChBnB,EAAS,KAAK,KAAMmB,CAAE,EAGxB,IAAMI,EAAOD,GAAQE,GACrB,GAAI1B,EAAS,MAAO,CAClB,IAAM2B,EAAOzB,EAAS,OAAO,OAAO,EACpCyB,EAAK,KAAK,IAAK3B,EAAS,CAAC,EACrBA,EAAS,OAAS,QACpB2B,EAAK,KAAK,OAAQ3B,EAAS,IAAI,EAEjC2B,EAAK,KAAKF,CAAI,CAChB,MACEvB,EAAS,KAAKuB,CAAI,EAGlBzB,EAAS,SAAW,QACpBA,EAAS,aAAe,QACxBA,EAAS,WAAa,IAEtBc,IAAeZ,EAAS,SAAWA,GAAU,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,OAC7DW,EAAiBC,GAGnBM,EAAU,KAAKlB,CAAQ,CACzB,CAEA,OAAOkB,CACT,EAlIwB,YAoIXQ,GAAYnD,EAAA,SAAUC,EAAMmD,EAAW,CASlD,SAASC,EAAUC,EAAGC,EAAGC,EAAOC,EAAQC,EAAK,CAC3C,OACEJ,EACA,IACAC,EACA,KACCD,EAAIE,GACL,IACAD,EACA,KACCD,EAAIE,GACL,KACCD,EAAIE,EAASC,GACd,KACCJ,EAAIE,EAAQE,EAAM,KACnB,KACCH,EAAIE,GACL,IACAH,EACA,KACCC,EAAIE,EAET,CAtBSzD,EAAAqD,EAAA,aAuBT,IAAMM,EAAU1D,EAAK,OAAO,SAAS,EACrC,OAAA0D,EAAQ,KAAK,SAAUN,EAAUD,EAAU,EAAGA,EAAU,EAAGA,EAAU,MAAOA,EAAU,OAAQ,CAAC,CAAC,EAChGO,EAAQ,KAAK,QAAS,UAAU,EAEhCP,EAAU,EAAIA,EAAU,EAAIA,EAAU,OAAS,EAE/CjB,GAASlC,EAAMmD,CAAS,EACjBO,CACT,EAxCyB,aA0CrBlD,GAAW,GAEFmD,GAAqB5D,EAAA,CAAC6D,EAASC,EAAQC,EAAWC,IAAS,CACjEH,EAAQ,QAGbE,EAAU,QAASE,GAAa,CAC9B,IAAM7D,EAAQ0D,EAAO,IAAIG,CAAQ,EAC3BC,EAAWL,EAAQ,OAAO,SAAWzD,EAAM,QAAQ,EACrD,CAAC4D,EAAK,cAAgB5D,EAAM,MAC9B8D,EAAS,KAAK,KAAM9D,EAAM,MAAQA,EAAM,OAAS,CAAC,EACzC4D,EAAK,cACdE,EAAS,KAAK,KAAM9D,EAAM,KAAK,CAEnC,CAAC,CACH,EAbkC,sBAuB5B+D,GAA2BnE,EAAA,SAAUC,EAAMG,EAAO4D,EAAMI,EAAU,CACtE,IAAMC,EAASD,EAAWhE,EAAM,MAAQA,EAAM,OACxCkE,EAASlE,EAAM,EAAIA,EAAM,MAAQ,EACjCmE,EAAUF,EAASjE,EAAM,OAEzBoE,EAAmBvE,EAAK,OAAO,GAAG,EAAE,MAAM,EAChD,IAAIU,EAAI6D,EAEHJ,IACH3D,KACI,OAAO,KAAKL,EAAM,OAAS,CAAC,CAAC,EAAE,QAAU,CAAC4D,EAAK,YACjDrD,EAAE,KAAK,UAAWS,GAAgB,QAAQX,EAAQ,QAAQ,CAAC,EAAE,KAAK,SAAU,SAAS,EAEvFE,EAAE,OAAO,MAAM,EACZ,KAAK,KAAM,QAAUF,EAAQ,EAC7B,KAAK,KAAM6D,CAAM,EACjB,KAAK,KAAMC,CAAO,EAClB,KAAK,KAAMD,CAAM,EACjB,KAAK,KAAM,GAAI,EACf,KAAK,QAAS,gBAAgB,EAC9B,KAAK,eAAgB,OAAO,EAC5B,KAAK,SAAU,MAAM,EACrB,KAAK,OAAQlE,EAAM,IAAI,EAE1BO,EAAI6D,EAAiB,OAAO,GAAG,EAC/BpE,EAAM,SAAWK,GAEbL,EAAM,OAAS,MACjBO,EAAE,KAAK,KAAM,QAAUF,EAAQ,GAInC,IAAMgE,EAAqBC,GAAY,EACvC,IAAIC,EAAW,QACXvE,EAAM,YAAY,MACpBuE,EAAWvE,EAAM,WAAW,MAE5BqE,EAAK,KAAO,UAEVL,EACFO,GAAY,IAAI/E,EAAkB,GAElC+E,GAAY,IAAIhF,EAAe,GAEjC8E,EAAK,EAAIrE,EAAM,EACfqE,EAAK,EAAIJ,EACTI,EAAK,MAAQrE,EAAM,MACnBqE,EAAK,OAASrE,EAAM,OACpBqE,EAAK,MAAQE,EACbF,EAAK,GAAK,EACVA,EAAK,GAAK,EACVA,EAAK,KAAOrE,EAAM,KAClB,IAAMU,EAAWf,GAASY,EAAG8D,CAAI,EAGjC,GAFArE,EAAM,SAAWqE,EAEbrE,EAAM,YAAY,KAAM,CAC1B,IAAMwE,EAAUxE,EAAM,WAAW,KAAK,KAAK,EACvCwE,EAAQ,OAAO,CAAC,IAAM,IACVC,GAAkBlE,EAAG8D,EAAK,EAAIA,EAAK,MAAQ,GAAIA,EAAK,EAAI,GAAIG,EAAQ,OAAO,CAAC,CAAC,EAE7EE,GAAUnE,EAAG8D,EAAK,EAAIA,EAAK,MAAQ,GAAIA,EAAK,EAAI,GAAIG,CAAO,CAE7E,CAEAG,GAAuBf,EAAMgB,GAAS5E,EAAM,WAAW,CAAC,EACtDA,EAAM,YACNO,EACA8D,EAAK,EACLA,EAAK,EACLA,EAAK,MACLA,EAAK,OACL,CAAE,MAAO,SAAS5E,EAAe,EAAG,EACpCmE,CACF,EAEA,IAAIP,EAASrD,EAAM,OACnB,GAAIU,EAAS,KAAM,CACjB,IAAMmE,EAASnE,EAAS,KAAK,EAAE,QAAQ,EACvCV,EAAM,OAAS6E,EAAO,OACtBxB,EAASwB,EAAO,MAClB,CAEA,OAAOxB,CACT,EAnFiC,4BAqF3ByB,GAAqBlF,EAAA,SAAUC,EAAMG,EAAO4D,EAAMI,EAAU,CAChE,IAAMC,EAASD,EAAWhE,EAAM,MAAQA,EAAM,OACxCkE,EAASlE,EAAM,EAAIA,EAAM,MAAQ,EACjCmE,EAAUF,EAAS,GAEnBtB,EAAO9C,EAAK,OAAO,GAAG,EAAE,MAAM,EAE/BmE,IACH3D,KACAsC,EACG,OAAO,MAAM,EACb,KAAK,KAAM,QAAUtC,EAAQ,EAC7B,KAAK,KAAM6D,CAAM,EACjB,KAAK,KAAMC,CAAO,EAClB,KAAK,KAAMD,CAAM,EACjB,KAAK,KAAM,GAAI,EACf,KAAK,QAAS,gBAAgB,EAC9B,KAAK,eAAgB,OAAO,EAC5B,KAAK,SAAU,MAAM,EACrB,KAAK,OAAQlE,EAAM,IAAI,EAE1BA,EAAM,SAAWK,IAEnB,IAAM0E,EAAUlF,EAAK,OAAO,GAAG,EAC3BmF,EAAWtF,GACXsE,EACFgB,GAAY,IAAIxF,EAAkB,GAElCwF,GAAY,IAAIzF,EAAe,GAEjCwF,EAAQ,KAAK,QAASC,CAAQ,EAC9BD,EAAQ,KAAK,OAAQ/E,EAAM,IAAI,EAE/B,IAAMqE,EAAqBC,GAAY,EACvCD,EAAK,EAAIrE,EAAM,EACfqE,EAAK,EAAIJ,EACTI,EAAK,KAAO,UACZA,EAAK,MAAQrE,EAAM,MACnBqE,EAAK,OAASrE,EAAM,OACpBqE,EAAK,MAAQ,QACbA,EAAK,GAAK,EACVA,EAAK,GAAK,EAEVU,EACG,OAAO,MAAM,EACb,KAAK,KAAM,kBAAoB1E,EAAQ,EACvC,KAAK,KAAM6D,CAAM,EACjB,KAAK,KAAMD,EAAS,EAAE,EACtB,KAAK,KAAMC,CAAM,EACjB,KAAK,KAAMD,EAAS,EAAE,EAEzBc,EACG,OAAO,MAAM,EACb,KAAK,KAAM,iBAAmB1E,EAAQ,EACtC,KAAK,KAAM6D,EAAS5E,GAAmB,CAAC,EACxC,KAAK,KAAM2E,EAAS,EAAE,EACtB,KAAK,KAAMC,EAAS5E,GAAmB,CAAC,EACxC,KAAK,KAAM2E,EAAS,EAAE,EACzBc,EACG,OAAO,MAAM,EACb,KAAK,KAAMb,EAAS5E,GAAmB,CAAC,EACxC,KAAK,KAAM2E,EAAS,EAAE,EACtB,KAAK,KAAMC,CAAM,EACjB,KAAK,KAAMD,EAAS,EAAE,EACzBc,EACG,OAAO,MAAM,EACb,KAAK,KAAMb,CAAM,EACjB,KAAK,KAAMD,EAAS,EAAE,EACtB,KAAK,KAAMC,EAAS5E,GAAmB,EAAI,CAAC,EAC5C,KAAK,KAAM2E,EAAS,EAAE,EAEzB,IAAMgB,EAASF,EAAQ,OAAO,QAAQ,EACtCE,EAAO,KAAK,KAAMjF,EAAM,EAAIA,EAAM,MAAQ,CAAC,EAC3CiF,EAAO,KAAK,KAAMhB,EAAS,EAAE,EAC7BgB,EAAO,KAAK,IAAK,EAAE,EACnBA,EAAO,KAAK,QAASjF,EAAM,KAAK,EAChCiF,EAAO,KAAK,SAAUjF,EAAM,MAAM,EAElC,IAAM6E,EAASE,EAAQ,KAAK,EAAE,QAAQ,EACtC,OAAA/E,EAAM,OAAS6E,EAAO,OAEtBF,GAAuBf,EAAMgB,GAAS5E,EAAM,WAAW,CAAC,EACtDA,EAAM,YACN+E,EACAV,EAAK,EACLA,EAAK,EAAI,GACTA,EAAK,MACLA,EAAK,OACL,CAAE,MAAO,SAAS3E,EAAsB,EAAG,EAC3CkE,CACF,EAEO5D,EAAM,MACf,EA7F2B,sBA+FdkF,GAAYtF,EAAA,eAAgBC,EAAMG,EAAO4D,EAAMI,EAAU,CACpE,OAAQhE,EAAM,KAAM,CAClB,IAAK,QACH,OAAO,MAAM8E,GAAmBjF,EAAMG,EAAO4D,EAAMI,CAAQ,EAC7D,IAAK,cACH,OAAO,MAAMD,GAAyBlE,EAAMG,EAAO4D,EAAMI,CAAQ,CACrE,CACF,EAPyB,aASZmB,GAAUvF,EAAA,SAAUC,EAAMuF,EAAKxB,EAAM,CAEhD,IAAMrD,EADmBV,EAAK,OAAO,GAAG,EAExCwF,GAAmB9E,EAAG6E,CAAG,EACrBA,EAAI,MACNT,GAAuBf,CAAI,EACzBwB,EAAI,KACJ7E,EACA6E,EAAI,EACJA,EAAI,EAAIxB,EAAK,eAAiBwB,EAAI,eAAiB,GAAK,EACxDA,EAAI,MACJ,EACA,CAAE,MAAO,MAAO,EAChBxB,CACF,EAEFrD,EAAE,MAAM,CACV,EAjBuB,WAmBV+E,GAAgB1F,EAAA,SAAUC,EAAM,CAC3C,OAAOA,EAAK,OAAO,GAAG,CACxB,EAF6B,iBAahB0F,GAAiB3F,EAAA,SAAUC,EAAMgF,EAAQW,EAAa5B,EAAM6B,EAAkB,CACzF,IAAMpB,EAAqBC,GAAY,EACjC/D,EAAIsE,EAAO,SACjBR,EAAK,EAAIQ,EAAO,OAChBR,EAAK,EAAIQ,EAAO,OAChBR,EAAK,MAAQ,aAAgBoB,EAAmB,EAChDpB,EAAK,MAAQQ,EAAO,MAAQA,EAAO,OACnCR,EAAK,OAASmB,EAAcX,EAAO,OACnClF,GAASY,EAAG8D,CAAI,CAClB,EAT8B,kBAoBjBqB,GAAW9F,EAAA,eAAgBC,EAAM8F,EAAWC,EAAWhC,EAAM,CACxE,GAAM,CACJ,UAAAiC,EACA,cAAAC,EACA,eAAAC,EACA,cAAAC,EACA,kBAAmBC,EACnB,gBAAiBC,EACjB,kBAAmBC,CACrB,EAAIvC,EACErD,EAAIV,EAAK,OAAO,GAAG,EACnBuG,EAAexG,EAAA,SAAU+B,EAAQE,EAAQD,EAAOyE,EAAO,CAC3D,OAAO9F,EACJ,OAAO,MAAM,EACb,KAAK,KAAMoB,CAAM,EACjB,KAAK,KAAME,CAAM,EACjB,KAAK,KAAMD,CAAK,EAChB,KAAK,KAAMyE,CAAK,EAChB,KAAK,QAAS,UAAU,CAC7B,EARqB,gBASrBD,EAAaT,EAAU,OAAQA,EAAU,OAAQA,EAAU,MAAOA,EAAU,MAAM,EAClFS,EAAaT,EAAU,MAAOA,EAAU,OAAQA,EAAU,MAAOA,EAAU,KAAK,EAChFS,EAAaT,EAAU,OAAQA,EAAU,MAAOA,EAAU,MAAOA,EAAU,KAAK,EAChFS,EAAaT,EAAU,OAAQA,EAAU,OAAQA,EAAU,OAAQA,EAAU,KAAK,EAC9EA,EAAU,WAAa,QACzBA,EAAU,SAAS,QAAQ,SAAUW,EAAM,CACzCF,EAAaT,EAAU,OAAQW,EAAK,EAAGX,EAAU,MAAOW,EAAK,CAAC,EAAE,MAC9D,mBACA,MACF,CACF,CAAC,EAGH,IAAIC,EAAoBC,GAAW,EACnCD,EAAI,KAAOX,EACXW,EAAI,EAAIZ,EAAU,OAClBY,EAAI,EAAIZ,EAAU,OAClBY,EAAI,WAAaN,EACjBM,EAAI,SAAWL,EACfK,EAAI,WAAaJ,EACjBI,EAAI,OAAS,SACbA,EAAI,OAAS,SACbA,EAAI,MAAQ,GACZA,EAAI,MAAQP,GAAiB,GAC7BO,EAAI,OAASR,GAAkB,GAC/BQ,EAAI,WAAaT,EACjBS,EAAI,MAAQ,YAEZxD,GAAUxC,EAAGgG,CAAG,EAChBA,EAAMC,GAAW,EACjBD,EAAI,KAAOZ,EAAU,MACrBY,EAAI,EAAIZ,EAAU,OAASK,EAAgB,GAAKL,EAAU,MAAQA,EAAU,QAAU,EACtFY,EAAI,EAAIZ,EAAU,OAASE,EAAYC,EACvCS,EAAI,OAAS,SACbA,EAAI,OAAS,SACbA,EAAI,WAAaT,EACjBS,EAAI,MAAQ,WACZA,EAAI,WAAaN,EACjBM,EAAI,SAAWL,EACfK,EAAI,WAAaJ,EACjBI,EAAI,KAAO,GAEX,IAAIlF,EAAWuD,GAAS2B,EAAI,IAAI,EAAI,MAAMrF,GAAUX,EAAGgG,EAAKZ,CAAS,EAAI5D,GAASxB,EAAGgG,CAAG,EAExF,GAAIZ,EAAU,gBAAkB,QAC9B,OAAW,CAACc,EAAKH,CAAI,IAAK,OAAO,QAAQX,EAAU,aAAa,EAC9D,GAAIW,EAAK,QAAS,CAChBC,EAAI,KAAOD,EAAK,QAChBC,EAAI,EAAIZ,EAAU,QAAUA,EAAU,MAAQA,EAAU,QAAU,EAClEY,EAAI,EAAIZ,EAAU,SAASc,CAAG,EAAE,EAAIZ,EAAYC,EAChDS,EAAI,MAAQ,WACZA,EAAI,OAAS,SACbA,EAAI,OAAS,SACbA,EAAI,MAAQ,GACZA,EAAI,WAAaN,EACjBM,EAAI,SAAWL,EACfK,EAAI,WAAaJ,EACjBI,EAAI,KAAOZ,EAAU,KAEjBf,GAAS2B,EAAI,IAAI,GACnBZ,EAAU,OAASA,EAAU,SAASc,CAAG,EAAE,EAC3C,MAAMvF,GAAUX,EAAGgG,EAAKZ,CAAS,GAEjC5D,GAASxB,EAAGgG,CAAG,EAEjB,IAAIG,EAAgB,KAAK,MACvBrF,EACG,IAAKsF,IAAQA,EAAG,SAAWA,GAAI,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EACrD,OAAO,CAACC,EAAKC,IAASD,EAAMC,CAAI,CACrC,EACAlB,EAAU,SAASc,CAAG,EAAE,QAAUC,GAAiBb,EAAYC,EACjE,EAIJ,OAAAH,EAAU,OAAS,KAAK,MAAMA,EAAU,MAAQA,EAAU,MAAM,EACzDpF,CACT,EAjGwB,YAyGX8E,GAAqBzF,EAAA,SAAUC,EAAMgF,EAAQ,CAC1CQ,GAAmBxF,EAAMgF,CAAM,CAC/C,EAFkC,sBAIrBiC,GAAqBlH,EAAA,SAAUC,EAAM,CAChDA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,UAAU,EACrB,KAAK,YAAa,SAAS,EAC3B,KAAK,YAAa,SAAS,EAC3B,OAAO,MAAM,EACb,KAAK,YAAa,WAAW,EAC7B,KACC,IACA,i1ZACF,CACJ,EAbkC,sBAerBkH,GAAqBnH,EAAA,SAAUC,EAAM,CAChDA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,UAAU,EACrB,KAAK,QAAS,IAAI,EAClB,KAAK,SAAU,IAAI,EACnB,OAAO,MAAM,EACb,KAAK,YAAa,WAAW,EAC7B,KACC,IACA,0JACF,CACJ,EAbkC,sBAerBmH,GAAkBpH,EAAA,SAAUC,EAAM,CAC7CA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,OAAO,EAClB,KAAK,QAAS,IAAI,EAClB,KAAK,SAAU,IAAI,EACnB,OAAO,MAAM,EACb,KAAK,YAAa,WAAW,EAC7B,KACC,IACA,2UACF,CACJ,EAb+B,mBAoBlBoH,GAAkBrH,EAAA,SAAUC,EAAM,CAC7CA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,WAAW,EACtB,KAAK,OAAQ,GAAG,EAChB,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,oBAAoB,EACnC,OAAO,MAAM,EACb,KAAK,IAAK,wBAAwB,CACvC,EAb+B,mBAoBlBqH,GAAwBtH,EAAA,SAAUC,EAAM,CACnDA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,aAAa,EACxB,KAAK,OAAQ,IAAI,EACjB,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,2BAA2B,CAC1C,EAZqC,yBAmBxBsH,GAAuBvH,EAAA,SAAUC,EAAM,CAClDA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,gBAAgB,EAC3B,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,EAAE,EACf,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,KAAM,EAAE,EACb,KAAK,KAAM,EAAE,EACb,KAAK,IAAK,CAAC,CAEhB,EAfoC,wBAsBvBuH,GAAuBxH,EAAA,SAAUC,EAAM,CACrCA,EAAK,OAAO,MAAM,EAE5B,OAAO,QAAQ,EACf,KAAK,KAAM,WAAW,EACtB,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,CAAC,EACtB,KAAK,SAAU,MAAM,EACrB,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,GAAG,EAGhB,OAAO,MAAM,EACb,KAAK,OAAQ,MAAM,EACnB,KAAK,SAAU,SAAS,EACxB,MAAM,mBAAoB,MAAM,EAChC,KAAK,eAAgB,KAAK,EAC1B,KAAK,IAAK,yBAAyB,CAExC,EAnBoC,wBAqBvB2G,GAAa5G,EAAA,UAAY,CACpC,MAAO,CACL,EAAG,EACH,EAAG,EACH,KAAM,OACN,OAAQ,OACR,MAAO,OACP,MAAO,OACP,OAAQ,OACR,WAAY,EACZ,GAAI,EACJ,GAAI,EACJ,MAAO,GACP,OAAQ,MACV,CACF,EAf0B,cAiBb0E,GAAc1E,EAAA,UAAY,CACrC,MAAO,CACL,EAAG,EACH,EAAG,EACH,KAAM,UACN,OAAQ,OACR,MAAO,IACP,OAAQ,QACR,OAAQ,IACR,GAAI,EACJ,GAAI,CACN,CACF,EAZ2B,eAcrB+E,GAA0B,UAAY,CAU1C,SAAS0C,EAAOC,EAAS/G,EAAG2C,EAAGC,EAAGC,EAAOC,EAAQnD,EAAW,CAC1D,IAAM0C,EAAOrC,EACV,OAAO,MAAM,EACb,KAAK,IAAK2C,EAAIE,EAAQ,CAAC,EACvB,KAAK,IAAKD,EAAIE,EAAS,EAAI,CAAC,EAC5B,MAAM,cAAe,QAAQ,EAC7B,KAAKiE,CAAO,EACfC,EAAc3E,EAAM1C,CAAS,CAC/B,CARSN,EAAAyH,EAAA,UAoBT,SAASG,EAAQF,EAAS/G,EAAG2C,EAAGC,EAAGC,EAAOC,EAAQnD,EAAW0D,EAAM,CACjE,GAAM,CAAE,cAAA6D,EAAe,gBAAAC,EAAiB,gBAAAC,CAAgB,EAAI/D,EAEtD,CAACgE,EAAgBC,CAAgB,EAAIvF,GAAcmF,CAAa,EAEhEvF,EAAQoF,EAAQ,MAAMnF,EAAO,cAAc,EACjD,QAASO,EAAI,EAAGA,EAAIR,EAAM,OAAQQ,IAAK,CACrC,IAAMF,EAAKE,EAAIkF,EAAkBA,GAAkB1F,EAAM,OAAS,GAAM,EAClEU,EAAOrC,EACV,OAAO,MAAM,EACb,KAAK,IAAK2C,EAAIE,EAAQ,CAAC,EACvB,KAAK,IAAKD,CAAC,EACX,MAAM,cAAe,QAAQ,EAC7B,MAAM,YAAa0E,CAAgB,EACnC,MAAM,cAAeF,CAAe,EACpC,MAAM,cAAeD,CAAe,EACvC9E,EACG,OAAO,OAAO,EACd,KAAK,IAAKM,EAAIE,EAAQ,CAAC,EACvB,KAAK,KAAMZ,CAAE,EACb,KAAKN,EAAMQ,CAAC,CAAC,EAEhBE,EACG,KAAK,IAAKO,EAAIE,EAAS,CAAG,EAC1B,KAAK,oBAAqB,SAAS,EACnC,KAAK,qBAAsB,SAAS,EAEvCkE,EAAc3E,EAAM1C,CAAS,CAC/B,CACF,CA7BSN,EAAA4H,EAAA,WAyCT,SAASM,EAAKR,EAAS/G,EAAG2C,EAAGC,EAAGC,EAAOC,EAAQnD,EAAW0D,EAAM,CAC9D,IAAMmE,EAAIxH,EAAE,OAAO,QAAQ,EAQrBqC,EAPImF,EACP,OAAO,eAAe,EACtB,KAAK,IAAK7E,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASC,CAAK,EACnB,KAAK,SAAUC,CAAM,EAGrB,OAAO,WAAW,EAClB,MAAM,UAAW,OAAO,EACxB,MAAM,SAAU,MAAM,EACtB,MAAM,QAAS,MAAM,EAExBT,EACG,OAAO,KAAK,EACZ,MAAM,UAAW,YAAY,EAC7B,MAAM,aAAc,QAAQ,EAC5B,MAAM,iBAAkB,QAAQ,EAChC,KAAK0E,CAAO,EAEfE,EAAQF,EAASS,EAAG7E,EAAGC,EAAGC,EAAOC,EAAQnD,EAAW0D,CAAI,EACxD2D,EAAc3E,EAAM1C,CAAS,CAC/B,CAxBSN,EAAAkI,EAAA,QAqCT,eAAeE,EAAQV,EAAS/G,EAAG2C,EAAGC,EAAGC,EAAOC,EAAQnD,EAAW0D,EAAM,CAGvE,IAAMnC,EAAM,MAAMwG,GAA0BX,EAAmB9F,GAAU,CAAC,EACpEuG,EAAIxH,EAAE,OAAO,QAAQ,EAQrBqC,EAPImF,EACP,OAAO,eAAe,EACtB,KAAK,IAAK7E,EAAIE,EAAQ,EAAI3B,EAAI,MAAQ,CAAC,EACvC,KAAK,IAAK0B,EAAIE,EAAS,EAAI5B,EAAI,OAAS,CAAC,EACzC,KAAK,QAASA,EAAI,KAAK,EACvB,KAAK,SAAUA,EAAI,MAAM,EAEb,OAAO,WAAW,EAAE,MAAM,SAAU,MAAM,EAAE,MAAM,QAAS,MAAM,EAEhFmB,EACG,OAAO,KAAK,EACZ,MAAM,aAAc,QAAQ,EAC5B,MAAM,iBAAkB,QAAQ,EAChC,KAAK,MAAMrB,GAAqB+F,EAAmB9F,GAAU,CAAC,CAAC,EAElEgG,EAAQF,EAASS,EAAG7E,EAAGC,EAAGC,EAAOC,EAAQnD,EAAW0D,CAAI,EACxD2D,EAAc3E,EAAM1C,CAAS,CAC/B,CAtBeN,EAAAoI,EAAA,WA4Bf,SAAST,EAAcW,EAAQC,EAAmB,CAChD,QAAWvH,KAAOuH,EACZA,EAAkB,eAAevH,CAAG,GACtCsH,EAAO,KAAKtH,EAAKuH,EAAkBvH,CAAG,CAAC,CAG7C,CANS,OAAAhB,EAAA2H,EAAA,iBAQF,SAAU3D,EAAMgB,EAAW,GAAO,CACvC,OAAIA,EACKoD,EAEFpE,EAAK,gBAAkB,KAAOkE,EAAOlE,EAAK,gBAAkB,MAAQyD,EAASG,CACtF,CACF,EAAG,EAEGzG,GAAkC,UAAY,CAUlD,SAASsG,EAAOC,EAAS/G,EAAG2C,EAAGC,EAAGC,EAAOC,EAAQnD,EAAW,CAC1D,IAAM0C,EAAOrC,EACV,OAAO,MAAM,EACb,KAAK,IAAK2C,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,MAAM,cAAe,OAAO,EAC5B,KAAKmE,CAAO,EACfC,EAAc3E,EAAM1C,CAAS,CAC/B,CARSN,EAAAyH,EAAA,UAoBT,SAASG,EAAQF,EAAS/G,EAAG2C,EAAGC,EAAGC,EAAOC,EAAQnD,EAAW0D,EAAM,CACjE,GAAM,CAAE,cAAA6D,EAAe,gBAAAC,EAAiB,gBAAAC,CAAgB,EAAI/D,EAEtD1B,EAAQoF,EAAQ,MAAMnF,EAAO,cAAc,EACjD,QAASO,EAAI,EAAGA,EAAIR,EAAM,OAAQQ,IAAK,CACrC,IAAMF,EAAKE,EAAI+E,EAAiBA,GAAiBvF,EAAM,OAAS,GAAM,EAChEU,EAAOrC,EACV,OAAO,MAAM,EACb,KAAK,IAAK2C,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,MAAM,cAAe,OAAO,EAC5B,MAAM,YAAasE,CAAa,EAChC,MAAM,cAAeE,CAAe,EACpC,MAAM,cAAeD,CAAe,EACvC9E,EAAK,OAAO,OAAO,EAAE,KAAK,IAAKM,CAAC,EAAE,KAAK,KAAMV,CAAE,EAAE,KAAKN,EAAMQ,CAAC,CAAC,EAE9DE,EACG,KAAK,IAAKO,EAAIE,EAAS,CAAG,EAC1B,KAAK,oBAAqB,SAAS,EACnC,KAAK,qBAAsB,SAAS,EAEvCkE,EAAc3E,EAAM1C,CAAS,CAC/B,CACF,CAvBSN,EAAA4H,EAAA,WAmCT,SAASM,EAAKR,EAAS/G,EAAG2C,EAAGC,EAAGC,EAAOC,EAAQnD,EAAW0D,EAAM,CAC9D,IAAMmE,EAAIxH,EAAE,OAAO,QAAQ,EAQrBqC,EAPImF,EACP,OAAO,eAAe,EACtB,KAAK,IAAK7E,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASC,CAAK,EACnB,KAAK,SAAUC,CAAM,EAGrB,OAAO,WAAW,EAClB,MAAM,UAAW,OAAO,EACxB,MAAM,SAAU,MAAM,EACtB,MAAM,QAAS,MAAM,EAExBT,EACG,OAAO,KAAK,EACZ,MAAM,UAAW,YAAY,EAC7B,MAAM,aAAc,QAAQ,EAC5B,MAAM,iBAAkB,QAAQ,EAChC,KAAK0E,CAAO,EAEfE,EAAQF,EAASS,EAAG7E,EAAGC,EAAGC,EAAOC,EAAQnD,EAAW0D,CAAI,EACxD2D,EAAc3E,EAAM1C,CAAS,CAC/B,CAxBSN,EAAAkI,EAAA,QA8BT,SAASP,EAAcW,EAAQC,EAAmB,CAChD,QAAWvH,KAAOuH,EACZA,EAAkB,eAAevH,CAAG,GACtCsH,EAAO,KAAKtH,EAAKuH,EAAkBvH,CAAG,CAAC,CAG7C,CANS,OAAAhB,EAAA2H,EAAA,iBAQF,SAAU3D,EAAM,CACrB,OAAOA,EAAK,gBAAkB,KAAOkE,EAAOlE,EAAK,gBAAkB,MAAQyD,EAASG,CACtF,CACF,EAAG,EAEIY,EAAQ,CACb,SAAAzI,GACA,SAAAoC,GACA,UAAAgB,GACA,UAAAmC,GACA,QAAAC,GACA,UAAApF,GACA,cAAAuF,GACA,eAAAC,GACA,SAAAG,GACA,mBAAAL,GACA,gBAAA4B,GACA,sBAAAC,GACA,qBAAAC,GACA,qBAAAC,GACA,mBAAAN,GACA,mBAAAC,GACA,gBAAAC,GACA,WAAAR,GACA,YAAAlC,GACA,mBAAAd,GACA,0BACF,EC1lCA,IAAI6E,EAAO,CAAC,EAECC,EAAS,CACpB,KAAM,CACJ,OAAQ,OACR,MAAO,OACP,OAAQ,OACR,MAAO,MACT,EACA,YAAa,EACb,cAAe,CAAC,EAChB,YAAa,CAAC,EACd,OAAQ,CACN,UAAWC,EAAA,UAAY,CACrB,OACE,KAAK,IAAI,MACP,KACA,KAAK,OAAO,SAAW,EAAI,CAAC,CAAC,EAAI,KAAK,OAAO,IAAKC,GAAUA,EAAM,QAAU,CAAC,CAC/E,GACC,KAAK,MAAM,SAAW,EACnB,EACA,KAAK,MAAM,IAAKC,GAAOA,EAAG,QAAU,CAAC,EAAE,OAAO,CAACC,EAAKC,IAAMD,EAAMC,CAAC,IACpE,KAAK,SAAS,SAAW,EACtB,EACA,KAAK,SAAS,IAAKF,GAAOA,EAAG,QAAU,CAAC,EAAE,OAAO,CAACC,EAAKC,IAAMD,EAAMC,CAAC,IACvE,KAAK,MAAM,SAAW,EACnB,EACA,KAAK,MAAM,IAAKF,GAAOA,EAAG,QAAU,CAAC,EAAE,OAAO,CAACC,EAAKC,IAAMD,EAAMC,CAAC,EAEzE,EAhBW,aAiBX,MAAOJ,EAAA,UAAY,CACjB,KAAK,OAAS,CAAC,EACf,KAAK,MAAQ,CAAC,EACd,KAAK,MAAQ,CAAC,EACd,KAAK,SAAW,CAAC,EACjB,KAAK,MAAQ,CAAC,CAChB,EANO,SAOP,OAAQA,EAAA,SAAUK,EAAU,CAC1B,KAAK,MAAM,KAAKA,CAAQ,CAC1B,EAFQ,UAGR,SAAUL,EAAA,SAAUM,EAAY,CAC9B,KAAK,OAAO,KAAKA,CAAU,CAC7B,EAFU,YAGV,QAASN,EAAA,SAAUO,EAAW,CAC5B,KAAK,MAAM,KAAKA,CAAS,CAC3B,EAFS,WAGT,WAAYP,EAAA,SAAUQ,EAAU,CAC9B,KAAK,SAAS,KAAKA,CAAQ,CAC7B,EAFY,cAGZ,QAASR,EAAA,SAAUS,EAAW,CAC5B,KAAK,MAAM,KAAKA,CAAS,CAC3B,EAFS,WAGT,UAAWT,EAAA,UAAY,CACrB,OAAO,KAAK,OAAO,KAAK,OAAO,OAAS,CAAC,CAC3C,EAFW,aAGX,SAAUA,EAAA,UAAY,CACpB,OAAO,KAAK,MAAM,KAAK,MAAM,OAAS,CAAC,CACzC,EAFU,YAGV,YAAaA,EAAA,UAAY,CACvB,OAAO,KAAK,SAAS,KAAK,SAAS,OAAS,CAAC,CAC/C,EAFa,eAGb,SAAUA,EAAA,UAAY,CACpB,OAAO,KAAK,MAAM,KAAK,MAAM,OAAS,CAAC,CACzC,EAFU,YAGV,OAAQ,CAAC,EACT,MAAO,CAAC,EACR,MAAO,CAAC,EACR,SAAU,CAAC,EACX,MAAO,CAAC,CACV,EACA,KAAMA,EAAA,UAAY,CAChB,KAAK,cAAgB,CAAC,EACtB,KAAK,YAAc,CAAC,EACpB,KAAK,OAAO,MAAM,EAClB,KAAK,KAAO,CACV,OAAQ,OACR,MAAO,OACP,OAAQ,OACR,MAAO,MACT,EACA,KAAK,YAAc,EACnBU,GAAQC,EAAU,CAAC,CACrB,EAZM,QAaN,UAAWX,EAAA,SAAUY,EAAKC,EAAKC,EAAKC,EAAK,CACnCH,EAAIC,CAAG,IAAM,OACfD,EAAIC,CAAG,EAAIC,EAEXF,EAAIC,CAAG,EAAIE,EAAID,EAAKF,EAAIC,CAAG,CAAC,CAEhC,EANW,aAOX,aAAcb,EAAA,SAAUgB,EAAQC,EAAQC,EAAOC,EAAO,CAEpD,IAAMC,EAAQ,KACVC,EAAM,EAEV,SAASC,EAASC,EAAqB,CACrC,OAAOvB,EAAA,SAA0BwB,EAAM,CACrCH,IAEA,IAAMI,EAAIL,EAAM,cAAc,OAASC,EAAM,EAE7CD,EAAM,UAAUI,EAAM,SAAUP,EAASQ,EAAI3B,EAAK,UAAW,KAAK,GAAG,EACrEsB,EAAM,UAAUI,EAAM,QAASL,EAAQM,EAAI3B,EAAK,UAAW,KAAK,GAAG,EAEnEsB,EAAM,UAAUrB,EAAO,KAAM,SAAUiB,EAASS,EAAI3B,EAAK,UAAW,KAAK,GAAG,EAC5EsB,EAAM,UAAUrB,EAAO,KAAM,QAASmB,EAAQO,EAAI3B,EAAK,UAAW,KAAK,GAAG,EAEpEyB,IAAS,eACbH,EAAM,UAAUI,EAAM,SAAUR,EAASS,EAAI3B,EAAK,UAAW,KAAK,GAAG,EACrEsB,EAAM,UAAUI,EAAM,QAASN,EAAQO,EAAI3B,EAAK,UAAW,KAAK,GAAG,EAEnEsB,EAAM,UAAUrB,EAAO,KAAM,SAAUkB,EAASQ,EAAI3B,EAAK,UAAW,KAAK,GAAG,EAC5EsB,EAAM,UAAUrB,EAAO,KAAM,QAASoB,EAAQM,EAAI3B,EAAK,UAAW,KAAK,GAAG,EAE9E,EAlBO,mBAmBT,CApBSE,EAAAsB,EAAA,YAsBT,KAAK,cAAc,QAAQA,EAAS,CAAC,EACrC,KAAK,YAAY,QAAQA,EAAS,YAAY,CAAC,CACjD,EA7Bc,gBA8Bd,OAAQtB,EAAA,SAAUgB,EAAQC,EAAQC,EAAOC,EAAO,CAC9C,IAAMO,EAAUC,EAAO,OAAOX,EAAQE,CAAK,EACrCU,EAASD,EAAO,OAAOX,EAAQE,CAAK,EACpCW,EAAUF,EAAO,OAAOV,EAAQE,CAAK,EACrCW,EAASH,EAAO,OAAOV,EAAQE,CAAK,EAE1C,KAAK,UAAUpB,EAAO,KAAM,SAAU2B,EAAS,KAAK,GAAG,EACvD,KAAK,UAAU3B,EAAO,KAAM,SAAU8B,EAAS,KAAK,GAAG,EACvD,KAAK,UAAU9B,EAAO,KAAM,QAAS6B,EAAQ,KAAK,GAAG,EACrD,KAAK,UAAU7B,EAAO,KAAM,QAAS+B,EAAQ,KAAK,GAAG,EAErD,KAAK,aAAaJ,EAASG,EAASD,EAAQE,CAAM,CACpD,EAZQ,UAaR,cAAe9B,EAAA,SAAU+B,EAASC,EAASC,EAAQ,CACjD,IAAMC,EAAYD,EAAO,IAAIF,EAAQ,IAAI,EACnCI,EAAcC,GAAiBL,EAAQ,IAAI,EAAE,QAAU,EACvDM,EAAIH,EAAU,EAAIA,EAAU,MAAQ,GAAMC,EAAc,GAAKrC,EAAK,gBAAmB,EAC3F,KAAK,YAAY,KAAK,CACpB,OAAQuC,EACR,OAAQ,KAAK,YAAc,EAC3B,MAAOA,EAAIvC,EAAK,gBAChB,MAAO,OACP,MAAOiC,EAAQ,KACf,SAAUO,EAAQ,cAAcN,CAAO,CACzC,CAAC,CACH,EAZe,iBAaf,cAAehC,EAAA,SAAU+B,EAAS,CAEhC,IAAMQ,EAAyB,KAAK,YACjC,IAAI,SAAUC,EAAY,CACzB,OAAOA,EAAW,KACpB,CAAC,EACA,YAAYT,EAAQ,IAAI,EAC3B,OAAO,KAAK,YAAY,OAAOQ,EAAwB,CAAC,EAAE,CAAC,CAC7D,EARe,iBASf,WAAYvC,EAAA,SAAUyC,EAAQ,CAAE,QAAS,OAAW,KAAM,GAAO,MAAO,MAAU,EAAGC,EAAM,CACzF,MAAO,CACL,OAAQ,OACR,OAAQ,KAAK,YACb,MAAO,OACP,MAAO,OACP,MAAOD,EAAM,QACb,KAAMA,EAAM,KACZ,MAAOA,EAAM,MACb,OAAQ,EACR,KAAMC,CACR,CACF,EAZY,cAaZ,QAAS1C,EAAA,SAAUyC,EAAQ,CAAE,QAAS,OAAW,KAAM,GAAO,MAAO,MAAU,EAAGC,EAAM,CACtF,KAAK,cAAc,KAAK,KAAK,WAAWD,EAAOC,CAAI,CAAC,CACtD,EAFS,WAGT,QAAS1C,EAAA,UAAY,CACnB,OAAO,KAAK,cAAc,IAAI,CAChC,EAFS,WAGT,cAAeA,EAAA,UAAY,CACzB,OAAO,KAAK,cAAc,OACtB,KAAK,cAAc,KAAK,cAAc,OAAS,CAAC,EAAE,QAClD,EACN,EAJe,iBAKf,iBAAkBA,EAAA,SAAU+B,EAAS,CACnC,IAAMY,EAAO,KAAK,cAAc,IAAI,EACpCA,EAAK,SAAWA,EAAK,UAAY,CAAC,EAClCA,EAAK,cAAgBA,EAAK,eAAiB,CAAC,EAC5CA,EAAK,SAAS,KAAK,CAAE,EAAG5C,EAAO,eAAe,EAAG,OAAQ,CAAE,CAAC,EAC5D4C,EAAK,cAAc,KAAKZ,CAAO,EAC/B,KAAK,cAAc,KAAKY,CAAI,CAC9B,EAPkB,oBAQlB,gBAAiB3C,EAAA,UAAY,CACvB,KAAK,cAAc,IACrB,KAAK,iBAAmB,KAAK,YAEjC,EAJiB,mBAKjB,iBAAkBA,EAAA,UAAY,CACxB,KAAK,cAAc,IACrB,KAAK,YAAc,KAAK,iBAE5B,EAJkB,oBAKlB,gBAAiBA,EAAA,SAAU4C,EAAM,CAC/B,KAAK,YAAc,KAAK,YAAcA,EACtC,KAAK,KAAK,MAAQjB,EAAO,OAAO,KAAK,KAAK,MAAO,KAAK,WAAW,CACnE,EAHiB,mBAIjB,eAAgB3B,EAAA,UAAY,CAC1B,OAAO,KAAK,WACd,EAFgB,kBAGhB,UAAWA,EAAA,UAAY,CACrB,MAAO,CAAE,OAAQ,KAAK,KAAM,OAAQ,KAAK,MAAO,CAClD,EAFW,YAGb,EAoBM6C,GAAW7C,EAAA,eAAgB8C,EAAWrC,EAAsB,CAChEV,EAAO,gBAAgBD,EAAK,SAAS,EACrCW,EAAU,OAASX,EAAK,UACxBW,EAAU,OAASV,EAAO,eAAe,EACzC,IAAMgD,EAAqBC,GAAY,EACvCD,EAAK,EAAItC,EAAU,OACnBsC,EAAK,EAAItC,EAAU,OACnBsC,EAAK,MAAQtC,EAAU,OAASX,EAAK,MACrCiD,EAAK,MAAQ,OAEb,IAAME,EAAIH,EAAK,OAAO,GAAG,EACnBI,EAAWZ,EAAQ,SAASW,EAAGF,CAAI,EACnCI,EAAwBC,GAAW,EACzCD,EAAQ,EAAI1C,EAAU,OACtB0C,EAAQ,EAAI1C,EAAU,OACtB0C,EAAQ,MAAQJ,EAAK,MACrBI,EAAQ,GAAK,MACbA,EAAQ,KAAO1C,EAAU,QACzB0C,EAAQ,MAAQ,WAChBA,EAAQ,WAAarD,EAAK,eAC1BqD,EAAQ,SAAWrD,EAAK,aACxBqD,EAAQ,WAAarD,EAAK,eAC1BqD,EAAQ,OAASrD,EAAK,UACtBqD,EAAQ,WAAarD,EAAK,WAC1BqD,EAAQ,OAAS,SAEjB,IAAME,EAAWC,GAASH,EAAQ,IAAI,EAAI,MAAMI,GAAUN,EAAGE,CAAO,EAAIK,GAASP,EAAGE,CAAO,EAErFM,EAAa,KAAK,MACtBJ,EACG,IAAKK,IAAQA,EAAG,SAAWA,GAAI,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EACrD,OAAO,CAACvD,EAAKwD,IAASxD,EAAMwD,CAAI,CACrC,EAEAT,EAAS,KAAK,SAAUO,EAAa,EAAI3D,EAAK,UAAU,EACxDW,EAAU,QAAUgD,EAAa,EAAI3D,EAAK,WAC1CC,EAAO,gBAAgB0D,EAAa,EAAI3D,EAAK,UAAU,EACvDW,EAAU,MAAQA,EAAU,OAASgD,EAAa,EAAI3D,EAAK,WAC3DW,EAAU,MAAQA,EAAU,OAASsC,EAAK,MAC1ChD,EAAO,OAAOU,EAAU,OAAQA,EAAU,OAAQA,EAAU,MAAOA,EAAU,KAAK,EAClFV,EAAO,OAAO,QAAQU,CAAS,CACjC,EAzCiB,YA2CXmD,GAAc5D,EAAC6D,IACZ,CACL,WAAYA,EAAI,kBAChB,SAAUA,EAAI,gBACd,WAAYA,EAAI,iBAClB,GALkB,eAOdC,GAAW9D,EAAC6D,IACT,CACL,WAAYA,EAAI,eAChB,SAAUA,EAAI,aACd,WAAYA,EAAI,cAClB,GALe,YAOXE,GAAY/D,EAAC6D,IACV,CACL,WAAYA,EAAI,gBAChB,SAAUA,EAAI,cACd,WAAYA,EAAI,eAClB,GALgB,aAiBlB,eAAeG,GAAaC,EAAUzD,EAA2B,CAC/DT,EAAO,gBAAgB,EAAE,EACzB,GAAM,CAAE,OAAAiB,EAAQ,MAAAE,EAAO,QAAAa,CAAQ,EAAIvB,EAC7B0D,EAAQvC,EAAO,YAAYI,CAAO,EAAE,OACpCoC,EAAab,GAASvB,CAAO,EAC7BqC,EAAWD,EACb,MAAME,GAA0BtC,EAASpB,EAAU,CAAC,EACpD2D,EAAM,wBAAwBvC,EAAS6B,GAAY9D,CAAI,CAAC,EAE5D,GAAI,CAACqE,EAAY,CACf,IAAMI,EAAaH,EAAS,OAASF,EACrC1D,EAAS,QAAU+D,EACnBxE,EAAO,gBAAgBwE,CAAU,CACnC,CAEA,IAAIC,EACAC,EAAcL,EAAS,OAAS,GAC9BM,EAAYN,EAAS,MAE3B,GAAIpD,IAAWE,EAAO,CACpBsD,EAAazE,EAAO,eAAe,EAAI0E,EAClC3E,EAAK,cACR2E,GAAe3E,EAAK,UACpB0E,EAAazE,EAAO,eAAe,EAAI0E,GAEzCA,GAAe,GACf,IAAME,EAAKhD,EAAO,OAAO+C,EAAY,EAAG5E,EAAK,MAAQ,CAAC,EACtDC,EAAO,OACLiB,EAAS2D,EACT5E,EAAO,eAAe,EAAI,GAAK0E,EAC/BvD,EAAQyD,EACR5E,EAAO,eAAe,EAAI,GAAK0E,CACjC,CACF,MACEA,GAAe3E,EAAK,UACpB0E,EAAazE,EAAO,eAAe,EAAI0E,EACvC1E,EAAO,OAAOiB,EAAQwD,EAAa,GAAItD,EAAOsD,CAAU,EAE1D,OAAAzE,EAAO,gBAAgB0E,CAAW,EAClCjE,EAAS,QAAUiE,EACnBjE,EAAS,MAAQA,EAAS,OAASA,EAAS,OAC5CT,EAAO,OAAOS,EAAS,WAAYA,EAAS,OAAQA,EAAS,SAAUA,EAAS,KAAK,EAE9EgE,CACT,CA5CexE,EAAAgE,GAAA,gBAsDf,IAAMY,GAAc5E,EAAA,eAAgBgC,EAASxB,EAAUgE,EAAoBK,EAAkB,CAC3F,GAAM,CAAE,OAAA7D,EAAQ,MAAAE,EAAO,OAAAD,EAAQ,QAAAc,EAAS,KAAAR,EAAM,cAAAuD,EAAe,gBAAAC,CAAgB,EAAIvE,EAC3E4D,EAAWE,EAAM,wBAAwBvC,EAAS6B,GAAY9D,CAAI,CAAC,EACnEqD,EAAwBC,GAAW,EACzCD,EAAQ,EAAInC,EACZmC,EAAQ,EAAIlC,EAAS,GACrBkC,EAAQ,MAAQjC,EAAQF,EACxBmC,EAAQ,MAAQ,cAChBA,EAAQ,GAAK,MACbA,EAAQ,KAAOpB,EACfoB,EAAQ,WAAarD,EAAK,kBAC1BqD,EAAQ,SAAWrD,EAAK,gBACxBqD,EAAQ,WAAarD,EAAK,kBAC1BqD,EAAQ,OAASrD,EAAK,aACtBqD,EAAQ,OAAS,SACjBA,EAAQ,WAAarD,EAAK,YAC1BqD,EAAQ,MAAQ,GAEZG,GAASH,EAAQ,IAAI,EACvB,MAAMI,GAAUvB,EAASmB,EAAS,CAAE,OAAAnC,EAAQ,MAAAE,EAAO,OAAQsD,CAAW,CAAC,EAEvEhB,GAASxB,EAASmB,CAAO,EAG3B,IAAMuB,EAAYN,EAAS,MAEvBY,EACAhE,IAAWE,EACTpB,EAAK,YACPkF,EAAOhD,EACJ,OAAO,MAAM,EACb,KACC,IACA,MAAMhB,CAAM,IAAIwD,CAAU,MACxBxD,EAASW,EAAO,OAAO7B,EAAK,MAAQ,EAAG4E,EAAY,CAAC,CACtD,MAAMF,EAAa,EAAE,MAAMxD,CAAM,EACnC,EAEFgE,EAAOhD,EACJ,OAAO,MAAM,EACb,KACC,IACA,KACEhB,EACA,IACAwD,EACA,OACCxD,EAAS,IACV,KACCwD,EAAa,IACd,KACCxD,EAAS,IACV,KACCwD,EAAa,IACd,IACAxD,EACA,KACCwD,EAAa,GAClB,GAGJQ,EAAOhD,EAAQ,OAAO,MAAM,EAC5BgD,EAAK,KAAK,KAAMhE,CAAM,EACtBgE,EAAK,KAAK,KAAMR,CAAU,EAC1BQ,EAAK,KAAK,KAAM9D,CAAK,EACrB8D,EAAK,KAAK,KAAMR,CAAU,GAK1BjD,IAASsD,EAAQ,GAAG,SAAS,QAC7BtD,IAASsD,EAAQ,GAAG,SAAS,cAC7BtD,IAASsD,EAAQ,GAAG,SAAS,cAC7BtD,IAASsD,EAAQ,GAAG,SAAS,aAC7BtD,IAASsD,EAAQ,GAAG,SAAS,sBAE7BG,EAAK,MAAM,mBAAoB,MAAM,EACrCA,EAAK,KAAK,QAAS,cAAc,GAEjCA,EAAK,KAAK,QAAS,cAAc,EAGnC,IAAIC,EAAM,GACNnF,EAAK,sBACPmF,EAAMC,GAAO,EAAI,GAGnBF,EAAK,KAAK,eAAgB,CAAC,EAC3BA,EAAK,KAAK,SAAU,MAAM,EAC1BA,EAAK,MAAM,OAAQ,MAAM,GACrBzD,IAASsD,EAAQ,GAAG,SAAS,OAAStD,IAASsD,EAAQ,GAAG,SAAS,SACrEG,EAAK,KAAK,aAAc,OAASC,EAAM,aAAa,GAGpD1D,IAASsD,EAAQ,GAAG,SAAS,qBAC7BtD,IAASsD,EAAQ,GAAG,SAAS,wBAE7BG,EAAK,KAAK,eAAgB,OAASC,EAAM,aAAa,EACtDD,EAAK,KAAK,aAAc,OAASC,EAAM,aAAa,IAElD1D,IAASsD,EAAQ,GAAG,SAAS,aAAetD,IAASsD,EAAQ,GAAG,SAAS,eAC3EG,EAAK,KAAK,aAAc,OAASC,EAAM,eAAe,GAGpD1D,IAASsD,EAAQ,GAAG,SAAS,aAAetD,IAASsD,EAAQ,GAAG,SAAS,eAC3EG,EAAK,KAAK,aAAc,OAASC,EAAM,aAAa,GAIlDF,GAAmBjF,EAAK,uBAC1BkF,EAAK,KAAK,eAAgB,OAASC,EAAM,kBAAkB,EAC3DjD,EACG,OAAO,MAAM,EACb,KAAK,IAAKhB,CAAM,EAChB,KAAK,IAAKwD,EAAa,CAAC,EACxB,KAAK,cAAe,YAAY,EAChC,KAAK,YAAa,MAAM,EACxB,KAAK,cAAe,QAAQ,EAC5B,KAAK,QAAS,gBAAgB,EAC9B,KAAKM,CAAa,EAEzB,EAzHoB,eA2HdK,GAAwBnF,EAAA,SAC5BgC,EACAC,EACAmD,EACAC,EACAC,EACAC,EACAC,EACA,CACA,IAAIC,EAAY,EACZC,EAAa,EACbC,EACAC,EAAY,EAEhB,QAAWC,KAAYR,EAAW,CAChC,IAAMpF,EAAQgC,EAAO,IAAI4D,CAAQ,EAC3BC,EAAM7F,EAAM,IAGd0F,GAAWA,GAAWG,IACnBN,GACHzF,EAAO,OAAO,OAAO4F,CAAO,EAE9BD,GAAc5F,EAAK,UAAY6F,EAAQ,QAIrCG,GAAOA,GAAOH,IACXH,IACHM,EAAI,EAAIL,EAAYC,EACpBI,EAAI,EAAIR,GAEVI,GAAcI,EAAI,QAIpB7F,EAAM,MAAQA,EAAM,OAASH,EAAK,MAClCG,EAAM,OAAS0B,EAAO,OAAO1B,EAAM,QAAUH,EAAK,OAAQA,EAAK,MAAM,EACrEG,EAAM,OAASA,EAAM,QAAUH,EAAK,YAEpC8F,EAAYjE,EAAO,OAAOiE,EAAW3F,EAAM,MAAM,EAG7CmF,EAAc,IAAInF,EAAM,IAAI,IAC9ByF,GAAczF,EAAM,MAAQ,GAG9BA,EAAM,EAAIwF,EAAYC,EACtBzF,EAAM,OAASF,EAAO,eAAe,EAErCA,EAAO,OAAOE,EAAM,EAAGqF,EAAarF,EAAM,EAAIA,EAAM,MAAOA,EAAM,MAAM,EAEvEwF,GAAaxF,EAAM,MAAQyF,EACvBzF,EAAM,MACRA,EAAM,IAAI,MAAQwF,EAAYK,EAAI,OAAS7F,EAAM,IAAI,GAEvDyF,EAAazF,EAAM,OACnB0F,EAAU1F,EAAM,IAChBF,EAAO,OAAO,SAASE,CAAK,CAC9B,CAGI0F,GAAW,CAACH,GACdzF,EAAO,OAAO,OAAO4F,CAAO,EAI9B5F,EAAO,gBAAgB6F,CAAS,CAClC,EApE8B,yBAsEjBG,GAAa/F,EAAA,eAAgBgC,EAASC,EAAQoD,EAAWG,EAAU,CAC9E,GAAKA,EAME,CACL,IAAII,EAAY,EAChB7F,EAAO,gBAAgBD,EAAK,UAAY,CAAC,EACzC,QAAW+F,KAAYR,EAAW,CAChC,IAAMpF,EAAQgC,EAAO,IAAI4D,CAAQ,EAC5B5F,EAAM,QACTA,EAAM,MAAQF,EAAO,eAAe,GAEtC,IAAMiG,EAAS,MAAM1D,EAAQ,UAAUN,EAAS/B,EAAOH,EAAM,EAAI,EACjE8F,EAAYjE,EAAO,OAAOiE,EAAWI,CAAM,CAC7C,CACAjG,EAAO,gBAAgB6F,EAAY9F,EAAK,SAAS,CACnD,KAjBE,SAAW+F,KAAYR,EAAW,CAChC,IAAMpF,EAAQgC,EAAO,IAAI4D,CAAQ,EAEjC,MAAMvD,EAAQ,UAAUN,EAAS/B,EAAOH,EAAM,EAAK,CACrD,CAcJ,EApB0B,cAsBbmG,GAAkBjG,EAAA,SAAUgC,EAASC,EAAQoD,EAAWa,EAAK,CACxE,IAAIN,EAAY,EACZO,EAAW,EACf,QAAWN,KAAYR,EAAW,CAChC,IAAMpF,EAAQgC,EAAO,IAAI4D,CAAQ,EAC3BO,EAAeC,GAAsBpG,CAAK,EAC1CqG,EAAiBhE,EAAQ,UAC7BN,EACA/B,EACAmG,EACAtG,EACAA,EAAK,WACLoG,CACF,EACII,EAAe,OAASV,IAC1BA,EAAYU,EAAe,QAEzBA,EAAe,MAAQrG,EAAM,EAAIkG,IACnCA,EAAWG,EAAe,MAAQrG,EAAM,EAE5C,CAEA,MAAO,CAAE,UAAW2F,EAAW,SAAUO,CAAS,CACpD,EAvB+B,mBAyBlBzF,GAAUV,EAAA,SAAU6D,EAAK,CACpC0C,GAAgBzG,EAAM+D,CAAG,EAErBA,EAAI,aACN/D,EAAK,gBAAkBA,EAAK,eAAiBA,EAAK,kBAAoB+D,EAAI,YAExEA,EAAI,WACN/D,EAAK,cAAgBA,EAAK,aAAeA,EAAK,gBAAkB+D,EAAI,UAElEA,EAAI,aACN/D,EAAK,gBAAkBA,EAAK,eAAiBA,EAAK,kBAAoB+D,EAAI,WAE9E,EAZuB,WAcjBzB,GAAmBpC,EAAA,SAAUC,EAAO,CACxC,OAAOF,EAAO,YAAY,OAAO,SAAUyC,EAAY,CACrD,OAAOA,EAAW,QAAUvC,CAC9B,CAAC,CACH,EAJyB,oBAMnBuG,GAAmBxG,EAAA,SAAUC,EAAOgC,EAAQ,CAEhD,IAAMwE,EAAWxE,EAAO,IAAIhC,CAAK,EAC3ByG,EAActE,GAAiBnC,CAAK,EAEpC0G,EAAOD,EAAY,OACvB,SAAUvG,EAAKqC,EAAY,CACzB,OAAOb,EAAO,OAAOxB,EAAKqC,EAAW,MAAM,CAC7C,EACAiE,EAAS,EAAIA,EAAS,MAAQ,EAAI,CACpC,EACMG,EAAQF,EAAY,OACxB,SAAUvG,EAAKqC,EAAY,CACzB,OAAOb,EAAO,OAAOxB,EAAKqC,EAAW,KAAK,CAC5C,EACAiE,EAAS,EAAIA,EAAS,MAAQ,EAAI,CACpC,EACA,MAAO,CAACE,EAAMC,CAAK,CACrB,EAlByB,oBAoBzB,SAASC,GAAwBC,EAAYC,EAAKC,EAAWC,EAAYC,EAAW,CAClFnH,EAAO,gBAAgBiH,CAAS,EAChC,IAAIG,EAAeF,EACnB,GAAIF,EAAI,IAAMA,EAAI,SAAWD,EAAWC,EAAI,EAAE,EAAG,CAC/C,IAAMK,EAAYN,EAAWC,EAAI,EAAE,EAAE,MAC/BM,EAAWzD,GAAY9D,CAAI,EACjCiH,EAAI,QAAUzC,EAAM,UAAU,IAAIyC,EAAI,OAAO,IAAKK,EAAY,EAAItH,EAAK,YAAauH,CAAQ,EAC5FN,EAAI,MAAQK,EACZL,EAAI,KAAO,GAGX,IAAM3C,EAAWE,EAAM,wBAAwByC,EAAI,QAASM,CAAQ,EAC9D5C,EAAc9C,EAAO,OAAOyC,EAAS,OAAQtE,EAAK,cAAc,EACtEqH,EAAeF,EAAaxC,EAC5B6C,EAAI,MAAM,GAAG7C,CAAW,MAAMsC,EAAI,OAAO,EAAE,CAC7C,CACAG,EAAUH,CAAG,EACbhH,EAAO,gBAAgBoH,CAAY,CACrC,CAlBSnH,EAAA6G,GAAA,2BA8BT,SAASU,GACPR,EACAvG,EACAgE,EACAgD,EACAvF,EACAmD,EACAqC,EACA,CACA,SAASC,EAAmBzH,EAAO0H,EAAY,CACzC1H,EAAM,EAAIgC,EAAO,IAAI8E,EAAI,IAAI,EAAE,GACjChH,EAAO,OACLS,EAAS,MAAQmH,EACjBnH,EAAS,OACTA,EAAS,OACTA,EAAS,MAAQP,EAAM,OAAS,EAAIH,EAAK,UAC3C,EACAU,EAAS,MAAQA,EAAS,MAAQmH,IAElC5H,EAAO,OACLS,EAAS,OACTA,EAAS,OACTA,EAAS,MAAQmH,EACjBnH,EAAS,MAAQP,EAAM,OAAS,EAAIH,EAAK,UAC3C,EACAU,EAAS,MAAQA,EAAS,MAAQmH,EAEtC,CAlBS3H,EAAA0H,EAAA,sBAoBT,SAASE,EAAiB3H,EAAO0H,EAAY,CACvC1H,EAAM,EAAIgC,EAAO,IAAI8E,EAAI,EAAE,EAAE,GAC/BhH,EAAO,OACLS,EAAS,OAASmH,EAClBnH,EAAS,OACTA,EAAS,MACTA,EAAS,MAAQP,EAAM,OAAS,EAAIH,EAAK,UAC3C,EACAU,EAAS,OAASA,EAAS,OAASmH,IAEpC5H,EAAO,OACLS,EAAS,MACTA,EAAS,OACTA,EAAS,OAASmH,EAClBnH,EAAS,MAAQP,EAAM,OAAS,EAAIH,EAAK,UAC3C,EACAU,EAAS,OAASA,EAAS,OAASmH,EAExC,CAGA,GArBS3H,EAAA4H,EAAA,oBAqBLxC,EAAc,IAAI2B,EAAI,EAAE,GAAKS,EAAO,CACtC,IAAMvH,EAAQgC,EAAO,IAAI8E,EAAI,EAAE,EACzBY,EAAa1H,EAAM,MAAQ,QAAU4H,GAAmB,EAAI,EAAI5H,EAAM,MAAQ,EAAI,EACxFyH,EAAmBzH,EAAO0H,CAAU,EACpC1H,EAAM,OAASuE,EAAavE,EAAM,OAAS,EAC3CF,EAAO,gBAAgBE,EAAM,OAAS,CAAC,CACzC,SAESwH,EAAgB,IAAIV,EAAI,IAAI,GAAKS,EAAO,CAC/C,IAAMvH,EAAQgC,EAAO,IAAI8E,EAAI,IAAI,EACjC,GAAIjH,EAAK,aAAc,CACrB,IAAM6H,EAAa1H,EAAM,MAAQ,QAAU4H,GAAmB,EAAI5H,EAAM,MAAQ,EAChF2H,EAAiB3H,EAAO0H,CAAU,CACpC,CACA1H,EAAM,MAAQuE,EAAavE,EAAM,OAAS,EAC1CF,EAAO,gBAAgBE,EAAM,OAAS,CAAC,CACzC,SAESwH,EAAgB,IAAIV,EAAI,EAAE,GAAKS,EAAO,CAC7C,IAAMvH,EAAQgC,EAAO,IAAI8E,EAAI,EAAE,EAC/B,GAAIjH,EAAK,aAAc,CACrB,IAAM6H,EAAa1H,EAAM,MAAQ,QAAU4H,GAAmB,EAAI,EAAI5H,EAAM,MAAQ,EAAI,EACxFyH,EAAmBzH,EAAO0H,CAAU,CACtC,CACA1H,EAAM,MAAQuE,EAAavE,EAAM,OAAS,EAC1CF,EAAO,gBAAgBE,EAAM,OAAS,CAAC,CACzC,CACF,CA7ESD,EAAAuH,GAAA,8BAuFF,IAAMO,GAAO9H,EAAA,eAAgB+H,EAAeC,EAAYC,EAAkBpD,EAAkB,CACjG,GAAM,CAAE,cAAAqD,EAAe,SAAAC,CAAS,EAAIxH,EAAU,EAC9Cb,EAAOqI,EAEP,IAAIC,EACAF,IAAkB,YACpBE,EAAiBC,GAAO,KAAOL,CAAE,GAGnC,IAAMM,EACJJ,IAAkB,UACdG,GAAOD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EACrDC,GAAO,MAAM,EACbnC,EAAMgC,IAAkB,UAAYE,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAkB,SACtFrI,EAAO,KAAK,EACZuH,EAAI,MAAMzC,EAAQ,EAAE,EAEpB,IAAM7C,EACJkG,IAAkB,UAAYI,EAAK,OAAO,QAAQN,CAAE,IAAI,EAAIK,GAAO,QAAQL,CAAE,IAAI,EAG7E/F,EAAS4C,EAAQ,GAAG,UAAU,EAC9BO,EAAgBP,EAAQ,GAAG,iBAAiB,EAC5C4C,EAAkB5C,EAAQ,GAAG,mBAAmB,EAChD0D,EAAQ1D,EAAQ,GAAG,SAAS,EAC9BQ,EAAYR,EAAQ,GAAG,aAAa,EAClCU,EAAWV,EAAQ,GAAG,YAAY,EAClCpC,EAAQoC,EAAQ,GAAG,gBAAgB,EACnC2D,EAAW3D,EAAQ,GAAG,iBAAiB,EACvC4D,EAAe5D,EAAQ,GAAG,0BAA0B,EACpD6D,EAA0B,MAAMC,GAA2B1G,EAAQsD,EAAUV,CAAO,EAc1F,GAbA/E,EAAK,OAAS,MAAM8I,GAAsB3G,EAAQyG,EAAyBH,CAAK,EAEhFjG,EAAQ,mBAAmBN,CAAO,EAClCM,EAAQ,mBAAmBN,CAAO,EAClCM,EAAQ,gBAAgBN,CAAO,EAE3BwG,IACFzI,EAAO,gBAAgBD,EAAK,SAAS,EACjC2I,GACF1I,EAAO,gBAAgBwI,EAAM,CAAC,EAAE,aAAa,GAI7CzI,EAAK,yBAA2B,GAAM,CACxC,IAAM+I,EAAY,IAAI,IACtBtD,EAAS,QAASxD,GAAY,CAC5B8G,EAAU,IAAI9G,EAAQ,IAAI,EAC1B8G,EAAU,IAAI9G,EAAQ,EAAE,CAC1B,CAAC,EACDsD,EAAYA,EAAU,OAAQQ,GAAagD,EAAU,IAAIhD,CAAQ,CAAC,CACpE,CAEAV,GAAsBnD,EAASC,EAAQmD,EAAeC,EAAW,EAAGE,EAAU,EAAK,EACnF,IAAMuB,EAAa,MAAMgC,GAAoBvD,EAAUtD,EAAQyG,EAAyB7D,CAAO,EAG/FvC,EAAQ,gBAAgBN,CAAO,EAC/BM,EAAQ,qBAAqBN,CAAO,EACpCM,EAAQ,sBAAsBN,CAAO,EACrCM,EAAQ,qBAAqBN,CAAO,EAMpC,SAAS+G,EAAUhC,EAAUzB,EAAqB,CAChD,IAAM0D,EAAiBjJ,EAAO,cAAcgH,CAAG,EAC3CiC,EAAe,OAAS,GAAK1D,IAC/B0D,EAAe,OAAS1D,EAAc,EACtCA,GAAe,IAEjBhD,EAAQ,eACNN,EACAgH,EACA1D,EACAxF,EACAsC,GAAiB2E,EAAI,IAAI,EAAE,MAC7B,EAEAhH,EAAO,OAAOiJ,EAAe,OAAQ1D,EAAc,GAAI0D,EAAe,MAAO1D,CAAW,CAC1F,CAfStF,EAAA+I,EAAA,aAkBT,IAAIjE,EAAgB,EAChBmE,EAAoB,EAClBC,GAAiB,CAAC,EAClBC,EAAc,CAAC,EACjB3B,EAAQ,EACZ,QAAWT,KAAOxB,EAAU,CAC1B,IAAIhF,EAAWE,EAAWD,GAE1B,OAAQuG,EAAI,KAAM,CAChB,KAAKlC,EAAQ,GAAG,SAAS,KACvB9E,EAAO,iBAAiB,EACxBU,EAAYsG,EAAI,UAChB,MAAMlE,GAASb,EAASvB,CAAS,EACjC,MACF,KAAKoE,EAAQ,GAAG,SAAS,aACvB9E,EAAO,cAAcgH,EAAK/E,EAASC,CAAM,EACzC,MACF,KAAK4C,EAAQ,GAAG,SAAS,WACvBkE,EAAUhC,EAAKhH,EAAO,eAAe,CAAC,EACtC,MACF,KAAK8E,EAAQ,GAAG,SAAS,WACvBgC,GACEC,EACAC,EACAjH,EAAK,UACLA,EAAK,UAAYA,EAAK,cACrBiC,GAAYhC,EAAO,QAAQgC,CAAO,CACrC,EACA,MACF,KAAK8C,EAAQ,GAAG,SAAS,SACvBtE,EAAYR,EAAO,QAAQ,EAC3B,MAAMuC,EAAQ,SAASN,EAASzB,EAAW,OAAQT,CAAI,EACvDC,EAAO,gBAAgBQ,EAAU,MAAQR,EAAO,eAAe,CAAC,EAChEA,EAAO,OAAO,QAAQQ,CAAS,EAC/B,MACF,KAAKsE,EAAQ,GAAG,SAAS,WACvBgC,GAAwBC,EAAYC,EAAKjH,EAAK,UAAWA,EAAK,UAAYiC,GACxEhC,EAAO,QAAQ,OAAWgC,EAAQ,OAAO,CAC3C,EACA,MACF,KAAK8C,EAAQ,GAAG,SAAS,SACvBtE,EAAYR,EAAO,QAAQ,EAC3BoJ,EAAY,KAAK5I,CAAS,EAC1BR,EAAO,OAAO,QAAQQ,CAAS,EAC/BR,EAAO,gBAAgBQ,EAAU,MAAQR,EAAO,eAAe,CAAC,EAChE,MACF,KAAK8E,EAAQ,GAAG,SAAS,UACvBgC,GACEC,EACAC,EACAjH,EAAK,UACLA,EAAK,UAAYA,EAAK,cACrBiC,GAAYhC,EAAO,QAAQgC,CAAO,CACrC,EACA,MACF,KAAK8C,EAAQ,GAAG,SAAS,QACvBtE,EAAYR,EAAO,QAAQ,EAC3B,MAAMuC,EAAQ,SAASN,EAASzB,EAAW,MAAOT,CAAI,EACtDC,EAAO,gBAAgBQ,EAAU,MAAQR,EAAO,eAAe,CAAC,EAChEA,EAAO,OAAO,QAAQQ,CAAS,EAC/B,MACF,KAAKsE,EAAQ,GAAG,SAAS,UACvBgC,GACEC,EACAC,EACAjH,EAAK,UACLA,EAAK,UAAYA,EAAK,cACrBiC,GAAYhC,EAAO,QAAQgC,CAAO,CACrC,EACA,MACF,KAAK8C,EAAQ,GAAG,SAAS,SACvBgC,GACEC,EACAC,EACAjH,EAAK,UAAYA,EAAK,cACtBA,EAAK,UACJiC,GAAYhC,EAAO,iBAAiBgC,CAAO,CAC9C,EACA,MACF,KAAK8C,EAAQ,GAAG,SAAS,QACvBtE,EAAYR,EAAO,QAAQ,EAC3B,MAAMuC,EAAQ,SAASN,EAASzB,EAAW,MAAOT,CAAI,EACtDC,EAAO,gBAAgBQ,EAAU,MAAQR,EAAO,eAAe,CAAC,EAChEA,EAAO,OAAO,QAAQQ,CAAS,EAC/B,MACF,KAAKsE,EAAQ,GAAG,SAAS,UACzB,KAAKA,EAAQ,GAAG,SAAS,eACvBgC,GACEC,EACAC,EACAjH,EAAK,UACLA,EAAK,UAAYA,EAAK,cACrBiC,GAAYhC,EAAO,QAAQgC,CAAO,CACrC,EACAhC,EAAO,gBAAgB,EACvB,MACF,KAAK8E,EAAQ,GAAG,SAAS,QACvBgC,GACEC,EACAC,EACAjH,EAAK,UAAYA,EAAK,cACtBA,EAAK,UACJiC,GAAYhC,EAAO,iBAAiBgC,CAAO,CAC9C,EACA,MACF,KAAK8C,EAAQ,GAAG,SAAS,QACvBtE,EAAYR,EAAO,QAAQ,EAC3B,MAAMuC,EAAQ,SAASN,EAASzB,EAAW,MAAOT,CAAI,EACtDC,EAAO,gBAAgBQ,EAAU,MAAQR,EAAO,eAAe,CAAC,EAChEA,EAAO,OAAO,QAAQQ,CAAS,EAC/B,MACF,KAAKsE,EAAQ,GAAG,SAAS,WACvBC,EAAgBiC,EAAI,QAAQ,OAASjC,EACrCmE,EAAoBlC,EAAI,QAAQ,MAAQkC,EACpClC,EAAI,QAAQ,QACdlC,EAAQ,GAAG,sBAAsB,EAEjCA,EAAQ,GAAG,uBAAuB,EAEpC,MACF,KAAKA,EAAQ,GAAG,SAAS,eACvBgC,GACEC,EACAC,EACAjH,EAAK,UACLA,EAAK,UAAYA,EAAK,cACrBiC,GAAYhC,EAAO,QAAQgC,CAAO,CACrC,EACA,MACF,KAAK8C,EAAQ,GAAG,SAAS,gBACvBgC,GACEC,EACAC,EACAjH,EAAK,UAAYA,EAAK,cACtBA,EAAK,UACJiC,GAAYhC,EAAO,iBAAiBgC,CAAO,CAC9C,EACA,MACF,KAAK8C,EAAQ,GAAG,SAAS,aACvBtE,EAAYR,EAAO,QAAQ,EAC3B,MAAMuC,EAAQ,SAASN,EAASzB,EAAW,WAAYT,CAAI,EAC3DC,EAAO,gBAAgBQ,EAAU,MAAQR,EAAO,eAAe,CAAC,EAChEA,EAAO,OAAO,QAAQQ,CAAS,EAC/B,MACF,KAAKsE,EAAQ,GAAG,SAAS,YACvBgC,GACEC,EACAC,EACAjH,EAAK,UACLA,EAAK,UAAYA,EAAK,cACrBiC,GAAYhC,EAAO,QAAQgC,CAAO,CACrC,EACA,MACF,KAAK8C,EAAQ,GAAG,SAAS,UACvBtE,EAAYR,EAAO,QAAQ,EAC3B,MAAMuC,EAAQ,SAASN,EAASzB,EAAW,QAAST,CAAI,EACxDC,EAAO,gBAAgBQ,EAAU,MAAQR,EAAO,eAAe,CAAC,EAChEA,EAAO,OAAO,QAAQQ,CAAS,EAC/B,MACF,QACE,GAAI,CACFC,GAAWuG,EAAI,SACfvG,GAAS,OAAST,EAAO,eAAe,EACxCS,GAAS,cAAgBsE,EACzBtE,GAAS,gBAAkBqE,EAAQ,GAAG,oBAAoB,EAC1D,IAAML,EAAa,MAAMR,GAAahC,EAASxB,EAAQ,EACvD+G,GACER,EACAvG,GACAgE,EACAgD,EACAvF,EACAmD,EACAqC,CACF,EACAyB,GAAe,KAAK,CAAE,aAAc1I,GAAU,WAAYgE,CAAW,CAAC,EACtEzE,EAAO,OAAO,WAAWS,EAAQ,CACnC,OAAS4I,EAAG,CACV9B,EAAI,MAAM,8BAA+B8B,CAAC,CAC5C,CACJ,CAIE,CACEvE,EAAQ,GAAG,SAAS,WACpBA,EAAQ,GAAG,SAAS,YACpBA,EAAQ,GAAG,SAAS,MACpBA,EAAQ,GAAG,SAAS,OACpBA,EAAQ,GAAG,SAAS,YACpBA,EAAQ,GAAG,SAAS,aACpBA,EAAQ,GAAG,SAAS,YACpBA,EAAQ,GAAG,SAAS,aACpBA,EAAQ,GAAG,SAAS,oBACpBA,EAAQ,GAAG,SAAS,oBACtB,EAAE,SAASkC,EAAI,IAAI,IAEnBjC,EAAgBA,EAAgBmE,GAElCzB,GACF,CAEAF,EAAI,MAAM,gBAAiBlC,CAAa,EACxCkC,EAAI,MAAM,kBAAmBG,CAAe,EAC5C,MAAM1B,GAAW/D,EAASC,EAAQoD,EAAW,EAAK,EAElD,QAAW+D,KAAKF,GACd,MAAMtE,GAAY5C,EAASoH,EAAE,aAAcA,EAAE,WAAYvE,CAAO,EAE9D/E,EAAK,cACP,MAAMiG,GAAW/D,EAASC,EAAQoD,EAAW,EAAI,EAEnD8D,EAAY,QAASC,GAAM9G,EAAQ,mBAAmBN,EAASoH,CAAC,CAAC,EACjEC,GAAmBrH,EAASC,EAAQoD,EAAWvF,CAAI,EAEnD,QAAWgG,KAAO/F,EAAO,OAAO,MAC9B+F,EAAI,OAAS/F,EAAO,eAAe,EAAI+F,EAAI,EAC3C/F,EAAO,OAAO+F,EAAI,EAAGA,EAAI,EAAGA,EAAI,EAAIA,EAAI,MAAOA,EAAI,MAAM,EACzDA,EAAI,OAASA,EAAI,EACjBA,EAAI,OAASA,EAAI,EACjBA,EAAI,MAAQA,EAAI,OAASA,EAAI,MAC7BA,EAAI,MAAQA,EAAI,OAASA,EAAI,OAC7BA,EAAI,OAAS,kBACbxD,EAAQ,QAAQN,EAAS8D,EAAKhG,CAAI,EAGhC0I,GACFzI,EAAO,gBAAgBD,EAAK,SAAS,EAIvC,IAAMwJ,EAAkBrD,GAAgBjE,EAASC,EAAQoD,EAAWa,CAAG,EAEjE,CAAE,OAAQJ,CAAI,EAAI/F,EAAO,UAAU,EAErC+F,EAAI,SAAW,SACjBA,EAAI,OAAS,GAEXA,EAAI,SAAW,SACjBA,EAAI,OAAS,GAEXA,EAAI,QAAU,SAChBA,EAAI,MAAQ,GAEVA,EAAI,QAAU,SAChBA,EAAI,MAAQ,GAId,IAAIyD,EAAYzD,EAAI,MAAQA,EAAI,OAC5ByD,EAAYD,EAAgB,YAC9BC,EAAYD,EAAgB,WAG9B,IAAItD,EAASuD,EAAY,EAAIzJ,EAAK,eAC9BA,EAAK,eACPkG,EAASA,EAASlG,EAAK,UAAYA,EAAK,iBAI1C,IAAI0J,EAAW1D,EAAI,MAAQA,EAAI,OAC3B0D,EAAWF,EAAgB,WAC7BE,EAAWF,EAAgB,UAE7B,IAAMG,GAAQD,EAAW,EAAI1J,EAAK,eAE9B2C,GACFT,EACG,OAAO,MAAM,EACb,KAAKS,CAAK,EACV,KAAK,KAAMqD,EAAI,MAAQA,EAAI,QAAU,EAAI,EAAIhG,EAAK,cAAc,EAChE,KAAK,IAAK,GAAG,EAGlB4J,GAAiB1H,EAASgE,EAAQyD,GAAO3J,EAAK,WAAW,EAEzD,IAAM6J,EAAoBlH,EAAQ,GAAK,EACvCT,EAAQ,KACN,UACA8D,EAAI,OACFhG,EAAK,eACL,MACCA,EAAK,eAAiB6J,GACvB,IACAF,GACA,KACCzD,EAAS2D,EACd,EAEArC,EAAI,MAAM,UAAWvH,EAAO,MAAM,CACpC,EAtXoB,QAmYpB,eAAe4I,GACb1G,EACAsD,EACAV,EACiC,CACjC,IAAM6D,EAA0B,CAAC,EAEjC,QAAW3B,KAAOxB,EAChB,GAAItD,EAAO,IAAI8E,EAAI,EAAE,GAAK9E,EAAO,IAAI8E,EAAI,IAAI,EAAG,CAC9C,IAAM9G,EAAQgC,EAAO,IAAI8E,EAAI,EAAE,EAQ/B,GALIA,EAAI,YAAclC,EAAQ,GAAG,UAAU,QAAU,CAAC5E,EAAM,WAKxD8G,EAAI,YAAclC,EAAQ,GAAG,UAAU,SAAW,CAAC5E,EAAM,UAC3D,SAGF,IAAM2J,EAAS7C,EAAI,YAAc,OAC3B8C,EAAY,CAACD,EAEbE,EAAWF,EAAS9F,GAAShE,CAAI,EAAI8D,GAAY9D,CAAI,EACrDiK,EAAiBhD,EAAI,KACvBzC,EAAM,UAAUyC,EAAI,QAASjH,EAAK,MAAQ,EAAIA,EAAK,YAAagK,CAAQ,EACxE/C,EAAI,QAIFiD,GAHoB1G,GAASyG,CAAc,EAC7C,MAAM1F,GAA0B0C,EAAI,QAASpG,EAAU,CAAC,EACxD2D,EAAM,wBAAwByF,EAAgBD,CAAQ,GACnB,MAAQ,EAAIhK,EAAK,YAkBpD+J,GAAa9C,EAAI,OAAS9G,EAAM,UAClCyI,EAAwB3B,EAAI,EAAE,EAAIpF,EAAO,OACvC+G,EAAwB3B,EAAI,EAAE,GAAK,EACnCiD,CACF,EACSH,GAAa9C,EAAI,OAAS9G,EAAM,UACzCyI,EAAwB3B,EAAI,IAAI,EAAIpF,EAAO,OACzC+G,EAAwB3B,EAAI,IAAI,GAAK,EACrCiD,CACF,EACSH,GAAa9C,EAAI,OAASA,EAAI,IACvC2B,EAAwB3B,EAAI,IAAI,EAAIpF,EAAO,OACzC+G,EAAwB3B,EAAI,IAAI,GAAK,EACrCiD,EAAe,CACjB,EAEAtB,EAAwB3B,EAAI,EAAE,EAAIpF,EAAO,OACvC+G,EAAwB3B,EAAI,EAAE,GAAK,EACnCiD,EAAe,CACjB,GACSjD,EAAI,YAAclC,EAAQ,GAAG,UAAU,QAChD6D,EAAwB3B,EAAI,IAAI,EAAIpF,EAAO,OACzC+G,EAAwB3B,EAAI,IAAI,GAAK,EACrCiD,CACF,EACSjD,EAAI,YAAclC,EAAQ,GAAG,UAAU,OAChD6D,EAAwBzI,EAAM,SAAS,EAAI0B,EAAO,OAChD+G,EAAwBzI,EAAM,SAAS,GAAK,EAC5C+J,CACF,EACSjD,EAAI,YAAclC,EAAQ,GAAG,UAAU,OAC5C5E,EAAM,YACRyI,EAAwBzI,EAAM,SAAS,EAAI0B,EAAO,OAChD+G,EAAwBzI,EAAM,SAAS,GAAK,EAC5C+J,EAAe,CACjB,GAGE/J,EAAM,YACRyI,EAAwB3B,EAAI,IAAI,EAAIpF,EAAO,OACzC+G,EAAwB3B,EAAI,IAAI,GAAK,EACrCiD,EAAe,CACjB,GAGN,CAGF,OAAA1C,EAAI,MAAM,2BAA4BoB,CAAuB,EACtDA,CACT,CAnGe1I,EAAA2I,GAAA,8BAqGf,IAAMtC,GAAwBrG,EAAA,SAAUC,EAAO,CAC7C,IAAIgK,EAAqB,EACnBH,EAAW/F,GAAUjE,CAAI,EAC/B,QAAWe,KAAOZ,EAAM,MAAO,CAE7B,IAAMiK,EADkB5F,EAAM,wBAAwBzD,EAAKiJ,CAAQ,EAChC,MAAQ,EAAIhK,EAAK,YAAc,EAAIA,EAAK,UACvEmK,EAAqBC,IACvBD,EAAqBC,EAEzB,CAEA,OAAOD,CACT,EAZ8B,yBAyB9B,eAAerB,GACb3G,EACAkI,EACA5B,EACA,CACA,IAAI3C,EAAY,EAChB,QAAWwE,KAAQnI,EAAO,KAAK,EAAG,CAChC,IAAMhC,EAAQgC,EAAO,IAAImI,CAAI,EACzBnK,EAAM,OACRA,EAAM,YAAcqE,EAAM,UACxBrE,EAAM,YACNH,EAAK,MAAQ,EAAIA,EAAK,YACtBiE,GAAUjE,CAAI,CAChB,GAEF,IAAMuK,EAAU/G,GAASrD,EAAM,WAAW,EACtC,MAAMoE,GAA0BpE,EAAM,YAAaU,EAAU,CAAC,EAC9D2D,EAAM,wBAAwBrE,EAAM,YAAa8D,GAAUjE,CAAI,CAAC,EAEpEG,EAAM,MAAQA,EAAM,KAChBH,EAAK,MACL6B,EAAO,OAAO7B,EAAK,MAAOuK,EAAQ,MAAQ,EAAIvK,EAAK,WAAW,EAElEG,EAAM,OAASA,EAAM,KAAO0B,EAAO,OAAO0I,EAAQ,OAAQvK,EAAK,MAAM,EAAIA,EAAK,OAC9E8F,EAAYjE,EAAO,OAAOiE,EAAW3F,EAAM,MAAM,CACnD,CAEA,QAAW4F,KAAYsE,EAAqB,CAC1C,IAAMlK,EAAQgC,EAAO,IAAI4D,CAAQ,EAEjC,GAAI,CAAC5F,EACH,SAGF,IAAMqK,EAAYrI,EAAO,IAAIhC,EAAM,SAAS,EAG5C,GAAI,CAACqK,EAAW,CAEd,IAAMC,EADeJ,EAAoBtE,CAAQ,EACf/F,EAAK,YAAcG,EAAM,MAAQ,EACnEA,EAAM,OAAS0B,EAAO,OAAO4I,EAAYzK,EAAK,WAAW,EACzD,QACF,CAGA,IAAMyK,EADeJ,EAAoBtE,CAAQ,EACf/F,EAAK,YAAcG,EAAM,MAAQ,EAAIqK,EAAU,MAAQ,EAEzFrK,EAAM,OAAS0B,EAAO,OAAO4I,EAAYzK,EAAK,WAAW,CAC3D,CAEA,IAAI0K,EAAe,EACnB,OAAAjC,EAAM,QAASzC,GAAQ,CACrB,IAAMgE,EAAWlG,GAAY9D,CAAI,EAC7B2K,EAAa3E,EAAI,UAAU,OAAO,CAAC4E,EAAOC,IACpCD,GAASzI,EAAO,IAAI0I,CAAI,EAAE,OAAS1I,EAAO,IAAI0I,CAAI,EAAE,QAAU,GACrE,CAAC,EAEJF,GAAc,EAAI3K,EAAK,cACnBgG,EAAI,OACNA,EAAI,KAAOxB,EAAM,UAAUwB,EAAI,KAAM2E,EAAa,EAAI3K,EAAK,YAAagK,CAAQ,GAGlF,IAAMc,EAAmBtG,EAAM,wBAAwBwB,EAAI,KAAMgE,CAAQ,EACzEU,EAAe7I,EAAO,OAAOiJ,EAAiB,OAAQJ,CAAY,EAClE,IAAMK,EAAWlJ,EAAO,OAAO8I,EAAYG,EAAiB,MAAQ,EAAI9K,EAAK,WAAW,EAExF,GADAgG,EAAI,OAAShG,EAAK,cACd2K,EAAaI,EAAU,CACzB,IAAMC,GAAWD,EAAWJ,GAAc,EAC1C3E,EAAI,QAAUgF,CAChB,CACF,CAAC,EACDvC,EAAM,QAASzC,GAASA,EAAI,cAAgB0E,CAAa,EAElD7I,EAAO,OAAOiE,EAAW9F,EAAK,MAAM,CAC7C,CA1EeE,EAAA4I,GAAA,yBA4Ef,IAAMmC,GAAiB/K,EAAA,eAAgB+G,EAAK9E,EAAQ4C,EAAS,CAC3D,IAAMmG,EAAY/I,EAAO,IAAI8E,EAAI,IAAI,EAC/BkE,EAAUhJ,EAAO,IAAI8E,EAAI,EAAE,EAC3B/F,EAASgK,EAAU,EACnB9J,EAAQ+J,EAAQ,EAChBC,EAAanE,EAAI,MAAQA,EAAI,QAE/BoE,EAAyE7H,GAASyD,EAAI,OAAO,EAC7F,MAAM1C,GAA0B0C,EAAI,QAASpG,EAAU,CAAC,EACxD2D,EAAM,wBACJ4G,EAAa5G,EAAM,UAAUyC,EAAI,QAASjH,EAAK,MAAOgE,GAAShE,CAAI,CAAC,EAAIiH,EAAI,QAC5EjD,GAAShE,CAAI,CACf,EACEW,EAAY,CAChB,MAAOyK,EACHpL,EAAK,MACL6B,EAAO,OAAO7B,EAAK,MAAOqL,EAAe,MAAQ,EAAIrL,EAAK,UAAU,EACxE,OAAQ,EACR,OAAQkL,EAAU,EAClB,MAAO,EACP,OAAQ,EACR,MAAO,EACP,QAASjE,EAAI,OACf,EACA,OAAIA,EAAI,YAAclC,EAAQ,GAAG,UAAU,SACzCpE,EAAU,MAAQyK,EACdvJ,EAAO,OAAO7B,EAAK,MAAOqL,EAAe,KAAK,EAC9CxJ,EAAO,OACLqJ,EAAU,MAAQ,EAAIC,EAAQ,MAAQ,EACtCE,EAAe,MAAQ,EAAIrL,EAAK,UAClC,EACJW,EAAU,OAASO,GAAUgK,EAAU,MAAQlL,EAAK,aAAe,GAC1DiH,EAAI,YAAclC,EAAQ,GAAG,UAAU,QAChDpE,EAAU,MAAQyK,EACdvJ,EAAO,OAAO7B,EAAK,MAAOqL,EAAe,MAAQ,EAAIrL,EAAK,UAAU,EACpE6B,EAAO,OACLqJ,EAAU,MAAQ,EAAIC,EAAQ,MAAQ,EACtCE,EAAe,MAAQ,EAAIrL,EAAK,UAClC,EACJW,EAAU,OAASO,EAASP,EAAU,OAASuK,EAAU,MAAQlL,EAAK,aAAe,GAC5EiH,EAAI,KAAOA,EAAI,MACxBoE,EAAiB7G,EAAM,wBACrB4G,EACI5G,EAAM,UAAUyC,EAAI,QAASpF,EAAO,OAAO7B,EAAK,MAAOkL,EAAU,KAAK,EAAGlH,GAAShE,CAAI,CAAC,EACvFiH,EAAI,QACRjD,GAAShE,CAAI,CACf,EACAW,EAAU,MAAQyK,EACdvJ,EAAO,OAAO7B,EAAK,MAAOkL,EAAU,KAAK,EACzCrJ,EAAO,OAAOqJ,EAAU,MAAOlL,EAAK,MAAOqL,EAAe,MAAQ,EAAIrL,EAAK,UAAU,EACzFW,EAAU,OAASO,GAAUgK,EAAU,MAAQvK,EAAU,OAAS,IAElEA,EAAU,MACR,KAAK,IAAIO,EAASgK,EAAU,MAAQ,GAAK9J,EAAQ+J,EAAQ,MAAQ,EAAE,EAAInL,EAAK,YAC9EW,EAAU,OACRO,EAASE,EACLF,EAASgK,EAAU,MAAQ,EAAIlL,EAAK,YAAc,EAClDoB,EAAQ+J,EAAQ,MAAQ,EAAInL,EAAK,YAAc,GAEnDoL,IACFzK,EAAU,QAAU6D,EAAM,UACxByC,EAAI,QACJtG,EAAU,MAAQ,EAAIX,EAAK,YAC3BgE,GAAShE,CAAI,CACf,GAEFwH,EAAI,MACF,OAAO7G,EAAU,MAAM,IAAIA,EAAU,KAAK,IAAIA,EAAU,MAAM,IAAIA,EAAU,KAAK,IAAIA,EAAU,KAAK,IAAIA,EAAU,MAAM,IAAIsG,EAAI,OAAO,GACzI,EACOtG,CACT,EAtEuB,kBAwEjB2K,GAAoBpL,EAAA,SAAU+G,EAAK9E,EAAQ4C,EAAS,CACxD,GACE,CAAC,CACCA,EAAQ,GAAG,SAAS,WACpBA,EAAQ,GAAG,SAAS,YACpBA,EAAQ,GAAG,SAAS,MACpBA,EAAQ,GAAG,SAAS,OACpBA,EAAQ,GAAG,SAAS,YACpBA,EAAQ,GAAG,SAAS,aACpBA,EAAQ,GAAG,SAAS,YACpBA,EAAQ,GAAG,SAAS,aACpBA,EAAQ,GAAG,SAAS,oBACpBA,EAAQ,GAAG,SAAS,oBACtB,EAAE,SAASkC,EAAI,IAAI,EAEnB,MAAO,CAAC,EAEV,GAAM,CAACsE,EAAUC,CAAS,EAAI9E,GAAiBO,EAAI,KAAM9E,CAAM,EACzD,CAACsJ,EAAQC,CAAO,EAAIhF,GAAiBO,EAAI,GAAI9E,CAAM,EACnDwJ,EAAiBJ,GAAYE,EAC/BvK,EAASyK,EAAiBH,EAAYD,EACtCnK,EAAQuK,EAAiBF,EAASC,EAGhCE,EAAsB,KAAK,IAAIH,EAASC,CAAO,EAAI,EAOnDG,EAAc3L,EAAC4L,GACZH,EAAiB,CAACG,EAAQA,EADf,eAIhB7E,EAAI,OAASA,EAAI,GAInB7F,EAAQF,GASJ+F,EAAI,UAAY,CAAC2E,IACnBxK,GAASyK,EAAY7L,EAAK,gBAAkB,EAAI,CAAC,GAO9C,CAAC+E,EAAQ,GAAG,SAAS,WAAYA,EAAQ,GAAG,SAAS,WAAW,EAAE,SAASkC,EAAI,IAAI,IACtF7F,GAASyK,EAAY,CAAC,GAOtB,CAAC9G,EAAQ,GAAG,SAAS,oBAAqBA,EAAQ,GAAG,SAAS,oBAAoB,EAAE,SAClFkC,EAAI,IACN,IAEA/F,GAAU2K,EAAY,CAAC,IAI3B,IAAME,EAAY,CAACR,EAAUC,EAAWC,EAAQC,CAAO,EACjDM,EAAe,KAAK,IAAI9K,EAASE,CAAK,EACxC6F,EAAI,MAAQA,EAAI,UAClBA,EAAI,QAAUzC,EAAM,UAClByC,EAAI,QACJpF,EAAO,OAAOmK,EAAe,EAAIhM,EAAK,YAAaA,EAAK,KAAK,EAC7D8D,GAAY9D,CAAI,CAClB,GAEF,IAAMiM,EAAUzH,EAAM,wBAAwByC,EAAI,QAASnD,GAAY9D,CAAI,CAAC,EAE5E,MAAO,CACL,MAAO6B,EAAO,OACZoF,EAAI,KAAO,EAAIgF,EAAQ,MAAQ,EAAIjM,EAAK,YACxCgM,EAAe,EAAIhM,EAAK,YACxBA,EAAK,KACP,EACA,OAAQ,EACR,OAAAkB,EACA,MAAAE,EACA,OAAQ,EACR,MAAO,EACP,QAAS6F,EAAI,QACb,KAAMA,EAAI,KACV,KAAMA,EAAI,KACV,WAAY,KAAK,IAAI,MAAM,KAAM8E,CAAS,EAC1C,SAAU,KAAK,IAAI,MAAM,KAAMA,CAAS,CAC1C,CACF,EApG0B,qBAsGpB/C,GAAsB9I,EAAA,eAAgBuF,EAAUtD,EAAQ+J,EAAmBnH,EAAS,CACxF,IAAMoH,EAAQ,CAAC,EACTC,EAAQ,CAAC,EACXC,EAAS1L,EAAWD,EAExB,QAAWuG,KAAOxB,EAAU,CAC1B,OAAQwB,EAAI,KAAM,CAChB,KAAKlC,EAAQ,GAAG,SAAS,WACzB,KAAKA,EAAQ,GAAG,SAAS,UACzB,KAAKA,EAAQ,GAAG,SAAS,UACzB,KAAKA,EAAQ,GAAG,SAAS,UACzB,KAAKA,EAAQ,GAAG,SAAS,eACzB,KAAKA,EAAQ,GAAG,SAAS,eACzB,KAAKA,EAAQ,GAAG,SAAS,YACvBqH,EAAM,KAAK,CACT,GAAInF,EAAI,GACR,IAAKA,EAAI,QACT,KAAM,OAAO,iBACb,GAAI,OAAO,iBACX,MAAO,CACT,CAAC,EACD,MACF,KAAKlC,EAAQ,GAAG,SAAS,SACzB,KAAKA,EAAQ,GAAG,SAAS,QACzB,KAAKA,EAAQ,GAAG,SAAS,gBACnBkC,EAAI,UACNoF,EAAUD,EAAM,IAAI,EACpBD,EAAME,EAAQ,EAAE,EAAIA,EACpBF,EAAMlF,EAAI,EAAE,EAAIoF,EAChBD,EAAM,KAAKC,CAAO,GAEpB,MACF,KAAKtH,EAAQ,GAAG,SAAS,SACzB,KAAKA,EAAQ,GAAG,SAAS,QACzB,KAAKA,EAAQ,GAAG,SAAS,QACzB,KAAKA,EAAQ,GAAG,SAAS,QACzB,KAAKA,EAAQ,GAAG,SAAS,aACzB,KAAKA,EAAQ,GAAG,SAAS,UACvBsH,EAAUD,EAAM,IAAI,EACpBD,EAAME,EAAQ,EAAE,EAAIA,EACpB,MACF,KAAKtH,EAAQ,GAAG,SAAS,aACvB,CACE,IAAM3C,EAAYD,EAAO,IAAI8E,EAAI,KAAOA,EAAI,KAAOA,EAAI,GAAG,KAAK,EACzD5E,EAAcC,GAAiB2E,EAAI,KAAOA,EAAI,KAAOA,EAAI,GAAG,KAAK,EAAE,OACnE1E,EACJH,EAAU,EAAIA,EAAU,MAAQ,GAAMC,EAAc,GAAKrC,EAAK,gBAAmB,EAC7EsM,EAAQ,CACZ,OAAQ/J,EACR,MAAOA,EAAIvC,EAAK,gBAChB,MAAOiH,EAAI,KACX,QAAS,EACX,EACAhH,EAAO,YAAY,KAAKqM,CAAK,CAC/B,CACA,MACF,KAAKvH,EAAQ,GAAG,SAAS,WACvB,CACE,IAAMtC,EAAyBxC,EAAO,YACnC,IAAKsM,GAAMA,EAAE,KAAK,EAClB,YAAYtF,EAAI,IAAI,EACvBhH,EAAO,YAAY,OAAOwC,EAAwB,CAAC,EAAE,OAAO,EAAG,CAAC,CAClE,CACA,KACJ,CACewE,EAAI,YAAc,QAE/BtG,EAAY,MAAMsK,GAAehE,EAAK9E,EAAQ4C,CAAO,EACrDkC,EAAI,UAAYtG,EAChByL,EAAM,QAASI,GAAQ,CACrBH,EAAUG,EACVH,EAAQ,KAAOxK,EAAO,OAAOwK,EAAQ,KAAM1L,EAAU,MAAM,EAC3D0L,EAAQ,GAAKxK,EAAO,OAAOwK,EAAQ,GAAI1L,EAAU,OAASA,EAAU,KAAK,EACzE0L,EAAQ,MACNxK,EAAO,OAAOwK,EAAQ,MAAO,KAAK,IAAIA,EAAQ,KAAOA,EAAQ,EAAE,CAAC,EAAIrM,EAAK,aAC7E,CAAC,IAEDU,EAAW4K,GAAkBrE,EAAK9E,EAAQ4C,CAAO,EACjDkC,EAAI,SAAWvG,EACXA,EAAS,QAAUA,EAAS,OAAS0L,EAAM,OAAS,GACtDA,EAAM,QAASI,GAAQ,CAErB,GADAH,EAAUG,EACN9L,EAAS,SAAWA,EAAS,MAAO,CACtC,IAAM+L,EAAOtK,EAAO,IAAI8E,EAAI,IAAI,EAC1ByF,EAAKvK,EAAO,IAAI8E,EAAI,EAAE,EAC5BoF,EAAQ,KAAOxK,EAAO,OACpB4K,EAAK,EAAI/L,EAAS,MAAQ,EAC1B+L,EAAK,EAAIA,EAAK,MAAQ,EACtBJ,EAAQ,IACV,EACAA,EAAQ,GAAKxK,EAAO,OAClB6K,EAAG,EAAIhM,EAAS,MAAQ,EACxBgM,EAAG,EAAID,EAAK,MAAQ,EACpBJ,EAAQ,EACV,EACAA,EAAQ,MACNxK,EAAO,OAAOwK,EAAQ,MAAO,KAAK,IAAIA,EAAQ,GAAKA,EAAQ,IAAI,CAAC,EAChErM,EAAK,aACT,MACEqM,EAAQ,KAAOxK,EAAO,OAAOnB,EAAS,OAAQ2L,EAAQ,IAAI,EAC1DA,EAAQ,GAAKxK,EAAO,OAAOnB,EAAS,MAAO2L,EAAQ,EAAE,EACrDA,EAAQ,MAAQxK,EAAO,OAAOwK,EAAQ,MAAO3L,EAAS,KAAK,EAAIV,EAAK,aAExE,CAAC,EAGP,CACA,OAAAC,EAAO,YAAc,CAAC,EACtBuH,EAAI,MAAM,oBAAqB2E,CAAK,EAC7BA,CACT,EA9G4B,uBAgHrBQ,GAAQ,CACb,OAAA1M,EACA,WAAAgG,GACA,gBAAAE,GACA,QAAAvF,GACA,KAAAoH,EACF,ECrmDO,IAAM4E,GAA6B,CACxC,OAAAC,GACA,IAAI,IAAK,CACP,OAAO,IAAIC,EACb,EACA,SAAAC,GACA,OAAAC,GACA,KAAMC,EAACC,GAAuB,CACvBA,EAAI,WACPA,EAAI,SAAW,CAAC,GAEdA,EAAI,OACNA,EAAI,SAAS,KAAOA,EAAI,KACxBC,GAAU,CAAE,SAAU,CAAE,KAAMD,EAAI,IAAK,CAAE,CAAC,EAE9C,EARM,OASR", "names": ["parser", "o", "__name", "k", "v", "l", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "$Vg", "$Vh", "$Vi", "$Vj", "$Vk", "$Vl", "$Vm", "$Vn", "$Vo", "$Vp", "$Vq", "$Vr", "$Vs", "$Vt", "$Vu", "$Vv", "$Vw", "$Vx", "$Vy", "$Vz", "$VA", "$VB", "$VC", "$VD", "$VE", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "recovering", "TERROR", "EOF", "args", "lexer", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "preErrorSymbol", "state", "action", "a", "r", "yyval", "p", "len", "newState", "expected", "errStr", "ch", "lines", "oldLines", "past", "next", "pre", "c", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "i", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "YYSTATE", "<PERSON><PERSON><PERSON>", "sequenceDiagram_default", "parser", "LINETYPE", "ARROWTYPE", "PLACEMENT", "SequenceDB", "ImperativeState", "setAccTitle", "setAccDescription", "setDiagramTitle", "getAccTitle", "getAccDescription", "getDiagramTitle", "getConfig", "__name", "data", "id", "name", "description", "type", "assignedBox", "old", "prevActorInRecords", "part", "i", "count", "idFrom", "idTo", "message", "answer", "messageType", "activate", "error", "b", "wrapSetting", "text", "wrap", "clear", "str", "trimmedStr", "cleanedText", "log", "match", "color", "title", "style", "sanitizeText", "actor", "placement", "note", "actors", "actorId", "sanitizedText", "links", "e", "sep", "label", "link", "key", "properties", "elem", "details", "param", "item", "getStyles", "__name", "options", "styles_default", "import_sanitize_url", "ACTOR_TYPE_WIDTH", "TOP_ACTOR_CLASS", "BOTTOM_ACTOR_CLASS", "ACTOR_BOX_CLASS", "ACTOR_MAN_FIGURE_CLASS", "drawRect", "__name", "elem", "rectData", "drawPopup", "actor", "minMenuWidth", "textAttrs", "forceMenus", "links", "actor<PERSON>nt", "displayValue", "g", "<PERSON><PERSON><PERSON>", "menuWidth", "rectElem", "linkY", "key", "linkElem", "sanitizedLink", "_drawMenuItemTextCandidateFunc", "popupMenuToggle", "popId", "drawKatex", "textData", "msgModel", "textElem", "linesSanitized", "renderKatexSanitized", "getConfig", "dim", "rectDim", "startx", "stopx", "starty", "temp", "drawText", "prevTextHeight", "textHeight", "lines", "common_default", "_textFontSize", "_textFontSizePx", "parseFontSize", "textElems", "dy", "yfunc", "i", "line", "text", "ZERO_WIDTH_SPACE", "span", "drawLabel", "txtObject", "genPoints", "x", "y", "width", "height", "cut", "polygon", "fixLifeLineHeights", "diagram", "actors", "<PERSON><PERSON><PERSON><PERSON>", "conf", "<PERSON><PERSON><PERSON>", "actor<PERSON><PERSON>", "drawActorTypeParticipant", "<PERSON><PERSON>ooter", "actorY", "center", "centerY", "boxplusLineGroup", "rect", "getNoteRect", "cssclass", "iconSrc", "drawEmbeddedImage", "drawImage", "_drawTextCandidateFunc", "hasKatex", "bounds", "drawActorTypeActor", "actElem", "cssClass", "circle", "drawActor", "drawBox", "box", "drawBackgroundRect", "anchorElement", "drawActivation", "verticalPos", "actorActivations", "drawLoop", "loopModel", "labelText", "boxMargin", "boxTextMargin", "labelBoxHeight", "labelBoxWidth", "fontFamily", "fontSize", "fontWeight", "drawLoopLine", "stopy", "item", "txt", "getTextObj", "idx", "sectionHeight", "te", "acc", "curr", "insertDatabaseIcon", "insertComputerIcon", "insertClockIcon", "insertArrowHead", "insertArrowFilledHead", "insertSequenceNumber", "insertArrowCrossHead", "byText", "content", "_setTextAttrs", "byTspan", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_actorFontSize", "_actorFontSizePx", "byFo", "s", "byKatex", "calculateMathMLDimensions", "toText", "fromTextAttrsDict", "svgDraw_default", "conf", "bounds", "__name", "actor", "it", "acc", "h", "boxModel", "<PERSON><PERSON><PERSON><PERSON>", "loopModel", "msgModel", "noteModel", "setConf", "getConfig", "obj", "key", "val", "fun", "startx", "starty", "stopx", "stopy", "_self", "cnt", "updateFn", "type", "item", "n", "_startx", "common_default", "_stopx", "_starty", "_stopy", "message", "diagram", "actors", "<PERSON><PERSON><PERSON><PERSON>", "stackedSize", "actorActivations", "x", "svgDraw_default", "lastActorActivationIdx", "activation", "title", "fill", "loop", "bump", "drawNote", "elem", "rect", "getNoteRect", "g", "rectElem", "textObj", "getTextObj", "textElem", "hasKatex", "drawKatex", "drawText", "textHeight", "te", "curr", "messageFont", "cnf", "noteFont", "<PERSON><PERSON><PERSON>", "boundMessage", "_diagram", "lines", "isKatexMsg", "textDims", "calculateMathMLDimensions", "utils_default", "lineHeight", "lineStartY", "totalOffset", "textWidth", "dx", "drawMessage", "diagObj", "sequenceIndex", "sequenceVisible", "line", "url", "getUrl", "addActorRenderingData", "createdActors", "<PERSON><PERSON><PERSON><PERSON>", "verticalPos", "messages", "<PERSON><PERSON>ooter", "prevWidth", "<PERSON>v<PERSON><PERSON><PERSON>", "prevBox", "maxHeight", "<PERSON><PERSON><PERSON>", "box", "drawActors", "height", "drawActorsPopup", "doc", "max<PERSON><PERSON><PERSON>", "minMenuWidth", "getRequiredPopupWidth", "menuDimensions", "assignWithDepth_default", "activationBounds", "<PERSON><PERSON><PERSON><PERSON>", "activations", "left", "right", "adjustLoopHeightForWrap", "loopWidths", "msg", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "addLoopFn", "heightAdjust", "loopWidth", "textConf", "log", "adjustCreatedDestroyedData", "index", "destroyedActors", "receiverAdjustment", "adjustment", "senderAdjustment", "ACTOR_TYPE_WIDTH", "draw", "_text", "id", "_version", "securityLevel", "sequence", "sandboxElement", "select_default", "root", "boxes", "hasBoxes", "hasBoxTitles", "maxMessageWidthPerActor", "getMaxMessageWidthPerActor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newActors", "calculateLoopBounds", "activeEnd", "activationData", "sequenceIndexStep", "messagesToDraw", "backgrounds", "e", "fixLifeLineHeights", "requiredBoxSize", "boxHeight", "boxWidth", "width", "configureSvgSize", "extraVertForTitle", "isNote", "isMessage", "textFont", "wrappedMessage", "messageWidth", "required<PERSON><PERSON><PERSON><PERSON>id<PERSON>", "labelWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prop", "actDims", "nextActor", "<PERSON><PERSON><PERSON><PERSON>", "maxBoxHeight", "totalWidth", "total", "a<PERSON><PERSON>", "boxMsgDimensions", "min<PERSON><PERSON><PERSON>", "missing", "buildNoteModel", "fromActor", "to<PERSON><PERSON>", "shouldWrap", "textDimensions", "buildMessageModel", "fromLeft", "fromRight", "toLeft", "toRight", "isArrowToRight", "isArrowToActivation", "adjustValue", "value", "allBounds", "boundedWidth", "msgDims", "_maxWidthPerActor", "loops", "stack", "current", "toAdd", "a", "stk", "from", "to", "sequenceRenderer_default", "diagram", "sequenceDiagram_default", "SequenceDB", "sequenceRenderer_default", "styles_default", "__name", "cnf", "setConfig"]}