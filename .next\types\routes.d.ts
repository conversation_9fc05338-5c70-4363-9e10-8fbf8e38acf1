// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/"
type AppRouteHandlerRoutes = "/api/ai/anthropic/[...slug]" | "/api/ai/azure/[...slug]" | "/api/ai/deepseek/[...slug]" | "/api/ai/google/[...slug]" | "/api/ai/mistral/[...slug]" | "/api/ai/ollama/[...slug]" | "/api/ai/openai/[...slug]" | "/api/ai/openaicompatible/[...slug]" | "/api/ai/openrouter/[...slug]" | "/api/ai/pollinations/[...slug]" | "/api/ai/xai/[...slug]" | "/api/crawler" | "/api/mcp" | "/api/mcp/[...slug]" | "/api/search/bocha/[...slug]" | "/api/search/exa/[...slug]" | "/api/search/firecrawl/[...slug]" | "/api/search/searxng/[...slug]" | "/api/search/tavily/[...slug]" | "/api/sse" | "/api/sse/live"
type PageRoutes = never
type LayoutRoutes = "/"
type RedirectRoutes = never
type RewriteRoutes = never
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes | AppRouteHandlerRoutes


interface ParamMap {
  "/": {}
  "/api/ai/anthropic/[...slug]": { "slug": string[]; }
  "/api/ai/azure/[...slug]": { "slug": string[]; }
  "/api/ai/deepseek/[...slug]": { "slug": string[]; }
  "/api/ai/google/[...slug]": { "slug": string[]; }
  "/api/ai/mistral/[...slug]": { "slug": string[]; }
  "/api/ai/ollama/[...slug]": { "slug": string[]; }
  "/api/ai/openai/[...slug]": { "slug": string[]; }
  "/api/ai/openaicompatible/[...slug]": { "slug": string[]; }
  "/api/ai/openrouter/[...slug]": { "slug": string[]; }
  "/api/ai/pollinations/[...slug]": { "slug": string[]; }
  "/api/ai/xai/[...slug]": { "slug": string[]; }
  "/api/crawler": {}
  "/api/mcp": {}
  "/api/mcp/[...slug]": { "slug": string[]; }
  "/api/search/bocha/[...slug]": { "slug": string[]; }
  "/api/search/exa/[...slug]": { "slug": string[]; }
  "/api/search/firecrawl/[...slug]": { "slug": string[]; }
  "/api/search/searxng/[...slug]": { "slug": string[]; }
  "/api/search/tavily/[...slug]": { "slug": string[]; }
  "/api/sse": {}
  "/api/sse/live": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap, AppRouteHandlerRoutes }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }

  /**
   * Context for Next.js App Router route handlers
   * @example
   * ```tsx
   * export async function GET(request: NextRequest, context: RouteContext<'/api/users/[id]'>) {
   *   const { id } = await context.params
   *   return Response.json({ id })
   * }
   * ```
   */
  interface RouteContext<AppRouteHandlerRoute extends AppRouteHandlerRoutes> {
    params: Promise<ParamMap[AppRouteHandlerRoute]>
  }
}
