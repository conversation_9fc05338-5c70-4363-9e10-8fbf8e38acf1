import{a as Jt}from"./chunk-LM6QDVU5.mjs";import{a as ve,b as dt,d as k,e as D}from"./chunk-Z2NOIGJN.mjs";import{c as gt,g as nt}from"./chunk-IXVBHSNP.mjs";import{i as wt,k as Ne,o as ht,q as U}from"./chunk-3R3PQ5PD.mjs";import{$ as At,A as Z,B as Qt,C as Et,E as Se,F as we,Y as q,c as z,ha as Y,l as be,t as ft,y as $t}from"./chunk-F632ZYSZ.mjs";import{a as x}from"./chunk-GTKDMUJJ.mjs";var T=x(async(c,t,i)=>{let r,e=t.useHtmlLabels||Z(q()?.htmlLabels);i?r=i:r="node default";let a=c.insert("g").attr("class",r).attr("id",t.domId||t.id),h=a.insert("g").attr("class","label").attr("style",U(t.labelStyle)),s;t.label===void 0?s="":s=typeof t.label=="string"?t.label:t.label[0];let l=await nt(h,$t(ht(s),q()),{useHtmlLabels:e,width:t.width||q().flowchart?.wrappingWidth,cssClasses:"markdown-node-label",style:t.labelStyle,addSvgBackground:!!t.icon||!!t.img}),o=l.getBBox(),n=(t?.padding??0)/2;if(e){let p=l.children[0],f=Y(l),m=p.getElementsByTagName("img");if(m){let g=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...m].map(d=>new Promise(y=>{function u(){if(d.style.display="flex",d.style.flexDirection="column",g){let b=q().fontSize?q().fontSize:window.getComputedStyle(document.body).fontSize,S=5,[M=be.fontSize]=Ne(b),w=M*S+"px";d.style.minWidth=w,d.style.maxWidth=w}else d.style.width="100%";y(d)}x(u,"setupImage"),setTimeout(()=>{d.complete&&u()}),d.addEventListener("error",u),d.addEventListener("load",u)})))}o=p.getBoundingClientRect(),f.attr("width",o.width),f.attr("height",o.height)}return e?h.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"):h.attr("transform","translate(0, "+-o.height/2+")"),t.centerLabel&&h.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"),h.insert("rect",":first-child"),{shapeSvg:a,bbox:o,halfPadding:n,label:h}},"labelHelper"),jt=x(async(c,t,i)=>{let r=i.useHtmlLabels||Z(q()?.flowchart?.htmlLabels),e=c.insert("g").attr("class","label").attr("style",i.labelStyle||""),a=await nt(e,$t(ht(t),q()),{useHtmlLabels:r,width:i.width||q()?.flowchart?.wrappingWidth,style:i.labelStyle,addSvgBackground:!!i.icon||!!i.img}),h=a.getBBox(),s=i.padding/2;if(Z(q()?.flowchart?.htmlLabels)){let l=a.children[0],o=Y(a);h=l.getBoundingClientRect(),o.attr("width",h.width),o.attr("height",h.height)}return r?e.attr("transform","translate("+-h.width/2+", "+-h.height/2+")"):e.attr("transform","translate(0, "+-h.height/2+")"),i.centerLabel&&e.attr("transform","translate("+-h.width/2+", "+-h.height/2+")"),e.insert("rect",":first-child"),{shapeSvg:c,bbox:h,halfPadding:s,label:e}},"insertLabel"),$=x((c,t)=>{let i=t.node().getBBox();c.width=i.width,c.height=i.height},"updateNodeBounds");var C=x((c,t)=>(c.look==="handDrawn"?"rough-node":"node")+" "+c.cssClasses+" "+(t||""),"getNodeClasses");function H(c){let t=c.map((i,r)=>`${r===0?"M":"L"}${i.x},${i.y}`);return t.push("Z"),t.join(" ")}x(H,"createPathFromPoints");function lt(c,t,i,r,e,a){let h=[],l=i-c,o=r-t,n=l/a,p=2*Math.PI/n,f=t+o/2;for(let m=0;m<=50;m++){let g=m/50,d=c+g*l,y=f+e*Math.sin(p*(d-c));h.push({x:d,y})}return h}x(lt,"generateFullSineWavePoints");function Nt(c,t,i,r,e,a){let h=[],s=e*Math.PI/180,n=(a*Math.PI/180-s)/(r-1);for(let p=0;p<r;p++){let f=s+p*n,m=c+i*Math.cos(f),g=t+i*Math.sin(f);h.push({x:-m,y:-g})}return h}x(Nt,"generateCirclePoints");function Kt(c,t,i){if(c&&c.length){let[r,e]=t,a=Math.PI/180*i,h=Math.cos(a),s=Math.sin(a);for(let l of c){let[o,n]=l;l[0]=(o-r)*h-(n-e)*s+r,l[1]=(o-r)*s+(n-e)*h+e}}}x(Kt,"t");function Js(c,t){return c[0]===t[0]&&c[1]===t[1]}x(Js,"e");function Ks(c,t,i,r=1){let e=i,a=Math.max(t,.1),h=c[0]&&c[0][0]&&typeof c[0][0]=="number"?[c]:c,s=[0,0];if(e)for(let o of h)Kt(o,s,e);let l=function(o,n,p){let f=[];for(let b of o){let S=[...b];Js(S[0],S[S.length-1])||S.push([S[0][0],S[0][1]]),S.length>2&&f.push(S)}let m=[];n=Math.max(n,.1);let g=[];for(let b of f)for(let S=0;S<b.length-1;S++){let M=b[S],w=b[S+1];if(M[1]!==w[1]){let P=Math.min(M[1],w[1]);g.push({ymin:P,ymax:Math.max(M[1],w[1]),x:P===M[1]?M[0]:w[0],islope:(w[0]-M[0])/(w[1]-M[1])})}}if(g.sort((b,S)=>b.ymin<S.ymin?-1:b.ymin>S.ymin?1:b.x<S.x?-1:b.x>S.x?1:b.ymax===S.ymax?0:(b.ymax-S.ymax)/Math.abs(b.ymax-S.ymax)),!g.length)return m;let d=[],y=g[0].ymin,u=0;for(;d.length||g.length;){if(g.length){let b=-1;for(let S=0;S<g.length&&!(g[S].ymin>y);S++)b=S;g.splice(0,b+1).forEach(S=>{d.push({s:y,edge:S})})}if(d=d.filter(b=>!(b.edge.ymax<=y)),d.sort((b,S)=>b.edge.x===S.edge.x?0:(b.edge.x-S.edge.x)/Math.abs(b.edge.x-S.edge.x)),(p!==1||u%n==0)&&d.length>1)for(let b=0;b<d.length;b+=2){let S=b+1;if(S>=d.length)break;let M=d[b].edge,w=d[S].edge;m.push([[Math.round(M.x),y],[Math.round(w.x),y]])}y+=p,d.forEach(b=>{b.edge.x=b.edge.x+p*b.edge.islope}),u++}return m}(h,a,r);if(e){for(let o of h)Kt(o,s,-e);(function(o,n,p){let f=[];o.forEach(m=>f.push(...m)),Kt(f,n,p)})(l,s,-e)}return l}x(Ks,"s");function Tt(c,t){var i;let r=t.hachureAngle+90,e=t.hachureGap;e<0&&(e=4*t.strokeWidth),e=Math.round(Math.max(e,.1));let a=1;return t.roughness>=1&&(((i=t.randomizer)===null||i===void 0?void 0:i.next())||Math.random())>.7&&(a=e),Ks(c,e,r,a||1)}x(Tt,"n");var Bt=class{static{x(this,"o")}constructor(t){this.helper=t}fillPolygons(t,i){return this._fillPolygons(t,i)}_fillPolygons(t,i){let r=Tt(t,i);return{type:"fillSketch",ops:this.renderLines(r,i)}}renderLines(t,i){let r=[];for(let e of t)r.push(...this.helper.doubleLineOps(e[0][0],e[0][1],e[1][0],e[1][1],i));return r}};function zt(c){let t=c[0],i=c[1];return Math.sqrt(Math.pow(t[0]-i[0],2)+Math.pow(t[1]-i[1],2))}x(zt,"a");var ae=class extends Bt{static{x(this,"h")}fillPolygons(t,i){let r=i.hachureGap;r<0&&(r=4*i.strokeWidth),r=Math.max(r,.1);let e=Tt(t,Object.assign({},i,{hachureGap:r})),a=Math.PI/180*i.hachureAngle,h=[],s=.5*r*Math.cos(a),l=.5*r*Math.sin(a);for(let[o,n]of e)zt([o,n])&&h.push([[o[0]-s,o[1]+l],[...n]],[[o[0]+s,o[1]-l],[...n]]);return{type:"fillSketch",ops:this.renderLines(h,i)}}},oe=class extends Bt{static{x(this,"r")}fillPolygons(t,i){let r=this._fillPolygons(t,i),e=Object.assign({},i,{hachureAngle:i.hachureAngle+90}),a=this._fillPolygons(t,e);return r.ops=r.ops.concat(a.ops),r}},ie=class{static{x(this,"i")}constructor(t){this.helper=t}fillPolygons(t,i){let r=Tt(t,i=Object.assign({},i,{hachureAngle:0}));return this.dotsOnLines(r,i)}dotsOnLines(t,i){let r=[],e=i.hachureGap;e<0&&(e=4*i.strokeWidth),e=Math.max(e,.1);let a=i.fillWeight;a<0&&(a=i.strokeWidth/2);let h=e/4;for(let s of t){let l=zt(s),o=l/e,n=Math.ceil(o)-1,p=l-n*e,f=(s[0][0]+s[1][0])/2-e/4,m=Math.min(s[0][1],s[1][1]);for(let g=0;g<n;g++){let d=m+p+g*e,y=f-h+2*Math.random()*h,u=d-h+2*Math.random()*h,b=this.helper.ellipse(y,u,a,a,i);r.push(...b.ops)}}return{type:"fillSketch",ops:r}}},ne=class{static{x(this,"c")}constructor(t){this.helper=t}fillPolygons(t,i){let r=Tt(t,i);return{type:"fillSketch",ops:this.dashedLine(r,i)}}dashedLine(t,i){let r=i.dashOffset<0?i.hachureGap<0?4*i.strokeWidth:i.hachureGap:i.dashOffset,e=i.dashGap<0?i.hachureGap<0?4*i.strokeWidth:i.hachureGap:i.dashGap,a=[];return t.forEach(h=>{let s=zt(h),l=Math.floor(s/(r+e)),o=(s+e-l*(r+e))/2,n=h[0],p=h[1];n[0]>p[0]&&(n=h[1],p=h[0]);let f=Math.atan((p[1]-n[1])/(p[0]-n[0]));for(let m=0;m<l;m++){let g=m*(r+e),d=g+r,y=[n[0]+g*Math.cos(f)+o*Math.cos(f),n[1]+g*Math.sin(f)+o*Math.sin(f)],u=[n[0]+d*Math.cos(f)+o*Math.cos(f),n[1]+d*Math.sin(f)+o*Math.sin(f)];a.push(...this.helper.doubleLineOps(y[0],y[1],u[0],u[1],i))}}),a}},le=class{static{x(this,"l")}constructor(t){this.helper=t}fillPolygons(t,i){let r=i.hachureGap<0?4*i.strokeWidth:i.hachureGap,e=i.zigzagOffset<0?r:i.zigzagOffset,a=Tt(t,i=Object.assign({},i,{hachureGap:r+e}));return{type:"fillSketch",ops:this.zigzagLines(a,e,i)}}zigzagLines(t,i,r){let e=[];return t.forEach(a=>{let h=zt(a),s=Math.round(h/(2*i)),l=a[0],o=a[1];l[0]>o[0]&&(l=a[1],o=a[0]);let n=Math.atan((o[1]-l[1])/(o[0]-l[0]));for(let p=0;p<s;p++){let f=2*p*i,m=2*(p+1)*i,g=Math.sqrt(2*Math.pow(i,2)),d=[l[0]+f*Math.cos(n),l[1]+f*Math.sin(n)],y=[l[0]+m*Math.cos(n),l[1]+m*Math.sin(n)],u=[d[0]+g*Math.cos(n+Math.PI/4),d[1]+g*Math.sin(n+Math.PI/4)];e.push(...this.helper.doubleLineOps(d[0],d[1],u[0],u[1],r),...this.helper.doubleLineOps(u[0],u[1],y[0],y[1],r))}}),e}},et={},ce=class{static{x(this,"p")}constructor(t){this.seed=t}next(){return this.seed?(2**31-1&(this.seed=Math.imul(48271,this.seed)))/2**31:Math.random()}},tr=0,te=1,De=2,Ot={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0};function ee(c,t){return c.type===t}x(ee,"k");function de(c){let t=[],i=function(h){let s=new Array;for(;h!=="";)if(h.match(/^([ \t\r\n,]+)/))h=h.substr(RegExp.$1.length);else if(h.match(/^([aAcChHlLmMqQsStTvVzZ])/))s[s.length]={type:tr,text:RegExp.$1},h=h.substr(RegExp.$1.length);else{if(!h.match(/^(([-+]?[0-9]+(\.[0-9]*)?|[-+]?\.[0-9]+)([eE][-+]?[0-9]+)?)/))return[];s[s.length]={type:te,text:`${parseFloat(RegExp.$1)}`},h=h.substr(RegExp.$1.length)}return s[s.length]={type:De,text:""},s}(c),r="BOD",e=0,a=i[e];for(;!ee(a,De);){let h=0,s=[];if(r==="BOD"){if(a.text!=="M"&&a.text!=="m")return de("M0,0"+c);e++,h=Ot[a.text],r=a.text}else ee(a,te)?h=Ot[r]:(e++,h=Ot[a.text],r=a.text);if(!(e+h<i.length))throw new Error("Path data ended short");for(let l=e;l<e+h;l++){let o=i[l];if(!ee(o,te))throw new Error("Param not a number: "+r+","+o.text);s[s.length]=+o.text}if(typeof Ot[r]!="number")throw new Error("Bad segment: "+r);{let l={key:r,data:s};t.push(l),e+=h,a=i[e],r==="M"&&(r="L"),r==="m"&&(r="l")}}return t}x(de,"b");function Re(c){let t=0,i=0,r=0,e=0,a=[];for(let{key:h,data:s}of c)switch(h){case"M":a.push({key:"M",data:[...s]}),[t,i]=s,[r,e]=s;break;case"m":t+=s[0],i+=s[1],a.push({key:"M",data:[t,i]}),r=t,e=i;break;case"L":a.push({key:"L",data:[...s]}),[t,i]=s;break;case"l":t+=s[0],i+=s[1],a.push({key:"L",data:[t,i]});break;case"C":a.push({key:"C",data:[...s]}),t=s[4],i=s[5];break;case"c":{let l=s.map((o,n)=>n%2?o+i:o+t);a.push({key:"C",data:l}),t=l[4],i=l[5];break}case"Q":a.push({key:"Q",data:[...s]}),t=s[2],i=s[3];break;case"q":{let l=s.map((o,n)=>n%2?o+i:o+t);a.push({key:"Q",data:l}),t=l[2],i=l[3];break}case"A":a.push({key:"A",data:[...s]}),t=s[5],i=s[6];break;case"a":t+=s[5],i+=s[6],a.push({key:"A",data:[s[0],s[1],s[2],s[3],s[4],t,i]});break;case"H":a.push({key:"H",data:[...s]}),t=s[0];break;case"h":t+=s[0],a.push({key:"H",data:[t]});break;case"V":a.push({key:"V",data:[...s]}),i=s[0];break;case"v":i+=s[0],a.push({key:"V",data:[i]});break;case"S":a.push({key:"S",data:[...s]}),t=s[2],i=s[3];break;case"s":{let l=s.map((o,n)=>n%2?o+i:o+t);a.push({key:"S",data:l}),t=l[2],i=l[3];break}case"T":a.push({key:"T",data:[...s]}),t=s[0],i=s[1];break;case"t":t+=s[0],i+=s[1],a.push({key:"T",data:[t,i]});break;case"Z":case"z":a.push({key:"Z",data:[]}),t=r,i=e}return a}x(Re,"y");function Ge(c){let t=[],i="",r=0,e=0,a=0,h=0,s=0,l=0;for(let{key:o,data:n}of c){switch(o){case"M":t.push({key:"M",data:[...n]}),[r,e]=n,[a,h]=n;break;case"C":t.push({key:"C",data:[...n]}),r=n[4],e=n[5],s=n[2],l=n[3];break;case"L":t.push({key:"L",data:[...n]}),[r,e]=n;break;case"H":r=n[0],t.push({key:"L",data:[r,e]});break;case"V":e=n[0],t.push({key:"L",data:[r,e]});break;case"S":{let p=0,f=0;i==="C"||i==="S"?(p=r+(r-s),f=e+(e-l)):(p=r,f=e),t.push({key:"C",data:[p,f,...n]}),s=n[0],l=n[1],r=n[2],e=n[3];break}case"T":{let[p,f]=n,m=0,g=0;i==="Q"||i==="T"?(m=r+(r-s),g=e+(e-l)):(m=r,g=e);let d=r+2*(m-r)/3,y=e+2*(g-e)/3,u=p+2*(m-p)/3,b=f+2*(g-f)/3;t.push({key:"C",data:[d,y,u,b,p,f]}),s=m,l=g,r=p,e=f;break}case"Q":{let[p,f,m,g]=n,d=r+2*(p-r)/3,y=e+2*(f-e)/3,u=m+2*(p-m)/3,b=g+2*(f-g)/3;t.push({key:"C",data:[d,y,u,b,m,g]}),s=p,l=f,r=m,e=g;break}case"A":{let p=Math.abs(n[0]),f=Math.abs(n[1]),m=n[2],g=n[3],d=n[4],y=n[5],u=n[6];p===0||f===0?(t.push({key:"C",data:[r,e,y,u,y,u]}),r=y,e=u):(r!==y||e!==u)&&(Ee(r,e,y,u,p,f,m,g,d).forEach(function(b){t.push({key:"C",data:b})}),r=y,e=u);break}case"Z":t.push({key:"Z",data:[]}),r=a,e=h}i=o}return t}x(Ge,"m");function Pt(c,t,i){return[c*Math.cos(i)-t*Math.sin(i),c*Math.sin(i)+t*Math.cos(i)]}x(Pt,"w");function Ee(c,t,i,r,e,a,h,s,l,o){let n=(p=h,Math.PI*p/180);var p;let f=[],m=0,g=0,d=0,y=0;if(o)[m,g,d,y]=o;else{[c,t]=Pt(c,t,-n),[i,r]=Pt(i,r,-n);let G=(c-i)/2,A=(t-r)/2,V=G*G/(e*e)+A*A/(a*a);V>1&&(V=Math.sqrt(V),e*=V,a*=V);let j=e*e,I=a*a,F=j*I-j*A*A-I*G*G,Q=j*A*A+I*G*G,it=(s===l?-1:1)*Math.sqrt(Math.abs(F/Q));d=it*e*A/a+(c+i)/2,y=it*-a*G/e+(t+r)/2,m=Math.asin(parseFloat(((t-y)/a).toFixed(9))),g=Math.asin(parseFloat(((r-y)/a).toFixed(9))),c<d&&(m=Math.PI-m),i<d&&(g=Math.PI-g),m<0&&(m=2*Math.PI+m),g<0&&(g=2*Math.PI+g),l&&m>g&&(m-=2*Math.PI),!l&&g>m&&(g-=2*Math.PI)}let u=g-m;if(Math.abs(u)>120*Math.PI/180){let G=g,A=i,V=r;g=l&&g>m?m+120*Math.PI/180*1:m+120*Math.PI/180*-1,f=Ee(i=d+e*Math.cos(g),r=y+a*Math.sin(g),A,V,e,a,h,0,l,[g,G,d,y])}u=g-m;let b=Math.cos(m),S=Math.sin(m),M=Math.cos(g),w=Math.sin(g),P=Math.tan(u/4),B=4/3*e*P,R=4/3*a*P,E=[c,t],L=[c+B*S,t-R*b],O=[i+B*w,r-R*M],W=[i,r];if(L[0]=2*E[0]-L[0],L[1]=2*E[1]-L[1],o)return[L,O,W].concat(f);{f=[L,O,W].concat(f);let G=[];for(let A=0;A<f.length;A+=3){let V=Pt(f[A][0],f[A][1],n),j=Pt(f[A+1][0],f[A+1][1],n),I=Pt(f[A+2][0],f[A+2][1],n);G.push([V[0],V[1],j[0],j[1],I[0],I[1]])}return G}}x(Ee,"x");var er={randOffset:x(function(c,t){return _(c,t)},"randOffset"),randOffsetWithRange:x(function(c,t,i){return It(c,t,i)},"randOffsetWithRange"),ellipse:x(function(c,t,i,r,e){let a=je(i,r,e);return he(c,t,e,a).opset},"ellipse"),doubleLineOps:x(function(c,t,i,r,e){return ut(c,t,i,r,e,!0)},"doubleLineOps")};function Ae(c,t,i,r,e){return{type:"path",ops:ut(c,t,i,r,e)}}x(Ae,"v");function Wt(c,t,i){let r=(c||[]).length;if(r>2){let e=[];for(let a=0;a<r-1;a++)e.push(...ut(c[a][0],c[a][1],c[a+1][0],c[a+1][1],i));return t&&e.push(...ut(c[r-1][0],c[r-1][1],c[0][0],c[0][1],i)),{type:"path",ops:e}}return r===2?Ae(c[0][0],c[0][1],c[1][0],c[1][1],i):{type:"path",ops:[]}}x(Wt,"S");function sr(c,t,i,r,e){return function(a,h){return Wt(a,!0,h)}([[c,t],[c+i,t],[c+i,t+r],[c,t+r]],e)}x(sr,"O");function Me(c,t){if(c.length){let i=typeof c[0][0]=="number"?[c]:c,r=Lt(i[0],1*(1+.2*t.roughness),t),e=t.disableMultiStroke?[]:Lt(i[0],1.5*(1+.22*t.roughness),Pe(t));for(let a=1;a<i.length;a++){let h=i[a];if(h.length){let s=Lt(h,1*(1+.2*t.roughness),t),l=t.disableMultiStroke?[]:Lt(h,1.5*(1+.22*t.roughness),Pe(t));for(let o of s)o.op!=="move"&&r.push(o);for(let o of l)o.op!=="move"&&e.push(o)}}return{type:"path",ops:r.concat(e)}}return{type:"path",ops:[]}}x(Me,"L");function je(c,t,i){let r=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(c/2,2)+Math.pow(t/2,2))/2)),e=Math.ceil(Math.max(i.curveStepCount,i.curveStepCount/Math.sqrt(200)*r)),a=2*Math.PI/e,h=Math.abs(c/2),s=Math.abs(t/2),l=1-i.curveFitting;return h+=_(h*l,i),s+=_(s*l,i),{increment:a,rx:h,ry:s}}x(je,"T");function he(c,t,i,r){let[e,a]=Ce(r.increment,c,t,r.rx,r.ry,1,r.increment*It(.1,It(.4,1,i),i),i),h=Ft(e,null,i);if(!i.disableMultiStroke&&i.roughness!==0){let[s]=Ce(r.increment,c,t,r.rx,r.ry,1.5,0,i),l=Ft(s,null,i);h=h.concat(l)}return{estimatedPoints:a,opset:{type:"path",ops:h}}}x(he,"D");function ke(c,t,i,r,e,a,h,s,l){let o=c,n=t,p=Math.abs(i/2),f=Math.abs(r/2);p+=_(.01*p,l),f+=_(.01*f,l);let m=e,g=a;for(;m<0;)m+=2*Math.PI,g+=2*Math.PI;g-m>2*Math.PI&&(m=0,g=2*Math.PI);let d=2*Math.PI/l.curveStepCount,y=Math.min(d/2,(g-m)/2),u=Be(y,o,n,p,f,m,g,1,l);if(!l.disableMultiStroke){let b=Be(y,o,n,p,f,m,g,1.5,l);u.push(...b)}return h&&(s?u.push(...ut(o,n,o+p*Math.cos(m),n+f*Math.sin(m),l),...ut(o,n,o+p*Math.cos(g),n+f*Math.sin(g),l)):u.push({op:"lineTo",data:[o,n]},{op:"lineTo",data:[o+p*Math.cos(m),n+f*Math.sin(m)]})),{type:"path",ops:u}}x(ke,"A");function $e(c,t){let i=Ge(Re(de(c))),r=[],e=[0,0],a=[0,0];for(let{key:h,data:s}of i)switch(h){case"M":a=[s[0],s[1]],e=[s[0],s[1]];break;case"L":r.push(...ut(a[0],a[1],s[0],s[1],t)),a=[s[0],s[1]];break;case"C":{let[l,o,n,p,f,m]=s;r.push(...rr(l,o,n,p,f,m,a,t)),a=[f,m];break}case"Z":r.push(...ut(a[0],a[1],e[0],e[1],t)),a=[e[0],e[1]]}return{type:"path",ops:r}}x($e,"_");function se(c,t){let i=[];for(let r of c)if(r.length){let e=t.maxRandomnessOffset||0,a=r.length;if(a>2){i.push({op:"move",data:[r[0][0]+_(e,t),r[0][1]+_(e,t)]});for(let h=1;h<a;h++)i.push({op:"lineTo",data:[r[h][0]+_(e,t),r[h][1]+_(e,t)]})}}return{type:"fillPath",ops:i}}x(se,"I");function Mt(c,t){return function(i,r){let e=i.fillStyle||"hachure";if(!et[e])switch(e){case"zigzag":et[e]||(et[e]=new ae(r));break;case"cross-hatch":et[e]||(et[e]=new oe(r));break;case"dots":et[e]||(et[e]=new ie(r));break;case"dashed":et[e]||(et[e]=new ne(r));break;case"zigzag-line":et[e]||(et[e]=new le(r));break;default:e="hachure",et[e]||(et[e]=new Bt(r))}return et[e]}(t,er).fillPolygons(c,t)}x(Mt,"C");function Pe(c){let t=Object.assign({},c);return t.randomizer=void 0,c.seed&&(t.seed=c.seed+1),t}x(Pe,"z");function Oe(c){return c.randomizer||(c.randomizer=new ce(c.seed||0)),c.randomizer.next()}x(Oe,"W");function It(c,t,i,r=1){return i.roughness*r*(Oe(i)*(t-c)+c)}x(It,"E");function _(c,t,i=1){return It(-c,c,t,i)}x(_,"G");function ut(c,t,i,r,e,a=!1){let h=a?e.disableMultiStrokeFill:e.disableMultiStroke,s=pe(c,t,i,r,e,!0,!1);if(h)return s;let l=pe(c,t,i,r,e,!0,!0);return s.concat(l)}x(ut,"$");function pe(c,t,i,r,e,a,h){let s=Math.pow(c-i,2)+Math.pow(t-r,2),l=Math.sqrt(s),o=1;o=l<200?1:l>500?.4:-.0016668*l+1.233334;let n=e.maxRandomnessOffset||0;n*n*100>s&&(n=l/10);let p=n/2,f=.2+.2*Oe(e),m=e.bowing*e.maxRandomnessOffset*(r-t)/200,g=e.bowing*e.maxRandomnessOffset*(c-i)/200;m=_(m,e,o),g=_(g,e,o);let d=[],y=x(()=>_(p,e,o),"M"),u=x(()=>_(n,e,o),"k"),b=e.preserveVertices;return a&&(h?d.push({op:"move",data:[c+(b?0:y()),t+(b?0:y())]}):d.push({op:"move",data:[c+(b?0:_(n,e,o)),t+(b?0:_(n,e,o))]})),h?d.push({op:"bcurveTo",data:[m+c+(i-c)*f+y(),g+t+(r-t)*f+y(),m+c+2*(i-c)*f+y(),g+t+2*(r-t)*f+y(),i+(b?0:y()),r+(b?0:y())]}):d.push({op:"bcurveTo",data:[m+c+(i-c)*f+u(),g+t+(r-t)*f+u(),m+c+2*(i-c)*f+u(),g+t+2*(r-t)*f+u(),i+(b?0:u()),r+(b?0:u())]}),d}x(pe,"R");function Lt(c,t,i){if(!c.length)return[];let r=[];r.push([c[0][0]+_(t,i),c[0][1]+_(t,i)]),r.push([c[0][0]+_(t,i),c[0][1]+_(t,i)]);for(let e=1;e<c.length;e++)r.push([c[e][0]+_(t,i),c[e][1]+_(t,i)]),e===c.length-1&&r.push([c[e][0]+_(t,i),c[e][1]+_(t,i)]);return Ft(r,null,i)}x(Lt,"j");function Ft(c,t,i){let r=c.length,e=[];if(r>3){let a=[],h=1-i.curveTightness;e.push({op:"move",data:[c[1][0],c[1][1]]});for(let s=1;s+2<r;s++){let l=c[s];a[0]=[l[0],l[1]],a[1]=[l[0]+(h*c[s+1][0]-h*c[s-1][0])/6,l[1]+(h*c[s+1][1]-h*c[s-1][1])/6],a[2]=[c[s+1][0]+(h*c[s][0]-h*c[s+2][0])/6,c[s+1][1]+(h*c[s][1]-h*c[s+2][1])/6],a[3]=[c[s+1][0],c[s+1][1]],e.push({op:"bcurveTo",data:[a[1][0],a[1][1],a[2][0],a[2][1],a[3][0],a[3][1]]})}if(t&&t.length===2){let s=i.maxRandomnessOffset;e.push({op:"lineTo",data:[t[0]+_(s,i),t[1]+_(s,i)]})}}else r===3?(e.push({op:"move",data:[c[1][0],c[1][1]]}),e.push({op:"bcurveTo",data:[c[1][0],c[1][1],c[2][0],c[2][1],c[2][0],c[2][1]]})):r===2&&e.push(...pe(c[0][0],c[0][1],c[1][0],c[1][1],i,!0,!0));return e}x(Ft,"q");function Ce(c,t,i,r,e,a,h,s){let l=[],o=[];if(s.roughness===0){c/=4,o.push([t+r*Math.cos(-c),i+e*Math.sin(-c)]);for(let n=0;n<=2*Math.PI;n+=c){let p=[t+r*Math.cos(n),i+e*Math.sin(n)];l.push(p),o.push(p)}o.push([t+r*Math.cos(0),i+e*Math.sin(0)]),o.push([t+r*Math.cos(c),i+e*Math.sin(c)])}else{let n=_(.5,s)-Math.PI/2;o.push([_(a,s)+t+.9*r*Math.cos(n-c),_(a,s)+i+.9*e*Math.sin(n-c)]);let p=2*Math.PI+n-.01;for(let f=n;f<p;f+=c){let m=[_(a,s)+t+r*Math.cos(f),_(a,s)+i+e*Math.sin(f)];l.push(m),o.push(m)}o.push([_(a,s)+t+r*Math.cos(n+2*Math.PI+.5*h),_(a,s)+i+e*Math.sin(n+2*Math.PI+.5*h)]),o.push([_(a,s)+t+.98*r*Math.cos(n+h),_(a,s)+i+.98*e*Math.sin(n+h)]),o.push([_(a,s)+t+.9*r*Math.cos(n+.5*h),_(a,s)+i+.9*e*Math.sin(n+.5*h)])}return[o,l]}x(Ce,"F");function Be(c,t,i,r,e,a,h,s,l){let o=a+_(.1,l),n=[];n.push([_(s,l)+t+.9*r*Math.cos(o-c),_(s,l)+i+.9*e*Math.sin(o-c)]);for(let p=o;p<=h;p+=c)n.push([_(s,l)+t+r*Math.cos(p),_(s,l)+i+e*Math.sin(p)]);return n.push([t+r*Math.cos(h),i+e*Math.sin(h)]),n.push([t+r*Math.cos(h),i+e*Math.sin(h)]),Ft(n,null,l)}x(Be,"V");function rr(c,t,i,r,e,a,h,s){let l=[],o=[s.maxRandomnessOffset||1,(s.maxRandomnessOffset||1)+.3],n=[0,0],p=s.disableMultiStroke?1:2,f=s.preserveVertices;for(let m=0;m<p;m++)m===0?l.push({op:"move",data:[h[0],h[1]]}):l.push({op:"move",data:[h[0]+(f?0:_(o[0],s)),h[1]+(f?0:_(o[0],s))]}),n=f?[e,a]:[e+_(o[m],s),a+_(o[m],s)],l.push({op:"bcurveTo",data:[c+_(o[m],s),t+_(o[m],s),i+_(o[m],s),r+_(o[m],s),n[0],n[1]]});return l}x(rr,"Z");function Ct(c){return[...c]}x(Ct,"Q");function Te(c,t=0){let i=c.length;if(i<3)throw new Error("A curve must have at least three points.");let r=[];if(i===3)r.push(Ct(c[0]),Ct(c[1]),Ct(c[2]),Ct(c[2]));else{let e=[];e.push(c[0],c[0]);for(let s=1;s<c.length;s++)e.push(c[s]),s===c.length-1&&e.push(c[s]);let a=[],h=1-t;r.push(Ct(e[0]));for(let s=1;s+2<e.length;s++){let l=e[s];a[0]=[l[0],l[1]],a[1]=[l[0]+(h*e[s+1][0]-h*e[s-1][0])/6,l[1]+(h*e[s+1][1]-h*e[s-1][1])/6],a[2]=[e[s+1][0]+(h*e[s][0]-h*e[s+2][0])/6,e[s+1][1]+(h*e[s][1]-h*e[s+2][1])/6],a[3]=[e[s+1][0],e[s+1][1]],r.push(a[1],a[2],a[3])}}return r}x(Te,"H");function Vt(c,t){return Math.pow(c[0]-t[0],2)+Math.pow(c[1]-t[1],2)}x(Vt,"N");function ar(c,t,i){let r=Vt(t,i);if(r===0)return Vt(c,t);let e=((c[0]-t[0])*(i[0]-t[0])+(c[1]-t[1])*(i[1]-t[1]))/r;return e=Math.max(0,Math.min(1,e)),Vt(c,vt(t,i,e))}x(ar,"B");function vt(c,t,i){return[c[0]+(t[0]-c[0])*i,c[1]+(t[1]-c[1])*i]}x(vt,"J");function me(c,t,i,r){let e=r||[];if(function(s,l){let o=s[l+0],n=s[l+1],p=s[l+2],f=s[l+3],m=3*n[0]-2*o[0]-f[0];m*=m;let g=3*n[1]-2*o[1]-f[1];g*=g;let d=3*p[0]-2*f[0]-o[0];d*=d;let y=3*p[1]-2*f[1]-o[1];return y*=y,m<d&&(m=d),g<y&&(g=y),m+g}(c,t)<i){let s=c[t+0];e.length?(a=e[e.length-1],h=s,Math.sqrt(Vt(a,h))>1&&e.push(s)):e.push(s),e.push(c[t+3])}else{let l=c[t+0],o=c[t+1],n=c[t+2],p=c[t+3],f=vt(l,o,.5),m=vt(o,n,.5),g=vt(n,p,.5),d=vt(f,m,.5),y=vt(m,g,.5),u=vt(d,y,.5);me([l,f,d,u],0,i,e),me([u,y,g,p],0,i,e)}var a,h;return e}x(me,"K");function or(c,t){return _t(c,0,c.length,t)}x(or,"U");function _t(c,t,i,r,e){let a=e||[],h=c[t],s=c[i-1],l=0,o=1;for(let n=t+1;n<i-1;++n){let p=ar(c[n],h,s);p>l&&(l=p,o=n)}return Math.sqrt(l)>r?(_t(c,t,o+1,r,a),_t(c,o,i,r,a)):(a.length||a.push(h),a.push(s)),a}x(_t,"X");function re(c,t=.15,i){let r=[],e=(c.length-1)/3;for(let a=0;a<e;a++)me(c,3*a,t,r);return i&&i>0?_t(r,0,r.length,i):r}x(re,"Y");var ot="none",kt=class{static{x(this,"et")}constructor(t){this.defaultOptions={maxRandomnessOffset:2,roughness:1,bowing:1,stroke:"#000",strokeWidth:1,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:"hachure",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,seed:0,disableMultiStroke:!1,disableMultiStrokeFill:!1,preserveVertices:!1,fillShapeRoughnessGain:.8},this.config=t||{},this.config.options&&(this.defaultOptions=this._o(this.config.options))}static newSeed(){return Math.floor(Math.random()*2**31)}_o(t){return t?Object.assign({},this.defaultOptions,t):this.defaultOptions}_d(t,i,r){return{shape:t,sets:i||[],options:r||this.defaultOptions}}line(t,i,r,e,a){let h=this._o(a);return this._d("line",[Ae(t,i,r,e,h)],h)}rectangle(t,i,r,e,a){let h=this._o(a),s=[],l=sr(t,i,r,e,h);if(h.fill){let o=[[t,i],[t+r,i],[t+r,i+e],[t,i+e]];h.fillStyle==="solid"?s.push(se([o],h)):s.push(Mt([o],h))}return h.stroke!==ot&&s.push(l),this._d("rectangle",s,h)}ellipse(t,i,r,e,a){let h=this._o(a),s=[],l=je(r,e,h),o=he(t,i,h,l);if(h.fill)if(h.fillStyle==="solid"){let n=he(t,i,h,l).opset;n.type="fillPath",s.push(n)}else s.push(Mt([o.estimatedPoints],h));return h.stroke!==ot&&s.push(o.opset),this._d("ellipse",s,h)}circle(t,i,r,e){let a=this.ellipse(t,i,r,r,e);return a.shape="circle",a}linearPath(t,i){let r=this._o(i);return this._d("linearPath",[Wt(t,!1,r)],r)}arc(t,i,r,e,a,h,s=!1,l){let o=this._o(l),n=[],p=ke(t,i,r,e,a,h,s,!0,o);if(s&&o.fill)if(o.fillStyle==="solid"){let f=Object.assign({},o);f.disableMultiStroke=!0;let m=ke(t,i,r,e,a,h,!0,!1,f);m.type="fillPath",n.push(m)}else n.push(function(f,m,g,d,y,u,b){let S=f,M=m,w=Math.abs(g/2),P=Math.abs(d/2);w+=_(.01*w,b),P+=_(.01*P,b);let B=y,R=u;for(;B<0;)B+=2*Math.PI,R+=2*Math.PI;R-B>2*Math.PI&&(B=0,R=2*Math.PI);let E=(R-B)/b.curveStepCount,L=[];for(let O=B;O<=R;O+=E)L.push([S+w*Math.cos(O),M+P*Math.sin(O)]);return L.push([S+w*Math.cos(R),M+P*Math.sin(R)]),L.push([S,M]),Mt([L],b)}(t,i,r,e,a,h,o));return o.stroke!==ot&&n.push(p),this._d("arc",n,o)}curve(t,i){let r=this._o(i),e=[],a=Me(t,r);if(r.fill&&r.fill!==ot)if(r.fillStyle==="solid"){let h=Me(t,Object.assign(Object.assign({},r),{disableMultiStroke:!0,roughness:r.roughness?r.roughness+r.fillShapeRoughnessGain:0}));e.push({type:"fillPath",ops:this._mergedShape(h.ops)})}else{let h=[],s=t;if(s.length){let l=typeof s[0][0]=="number"?[s]:s;for(let o of l)o.length<3?h.push(...o):o.length===3?h.push(...re(Te([o[0],o[0],o[1],o[2]]),10,(1+r.roughness)/2)):h.push(...re(Te(o),10,(1+r.roughness)/2))}h.length&&e.push(Mt([h],r))}return r.stroke!==ot&&e.push(a),this._d("curve",e,r)}polygon(t,i){let r=this._o(i),e=[],a=Wt(t,!0,r);return r.fill&&(r.fillStyle==="solid"?e.push(se([t],r)):e.push(Mt([t],r))),r.stroke!==ot&&e.push(a),this._d("polygon",e,r)}path(t,i){let r=this._o(i),e=[];if(!t)return this._d("path",e,r);t=(t||"").replace(/\n/g," ").replace(/(-\s)/g,"-").replace("/(ss)/g"," ");let a=r.fill&&r.fill!=="transparent"&&r.fill!==ot,h=r.stroke!==ot,s=!!(r.simplification&&r.simplification<1),l=function(n,p,f){let m=Ge(Re(de(n))),g=[],d=[],y=[0,0],u=[],b=x(()=>{u.length>=4&&d.push(...re(u,p)),u=[]},"i"),S=x(()=>{b(),d.length&&(g.push(d),d=[])},"c");for(let{key:w,data:P}of m)switch(w){case"M":S(),y=[P[0],P[1]],d.push(y);break;case"L":b(),d.push([P[0],P[1]]);break;case"C":if(!u.length){let B=d.length?d[d.length-1]:y;u.push([B[0],B[1]])}u.push([P[0],P[1]]),u.push([P[2],P[3]]),u.push([P[4],P[5]]);break;case"Z":b(),d.push([y[0],y[1]])}if(S(),!f)return g;let M=[];for(let w of g){let P=or(w,f);P.length&&M.push(P)}return M}(t,1,s?4-4*(r.simplification||1):(1+r.roughness)/2),o=$e(t,r);if(a)if(r.fillStyle==="solid")if(l.length===1){let n=$e(t,Object.assign(Object.assign({},r),{disableMultiStroke:!0,roughness:r.roughness?r.roughness+r.fillShapeRoughnessGain:0}));e.push({type:"fillPath",ops:this._mergedShape(n.ops)})}else e.push(se(l,r));else e.push(Mt(l,r));return h&&(s?l.forEach(n=>{e.push(Wt(n,!1,r))}):e.push(o)),this._d("path",e,r)}opsToPath(t,i){let r="";for(let e of t.ops){let a=typeof i=="number"&&i>=0?e.data.map(h=>+h.toFixed(i)):e.data;switch(e.op){case"move":r+=`M${a[0]} ${a[1]} `;break;case"bcurveTo":r+=`C${a[0]} ${a[1]}, ${a[2]} ${a[3]}, ${a[4]} ${a[5]} `;break;case"lineTo":r+=`L${a[0]} ${a[1]} `}}return r.trim()}toPaths(t){let i=t.sets||[],r=t.options||this.defaultOptions,e=[];for(let a of i){let h=null;switch(a.type){case"path":h={d:this.opsToPath(a),stroke:r.stroke,strokeWidth:r.strokeWidth,fill:ot};break;case"fillPath":h={d:this.opsToPath(a),stroke:ot,strokeWidth:0,fill:r.fill||ot};break;case"fillSketch":h=this.fillSketch(a,r)}h&&e.push(h)}return e}fillSketch(t,i){let r=i.fillWeight;return r<0&&(r=i.strokeWidth/2),{d:this.opsToPath(t),stroke:i.fill||ot,strokeWidth:r,fill:ot}}_mergedShape(t){return t.filter((i,r)=>r===0||i.op!=="move")}},fe=class{static{x(this,"st")}constructor(t,i){this.canvas=t,this.ctx=this.canvas.getContext("2d"),this.gen=new kt(i)}draw(t){let i=t.sets||[],r=t.options||this.getDefaultOptions(),e=this.ctx,a=t.options.fixedDecimalPlaceDigits;for(let h of i)switch(h.type){case"path":e.save(),e.strokeStyle=r.stroke==="none"?"transparent":r.stroke,e.lineWidth=r.strokeWidth,r.strokeLineDash&&e.setLineDash(r.strokeLineDash),r.strokeLineDashOffset&&(e.lineDashOffset=r.strokeLineDashOffset),this._drawToContext(e,h,a),e.restore();break;case"fillPath":{e.save(),e.fillStyle=r.fill||"";let s=t.shape==="curve"||t.shape==="polygon"||t.shape==="path"?"evenodd":"nonzero";this._drawToContext(e,h,a,s),e.restore();break}case"fillSketch":this.fillSketch(e,h,r)}}fillSketch(t,i,r){let e=r.fillWeight;e<0&&(e=r.strokeWidth/2),t.save(),r.fillLineDash&&t.setLineDash(r.fillLineDash),r.fillLineDashOffset&&(t.lineDashOffset=r.fillLineDashOffset),t.strokeStyle=r.fill||"",t.lineWidth=e,this._drawToContext(t,i,r.fixedDecimalPlaceDigits),t.restore()}_drawToContext(t,i,r,e="nonzero"){t.beginPath();for(let a of i.ops){let h=typeof r=="number"&&r>=0?a.data.map(s=>+s.toFixed(r)):a.data;switch(a.op){case"move":t.moveTo(h[0],h[1]);break;case"bcurveTo":t.bezierCurveTo(h[0],h[1],h[2],h[3],h[4],h[5]);break;case"lineTo":t.lineTo(h[0],h[1])}}i.type==="fillPath"?t.fill(e):t.stroke()}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}line(t,i,r,e,a){let h=this.gen.line(t,i,r,e,a);return this.draw(h),h}rectangle(t,i,r,e,a){let h=this.gen.rectangle(t,i,r,e,a);return this.draw(h),h}ellipse(t,i,r,e,a){let h=this.gen.ellipse(t,i,r,e,a);return this.draw(h),h}circle(t,i,r,e){let a=this.gen.circle(t,i,r,e);return this.draw(a),a}linearPath(t,i){let r=this.gen.linearPath(t,i);return this.draw(r),r}polygon(t,i){let r=this.gen.polygon(t,i);return this.draw(r),r}arc(t,i,r,e,a,h,s=!1,l){let o=this.gen.arc(t,i,r,e,a,h,s,l);return this.draw(o),o}curve(t,i){let r=this.gen.curve(t,i);return this.draw(r),r}path(t,i){let r=this.gen.path(t,i);return this.draw(r),r}},Ht="http://www.w3.org/2000/svg",ge=class{static{x(this,"ot")}constructor(t,i){this.svg=t,this.gen=new kt(i)}draw(t){let i=t.sets||[],r=t.options||this.getDefaultOptions(),e=this.svg.ownerDocument||window.document,a=e.createElementNS(Ht,"g"),h=t.options.fixedDecimalPlaceDigits;for(let s of i){let l=null;switch(s.type){case"path":l=e.createElementNS(Ht,"path"),l.setAttribute("d",this.opsToPath(s,h)),l.setAttribute("stroke",r.stroke),l.setAttribute("stroke-width",r.strokeWidth+""),l.setAttribute("fill","none"),r.strokeLineDash&&l.setAttribute("stroke-dasharray",r.strokeLineDash.join(" ").trim()),r.strokeLineDashOffset&&l.setAttribute("stroke-dashoffset",`${r.strokeLineDashOffset}`);break;case"fillPath":l=e.createElementNS(Ht,"path"),l.setAttribute("d",this.opsToPath(s,h)),l.setAttribute("stroke","none"),l.setAttribute("stroke-width","0"),l.setAttribute("fill",r.fill||""),t.shape!=="curve"&&t.shape!=="polygon"||l.setAttribute("fill-rule","evenodd");break;case"fillSketch":l=this.fillSketch(e,s,r)}l&&a.appendChild(l)}return a}fillSketch(t,i,r){let e=r.fillWeight;e<0&&(e=r.strokeWidth/2);let a=t.createElementNS(Ht,"path");return a.setAttribute("d",this.opsToPath(i,r.fixedDecimalPlaceDigits)),a.setAttribute("stroke",r.fill||""),a.setAttribute("stroke-width",e+""),a.setAttribute("fill","none"),r.fillLineDash&&a.setAttribute("stroke-dasharray",r.fillLineDash.join(" ").trim()),r.fillLineDashOffset&&a.setAttribute("stroke-dashoffset",`${r.fillLineDashOffset}`),a}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}opsToPath(t,i){return this.gen.opsToPath(t,i)}line(t,i,r,e,a){let h=this.gen.line(t,i,r,e,a);return this.draw(h)}rectangle(t,i,r,e,a){let h=this.gen.rectangle(t,i,r,e,a);return this.draw(h)}ellipse(t,i,r,e,a){let h=this.gen.ellipse(t,i,r,e,a);return this.draw(h)}circle(t,i,r,e){let a=this.gen.circle(t,i,r,e);return this.draw(a)}linearPath(t,i){let r=this.gen.linearPath(t,i);return this.draw(r)}polygon(t,i){let r=this.gen.polygon(t,i);return this.draw(r)}arc(t,i,r,e,a,h,s=!1,l){let o=this.gen.arc(t,i,r,e,a,h,s,l);return this.draw(o)}curve(t,i){let r=this.gen.curve(t,i);return this.draw(r)}path(t,i){let r=this.gen.path(t,i);return this.draw(r)}},v={canvas:x((c,t)=>new fe(c,t),"canvas"),svg:x((c,t)=>new ge(c,t),"svg"),generator:x(c=>new kt(c),"generator"),newSeed:x(()=>kt.newSeed(),"newSeed")};var ir=x((c,t)=>{var i=c.x,r=c.y,e=t.x-i,a=t.y-r,h=c.width/2,s=c.height/2,l,o;return Math.abs(a)*h>Math.abs(e)*s?(a<0&&(s=-s),l=a===0?0:s*e/a,o=s):(e<0&&(h=-h),l=h,o=e===0?0:h*a/e),{x:i+l,y:r+o}},"intersectRect"),yt=ir;function nr(c,t){t&&c.attr("style",t)}x(nr,"applyStyle");async function lr(c){let t=Y(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),i=t.append("xhtml:div"),r=q(),e=c.label;c.label&&Et(c.label)&&(e=await Se(c.label.replace(we.lineBreakRegex,`
`),r));let h='<span class="'+(c.isNode?"nodeLabel":"edgeLabel")+'" '+(c.labelStyle?'style="'+c.labelStyle+'"':"")+">"+e+"</span>";return i.html($t(h,r)),nr(i,c.labelStyle),i.style("display","inline-block"),i.style("padding-right","1px"),i.style("white-space","nowrap"),i.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}x(lr,"addHtmlLabel");var cr=x(async(c,t,i,r)=>{let e=c||"";if(typeof e=="object"&&(e=e[0]),Z(q().flowchart.htmlLabels)){e=e.replace(/\\n|\n/g,"<br />"),z.info("vertexText"+e);let a={isNode:r,label:ht(e).replace(/fa[blrs]?:fa-[\w-]+/g,s=>`<i class='${s.replace(":"," ")}'></i>`),labelStyle:t&&t.replace("fill:","color:")};return await lr(a)}else{let a=document.createElementNS("http://www.w3.org/2000/svg","text");a.setAttribute("style",t.replace("color:","fill:"));let h=[];typeof e=="string"?h=e.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(e)?h=e:h=[];for(let s of h){let l=document.createElementNS("http://www.w3.org/2000/svg","tspan");l.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),l.setAttribute("dy","1em"),l.setAttribute("x","0"),i?l.setAttribute("class","title-row"):l.setAttribute("class","row"),l.textContent=s.trim(),a.appendChild(l)}return a}},"createLabel"),Rt=cr;var st=x((c,t,i,r,e)=>["M",c+e,t,"H",c+i-e,"A",e,e,0,0,1,c+i,t+e,"V",t+r-e,"A",e,e,0,0,1,c+i-e,t+r,"H",c+e,"A",e,e,0,0,1,c,t+r-e,"V",t+e,"A",e,e,0,0,1,c+e,t,"Z"].join(" "),"createRoundedRectPathD");var Le=x(async(c,t)=>{z.info("Creating subgraph rect for ",t.id,t);let i=q(),{themeVariables:r,handDrawnSeed:e}=i,{clusterBkg:a,clusterBorder:h}=r,{labelStyles:s,nodeStyles:l,borderStyles:o,backgroundStyles:n}=k(t),p=c.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),f=Z(i.flowchart.htmlLabels),m=p.insert("g").attr("class","cluster-label "),g=await nt(m,t.label,{style:t.labelStyle,useHtmlLabels:f,isNode:!0}),d=g.getBBox();if(Z(i.flowchart.htmlLabels)){let B=g.children[0],R=Y(g);d=B.getBoundingClientRect(),R.attr("width",d.width),R.attr("height",d.height)}let y=t.width<=d.width+t.padding?d.width+t.padding:t.width;t.width<=d.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;let u=t.height,b=t.x-y/2,S=t.y-u/2;z.trace("Data ",t,JSON.stringify(t));let M;if(t.look==="handDrawn"){let B=v.svg(p),R=D(t,{roughness:.7,fill:a,stroke:h,fillWeight:3,seed:e}),E=B.path(st(b,S,y,u,0),R);M=p.insert(()=>(z.debug("Rough node insert CXC",E),E),":first-child"),M.select("path:nth-child(2)").attr("style",o.join(";")),M.select("path").attr("style",n.join(";").replace("fill","stroke"))}else M=p.insert("rect",":first-child"),M.attr("style",l).attr("rx",t.rx).attr("ry",t.ry).attr("x",b).attr("y",S).attr("width",y).attr("height",u);let{subGraphTitleTopMargin:w}=Jt(i);if(m.attr("transform",`translate(${t.x-d.width/2}, ${t.y-t.height/2+w})`),s){let B=m.select("span");B&&B.attr("style",s)}let P=M.node().getBBox();return t.offsetX=0,t.width=P.width,t.height=P.height,t.offsetY=d.height-t.padding/2,t.intersect=function(B){return yt(t,B)},{cluster:p,labelBBox:d}},"rect"),hr=x((c,t)=>{let i=c.insert("g").attr("class","note-cluster").attr("id",t.id),r=i.insert("rect",":first-child"),e=0*t.padding,a=e/2;r.attr("rx",t.rx).attr("ry",t.ry).attr("x",t.x-t.width/2-a).attr("y",t.y-t.height/2-a).attr("width",t.width+e).attr("height",t.height+e).attr("fill","none");let h=r.node().getBBox();return t.width=h.width,t.height=h.height,t.intersect=function(s){return yt(t,s)},{cluster:i,labelBBox:{width:0,height:0}}},"noteGroup"),pr=x(async(c,t)=>{let i=q(),{themeVariables:r,handDrawnSeed:e}=i,{altBackground:a,compositeBackground:h,compositeTitleBackground:s,nodeBorder:l}=r,o=c.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-id",t.id).attr("data-look",t.look),n=o.insert("g",":first-child"),p=o.insert("g").attr("class","cluster-label"),f=o.append("rect"),m=p.node().appendChild(await Rt(t.label,t.labelStyle,void 0,!0)),g=m.getBBox();if(Z(i.flowchart.htmlLabels)){let E=m.children[0],L=Y(m);g=E.getBoundingClientRect(),L.attr("width",g.width),L.attr("height",g.height)}let d=0*t.padding,y=d/2,u=(t.width<=g.width+t.padding?g.width+t.padding:t.width)+d;t.width<=g.width+t.padding?t.diff=(u-t.width)/2-t.padding:t.diff=-t.padding;let b=t.height+d,S=t.height+d-g.height-6,M=t.x-u/2,w=t.y-b/2;t.width=u;let P=t.y-t.height/2-y+g.height+2,B;if(t.look==="handDrawn"){let E=t.cssClasses.includes("statediagram-cluster-alt"),L=v.svg(o),O=t.rx||t.ry?L.path(st(M,w,u,b,10),{roughness:.7,fill:s,fillStyle:"solid",stroke:l,seed:e}):L.rectangle(M,w,u,b,{seed:e});B=o.insert(()=>O,":first-child");let W=L.rectangle(M,P,u,S,{fill:E?a:h,fillStyle:E?"hachure":"solid",stroke:l,seed:e});B=o.insert(()=>O,":first-child"),f=o.insert(()=>W)}else B=n.insert("rect",":first-child"),B.attr("class","outer").attr("x",M).attr("y",w).attr("width",u).attr("height",b).attr("data-look",t.look),f.attr("class","inner").attr("x",M).attr("y",P).attr("width",u).attr("height",S);p.attr("transform",`translate(${t.x-g.width/2}, ${w+1-(Z(i.flowchart.htmlLabels)?0:3)})`);let R=B.node().getBBox();return t.height=R.height,t.offsetX=0,t.offsetY=g.height-t.padding/2,t.labelBBox=g,t.intersect=function(E){return yt(t,E)},{cluster:o,labelBBox:g}},"roundedWithTitle"),mr=x(async(c,t)=>{z.info("Creating subgraph rect for ",t.id,t);let i=q(),{themeVariables:r,handDrawnSeed:e}=i,{clusterBkg:a,clusterBorder:h}=r,{labelStyles:s,nodeStyles:l,borderStyles:o,backgroundStyles:n}=k(t),p=c.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),f=Z(i.flowchart.htmlLabels),m=p.insert("g").attr("class","cluster-label "),g=await nt(m,t.label,{style:t.labelStyle,useHtmlLabels:f,isNode:!0,width:t.width}),d=g.getBBox();if(Z(i.flowchart.htmlLabels)){let B=g.children[0],R=Y(g);d=B.getBoundingClientRect(),R.attr("width",d.width),R.attr("height",d.height)}let y=t.width<=d.width+t.padding?d.width+t.padding:t.width;t.width<=d.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;let u=t.height,b=t.x-y/2,S=t.y-u/2;z.trace("Data ",t,JSON.stringify(t));let M;if(t.look==="handDrawn"){let B=v.svg(p),R=D(t,{roughness:.7,fill:a,stroke:h,fillWeight:4,seed:e}),E=B.path(st(b,S,y,u,t.rx),R);M=p.insert(()=>(z.debug("Rough node insert CXC",E),E),":first-child"),M.select("path:nth-child(2)").attr("style",o.join(";")),M.select("path").attr("style",n.join(";").replace("fill","stroke"))}else M=p.insert("rect",":first-child"),M.attr("style",l).attr("rx",t.rx).attr("ry",t.ry).attr("x",b).attr("y",S).attr("width",y).attr("height",u);let{subGraphTitleTopMargin:w}=Jt(i);if(m.attr("transform",`translate(${t.x-d.width/2}, ${t.y-t.height/2+w})`),s){let B=m.select("span");B&&B.attr("style",s)}let P=M.node().getBBox();return t.offsetX=0,t.width=P.width,t.height=P.height,t.offsetY=d.height-t.padding/2,t.intersect=function(B){return yt(t,B)},{cluster:p,labelBBox:d}},"kanbanSection"),fr=x((c,t)=>{let i=q(),{themeVariables:r,handDrawnSeed:e}=i,{nodeBorder:a}=r,h=c.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-look",t.look),s=h.insert("g",":first-child"),l=0*t.padding,o=t.width+l;t.diff=-t.padding;let n=t.height+l,p=t.x-o/2,f=t.y-n/2;t.width=o;let m;if(t.look==="handDrawn"){let y=v.svg(h).rectangle(p,f,o,n,{fill:"lightgrey",roughness:.5,strokeLineDash:[5],stroke:a,seed:e});m=h.insert(()=>y,":first-child")}else m=s.insert("rect",":first-child"),m.attr("class","divider").attr("x",p).attr("y",f).attr("width",o).attr("height",n).attr("data-look",t.look);let g=m.node().getBBox();return t.height=g.height,t.offsetX=0,t.offsetY=0,t.intersect=function(d){return yt(t,d)},{cluster:h,labelBBox:{}}},"divider"),gr=Le,dr={rect:Le,squareRect:gr,roundedWithTitle:pr,noteGroup:hr,divider:fr,kanbanSection:mr},He=new Map,ga=x(async(c,t)=>{let i=t.shape||"rect",r=await dr[i](c,t);return He.set(t.id,r),r},"insertCluster");var da=x(()=>{He=new Map},"clear");function ur(c,t){return c.intersect(t)}x(ur,"intersectNode");var We=ur;function yr(c,t,i,r){var e=c.x,a=c.y,h=e-r.x,s=a-r.y,l=Math.sqrt(t*t*s*s+i*i*h*h),o=Math.abs(t*i*h/l);r.x<e&&(o=-o);var n=Math.abs(t*i*s/l);return r.y<a&&(n=-n),{x:e+o,y:a+n}}x(yr,"intersectEllipse");var qt=yr;function xr(c,t,i){return qt(c,t,t,i)}x(xr,"intersectCircle");var Ve=xr;function br(c,t,i,r){var e,a,h,s,l,o,n,p,f,m,g,d,y,u,b;if(e=t.y-c.y,h=c.x-t.x,l=t.x*c.y-c.x*t.y,f=e*i.x+h*i.y+l,m=e*r.x+h*r.y+l,!(f!==0&&m!==0&&Ie(f,m))&&(a=r.y-i.y,s=i.x-r.x,o=r.x*i.y-i.x*r.y,n=a*c.x+s*c.y+o,p=a*t.x+s*t.y+o,!(n!==0&&p!==0&&Ie(n,p))&&(g=e*s-a*h,g!==0)))return d=Math.abs(g/2),y=h*o-s*l,u=y<0?(y-d)/g:(y+d)/g,y=a*l-e*o,b=y<0?(y-d)/g:(y+d)/g,{x:u,y:b}}x(br,"intersectLine");function Ie(c,t){return c*t>0}x(Ie,"sameSign");var Fe=br;function Sr(c,t,i){let r=c.x,e=c.y,a=[],h=Number.POSITIVE_INFINITY,s=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(n){h=Math.min(h,n.x),s=Math.min(s,n.y)}):(h=Math.min(h,t.x),s=Math.min(s,t.y));let l=r-c.width/2-h,o=e-c.height/2-s;for(let n=0;n<t.length;n++){let p=t[n],f=t[n<t.length-1?n+1:0],m=Fe(c,i,{x:l+p.x,y:o+p.y},{x:l+f.x,y:o+f.y});m&&a.push(m)}return a.length?(a.length>1&&a.sort(function(n,p){let f=n.x-i.x,m=n.y-i.y,g=Math.sqrt(f*f+m*m),d=p.x-i.x,y=p.y-i.y,u=Math.sqrt(d*d+y*y);return g<u?-1:g===u?0:1}),a[0]):c}x(Sr,"intersectPolygon");var _e=Sr;var N={node:We,circle:Ve,ellipse:qt,polygon:_e,rect:yt};function ze(c,t){let{labelStyles:i}=k(t);t.labelStyle=i;let r=C(t),e=r;r||(e="anchor");let a=c.insert("g").attr("class",e).attr("id",t.domId||t.id),h=1,{cssStyles:s}=t,l=v.svg(a),o=D(t,{fill:"black",stroke:"none",fillStyle:"solid"});t.look!=="handDrawn"&&(o.roughness=0);let n=l.circle(0,0,h*2,o),p=a.insert(()=>n,":first-child");return p.attr("class","anchor").attr("style",U(s)),$(t,p),t.intersect=function(f){return z.info("Circle intersect",t,h,f),N.circle(t,h,f)},a}x(ze,"anchor");function qe(c,t,i,r,e,a,h){let l=(c+i)/2,o=(t+r)/2,n=Math.atan2(r-t,i-c),p=(i-c)/2,f=(r-t)/2,m=p/e,g=f/a,d=Math.sqrt(m**2+g**2);if(d>1)throw new Error("The given radii are too small to create an arc between the points.");let y=Math.sqrt(1-d**2),u=l+y*a*Math.sin(n)*(h?-1:1),b=o-y*e*Math.cos(n)*(h?-1:1),S=Math.atan2((t-b)/a,(c-u)/e),w=Math.atan2((r-b)/a,(i-u)/e)-S;h&&w<0&&(w+=2*Math.PI),!h&&w>0&&(w-=2*Math.PI);let P=[];for(let B=0;B<20;B++){let R=B/19,E=S+R*w,L=u+e*Math.cos(E),O=b+a*Math.sin(E);P.push({x:L,y:O})}return P}x(qe,"generateArcPoints");async function Xe(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=a.width+t.padding+20,s=a.height+t.padding,l=s/2,o=l/(2.5+s/50),{cssStyles:n}=t,p=[{x:h/2,y:-s/2},{x:-h/2,y:-s/2},...qe(-h/2,-s/2,-h/2,s/2,o,l,!1),{x:h/2,y:s/2},...qe(h/2,s/2,h/2,-s/2,o,l,!0)],f=v.svg(e),m=D(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");let g=H(p),d=f.path(g,m),y=e.insert(()=>d,":first-child");return y.attr("class","basic label-container"),n&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",n),r&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",r),y.attr("transform",`translate(${o/2}, 0)`),$(t,y),t.intersect=function(u){return N.polygon(t,p,u)},e}x(Xe,"bowTieRect");function rt(c,t,i,r){return c.insert("polygon",":first-child").attr("points",r.map(function(e){return e.x+","+e.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+i/2+")")}x(rt,"insertPolygonShape");async function Ye(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=a.height+t.padding,s=12,l=a.width+t.padding+s,o=0,n=l,p=-h,f=0,m=[{x:o+s,y:p},{x:n,y:p},{x:n,y:f},{x:o,y:f},{x:o,y:p+s},{x:o+s,y:p}],g,{cssStyles:d}=t;if(t.look==="handDrawn"){let y=v.svg(e),u=D(t,{}),b=H(m),S=y.path(b,u);g=e.insert(()=>S,":first-child").attr("transform",`translate(${-l/2}, ${h/2})`),d&&g.attr("style",d)}else g=rt(e,l,h,m);return r&&g.attr("style",r),$(t,g),t.intersect=function(y){return N.polygon(t,m,y)},e}x(Ye,"card");function Ze(c,t){let{nodeStyles:i}=k(t);t.label="";let r=c.insert("g").attr("class",C(t)).attr("id",t.domId??t.id),{cssStyles:e}=t,a=Math.max(28,t.width??0),h=[{x:0,y:a/2},{x:a/2,y:0},{x:0,y:-a/2},{x:-a/2,y:0}],s=v.svg(r),l=D(t,{});t.look!=="handDrawn"&&(l.roughness=0,l.fillStyle="solid");let o=H(h),n=s.path(o,l),p=r.insert(()=>n,":first-child");return e&&t.look!=="handDrawn"&&p.selectAll("path").attr("style",e),i&&t.look!=="handDrawn"&&p.selectAll("path").attr("style",i),t.width=28,t.height=28,t.intersect=function(f){return N.polygon(t,h,f)},r}x(Ze,"choice");async function Ue(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,halfPadding:h}=await T(c,t,C(t)),s=a.width/2+h,l,{cssStyles:o}=t;if(t.look==="handDrawn"){let n=v.svg(e),p=D(t,{}),f=n.circle(0,0,s*2,p);l=e.insert(()=>f,":first-child"),l.attr("class","basic label-container").attr("style",U(o))}else l=e.insert("circle",":first-child").attr("class","basic label-container").attr("style",r).attr("r",s).attr("cx",0).attr("cy",0);return $(t,l),t.intersect=function(n){return z.info("Circle intersect",t,s,n),N.circle(t,s,n)},e}x(Ue,"circle");function wr(c){let t=Math.cos(Math.PI/4),i=Math.sin(Math.PI/4),r=c*2,e={x:r/2*t,y:r/2*i},a={x:-(r/2)*t,y:r/2*i},h={x:-(r/2)*t,y:-(r/2)*i},s={x:r/2*t,y:-(r/2)*i};return`M ${a.x},${a.y} L ${s.x},${s.y}
                   M ${e.x},${e.y} L ${h.x},${h.y}`}x(wr,"createLine");function Qe(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i,t.label="";let e=c.insert("g").attr("class",C(t)).attr("id",t.domId??t.id),a=Math.max(30,t?.width??0),{cssStyles:h}=t,s=v.svg(e),l=D(t,{});t.look!=="handDrawn"&&(l.roughness=0,l.fillStyle="solid");let o=s.circle(0,0,a*2,l),n=wr(a),p=s.path(n,l),f=e.insert(()=>o,":first-child");return f.insert(()=>p),h&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",h),r&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",r),$(t,f),t.intersect=function(m){return z.info("crossedCircle intersect",t,{radius:a,point:m}),N.circle(t,a,m)},e}x(Qe,"crossedCircle");function xt(c,t,i,r=100,e=0,a=180){let h=[],s=e*Math.PI/180,n=(a*Math.PI/180-s)/(r-1);for(let p=0;p<r;p++){let f=s+p*n,m=c+i*Math.cos(f),g=t+i*Math.sin(f);h.push({x:-m,y:-g})}return h}x(xt,"generateCirclePoints");async function Je(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=a.width+(t.padding??0),l=a.height+(t.padding??0),o=Math.max(5,l*.1),{cssStyles:n}=t,p=[...xt(s/2,-l/2,o,30,-90,0),{x:-s/2-o,y:o},...xt(s/2+o*2,-o,o,20,-180,-270),...xt(s/2+o*2,o,o,20,-90,-180),{x:-s/2-o,y:-l/2},...xt(s/2,l/2,o,20,0,90)],f=[{x:s/2,y:-l/2-o},{x:-s/2,y:-l/2-o},...xt(s/2,-l/2,o,20,-90,0),{x:-s/2-o,y:-o},...xt(s/2+s*.1,-o,o,20,-180,-270),...xt(s/2+s*.1,o,o,20,-90,-180),{x:-s/2-o,y:l/2},...xt(s/2,l/2,o,20,0,90),{x:-s/2,y:l/2+o},{x:s/2,y:l/2+o}],m=v.svg(e),g=D(t,{fill:"none"});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");let y=H(p).replace("Z",""),u=m.path(y,g),b=H(f),S=m.path(b,{...g}),M=e.insert("g",":first-child");return M.insert(()=>S,":first-child").attr("stroke-opacity",0),M.insert(()=>u,":first-child"),M.attr("class","text"),n&&t.look!=="handDrawn"&&M.selectAll("path").attr("style",n),r&&t.look!=="handDrawn"&&M.selectAll("path").attr("style",r),M.attr("transform",`translate(${o}, 0)`),h.attr("transform",`translate(${-s/2+o-(a.x-(a.left??0))},${-l/2+(t.padding??0)/2-(a.y-(a.top??0))})`),$(t,M),t.intersect=function(w){return N.polygon(t,f,w)},e}x(Je,"curlyBraceLeft");function bt(c,t,i,r=100,e=0,a=180){let h=[],s=e*Math.PI/180,n=(a*Math.PI/180-s)/(r-1);for(let p=0;p<r;p++){let f=s+p*n,m=c+i*Math.cos(f),g=t+i*Math.sin(f);h.push({x:m,y:g})}return h}x(bt,"generateCirclePoints");async function Ke(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=a.width+(t.padding??0),l=a.height+(t.padding??0),o=Math.max(5,l*.1),{cssStyles:n}=t,p=[...bt(s/2,-l/2,o,20,-90,0),{x:s/2+o,y:-o},...bt(s/2+o*2,-o,o,20,-180,-270),...bt(s/2+o*2,o,o,20,-90,-180),{x:s/2+o,y:l/2},...bt(s/2,l/2,o,20,0,90)],f=[{x:-s/2,y:-l/2-o},{x:s/2,y:-l/2-o},...bt(s/2,-l/2,o,20,-90,0),{x:s/2+o,y:-o},...bt(s/2+o*2,-o,o,20,-180,-270),...bt(s/2+o*2,o,o,20,-90,-180),{x:s/2+o,y:l/2},...bt(s/2,l/2,o,20,0,90),{x:s/2,y:l/2+o},{x:-s/2,y:l/2+o}],m=v.svg(e),g=D(t,{fill:"none"});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");let y=H(p).replace("Z",""),u=m.path(y,g),b=H(f),S=m.path(b,{...g}),M=e.insert("g",":first-child");return M.insert(()=>S,":first-child").attr("stroke-opacity",0),M.insert(()=>u,":first-child"),M.attr("class","text"),n&&t.look!=="handDrawn"&&M.selectAll("path").attr("style",n),r&&t.look!=="handDrawn"&&M.selectAll("path").attr("style",r),M.attr("transform",`translate(${-o}, 0)`),h.attr("transform",`translate(${-s/2+(t.padding??0)/2-(a.x-(a.left??0))},${-l/2+(t.padding??0)/2-(a.y-(a.top??0))})`),$(t,M),t.intersect=function(w){return N.polygon(t,f,w)},e}x(Ke,"curlyBraceRight");function K(c,t,i,r=100,e=0,a=180){let h=[],s=e*Math.PI/180,n=(a*Math.PI/180-s)/(r-1);for(let p=0;p<r;p++){let f=s+p*n,m=c+i*Math.cos(f),g=t+i*Math.sin(f);h.push({x:-m,y:-g})}return h}x(K,"generateCirclePoints");async function ts(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=a.width+(t.padding??0),l=a.height+(t.padding??0),o=Math.max(5,l*.1),{cssStyles:n}=t,p=[...K(s/2,-l/2,o,30,-90,0),{x:-s/2-o,y:o},...K(s/2+o*2,-o,o,20,-180,-270),...K(s/2+o*2,o,o,20,-90,-180),{x:-s/2-o,y:-l/2},...K(s/2,l/2,o,20,0,90)],f=[...K(-s/2+o+o/2,-l/2,o,20,-90,-180),{x:s/2-o/2,y:o},...K(-s/2-o/2,-o,o,20,0,90),...K(-s/2-o/2,o,o,20,-90,0),{x:s/2-o/2,y:-o},...K(-s/2+o+o/2,l/2,o,30,-180,-270)],m=[{x:s/2,y:-l/2-o},{x:-s/2,y:-l/2-o},...K(s/2,-l/2,o,20,-90,0),{x:-s/2-o,y:-o},...K(s/2+o*2,-o,o,20,-180,-270),...K(s/2+o*2,o,o,20,-90,-180),{x:-s/2-o,y:l/2},...K(s/2,l/2,o,20,0,90),{x:-s/2,y:l/2+o},{x:s/2-o-o/2,y:l/2+o},...K(-s/2+o+o/2,-l/2,o,20,-90,-180),{x:s/2-o/2,y:o},...K(-s/2-o/2,-o,o,20,0,90),...K(-s/2-o/2,o,o,20,-90,0),{x:s/2-o/2,y:-o},...K(-s/2+o+o/2,l/2,o,30,-180,-270)],g=v.svg(e),d=D(t,{fill:"none"});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");let u=H(p).replace("Z",""),b=g.path(u,d),M=H(f).replace("Z",""),w=g.path(M,d),P=H(m),B=g.path(P,{...d}),R=e.insert("g",":first-child");return R.insert(()=>B,":first-child").attr("stroke-opacity",0),R.insert(()=>b,":first-child"),R.insert(()=>w,":first-child"),R.attr("class","text"),n&&t.look!=="handDrawn"&&R.selectAll("path").attr("style",n),r&&t.look!=="handDrawn"&&R.selectAll("path").attr("style",r),R.attr("transform",`translate(${o-o/4}, 0)`),h.attr("transform",`translate(${-s/2+(t.padding??0)/2-(a.x-(a.left??0))},${-l/2+(t.padding??0)/2-(a.y-(a.top??0))})`),$(t,R),t.intersect=function(E){return N.polygon(t,m,E)},e}x(ts,"curlyBraces");async function es(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=80,s=20,l=Math.max(h,(a.width+(t.padding??0)*2)*1.25,t?.width??0),o=Math.max(s,a.height+(t.padding??0)*2,t?.height??0),n=o/2,{cssStyles:p}=t,f=v.svg(e),m=D(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");let g=l,d=o,y=g-n,u=d/4,b=[{x:y,y:0},{x:u,y:0},{x:0,y:d/2},{x:u,y:d},{x:y,y:d},...Nt(-y,-d/2,n,50,270,90)],S=H(b),M=f.path(S,m),w=e.insert(()=>M,":first-child");return w.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&w.selectChildren("path").attr("style",p),r&&t.look!=="handDrawn"&&w.selectChildren("path").attr("style",r),w.attr("transform",`translate(${-l/2}, ${-o/2})`),$(t,w),t.intersect=function(P){return N.polygon(t,b,P)},e}x(es,"curvedTrapezoid");var Nr=x((c,t,i,r,e,a)=>[`M${c},${t+a}`,`a${e},${a} 0,0,0 ${i},0`,`a${e},${a} 0,0,0 ${-i},0`,`l0,${r}`,`a${e},${a} 0,0,0 ${i},0`,`l0,${-r}`].join(" "),"createCylinderPathD"),vr=x((c,t,i,r,e,a)=>[`M${c},${t+a}`,`M${c+i},${t+a}`,`a${e},${a} 0,0,0 ${-i},0`,`l0,${r}`,`a${e},${a} 0,0,0 ${i},0`,`l0,${-r}`].join(" "),"createOuterCylinderPathD"),Dr=x((c,t,i,r,e,a)=>[`M${c-i/2},${-r/2}`,`a${e},${a} 0,0,0 ${i},0`].join(" "),"createInnerCylinderPathD");async function ss(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=Math.max(a.width+t.padding,t.width??0),l=s/2,o=l/(2.5+s/50),n=Math.max(a.height+o+t.padding,t.height??0),p,{cssStyles:f}=t;if(t.look==="handDrawn"){let m=v.svg(e),g=vr(0,0,s,n,l,o),d=Dr(0,o,s,n,l,o),y=m.path(g,D(t,{})),u=m.path(d,D(t,{fill:"none"}));p=e.insert(()=>u,":first-child"),p=e.insert(()=>y,":first-child"),p.attr("class","basic label-container"),f&&p.attr("style",f)}else{let m=Nr(0,0,s,n,l,o);p=e.insert("path",":first-child").attr("d",m).attr("class","basic label-container").attr("style",U(f)).attr("style",r)}return p.attr("label-offset-y",o),p.attr("transform",`translate(${-s/2}, ${-(n/2+o)})`),$(t,p),h.attr("transform",`translate(${-(a.width/2)-(a.x-(a.left??0))}, ${-(a.height/2)+(t.padding??0)/1.5-(a.y-(a.top??0))})`),t.intersect=function(m){let g=N.rect(t,m),d=g.x-(t.x??0);if(l!=0&&(Math.abs(d)<(t.width??0)/2||Math.abs(d)==(t.width??0)/2&&Math.abs(g.y-(t.y??0))>(t.height??0)/2-o)){let y=o*o*(1-d*d/(l*l));y>0&&(y=Math.sqrt(y)),y=o-y,m.y-(t.y??0)>0&&(y=-y),g.y+=y}return g},e}x(ss,"cylinder");async function rs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=a.width+t.padding,l=a.height+t.padding,o=l*.2,n=-s/2,p=-l/2-o/2,{cssStyles:f}=t,m=v.svg(e),g=D(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");let d=[{x:n,y:p+o},{x:-n,y:p+o},{x:-n,y:-p},{x:n,y:-p},{x:n,y:p},{x:-n,y:p},{x:-n,y:p+o}],y=m.polygon(d.map(b=>[b.x,b.y]),g),u=e.insert(()=>y,":first-child");return u.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",f),r&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",r),h.attr("transform",`translate(${n+(t.padding??0)/2-(a.x-(a.left??0))}, ${p+o+(t.padding??0)/2-(a.y-(a.top??0))})`),$(t,u),t.intersect=function(b){return N.rect(t,b)},e}x(rs,"dividedRectangle");async function as(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,halfPadding:h}=await T(c,t,C(t)),l=a.width/2+h+5,o=a.width/2+h,n,{cssStyles:p}=t;if(t.look==="handDrawn"){let f=v.svg(e),m=D(t,{roughness:.2,strokeWidth:2.5}),g=D(t,{roughness:.2,strokeWidth:1.5}),d=f.circle(0,0,l*2,m),y=f.circle(0,0,o*2,g);n=e.insert("g",":first-child"),n.attr("class",U(t.cssClasses)).attr("style",U(p)),n.node()?.appendChild(d),n.node()?.appendChild(y)}else{n=e.insert("g",":first-child");let f=n.insert("circle",":first-child"),m=n.insert("circle");n.attr("class","basic label-container").attr("style",r),f.attr("class","outer-circle").attr("style",r).attr("r",l).attr("cx",0).attr("cy",0),m.attr("class","inner-circle").attr("style",r).attr("r",o).attr("cx",0).attr("cy",0)}return $(t,n),t.intersect=function(f){return z.info("DoubleCircle intersect",t,l,f),N.circle(t,l,f)},e}x(as,"doublecircle");function os(c,t,{config:{themeVariables:i}}){let{labelStyles:r,nodeStyles:e}=k(t);t.label="",t.labelStyle=r;let a=c.insert("g").attr("class",C(t)).attr("id",t.domId??t.id),h=7,{cssStyles:s}=t,l=v.svg(a),{nodeBorder:o}=i,n=D(t,{fillStyle:"solid"});t.look!=="handDrawn"&&(n.roughness=0);let p=l.circle(0,0,h*2,n),f=a.insert(()=>p,":first-child");return f.selectAll("path").attr("style",`fill: ${o} !important;`),s&&s.length>0&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",s),e&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",e),$(t,f),t.intersect=function(m){return z.info("filledCircle intersect",t,{radius:h,point:m}),N.circle(t,h,m)},a}x(os,"filledCircle");async function is(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=a.width+(t.padding??0),l=s+a.height,o=s+a.height,n=[{x:0,y:-l},{x:o,y:-l},{x:o/2,y:0}],{cssStyles:p}=t,f=v.svg(e),m=D(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");let g=H(n),d=f.path(g,m),y=e.insert(()=>d,":first-child").attr("transform",`translate(${-l/2}, ${l/2})`);return p&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",p),r&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",r),t.width=s,t.height=l,$(t,y),h.attr("transform",`translate(${-a.width/2-(a.x-(a.left??0))}, ${-l/2+(t.padding??0)/2+(a.y-(a.top??0))})`),t.intersect=function(u){return z.info("Triangle intersect",t,n,u),N.polygon(t,n,u)},e}x(is,"flippedTriangle");function ns(c,t,{dir:i,config:{state:r,themeVariables:e}}){let{nodeStyles:a}=k(t);t.label="";let h=c.insert("g").attr("class",C(t)).attr("id",t.domId??t.id),{cssStyles:s}=t,l=Math.max(70,t?.width??0),o=Math.max(10,t?.height??0);i==="LR"&&(l=Math.max(10,t?.width??0),o=Math.max(70,t?.height??0));let n=-1*l/2,p=-1*o/2,f=v.svg(h),m=D(t,{stroke:e.lineColor,fill:e.lineColor});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");let g=f.rectangle(n,p,l,o,m),d=h.insert(()=>g,":first-child");s&&t.look!=="handDrawn"&&d.selectAll("path").attr("style",s),a&&t.look!=="handDrawn"&&d.selectAll("path").attr("style",a),$(t,d);let y=r?.padding??0;return t.width&&t.height&&(t.width+=y/2||0,t.height+=y/2||0),t.intersect=function(u){return N.rect(t,u)},h}x(ns,"forkJoin");async function ls(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let e=80,a=50,{shapeSvg:h,bbox:s}=await T(c,t,C(t)),l=Math.max(e,s.width+(t.padding??0)*2,t?.width??0),o=Math.max(a,s.height+(t.padding??0)*2,t?.height??0),n=o/2,{cssStyles:p}=t,f=v.svg(h),m=D(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");let g=[{x:-l/2,y:-o/2},{x:l/2-n,y:-o/2},...Nt(-l/2+n,0,n,50,90,270),{x:l/2-n,y:o/2},{x:-l/2,y:o/2}],d=H(g),y=f.path(d,m),u=h.insert(()=>y,":first-child");return u.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&u.selectChildren("path").attr("style",p),r&&t.look!=="handDrawn"&&u.selectChildren("path").attr("style",r),$(t,u),t.intersect=function(b){return z.info("Pill intersect",t,{radius:n,point:b}),N.polygon(t,g,b)},h}x(ls,"halfRoundedRectangle");async function cs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=a.height+(t.padding??0),s=a.width+(t.padding??0)*2.5,{cssStyles:l}=t,o=v.svg(e),n=D(t,{});t.look!=="handDrawn"&&(n.roughness=0,n.fillStyle="solid");let p=s/2,f=p/6;p=p+f;let m=h/2,g=m/2,d=p-g,y=[{x:-d,y:-m},{x:0,y:-m},{x:d,y:-m},{x:p,y:0},{x:d,y:m},{x:0,y:m},{x:-d,y:m},{x:-p,y:0}],u=H(y),b=o.path(u,n),S=e.insert(()=>b,":first-child");return S.attr("class","basic label-container"),l&&t.look!=="handDrawn"&&S.selectChildren("path").attr("style",l),r&&t.look!=="handDrawn"&&S.selectChildren("path").attr("style",r),t.width=s,t.height=h,$(t,S),t.intersect=function(M){return N.polygon(t,y,M)},e}x(cs,"hexagon");async function hs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.label="",t.labelStyle=i;let{shapeSvg:e}=await T(c,t,C(t)),a=Math.max(30,t?.width??0),h=Math.max(30,t?.height??0),{cssStyles:s}=t,l=v.svg(e),o=D(t,{});t.look!=="handDrawn"&&(o.roughness=0,o.fillStyle="solid");let n=[{x:0,y:0},{x:a,y:0},{x:0,y:h},{x:a,y:h}],p=H(n),f=l.path(p,o),m=e.insert(()=>f,":first-child");return m.attr("class","basic label-container"),s&&t.look!=="handDrawn"&&m.selectChildren("path").attr("style",s),r&&t.look!=="handDrawn"&&m.selectChildren("path").attr("style",r),m.attr("transform",`translate(${-a/2}, ${-h/2})`),$(t,m),t.intersect=function(g){return z.info("Pill intersect",t,{points:n}),N.polygon(t,n,g)},e}x(hs,"hourglass");async function ps(c,t,{config:{themeVariables:i,flowchart:r}}){let{labelStyles:e}=k(t);t.labelStyle=e;let a=t.assetHeight??48,h=t.assetWidth??48,s=Math.max(a,h),l=r?.wrappingWidth;t.width=Math.max(s,l??0);let{shapeSvg:o,bbox:n,label:p}=await T(c,t,"icon-shape default"),f=t.pos==="t",m=s,g=s,{nodeBorder:d}=i,{stylesMap:y}=dt(t),u=-g/2,b=-m/2,S=t.label?8:0,M=v.svg(o),w=D(t,{stroke:"none",fill:"none"});t.look!=="handDrawn"&&(w.roughness=0,w.fillStyle="solid");let P=M.rectangle(u,b,g,m,w),B=Math.max(g,n.width),R=m+n.height+S,E=M.rectangle(-B/2,-R/2,B,R,{...w,fill:"transparent",stroke:"none"}),L=o.insert(()=>P,":first-child"),O=o.insert(()=>E);if(t.icon){let W=o.append("g");W.html(`<g>${await gt(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);let G=W.node().getBBox(),A=G.width,V=G.height,j=G.x,I=G.y;W.attr("transform",`translate(${-A/2-j},${f?n.height/2+S/2-V/2-I:-n.height/2-S/2-V/2-I})`),W.attr("style",`color: ${y.get("stroke")??d};`)}return p.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))},${f?-R/2:R/2-n.height})`),L.attr("transform",`translate(0,${f?n.height/2+S/2:-n.height/2-S/2})`),$(t,O),t.intersect=function(W){if(z.info("iconSquare intersect",t,W),!t.label)return N.rect(t,W);let G=t.x??0,A=t.y??0,V=t.height??0,j=[];return f?j=[{x:G-n.width/2,y:A-V/2},{x:G+n.width/2,y:A-V/2},{x:G+n.width/2,y:A-V/2+n.height+S},{x:G+g/2,y:A-V/2+n.height+S},{x:G+g/2,y:A+V/2},{x:G-g/2,y:A+V/2},{x:G-g/2,y:A-V/2+n.height+S},{x:G-n.width/2,y:A-V/2+n.height+S}]:j=[{x:G-g/2,y:A-V/2},{x:G+g/2,y:A-V/2},{x:G+g/2,y:A-V/2+m},{x:G+n.width/2,y:A-V/2+m},{x:G+n.width/2/2,y:A+V/2},{x:G-n.width/2,y:A+V/2},{x:G-n.width/2,y:A-V/2+m},{x:G-g/2,y:A-V/2+m}],N.polygon(t,j,W)},o}x(ps,"icon");async function ms(c,t,{config:{themeVariables:i,flowchart:r}}){let{labelStyles:e}=k(t);t.labelStyle=e;let a=t.assetHeight??48,h=t.assetWidth??48,s=Math.max(a,h),l=r?.wrappingWidth;t.width=Math.max(s,l??0);let{shapeSvg:o,bbox:n,label:p}=await T(c,t,"icon-shape default"),f=20,m=t.label?8:0,g=t.pos==="t",{nodeBorder:d,mainBkg:y}=i,{stylesMap:u}=dt(t),b=v.svg(o),S=D(t,{});t.look!=="handDrawn"&&(S.roughness=0,S.fillStyle="solid");let M=u.get("fill");S.stroke=M??y;let w=o.append("g");t.icon&&w.html(`<g>${await gt(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);let P=w.node().getBBox(),B=P.width,R=P.height,E=P.x,L=P.y,O=Math.max(B,R)*Math.SQRT2+f*2,W=b.circle(0,0,O,S),G=Math.max(O,n.width),A=O+n.height+m,V=b.rectangle(-G/2,-A/2,G,A,{...S,fill:"transparent",stroke:"none"}),j=o.insert(()=>W,":first-child"),I=o.insert(()=>V);return w.attr("transform",`translate(${-B/2-E},${g?n.height/2+m/2-R/2-L:-n.height/2-m/2-R/2-L})`),w.attr("style",`color: ${u.get("stroke")??d};`),p.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))},${g?-A/2:A/2-n.height})`),j.attr("transform",`translate(0,${g?n.height/2+m/2:-n.height/2-m/2})`),$(t,I),t.intersect=function(F){return z.info("iconSquare intersect",t,F),N.rect(t,F)},o}x(ms,"iconCircle");async function fs(c,t,{config:{themeVariables:i,flowchart:r}}){let{labelStyles:e}=k(t);t.labelStyle=e;let a=t.assetHeight??48,h=t.assetWidth??48,s=Math.max(a,h),l=r?.wrappingWidth;t.width=Math.max(s,l??0);let{shapeSvg:o,bbox:n,halfPadding:p,label:f}=await T(c,t,"icon-shape default"),m=t.pos==="t",g=s+p*2,d=s+p*2,{nodeBorder:y,mainBkg:u}=i,{stylesMap:b}=dt(t),S=-d/2,M=-g/2,w=t.label?8:0,P=v.svg(o),B=D(t,{});t.look!=="handDrawn"&&(B.roughness=0,B.fillStyle="solid");let R=b.get("fill");B.stroke=R??u;let E=P.path(st(S,M,d,g,5),B),L=Math.max(d,n.width),O=g+n.height+w,W=P.rectangle(-L/2,-O/2,L,O,{...B,fill:"transparent",stroke:"none"}),G=o.insert(()=>E,":first-child").attr("class","icon-shape2"),A=o.insert(()=>W);if(t.icon){let V=o.append("g");V.html(`<g>${await gt(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);let j=V.node().getBBox(),I=j.width,F=j.height,Q=j.x,it=j.y;V.attr("transform",`translate(${-I/2-Q},${m?n.height/2+w/2-F/2-it:-n.height/2-w/2-F/2-it})`),V.attr("style",`color: ${b.get("stroke")??y};`)}return f.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))},${m?-O/2:O/2-n.height})`),G.attr("transform",`translate(0,${m?n.height/2+w/2:-n.height/2-w/2})`),$(t,A),t.intersect=function(V){if(z.info("iconSquare intersect",t,V),!t.label)return N.rect(t,V);let j=t.x??0,I=t.y??0,F=t.height??0,Q=[];return m?Q=[{x:j-n.width/2,y:I-F/2},{x:j+n.width/2,y:I-F/2},{x:j+n.width/2,y:I-F/2+n.height+w},{x:j+d/2,y:I-F/2+n.height+w},{x:j+d/2,y:I+F/2},{x:j-d/2,y:I+F/2},{x:j-d/2,y:I-F/2+n.height+w},{x:j-n.width/2,y:I-F/2+n.height+w}]:Q=[{x:j-d/2,y:I-F/2},{x:j+d/2,y:I-F/2},{x:j+d/2,y:I-F/2+g},{x:j+n.width/2,y:I-F/2+g},{x:j+n.width/2/2,y:I+F/2},{x:j-n.width/2,y:I+F/2},{x:j-n.width/2,y:I-F/2+g},{x:j-d/2,y:I-F/2+g}],N.polygon(t,Q,V)},o}x(fs,"iconRounded");async function gs(c,t,{config:{themeVariables:i,flowchart:r}}){let{labelStyles:e}=k(t);t.labelStyle=e;let a=t.assetHeight??48,h=t.assetWidth??48,s=Math.max(a,h),l=r?.wrappingWidth;t.width=Math.max(s,l??0);let{shapeSvg:o,bbox:n,halfPadding:p,label:f}=await T(c,t,"icon-shape default"),m=t.pos==="t",g=s+p*2,d=s+p*2,{nodeBorder:y,mainBkg:u}=i,{stylesMap:b}=dt(t),S=-d/2,M=-g/2,w=t.label?8:0,P=v.svg(o),B=D(t,{});t.look!=="handDrawn"&&(B.roughness=0,B.fillStyle="solid");let R=b.get("fill");B.stroke=R??u;let E=P.path(st(S,M,d,g,.1),B),L=Math.max(d,n.width),O=g+n.height+w,W=P.rectangle(-L/2,-O/2,L,O,{...B,fill:"transparent",stroke:"none"}),G=o.insert(()=>E,":first-child"),A=o.insert(()=>W);if(t.icon){let V=o.append("g");V.html(`<g>${await gt(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);let j=V.node().getBBox(),I=j.width,F=j.height,Q=j.x,it=j.y;V.attr("transform",`translate(${-I/2-Q},${m?n.height/2+w/2-F/2-it:-n.height/2-w/2-F/2-it})`),V.attr("style",`color: ${b.get("stroke")??y};`)}return f.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))},${m?-O/2:O/2-n.height})`),G.attr("transform",`translate(0,${m?n.height/2+w/2:-n.height/2-w/2})`),$(t,A),t.intersect=function(V){if(z.info("iconSquare intersect",t,V),!t.label)return N.rect(t,V);let j=t.x??0,I=t.y??0,F=t.height??0,Q=[];return m?Q=[{x:j-n.width/2,y:I-F/2},{x:j+n.width/2,y:I-F/2},{x:j+n.width/2,y:I-F/2+n.height+w},{x:j+d/2,y:I-F/2+n.height+w},{x:j+d/2,y:I+F/2},{x:j-d/2,y:I+F/2},{x:j-d/2,y:I-F/2+n.height+w},{x:j-n.width/2,y:I-F/2+n.height+w}]:Q=[{x:j-d/2,y:I-F/2},{x:j+d/2,y:I-F/2},{x:j+d/2,y:I-F/2+g},{x:j+n.width/2,y:I-F/2+g},{x:j+n.width/2/2,y:I+F/2},{x:j-n.width/2,y:I+F/2},{x:j-n.width/2,y:I-F/2+g},{x:j-d/2,y:I-F/2+g}],N.polygon(t,Q,V)},o}x(gs,"iconSquare");async function ds(c,t,{config:{flowchart:i}}){let r=new Image;r.src=t?.img??"",await r.decode();let e=Number(r.naturalWidth.toString().replace("px","")),a=Number(r.naturalHeight.toString().replace("px",""));t.imageAspectRatio=e/a;let{labelStyles:h}=k(t);t.labelStyle=h;let s=i?.wrappingWidth;t.defaultWidth=i?.wrappingWidth;let l=Math.max(t.label?s??0:0,t?.assetWidth??e),o=t.constraint==="on"&&t?.assetHeight?t.assetHeight*t.imageAspectRatio:l,n=t.constraint==="on"?o/t.imageAspectRatio:t?.assetHeight??a;t.width=Math.max(o,s??0);let{shapeSvg:p,bbox:f,label:m}=await T(c,t,"image-shape default"),g=t.pos==="t",d=-o/2,y=-n/2,u=t.label?8:0,b=v.svg(p),S=D(t,{});t.look!=="handDrawn"&&(S.roughness=0,S.fillStyle="solid");let M=b.rectangle(d,y,o,n,S),w=Math.max(o,f.width),P=n+f.height+u,B=b.rectangle(-w/2,-P/2,w,P,{...S,fill:"none",stroke:"none"}),R=p.insert(()=>M,":first-child"),E=p.insert(()=>B);if(t.img){let L=p.append("image");L.attr("href",t.img),L.attr("width",o),L.attr("height",n),L.attr("preserveAspectRatio","none"),L.attr("transform",`translate(${-o/2},${g?P/2-n:-P/2})`)}return m.attr("transform",`translate(${-f.width/2-(f.x-(f.left??0))},${g?-n/2-f.height/2-u/2:n/2-f.height/2+u/2})`),R.attr("transform",`translate(0,${g?f.height/2+u/2:-f.height/2-u/2})`),$(t,E),t.intersect=function(L){if(z.info("iconSquare intersect",t,L),!t.label)return N.rect(t,L);let O=t.x??0,W=t.y??0,G=t.height??0,A=[];return g?A=[{x:O-f.width/2,y:W-G/2},{x:O+f.width/2,y:W-G/2},{x:O+f.width/2,y:W-G/2+f.height+u},{x:O+o/2,y:W-G/2+f.height+u},{x:O+o/2,y:W+G/2},{x:O-o/2,y:W+G/2},{x:O-o/2,y:W-G/2+f.height+u},{x:O-f.width/2,y:W-G/2+f.height+u}]:A=[{x:O-o/2,y:W-G/2},{x:O+o/2,y:W-G/2},{x:O+o/2,y:W-G/2+n},{x:O+f.width/2,y:W-G/2+n},{x:O+f.width/2/2,y:W+G/2},{x:O-f.width/2,y:W+G/2},{x:O-f.width/2,y:W-G/2+n},{x:O-o/2,y:W-G/2+n}],N.polygon(t,A,L)},p}x(ds,"imageSquare");async function us(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=Math.max(a.width+(t.padding??0)*2,t?.width??0),s=Math.max(a.height+(t.padding??0)*2,t?.height??0),l=[{x:0,y:0},{x:h,y:0},{x:h+3*s/6,y:-s},{x:-3*s/6,y:-s}],o,{cssStyles:n}=t;if(t.look==="handDrawn"){let p=v.svg(e),f=D(t,{}),m=H(l),g=p.path(m,f);o=e.insert(()=>g,":first-child").attr("transform",`translate(${-h/2}, ${s/2})`),n&&o.attr("style",n)}else o=rt(e,h,s,l);return r&&o.attr("style",r),t.width=h,t.height=s,$(t,o),t.intersect=function(p){return N.polygon(t,l,p)},e}x(us,"inv_trapezoid");async function Dt(c,t,i){let{labelStyles:r,nodeStyles:e}=k(t);t.labelStyle=r;let{shapeSvg:a,bbox:h}=await T(c,t,C(t)),s=Math.max(h.width+i.labelPaddingX*2,t?.width||0),l=Math.max(h.height+i.labelPaddingY*2,t?.height||0),o=-s/2,n=-l/2,p,{rx:f,ry:m}=t,{cssStyles:g}=t;if(i?.rx&&i.ry&&(f=i.rx,m=i.ry),t.look==="handDrawn"){let d=v.svg(a),y=D(t,{}),u=f||m?d.path(st(o,n,s,l,f||0),y):d.rectangle(o,n,s,l,y);p=a.insert(()=>u,":first-child"),p.attr("class","basic label-container").attr("style",U(g))}else p=a.insert("rect",":first-child"),p.attr("class","basic label-container").attr("style",e).attr("rx",U(f)).attr("ry",U(m)).attr("x",o).attr("y",n).attr("width",s).attr("height",l);return $(t,p),t.intersect=function(d){return N.rect(t,d)},a}x(Dt,"drawRect");async function ys(c,t){let{shapeSvg:i,bbox:r,label:e}=await T(c,t,"label"),a=i.insert("rect",":first-child");return a.attr("width",.1).attr("height",.1),i.attr("class","label edgeLabel"),e.attr("transform",`translate(${-(r.width/2)-(r.x-(r.left??0))}, ${-(r.height/2)-(r.y-(r.top??0))})`),$(t,a),t.intersect=function(l){return N.rect(t,l)},i}x(ys,"labelRect");async function xs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=Math.max(a.width+(t.padding??0),t?.width??0),s=Math.max(a.height+(t.padding??0),t?.height??0),l=[{x:0,y:0},{x:h+3*s/6,y:0},{x:h,y:-s},{x:-(3*s)/6,y:-s}],o,{cssStyles:n}=t;if(t.look==="handDrawn"){let p=v.svg(e),f=D(t,{}),m=H(l),g=p.path(m,f);o=e.insert(()=>g,":first-child").attr("transform",`translate(${-h/2}, ${s/2})`),n&&o.attr("style",n)}else o=rt(e,h,s,l);return r&&o.attr("style",r),t.width=h,t.height=s,$(t,o),t.intersect=function(p){return N.polygon(t,l,p)},e}x(xs,"lean_left");async function bs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=Math.max(a.width+(t.padding??0),t?.width??0),s=Math.max(a.height+(t.padding??0),t?.height??0),l=[{x:-3*s/6,y:0},{x:h,y:0},{x:h+3*s/6,y:-s},{x:0,y:-s}],o,{cssStyles:n}=t;if(t.look==="handDrawn"){let p=v.svg(e),f=D(t,{}),m=H(l),g=p.path(m,f);o=e.insert(()=>g,":first-child").attr("transform",`translate(${-h/2}, ${s/2})`),n&&o.attr("style",n)}else o=rt(e,h,s,l);return r&&o.attr("style",r),t.width=h,t.height=s,$(t,o),t.intersect=function(p){return N.polygon(t,l,p)},e}x(bs,"lean_right");function Ss(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.label="",t.labelStyle=i;let e=c.insert("g").attr("class",C(t)).attr("id",t.domId??t.id),{cssStyles:a}=t,h=Math.max(35,t?.width??0),s=Math.max(35,t?.height??0),l=7,o=[{x:h,y:0},{x:0,y:s+l/2},{x:h-2*l,y:s+l/2},{x:0,y:2*s},{x:h,y:s-l/2},{x:2*l,y:s-l/2}],n=v.svg(e),p=D(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");let f=H(o),m=n.path(f,p),g=e.insert(()=>m,":first-child");return a&&t.look!=="handDrawn"&&g.selectAll("path").attr("style",a),r&&t.look!=="handDrawn"&&g.selectAll("path").attr("style",r),g.attr("transform",`translate(-${h/2},${-s})`),$(t,g),t.intersect=function(d){return z.info("lightningBolt intersect",t,d),N.polygon(t,o,d)},e}x(Ss,"lightningBolt");var Mr=x((c,t,i,r,e,a,h)=>[`M${c},${t+a}`,`a${e},${a} 0,0,0 ${i},0`,`a${e},${a} 0,0,0 ${-i},0`,`l0,${r}`,`a${e},${a} 0,0,0 ${i},0`,`l0,${-r}`,`M${c},${t+a+h}`,`a${e},${a} 0,0,0 ${i},0`].join(" "),"createCylinderPathD"),kr=x((c,t,i,r,e,a,h)=>[`M${c},${t+a}`,`M${c+i},${t+a}`,`a${e},${a} 0,0,0 ${-i},0`,`l0,${r}`,`a${e},${a} 0,0,0 ${i},0`,`l0,${-r}`,`M${c},${t+a+h}`,`a${e},${a} 0,0,0 ${i},0`].join(" "),"createOuterCylinderPathD"),$r=x((c,t,i,r,e,a)=>[`M${c-i/2},${-r/2}`,`a${e},${a} 0,0,0 ${i},0`].join(" "),"createInnerCylinderPathD");async function ws(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=Math.max(a.width+(t.padding??0),t.width??0),l=s/2,o=l/(2.5+s/50),n=Math.max(a.height+o+(t.padding??0),t.height??0),p=n*.1,f,{cssStyles:m}=t;if(t.look==="handDrawn"){let g=v.svg(e),d=kr(0,0,s,n,l,o,p),y=$r(0,o,s,n,l,o),u=D(t,{}),b=g.path(d,u),S=g.path(y,u);e.insert(()=>S,":first-child").attr("class","line"),f=e.insert(()=>b,":first-child"),f.attr("class","basic label-container"),m&&f.attr("style",m)}else{let g=Mr(0,0,s,n,l,o,p);f=e.insert("path",":first-child").attr("d",g).attr("class","basic label-container").attr("style",U(m)).attr("style",r)}return f.attr("label-offset-y",o),f.attr("transform",`translate(${-s/2}, ${-(n/2+o)})`),$(t,f),h.attr("transform",`translate(${-(a.width/2)-(a.x-(a.left??0))}, ${-(a.height/2)+o-(a.y-(a.top??0))})`),t.intersect=function(g){let d=N.rect(t,g),y=d.x-(t.x??0);if(l!=0&&(Math.abs(y)<(t.width??0)/2||Math.abs(y)==(t.width??0)/2&&Math.abs(d.y-(t.y??0))>(t.height??0)/2-o)){let u=o*o*(1-y*y/(l*l));u>0&&(u=Math.sqrt(u)),u=o-u,g.y-(t.y??0)>0&&(u=-u),d.y+=u}return d},e}x(ws,"linedCylinder");async function Ns(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=Math.max(a.width+(t.padding??0)*2,t?.width??0),l=Math.max(a.height+(t.padding??0)*2,t?.height??0),o=l/4,n=l+o,{cssStyles:p}=t,f=v.svg(e),m=D(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");let g=[{x:-s/2-s/2*.1,y:-n/2},{x:-s/2-s/2*.1,y:n/2},...lt(-s/2-s/2*.1,n/2,s/2+s/2*.1,n/2,o,.8),{x:s/2+s/2*.1,y:-n/2},{x:-s/2-s/2*.1,y:-n/2},{x:-s/2,y:-n/2},{x:-s/2,y:n/2*1.1},{x:-s/2,y:-n/2}],d=f.polygon(g.map(u=>[u.x,u.y]),m),y=e.insert(()=>d,":first-child");return y.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",p),r&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",r),y.attr("transform",`translate(0,${-o/2})`),h.attr("transform",`translate(${-s/2+(t.padding??0)+s/2*.1/2-(a.x-(a.left??0))},${-l/2+(t.padding??0)-o/2-(a.y-(a.top??0))})`),$(t,y),t.intersect=function(u){return N.polygon(t,g,u)},e}x(Ns,"linedWaveEdgedRect");async function vs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=Math.max(a.width+(t.padding??0)*2,t?.width??0),l=Math.max(a.height+(t.padding??0)*2,t?.height??0),o=5,n=-s/2,p=-l/2,{cssStyles:f}=t,m=v.svg(e),g=D(t,{}),d=[{x:n-o,y:p+o},{x:n-o,y:p+l+o},{x:n+s-o,y:p+l+o},{x:n+s-o,y:p+l},{x:n+s,y:p+l},{x:n+s,y:p+l-o},{x:n+s+o,y:p+l-o},{x:n+s+o,y:p-o},{x:n+o,y:p-o},{x:n+o,y:p},{x:n,y:p},{x:n,y:p+o}],y=[{x:n,y:p+o},{x:n+s-o,y:p+o},{x:n+s-o,y:p+l},{x:n+s,y:p+l},{x:n+s,y:p},{x:n,y:p}];t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");let u=H(d),b=m.path(u,g),S=H(y),M=m.path(S,{...g,fill:"none"}),w=e.insert(()=>M,":first-child");return w.insert(()=>b,":first-child"),w.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&w.selectAll("path").attr("style",f),r&&t.look!=="handDrawn"&&w.selectAll("path").attr("style",r),h.attr("transform",`translate(${-(a.width/2)-o-(a.x-(a.left??0))}, ${-(a.height/2)+o-(a.y-(a.top??0))})`),$(t,w),t.intersect=function(P){return N.polygon(t,d,P)},e}x(vs,"multiRect");async function Ds(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=Math.max(a.width+(t.padding??0)*2,t?.width??0),l=Math.max(a.height+(t.padding??0)*2,t?.height??0),o=l/4,n=l+o,p=-s/2,f=-n/2,m=5,{cssStyles:g}=t,d=lt(p-m,f+n+m,p+s-m,f+n+m,o,.8),y=d?.[d.length-1],u=[{x:p-m,y:f+m},{x:p-m,y:f+n+m},...d,{x:p+s-m,y:y.y-m},{x:p+s,y:y.y-m},{x:p+s,y:y.y-2*m},{x:p+s+m,y:y.y-2*m},{x:p+s+m,y:f-m},{x:p+m,y:f-m},{x:p+m,y:f},{x:p,y:f},{x:p,y:f+m}],b=[{x:p,y:f+m},{x:p+s-m,y:f+m},{x:p+s-m,y:y.y-m},{x:p+s,y:y.y-m},{x:p+s,y:f},{x:p,y:f}],S=v.svg(e),M=D(t,{});t.look!=="handDrawn"&&(M.roughness=0,M.fillStyle="solid");let w=H(u),P=S.path(w,M),B=H(b),R=S.path(B,M),E=e.insert(()=>P,":first-child");return E.insert(()=>R),E.attr("class","basic label-container"),g&&t.look!=="handDrawn"&&E.selectAll("path").attr("style",g),r&&t.look!=="handDrawn"&&E.selectAll("path").attr("style",r),E.attr("transform",`translate(0,${-o/2})`),h.attr("transform",`translate(${-(a.width/2)-m-(a.x-(a.left??0))}, ${-(a.height/2)+m-o/2-(a.y-(a.top??0))})`),$(t,E),t.intersect=function(L){return N.polygon(t,u,L)},e}x(Ds,"multiWaveEdgedRectangle");async function Ms(c,t,{config:{themeVariables:i}}){let{labelStyles:r,nodeStyles:e}=k(t);t.labelStyle=r,t.useHtmlLabels||ft().flowchart?.htmlLabels!==!1||(t.centerLabel=!0);let{shapeSvg:h,bbox:s,label:l}=await T(c,t,C(t)),o=Math.max(s.width+(t.padding??0)*2,t?.width??0),n=Math.max(s.height+(t.padding??0)*2,t?.height??0),p=-o/2,f=-n/2,{cssStyles:m}=t,g=v.svg(h),d=D(t,{fill:i.noteBkgColor,stroke:i.noteBorderColor});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");let y=g.rectangle(p,f,o,n,d),u=h.insert(()=>y,":first-child");return u.attr("class","basic label-container"),m&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",m),e&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",e),l.attr("transform",`translate(${-s.width/2-(s.x-(s.left??0))}, ${-(s.height/2)-(s.y-(s.top??0))})`),$(t,u),t.intersect=function(b){return N.rect(t,b)},h}x(Ms,"note");var Pr=x((c,t,i)=>[`M${c+i/2},${t}`,`L${c+i},${t-i/2}`,`L${c+i/2},${t-i}`,`L${c},${t-i/2}`,"Z"].join(" "),"createDecisionBoxPathD");async function ks(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=a.width+t.padding,s=a.height+t.padding,l=h+s,o=.5,n=[{x:l/2,y:0},{x:l,y:-l/2},{x:l/2,y:-l},{x:0,y:-l/2}],p,{cssStyles:f}=t;if(t.look==="handDrawn"){let m=v.svg(e),g=D(t,{}),d=Pr(0,0,l),y=m.path(d,g);p=e.insert(()=>y,":first-child").attr("transform",`translate(${-l/2+o}, ${l/2})`),f&&p.attr("style",f)}else p=rt(e,l,l,n),p.attr("transform",`translate(${-l/2+o}, ${l/2})`);return r&&p.attr("style",r),$(t,p),t.intersect=function(m){return z.debug(`APA12 Intersect called SPLIT
point:`,m,`
node:
`,t,`
res:`,N.polygon(t,n,m)),N.polygon(t,n,m)},e}x(ks,"question");async function $s(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=Math.max(a.width+(t.padding??0),t?.width??0),l=Math.max(a.height+(t.padding??0),t?.height??0),o=-s/2,n=-l/2,p=n/2,f=[{x:o+p,y:n},{x:o,y:0},{x:o+p,y:-n},{x:-o,y:-n},{x:-o,y:n}],{cssStyles:m}=t,g=v.svg(e),d=D(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");let y=H(f),u=g.path(y,d),b=e.insert(()=>u,":first-child");return b.attr("class","basic label-container"),m&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",m),r&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",r),b.attr("transform",`translate(${-p/2},0)`),h.attr("transform",`translate(${-p/2-a.width/2-(a.x-(a.left??0))}, ${-(a.height/2)-(a.y-(a.top??0))})`),$(t,b),t.intersect=function(S){return N.polygon(t,f,S)},e}x($s,"rect_left_inv_arrow");async function Ps(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let e;t.cssClasses?e="node "+t.cssClasses:e="node default";let a=c.insert("g").attr("class",e).attr("id",t.domId||t.id),h=a.insert("g"),s=a.insert("g").attr("class","label").attr("style",r),l=t.description,o=t.label,n=s.node().appendChild(await Rt(o,t.labelStyle,!0,!0)),p={width:0,height:0};if(Z(q()?.flowchart?.htmlLabels)){let R=n.children[0],E=Y(n);p=R.getBoundingClientRect(),E.attr("width",p.width),E.attr("height",p.height)}z.info("Text 2",l);let f=l||[],m=n.getBBox(),g=s.node().appendChild(await Rt(f.join?f.join("<br/>"):f,t.labelStyle,!0,!0)),d=g.children[0],y=Y(g);p=d.getBoundingClientRect(),y.attr("width",p.width),y.attr("height",p.height);let u=(t.padding||0)/2;Y(g).attr("transform","translate( "+(p.width>m.width?0:(m.width-p.width)/2)+", "+(m.height+u+5)+")"),Y(n).attr("transform","translate( "+(p.width<m.width?0:-(m.width-p.width)/2)+", 0)"),p=s.node().getBBox(),s.attr("transform","translate("+-p.width/2+", "+(-p.height/2-u+3)+")");let b=p.width+(t.padding||0),S=p.height+(t.padding||0),M=-p.width/2-u,w=-p.height/2-u,P,B;if(t.look==="handDrawn"){let R=v.svg(a),E=D(t,{}),L=R.path(st(M,w,b,S,t.rx||0),E),O=R.line(-p.width/2-u,-p.height/2-u+m.height+u,p.width/2+u,-p.height/2-u+m.height+u,E);B=a.insert(()=>(z.debug("Rough node insert CXC",L),O),":first-child"),P=a.insert(()=>(z.debug("Rough node insert CXC",L),L),":first-child")}else P=h.insert("rect",":first-child"),B=h.insert("line"),P.attr("class","outer title-state").attr("style",r).attr("x",-p.width/2-u).attr("y",-p.height/2-u).attr("width",p.width+(t.padding||0)).attr("height",p.height+(t.padding||0)),B.attr("class","divider").attr("x1",-p.width/2-u).attr("x2",p.width/2+u).attr("y1",-p.height/2-u+m.height+u).attr("y2",-p.height/2-u+m.height+u);return $(t,P),t.intersect=function(R){return N.rect(t,R)},a}x(Ps,"rectWithTitle");function Xt(c,t,i,r,e,a,h){let l=(c+i)/2,o=(t+r)/2,n=Math.atan2(r-t,i-c),p=(i-c)/2,f=(r-t)/2,m=p/e,g=f/a,d=Math.sqrt(m**2+g**2);if(d>1)throw new Error("The given radii are too small to create an arc between the points.");let y=Math.sqrt(1-d**2),u=l+y*a*Math.sin(n)*(h?-1:1),b=o-y*e*Math.cos(n)*(h?-1:1),S=Math.atan2((t-b)/a,(c-u)/e),w=Math.atan2((r-b)/a,(i-u)/e)-S;h&&w<0&&(w+=2*Math.PI),!h&&w>0&&(w-=2*Math.PI);let P=[];for(let B=0;B<20;B++){let R=B/19,E=S+R*w,L=u+e*Math.cos(E),O=b+a*Math.sin(E);P.push({x:L,y:O})}return P}x(Xt,"generateArcPoints");async function Cs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=t?.padding??0,s=t?.padding??0,l=(t?.width?t?.width:a.width)+h*2,o=(t?.height?t?.height:a.height)+s*2,n=5,p=5,{cssStyles:f}=t,m=v.svg(e),g=D(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");let d=[{x:-l/2+p,y:-o/2},{x:l/2-p,y:-o/2},...Xt(l/2-p,-o/2,l/2,-o/2+p,n,n,!0),{x:l/2,y:-o/2+p},{x:l/2,y:o/2-p},...Xt(l/2,o/2-p,l/2-p,o/2,n,n,!0),{x:l/2-p,y:o/2},{x:-l/2+p,y:o/2},...Xt(-l/2+p,o/2,-l/2,o/2-p,n,n,!0),{x:-l/2,y:o/2-p},{x:-l/2,y:-o/2+p},...Xt(-l/2,-o/2+p,-l/2+p,-o/2,n,n,!0)],y=H(d),u=m.path(y,g),b=e.insert(()=>u,":first-child");return b.attr("class","basic label-container outer-path"),f&&t.look!=="handDrawn"&&b.selectChildren("path").attr("style",f),r&&t.look!=="handDrawn"&&b.selectChildren("path").attr("style",r),$(t,b),t.intersect=function(S){return N.polygon(t,d,S)},e}x(Cs,"roundedRect");async function Bs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=t?.padding??0,l=Math.max(a.width+(t.padding??0)*2,t?.width??0),o=Math.max(a.height+(t.padding??0)*2,t?.height??0),n=-a.width/2-s,p=-a.height/2-s,{cssStyles:f}=t,m=v.svg(e),g=D(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");let d=[{x:n,y:p},{x:n+l+8,y:p},{x:n+l+8,y:p+o},{x:n-8,y:p+o},{x:n-8,y:p},{x:n,y:p},{x:n,y:p+o}],y=m.polygon(d.map(b=>[b.x,b.y]),g),u=e.insert(()=>y,":first-child");return u.attr("class","basic label-container").attr("style",U(f)),r&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",r),f&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",r),h.attr("transform",`translate(${-l/2+4+(t.padding??0)-(a.x-(a.left??0))},${-o/2+(t.padding??0)-(a.y-(a.top??0))})`),$(t,u),t.intersect=function(b){return N.rect(t,b)},e}x(Bs,"shadedProcess");async function Ts(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=Math.max(a.width+(t.padding??0)*2,t?.width??0),l=Math.max(a.height+(t.padding??0)*2,t?.height??0),o=-s/2,n=-l/2,{cssStyles:p}=t,f=v.svg(e),m=D(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");let g=[{x:o,y:n},{x:o,y:n+l},{x:o+s,y:n+l},{x:o+s,y:n-l/2}],d=H(g),y=f.path(d,m),u=e.insert(()=>y,":first-child");return u.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&u.selectChildren("path").attr("style",p),r&&t.look!=="handDrawn"&&u.selectChildren("path").attr("style",r),u.attr("transform",`translate(0, ${l/4})`),h.attr("transform",`translate(${-s/2+(t.padding??0)-(a.x-(a.left??0))}, ${-l/4+(t.padding??0)-(a.y-(a.top??0))})`),$(t,u),t.intersect=function(b){return N.polygon(t,g,b)},e}x(Ts,"slopedRect");async function Rs(c,t){let i={rx:0,ry:0,classes:"",labelPaddingX:(t?.padding||0)*2,labelPaddingY:(t?.padding||0)*1};return Dt(c,t,i)}x(Rs,"squareRect");async function Gs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=a.height+t.padding,s=a.width+h/4+t.padding,l=h/2,{cssStyles:o}=t,n=v.svg(e),p=D(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");let f=[{x:-s/2+l,y:-h/2},{x:s/2-l,y:-h/2},...Nt(-s/2+l,0,l,50,90,270),{x:s/2-l,y:h/2},...Nt(s/2-l,0,l,50,270,450)],m=H(f),g=n.path(m,p),d=e.insert(()=>g,":first-child");return d.attr("class","basic label-container outer-path"),o&&t.look!=="handDrawn"&&d.selectChildren("path").attr("style",o),r&&t.look!=="handDrawn"&&d.selectChildren("path").attr("style",r),$(t,d),t.intersect=function(y){return N.polygon(t,f,y)},e}x(Gs,"stadium");async function Es(c,t){return Dt(c,t,{rx:5,ry:5,classes:"flowchart-node"})}x(Es,"state");function As(c,t,{config:{themeVariables:i}}){let{labelStyles:r,nodeStyles:e}=k(t);t.labelStyle=r;let{cssStyles:a}=t,{lineColor:h,stateBorder:s,nodeBorder:l}=i,o=c.insert("g").attr("class","node default").attr("id",t.domId||t.id),n=v.svg(o),p=D(t,{});t.look!=="handDrawn"&&(p.roughness=0,p.fillStyle="solid");let f=n.circle(0,0,14,{...p,stroke:h,strokeWidth:2}),m=s??l,g=n.circle(0,0,5,{...p,fill:m,stroke:m,strokeWidth:2,fillStyle:"solid"}),d=o.insert(()=>f,":first-child");return d.insert(()=>g),a&&d.selectAll("path").attr("style",a),e&&d.selectAll("path").attr("style",e),$(t,d),t.intersect=function(y){return N.circle(t,7,y)},o}x(As,"stateEnd");function js(c,t,{config:{themeVariables:i}}){let{lineColor:r}=i,e=c.insert("g").attr("class","node default").attr("id",t.domId||t.id),a;if(t.look==="handDrawn"){let s=v.svg(e).circle(0,0,14,ve(r));a=e.insert(()=>s),a.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14)}else a=e.insert("circle",":first-child"),a.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14);return $(t,a),t.intersect=function(h){return N.circle(t,7,h)},e}x(js,"stateStart");async function Os(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=(t?.padding||0)/2,s=a.width+t.padding,l=a.height+t.padding,o=-a.width/2-h,n=-a.height/2-h,p=[{x:0,y:0},{x:s,y:0},{x:s,y:-l},{x:0,y:-l},{x:0,y:0},{x:-8,y:0},{x:s+8,y:0},{x:s+8,y:-l},{x:-8,y:-l},{x:-8,y:0}];if(t.look==="handDrawn"){let f=v.svg(e),m=D(t,{}),g=f.rectangle(o-8,n,s+16,l,m),d=f.line(o,n,o,n+l,m),y=f.line(o+s,n,o+s,n+l,m);e.insert(()=>d,":first-child"),e.insert(()=>y,":first-child");let u=e.insert(()=>g,":first-child"),{cssStyles:b}=t;u.attr("class","basic label-container").attr("style",U(b)),$(t,u)}else{let f=rt(e,s,l,p);r&&f.attr("style",r),$(t,f)}return t.intersect=function(f){return N.polygon(t,p,f)},e}x(Os,"subroutine");async function Ls(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=Math.max(a.width+(t.padding??0)*2,t?.width??0),s=Math.max(a.height+(t.padding??0)*2,t?.height??0),l=-h/2,o=-s/2,n=.2*s,p=.2*s,{cssStyles:f}=t,m=v.svg(e),g=D(t,{}),d=[{x:l-n/2,y:o},{x:l+h+n/2,y:o},{x:l+h+n/2,y:o+s},{x:l-n/2,y:o+s}],y=[{x:l+h-n/2,y:o+s},{x:l+h+n/2,y:o+s},{x:l+h+n/2,y:o+s-p}];t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");let u=H(d),b=m.path(u,g),S=H(y),M=m.path(S,{...g,fillStyle:"solid"}),w=e.insert(()=>M,":first-child");return w.insert(()=>b,":first-child"),w.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&w.selectAll("path").attr("style",f),r&&t.look!=="handDrawn"&&w.selectAll("path").attr("style",r),$(t,w),t.intersect=function(P){return N.polygon(t,d,P)},e}x(Ls,"taggedRect");async function Hs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=Math.max(a.width+(t.padding??0)*2,t?.width??0),l=Math.max(a.height+(t.padding??0)*2,t?.height??0),o=l/4,n=.2*s,p=.2*l,f=l+o,{cssStyles:m}=t,g=v.svg(e),d=D(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");let y=[{x:-s/2-s/2*.1,y:f/2},...lt(-s/2-s/2*.1,f/2,s/2+s/2*.1,f/2,o,.8),{x:s/2+s/2*.1,y:-f/2},{x:-s/2-s/2*.1,y:-f/2}],u=-s/2+s/2*.1,b=-f/2-p*.4,S=[{x:u+s-n,y:(b+l)*1.4},{x:u+s,y:b+l-p},{x:u+s,y:(b+l)*.9},...lt(u+s,(b+l)*1.3,u+s-n,(b+l)*1.5,-l*.03,.5)],M=H(y),w=g.path(M,d),P=H(S),B=g.path(P,{...d,fillStyle:"solid"}),R=e.insert(()=>B,":first-child");return R.insert(()=>w,":first-child"),R.attr("class","basic label-container"),m&&t.look!=="handDrawn"&&R.selectAll("path").attr("style",m),r&&t.look!=="handDrawn"&&R.selectAll("path").attr("style",r),R.attr("transform",`translate(0,${-o/2})`),h.attr("transform",`translate(${-s/2+(t.padding??0)-(a.x-(a.left??0))},${-l/2+(t.padding??0)-o/2-(a.y-(a.top??0))})`),$(t,R),t.intersect=function(E){return N.polygon(t,y,E)},e}x(Hs,"taggedWaveEdgedRectangle");async function Ws(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=Math.max(a.width+t.padding,t?.width||0),s=Math.max(a.height+t.padding,t?.height||0),l=-h/2,o=-s/2,n=e.insert("rect",":first-child");return n.attr("class","text").attr("style",r).attr("rx",0).attr("ry",0).attr("x",l).attr("y",o).attr("width",h).attr("height",s),$(t,n),t.intersect=function(p){return N.rect(t,p)},e}x(Ws,"text");var Cr=x((c,t,i,r,e,a)=>`M${c},${t}
    a${e},${a} 0,0,1 0,${-r}
    l${i},0
    a${e},${a} 0,0,1 0,${r}
    M${i},${-r}
    a${e},${a} 0,0,0 0,${r}
    l${-i},0`,"createCylinderPathD"),Br=x((c,t,i,r,e,a)=>[`M${c},${t}`,`M${c+i},${t}`,`a${e},${a} 0,0,0 0,${-r}`,`l${-i},0`,`a${e},${a} 0,0,0 0,${r}`,`l${i},0`].join(" "),"createOuterCylinderPathD"),Tr=x((c,t,i,r,e,a)=>[`M${c+i/2},${-r/2}`,`a${e},${a} 0,0,0 0,${r}`].join(" "),"createInnerCylinderPathD");async function Vs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h,halfPadding:s}=await T(c,t,C(t)),l=t.look==="neo"?s*2:s,o=a.height+l,n=o/2,p=n/(2.5+o/50),f=a.width+p+l,{cssStyles:m}=t,g;if(t.look==="handDrawn"){let d=v.svg(e),y=Br(0,0,f,o,p,n),u=Tr(0,0,f,o,p,n),b=d.path(y,D(t,{})),S=d.path(u,D(t,{fill:"none"}));g=e.insert(()=>S,":first-child"),g=e.insert(()=>b,":first-child"),g.attr("class","basic label-container"),m&&g.attr("style",m)}else{let d=Cr(0,0,f,o,p,n);g=e.insert("path",":first-child").attr("d",d).attr("class","basic label-container").attr("style",U(m)).attr("style",r),g.attr("class","basic label-container"),m&&g.selectAll("path").attr("style",m),r&&g.selectAll("path").attr("style",r)}return g.attr("label-offset-x",p),g.attr("transform",`translate(${-f/2}, ${o/2} )`),h.attr("transform",`translate(${-(a.width/2)-p-(a.x-(a.left??0))}, ${-(a.height/2)-(a.y-(a.top??0))})`),$(t,g),t.intersect=function(d){let y=N.rect(t,d),u=y.y-(t.y??0);if(n!=0&&(Math.abs(u)<(t.height??0)/2||Math.abs(u)==(t.height??0)/2&&Math.abs(y.x-(t.x??0))>(t.width??0)/2-p)){let b=p*p*(1-u*u/(n*n));b!=0&&(b=Math.sqrt(Math.abs(b))),b=p-b,d.x-(t.x??0)>0&&(b=-b),y.x+=b}return y},e}x(Vs,"tiltedCylinder");async function Is(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=a.width+t.padding,s=a.height+t.padding,l=[{x:-3*s/6,y:0},{x:h+3*s/6,y:0},{x:h,y:-s},{x:0,y:-s}],o,{cssStyles:n}=t;if(t.look==="handDrawn"){let p=v.svg(e),f=D(t,{}),m=H(l),g=p.path(m,f);o=e.insert(()=>g,":first-child").attr("transform",`translate(${-h/2}, ${s/2})`),n&&o.attr("style",n)}else o=rt(e,h,s,l);return r&&o.attr("style",r),t.width=h,t.height=s,$(t,o),t.intersect=function(p){return N.polygon(t,l,p)},e}x(Is,"trapezoid");async function Fs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=60,s=20,l=Math.max(h,a.width+(t.padding??0)*2,t?.width??0),o=Math.max(s,a.height+(t.padding??0)*2,t?.height??0),{cssStyles:n}=t,p=v.svg(e),f=D(t,{});t.look!=="handDrawn"&&(f.roughness=0,f.fillStyle="solid");let m=[{x:-l/2*.8,y:-o/2},{x:l/2*.8,y:-o/2},{x:l/2,y:-o/2*.6},{x:l/2,y:o/2},{x:-l/2,y:o/2},{x:-l/2,y:-o/2*.6}],g=H(m),d=p.path(g,f),y=e.insert(()=>d,":first-child");return y.attr("class","basic label-container"),n&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",n),r&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",r),$(t,y),t.intersect=function(u){return N.polygon(t,m,u)},e}x(Fs,"trapezoidalPentagon");async function _s(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=Z(q().flowchart?.htmlLabels),l=a.width+(t.padding??0),o=l+a.height,n=l+a.height,p=[{x:0,y:0},{x:n,y:0},{x:n/2,y:-o}],{cssStyles:f}=t,m=v.svg(e),g=D(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");let d=H(p),y=m.path(d,g),u=e.insert(()=>y,":first-child").attr("transform",`translate(${-o/2}, ${o/2})`);return f&&t.look!=="handDrawn"&&u.selectChildren("path").attr("style",f),r&&t.look!=="handDrawn"&&u.selectChildren("path").attr("style",r),t.width=l,t.height=o,$(t,u),h.attr("transform",`translate(${-a.width/2-(a.x-(a.left??0))}, ${o/2-(a.height+(t.padding??0)/(s?2:1)-(a.y-(a.top??0)))})`),t.intersect=function(b){return z.info("Triangle intersect",t,p,b),N.polygon(t,p,b)},e}x(_s,"triangle");async function zs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=Math.max(a.width+(t.padding??0)*2,t?.width??0),l=Math.max(a.height+(t.padding??0)*2,t?.height??0),o=l/8,n=l+o,{cssStyles:p}=t,m=70-s,g=m>0?m/2:0,d=v.svg(e),y=D(t,{});t.look!=="handDrawn"&&(y.roughness=0,y.fillStyle="solid");let u=[{x:-s/2-g,y:n/2},...lt(-s/2-g,n/2,s/2+g,n/2,o,.8),{x:s/2+g,y:-n/2},{x:-s/2-g,y:-n/2}],b=H(u),S=d.path(b,y),M=e.insert(()=>S,":first-child");return M.attr("class","basic label-container"),p&&t.look!=="handDrawn"&&M.selectAll("path").attr("style",p),r&&t.look!=="handDrawn"&&M.selectAll("path").attr("style",r),M.attr("transform",`translate(0,${-o/2})`),h.attr("transform",`translate(${-s/2+(t.padding??0)-(a.x-(a.left??0))},${-l/2+(t.padding??0)-o-(a.y-(a.top??0))})`),$(t,M),t.intersect=function(w){return N.polygon(t,u,w)},e}x(zs,"waveEdgedRectangle");async function qs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a}=await T(c,t,C(t)),h=100,s=50,l=Math.max(a.width+(t.padding??0)*2,t?.width??0),o=Math.max(a.height+(t.padding??0)*2,t?.height??0),n=l/o,p=l,f=o;p>f*n?f=p/n:p=f*n,p=Math.max(p,h),f=Math.max(f,s);let m=Math.min(f*.2,f/4),g=f+m*2,{cssStyles:d}=t,y=v.svg(e),u=D(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");let b=[{x:-p/2,y:g/2},...lt(-p/2,g/2,p/2,g/2,m,1),{x:p/2,y:-g/2},...lt(p/2,-g/2,-p/2,-g/2,m,-1)],S=H(b),M=y.path(S,u),w=e.insert(()=>M,":first-child");return w.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&w.selectAll("path").attr("style",d),r&&t.look!=="handDrawn"&&w.selectAll("path").attr("style",r),$(t,w),t.intersect=function(P){return N.polygon(t,b,P)},e}x(qs,"waveRectangle");async function Xs(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let{shapeSvg:e,bbox:a,label:h}=await T(c,t,C(t)),s=Math.max(a.width+(t.padding??0)*2,t?.width??0),l=Math.max(a.height+(t.padding??0)*2,t?.height??0),o=5,n=-s/2,p=-l/2,{cssStyles:f}=t,m=v.svg(e),g=D(t,{}),d=[{x:n-o,y:p-o},{x:n-o,y:p+l},{x:n+s,y:p+l},{x:n+s,y:p-o}],y=`M${n-o},${p-o} L${n+s},${p-o} L${n+s},${p+l} L${n-o},${p+l} L${n-o},${p-o}
                M${n-o},${p} L${n+s},${p}
                M${n},${p-o} L${n},${p+l}`;t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");let u=m.path(y,g),b=e.insert(()=>u,":first-child");return b.attr("transform",`translate(${o/2}, ${o/2})`),b.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",f),r&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",r),h.attr("transform",`translate(${-(a.width/2)+o/2-(a.x-(a.left??0))}, ${-(a.height/2)+o/2-(a.y-(a.top??0))})`),$(t,b),t.intersect=function(S){return N.polygon(t,d,S)},e}x(Xs,"windowPane");async function ue(c,t){let i=t;if(i.alias&&(t.label=i.alias),t.look==="handDrawn"){let{themeVariables:X}=ft(),{background:J}=X,tt={...t,id:t.id+"-background",look:"default",cssStyles:["stroke: none",`fill: ${J}`]};await ue(c,tt)}let r=ft();t.useHtmlLabels=r.htmlLabels;let e=r.er?.diagramPadding??10,a=r.er?.entityPadding??6,{cssStyles:h}=t,{labelStyles:s,nodeStyles:l}=k(t);if(i.attributes.length===0&&t.label){let X={rx:0,ry:0,labelPaddingX:e,labelPaddingY:e*1.5,classes:""};wt(t.label,r)+X.labelPaddingX*2<r.er.minEntityWidth&&(t.width=r.er.minEntityWidth);let J=await Dt(c,t,X);if(!Z(r.htmlLabels)){let tt=J.select("text"),at=tt.node()?.getBBox();tt.attr("transform",`translate(${-at.width/2}, 0)`)}return J}r.htmlLabels||(e*=1.25,a*=1.25);let o=C(t);o||(o="node default");let n=c.insert("g").attr("class",o).attr("id",t.domId||t.id),p=await Gt(n,t.label??"",r,0,0,["name"],s);p.height+=a;let f=0,m=[],g=[],d=0,y=0,u=0,b=0,S=!0,M=!0;for(let X of i.attributes){let J=await Gt(n,X.type,r,0,f,["attribute-type"],s);d=Math.max(d,J.width+e);let tt=await Gt(n,X.name,r,0,f,["attribute-name"],s);y=Math.max(y,tt.width+e);let at=await Gt(n,X.keys.join(),r,0,f,["attribute-keys"],s);u=Math.max(u,at.width+e);let St=await Gt(n,X.comment,r,0,f,["attribute-comment"],s);b=Math.max(b,St.width+e);let mt=Math.max(J.height,tt.height,at.height,St.height)+a;g.push({yOffset:f,rowHeight:mt}),f+=mt}let w=4;u<=e&&(S=!1,u=0,w--),b<=e&&(M=!1,b=0,w--);let P=n.node().getBBox();if(p.width+e*2-(d+y+u+b)>0){let X=p.width+e*2-(d+y+u+b);d+=X/w,y+=X/w,u>0&&(u+=X/w),b>0&&(b+=X/w)}let B=d+y+u+b,R=v.svg(n),E=D(t,{});t.look!=="handDrawn"&&(E.roughness=0,E.fillStyle="solid");let L=0;g.length>0&&(L=g.reduce((X,J)=>X+(J?.rowHeight??0),0));let O=Math.max(P.width+e*2,t?.width||0,B),W=Math.max((L??0)+p.height,t?.height||0),G=-O/2,A=-W/2;n.selectAll("g:not(:first-child)").each((X,J,tt)=>{let at=Y(tt[J]),St=at.attr("transform"),mt=0,xe=0;if(St){let Ut=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(St);Ut&&(mt=parseFloat(Ut[1]),xe=parseFloat(Ut[2]),at.attr("class").includes("attribute-name")?mt+=d:at.attr("class").includes("attribute-keys")?mt+=d+y:at.attr("class").includes("attribute-comment")&&(mt+=d+y+u))}at.attr("transform",`translate(${G+e/2+mt}, ${xe+A+p.height+a/2})`)}),n.select(".name").attr("transform","translate("+-p.width/2+", "+(A+a/2)+")");let V=R.rectangle(G,A,O,W,E),j=n.insert(()=>V,":first-child").attr("style",h.join("")),{themeVariables:I}=ft(),{rowEven:F,rowOdd:Q,nodeBorder:it}=I;m.push(0);for(let[X,J]of g.entries()){let at=(X+1)%2===0&&J.yOffset!==0,St=R.rectangle(G,p.height+A+J?.yOffset,O,J?.rowHeight,{...E,fill:at?F:Q,stroke:it});n.insert(()=>St,"g.label").attr("style",h.join("")).attr("class",`row-rect-${at?"even":"odd"}`)}let ct=R.line(G,p.height+A,O+G,p.height+A,E);n.insert(()=>ct).attr("class","divider"),ct=R.line(d+G,p.height+A,d+G,W+A,E),n.insert(()=>ct).attr("class","divider"),S&&(ct=R.line(d+y+G,p.height+A,d+y+G,W+A,E),n.insert(()=>ct).attr("class","divider")),M&&(ct=R.line(d+y+u+G,p.height+A,d+y+u+G,W+A,E),n.insert(()=>ct).attr("class","divider"));for(let X of m)ct=R.line(G,p.height+A+X,O+G,p.height+A+X,E),n.insert(()=>ct).attr("class","divider");if($(t,j),l&&t.look!=="handDrawn"){let J=l.split(";")?.filter(tt=>tt.includes("stroke"))?.map(tt=>`${tt}`).join("; ");n.selectAll("path").attr("style",J??""),n.selectAll(".row-rect-even path").attr("style",l)}return t.intersect=function(X){return N.rect(t,X)},n}x(ue,"erBox");async function Gt(c,t,i,r=0,e=0,a=[],h=""){let s=c.insert("g").attr("class",`label ${a.join(" ")}`).attr("transform",`translate(${r}, ${e})`).attr("style",h);t!==Qt(t)&&(t=Qt(t),t=t.replaceAll("<","&lt;").replaceAll(">","&gt;"));let l=s.node().appendChild(await nt(s,t,{width:wt(t,i)+100,style:h,useHtmlLabels:i.htmlLabels},i));if(t.includes("&lt;")||t.includes("&gt;")){let n=l.children[0];for(n.textContent=n.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">");n.childNodes[0];)n=n.childNodes[0],n.textContent=n.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">")}let o=l.getBBox();if(Z(i.htmlLabels)){let n=l.children[0];n.style.textAlign="start";let p=Y(l);o=n.getBoundingClientRect(),p.attr("width",o.width),p.attr("height",o.height)}return o}x(Gt,"addText");async function Ys(c,t,i,r,e=i.class.padding??12){let a=r?0:3,h=c.insert("g").attr("class",C(t)).attr("id",t.domId||t.id),s=null,l=null,o=null,n=null,p=0,f=0,m=0;if(s=h.insert("g").attr("class","annotation-group text"),t.annotations.length>0){let b=t.annotations[0];await Yt(s,{text:`\xAB${b}\xBB`},0),p=s.node().getBBox().height}l=h.insert("g").attr("class","label-group text"),await Yt(l,t,0,["font-weight: bolder"]);let g=l.node().getBBox();f=g.height,o=h.insert("g").attr("class","members-group text");let d=0;for(let b of t.members){let S=await Yt(o,b,d,[b.parseClassifier()]);d+=S+a}m=o.node().getBBox().height,m<=0&&(m=e/2),n=h.insert("g").attr("class","methods-group text");let y=0;for(let b of t.methods){let S=await Yt(n,b,y,[b.parseClassifier()]);y+=S+a}let u=h.node().getBBox();if(s!==null){let b=s.node().getBBox();s.attr("transform",`translate(${-b.width/2})`)}return l.attr("transform",`translate(${-g.width/2}, ${p})`),u=h.node().getBBox(),o.attr("transform",`translate(0, ${p+f+e*2})`),u=h.node().getBBox(),n.attr("transform",`translate(0, ${p+f+(m?m+e*4:e*2)})`),u=h.node().getBBox(),{shapeSvg:h,bbox:u}}x(Ys,"textHelper");async function Yt(c,t,i,r=[]){let e=c.insert("g").attr("class","label").attr("style",r.join("; ")),a=ft(),h="useHtmlLabels"in t?t.useHtmlLabels:Z(a.htmlLabels)??!0,s="";"text"in t?s=t.text:s=t.label,!h&&s.startsWith("\\")&&(s=s.substring(1)),Et(s)&&(h=!0);let l=await nt(e,At(ht(s)),{width:wt(s,a)+50,classes:"markdown-node-label",useHtmlLabels:h},a),o,n=1;if(h){let p=l.children[0],f=Y(l);n=p.innerHTML.split("<br>").length,p.innerHTML.includes("</math>")&&(n+=p.innerHTML.split("<mrow>").length-1);let m=p.getElementsByTagName("img");if(m){let g=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...m].map(d=>new Promise(y=>{function u(){if(d.style.display="flex",d.style.flexDirection="column",g){let b=a.fontSize?.toString()??window.getComputedStyle(document.body).fontSize,M=parseInt(b,10)*5+"px";d.style.minWidth=M,d.style.maxWidth=M}else d.style.width="100%";y(d)}x(u,"setupImage"),setTimeout(()=>{d.complete&&u()}),d.addEventListener("error",u),d.addEventListener("load",u)})))}o=p.getBoundingClientRect(),f.attr("width",o.width),f.attr("height",o.height)}else{r.includes("font-weight: bolder")&&Y(l).selectAll("tspan").attr("font-weight",""),n=l.children.length;let p=l.children[0];(l.textContent===""||l.textContent.includes("&gt"))&&(p.textContent=s[0]+s.substring(1).replaceAll("&gt;",">").replaceAll("&lt;","<").trim(),s[1]===" "&&(p.textContent=p.textContent[0]+" "+p.textContent.substring(1))),p.textContent==="undefined"&&(p.textContent=""),o=l.getBBox()}return e.attr("transform","translate(0,"+(-o.height/(2*n)+i)+")"),o.height}x(Yt,"addText");async function Zs(c,t){let i=q(),r=i.class.padding??12,e=r,a=t.useHtmlLabels??Z(i.htmlLabels)??!0,h=t;h.annotations=h.annotations??[],h.members=h.members??[],h.methods=h.methods??[];let{shapeSvg:s,bbox:l}=await Ys(c,t,i,a,e),{labelStyles:o,nodeStyles:n}=k(t);t.labelStyle=o,t.cssStyles=h.styles||"";let p=h.styles?.join(";")||n||"";t.cssStyles||(t.cssStyles=p.replaceAll("!important","").split(";"));let f=h.members.length===0&&h.methods.length===0&&!i.class?.hideEmptyMembersBox,m=v.svg(s),g=D(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");let d=l.width,y=l.height;h.members.length===0&&h.methods.length===0?y+=e:h.members.length>0&&h.methods.length===0&&(y+=e*2);let u=-d/2,b=-y/2,S=m.rectangle(u-r,b-r-(f?r:h.members.length===0&&h.methods.length===0?-r/2:0),d+2*r,y+2*r+(f?r*2:h.members.length===0&&h.methods.length===0?-r:0),g),M=s.insert(()=>S,":first-child");M.attr("class","basic label-container");let w=M.node().getBBox();s.selectAll(".text").each((E,L,O)=>{let W=Y(O[L]),G=W.attr("transform"),A=0;if(G){let F=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(G);F&&(A=parseFloat(F[2]))}let V=A+b+r-(f?r:h.members.length===0&&h.methods.length===0?-r/2:0);a||(V-=4);let j=u;(W.attr("class").includes("label-group")||W.attr("class").includes("annotation-group"))&&(j=-W.node()?.getBBox().width/2||0,s.selectAll("text").each(function(I,F,Q){window.getComputedStyle(Q[F]).textAnchor==="middle"&&(j=0)})),W.attr("transform",`translate(${j}, ${V})`)});let P=s.select(".annotation-group").node().getBBox().height-(f?r/2:0)||0,B=s.select(".label-group").node().getBBox().height-(f?r/2:0)||0,R=s.select(".members-group").node().getBBox().height-(f?r/2:0)||0;if(h.members.length>0||h.methods.length>0||f){let E=m.line(w.x,P+B+b+r,w.x+w.width,P+B+b+r,g);s.insert(()=>E).attr("class","divider").attr("style",p)}if(f||h.members.length>0||h.methods.length>0){let E=m.line(w.x,P+B+R+b+e*2+r,w.x+w.width,P+B+R+b+r+e*2,g);s.insert(()=>E).attr("class","divider").attr("style",p)}if(h.look!=="handDrawn"&&s.selectAll("path").attr("style",p),M.select(":nth-child(2)").attr("style",p),s.selectAll(".divider").select("path").attr("style",p),t.labelStyle?s.selectAll("span").attr("style",t.labelStyle):s.selectAll("span").attr("style",p),!a){let E=RegExp(/color\s*:\s*([^;]*)/),L=E.exec(p);if(L){let O=L[0].replace("color","fill");s.selectAll("tspan").attr("style",O)}else if(o){let O=E.exec(o);if(O){let W=O[0].replace("color","fill");s.selectAll("tspan").attr("style",W)}}}return $(t,M),t.intersect=function(E){return N.rect(t,E)},s}x(Zs,"classBox");async function Us(c,t){let{labelStyles:i,nodeStyles:r}=k(t);t.labelStyle=i;let e=t,a=t,h=20,s=20,l="verifyMethod"in t,o=C(t),n=c.insert("g").attr("class",o).attr("id",t.domId??t.id),p;l?p=await pt(n,`&lt;&lt;${e.type}&gt;&gt;`,0,t.labelStyle):p=await pt(n,"&lt;&lt;Element&gt;&gt;",0,t.labelStyle);let f=p,m=await pt(n,e.name,f,t.labelStyle+"; font-weight: bold;");if(f+=m+s,l){let P=await pt(n,`${e.requirementId?`ID: ${e.requirementId}`:""}`,f,t.labelStyle);f+=P;let B=await pt(n,`${e.text?`Text: ${e.text}`:""}`,f,t.labelStyle);f+=B;let R=await pt(n,`${e.risk?`Risk: ${e.risk}`:""}`,f,t.labelStyle);f+=R,await pt(n,`${e.verifyMethod?`Verification: ${e.verifyMethod}`:""}`,f,t.labelStyle)}else{let P=await pt(n,`${a.type?`Type: ${a.type}`:""}`,f,t.labelStyle);f+=P,await pt(n,`${a.docRef?`Doc Ref: ${a.docRef}`:""}`,f,t.labelStyle)}let g=(n.node()?.getBBox().width??200)+h,d=(n.node()?.getBBox().height??200)+h,y=-g/2,u=-d/2,b=v.svg(n),S=D(t,{});t.look!=="handDrawn"&&(S.roughness=0,S.fillStyle="solid");let M=b.rectangle(y,u,g,d,S),w=n.insert(()=>M,":first-child");if(w.attr("class","basic label-container").attr("style",r),n.selectAll(".label").each((P,B,R)=>{let E=Y(R[B]),L=E.attr("transform"),O=0,W=0;if(L){let j=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(L);j&&(O=parseFloat(j[1]),W=parseFloat(j[2]))}let G=W-d/2,A=y+h/2;(B===0||B===1)&&(A=O),E.attr("transform",`translate(${A}, ${G+h})`)}),f>p+m+s){let P=b.line(y,u+p+m+s,y+g,u+p+m+s,S);n.insert(()=>P).attr("style",r)}return $(t,w),t.intersect=function(P){return N.rect(t,P)},n}x(Us,"requirementBox");async function pt(c,t,i,r=""){if(t==="")return 0;let e=c.insert("g").attr("class","label").attr("style",r),a=q(),h=a.htmlLabels??!0,s=await nt(e,At(ht(t)),{width:wt(t,a)+50,classes:"markdown-node-label",useHtmlLabels:h,style:r},a),l;if(h){let o=s.children[0],n=Y(s);l=o.getBoundingClientRect(),n.attr("width",l.width),n.attr("height",l.height)}else{let o=s.children[0];for(let n of o.children)n.textContent=n.textContent.replaceAll("&gt;",">").replaceAll("&lt;","<"),r&&n.setAttribute("style",r);l=s.getBBox(),l.height+=6}return e.attr("transform",`translate(${-l.width/2},${-l.height/2+i})`),l.height}x(pt,"addText");var Rr=x(c=>{switch(c){case"Very High":return"red";case"High":return"orange";case"Medium":return null;case"Low":return"blue";case"Very Low":return"lightblue"}},"colorFromPriority");async function Qs(c,t,{config:i}){let{labelStyles:r,nodeStyles:e}=k(t);t.labelStyle=r||"";let a=10,h=t.width;t.width=(t.width??200)-10;let{shapeSvg:s,bbox:l,label:o}=await T(c,t,C(t)),n=t.padding||10,p="",f;"ticket"in t&&t.ticket&&i?.kanban?.ticketBaseUrl&&(p=i?.kanban?.ticketBaseUrl.replace("#TICKET#",t.ticket),f=s.insert("svg:a",":first-child").attr("class","kanban-ticket-link").attr("xlink:href",p).attr("target","_blank"));let m={useHtmlLabels:t.useHtmlLabels,labelStyle:t.labelStyle||"",width:t.width,img:t.img,padding:t.padding||8,centerLabel:!1},g,d;f?{label:g,bbox:d}=await jt(f,"ticket"in t&&t.ticket||"",m):{label:g,bbox:d}=await jt(s,"ticket"in t&&t.ticket||"",m);let{label:y,bbox:u}=await jt(s,"assigned"in t&&t.assigned||"",m);t.width=h;let b=10,S=t?.width||0,M=Math.max(d.height,u.height)/2,w=Math.max(l.height+b*2,t?.height||0)+M,P=-S/2,B=-w/2;o.attr("transform","translate("+(n-S/2)+", "+(-M-l.height/2)+")"),g.attr("transform","translate("+(n-S/2)+", "+(-M+l.height/2)+")"),y.attr("transform","translate("+(n+S/2-u.width-2*a)+", "+(-M+l.height/2)+")");let R,{rx:E,ry:L}=t,{cssStyles:O}=t;if(t.look==="handDrawn"){let W=v.svg(s),G=D(t,{}),A=E||L?W.path(st(P,B,S,w,E||0),G):W.rectangle(P,B,S,w,G);R=s.insert(()=>A,":first-child"),R.attr("class","basic label-container").attr("style",O||null)}else{R=s.insert("rect",":first-child"),R.attr("class","basic label-container __APA__").attr("style",e).attr("rx",E??5).attr("ry",L??5).attr("x",P).attr("y",B).attr("width",S).attr("height",w);let W="priority"in t&&t.priority;if(W){let G=s.append("line"),A=P+2,V=B+Math.floor((E??0)/2),j=B+w-Math.floor((E??0)/2);G.attr("x1",A).attr("y1",V).attr("x2",A).attr("y2",j).attr("stroke-width","4").attr("stroke",Rr(W))}}return $(t,R),t.height=w,t.intersect=function(W){return N.rect(t,W)},s}x(Qs,"kanbanItem");var Gr=[{semanticName:"Process",name:"Rectangle",shortName:"rect",description:"Standard process shape",aliases:["proc","process","rectangle"],internalAliases:["squareRect"],handler:Rs},{semanticName:"Event",name:"Rounded Rectangle",shortName:"rounded",description:"Represents an event",aliases:["event"],internalAliases:["roundedRect"],handler:Cs},{semanticName:"Terminal Point",name:"Stadium",shortName:"stadium",description:"Terminal point",aliases:["terminal","pill"],handler:Gs},{semanticName:"Subprocess",name:"Framed Rectangle",shortName:"fr-rect",description:"Subprocess",aliases:["subprocess","subproc","framed-rectangle","subroutine"],handler:Os},{semanticName:"Database",name:"Cylinder",shortName:"cyl",description:"Database storage",aliases:["db","database","cylinder"],handler:ss},{semanticName:"Start",name:"Circle",shortName:"circle",description:"Starting point",aliases:["circ"],handler:Ue},{semanticName:"Decision",name:"Diamond",shortName:"diam",description:"Decision-making step",aliases:["decision","diamond","question"],handler:ks},{semanticName:"Prepare Conditional",name:"Hexagon",shortName:"hex",description:"Preparation or condition step",aliases:["hexagon","prepare"],handler:cs},{semanticName:"Data Input/Output",name:"Lean Right",shortName:"lean-r",description:"Represents input or output",aliases:["lean-right","in-out"],internalAliases:["lean_right"],handler:bs},{semanticName:"Data Input/Output",name:"Lean Left",shortName:"lean-l",description:"Represents output or input",aliases:["lean-left","out-in"],internalAliases:["lean_left"],handler:xs},{semanticName:"Priority Action",name:"Trapezoid Base Bottom",shortName:"trap-b",description:"Priority action",aliases:["priority","trapezoid-bottom","trapezoid"],handler:Is},{semanticName:"Manual Operation",name:"Trapezoid Base Top",shortName:"trap-t",description:"Represents a manual task",aliases:["manual","trapezoid-top","inv-trapezoid"],internalAliases:["inv_trapezoid"],handler:us},{semanticName:"Stop",name:"Double Circle",shortName:"dbl-circ",description:"Represents a stop point",aliases:["double-circle"],internalAliases:["doublecircle"],handler:as},{semanticName:"Text Block",name:"Text Block",shortName:"text",description:"Text block",handler:Ws},{semanticName:"Card",name:"Notched Rectangle",shortName:"notch-rect",description:"Represents a card",aliases:["card","notched-rectangle"],handler:Ye},{semanticName:"Lined/Shaded Process",name:"Lined Rectangle",shortName:"lin-rect",description:"Lined process shape",aliases:["lined-rectangle","lined-process","lin-proc","shaded-process"],handler:Bs},{semanticName:"Start",name:"Small Circle",shortName:"sm-circ",description:"Small starting point",aliases:["start","small-circle"],internalAliases:["stateStart"],handler:js},{semanticName:"Stop",name:"Framed Circle",shortName:"fr-circ",description:"Stop point",aliases:["stop","framed-circle"],internalAliases:["stateEnd"],handler:As},{semanticName:"Fork/Join",name:"Filled Rectangle",shortName:"fork",description:"Fork or join in process flow",aliases:["join"],internalAliases:["forkJoin"],handler:ns},{semanticName:"Collate",name:"Hourglass",shortName:"hourglass",description:"Represents a collate operation",aliases:["hourglass","collate"],handler:hs},{semanticName:"Comment",name:"Curly Brace",shortName:"brace",description:"Adds a comment",aliases:["comment","brace-l"],handler:Je},{semanticName:"Comment Right",name:"Curly Brace",shortName:"brace-r",description:"Adds a comment",handler:Ke},{semanticName:"Comment with braces on both sides",name:"Curly Braces",shortName:"braces",description:"Adds a comment",handler:ts},{semanticName:"Com Link",name:"Lightning Bolt",shortName:"bolt",description:"Communication link",aliases:["com-link","lightning-bolt"],handler:Ss},{semanticName:"Document",name:"Document",shortName:"doc",description:"Represents a document",aliases:["doc","document"],handler:zs},{semanticName:"Delay",name:"Half-Rounded Rectangle",shortName:"delay",description:"Represents a delay",aliases:["half-rounded-rectangle"],handler:ls},{semanticName:"Direct Access Storage",name:"Horizontal Cylinder",shortName:"h-cyl",description:"Direct access storage",aliases:["das","horizontal-cylinder"],handler:Vs},{semanticName:"Disk Storage",name:"Lined Cylinder",shortName:"lin-cyl",description:"Disk storage",aliases:["disk","lined-cylinder"],handler:ws},{semanticName:"Display",name:"Curved Trapezoid",shortName:"curv-trap",description:"Represents a display",aliases:["curved-trapezoid","display"],handler:es},{semanticName:"Divided Process",name:"Divided Rectangle",shortName:"div-rect",description:"Divided process shape",aliases:["div-proc","divided-rectangle","divided-process"],handler:rs},{semanticName:"Extract",name:"Triangle",shortName:"tri",description:"Extraction process",aliases:["extract","triangle"],handler:_s},{semanticName:"Internal Storage",name:"Window Pane",shortName:"win-pane",description:"Internal storage",aliases:["internal-storage","window-pane"],handler:Xs},{semanticName:"Junction",name:"Filled Circle",shortName:"f-circ",description:"Junction point",aliases:["junction","filled-circle"],handler:os},{semanticName:"Loop Limit",name:"Trapezoidal Pentagon",shortName:"notch-pent",description:"Loop limit step",aliases:["loop-limit","notched-pentagon"],handler:Fs},{semanticName:"Manual File",name:"Flipped Triangle",shortName:"flip-tri",description:"Manual file operation",aliases:["manual-file","flipped-triangle"],handler:is},{semanticName:"Manual Input",name:"Sloped Rectangle",shortName:"sl-rect",description:"Manual input step",aliases:["manual-input","sloped-rectangle"],handler:Ts},{semanticName:"Multi-Document",name:"Stacked Document",shortName:"docs",description:"Multiple documents",aliases:["documents","st-doc","stacked-document"],handler:Ds},{semanticName:"Multi-Process",name:"Stacked Rectangle",shortName:"st-rect",description:"Multiple processes",aliases:["procs","processes","stacked-rectangle"],handler:vs},{semanticName:"Stored Data",name:"Bow Tie Rectangle",shortName:"bow-rect",description:"Stored data",aliases:["stored-data","bow-tie-rectangle"],handler:Xe},{semanticName:"Summary",name:"Crossed Circle",shortName:"cross-circ",description:"Summary",aliases:["summary","crossed-circle"],handler:Qe},{semanticName:"Tagged Document",name:"Tagged Document",shortName:"tag-doc",description:"Tagged document",aliases:["tag-doc","tagged-document"],handler:Hs},{semanticName:"Tagged Process",name:"Tagged Rectangle",shortName:"tag-rect",description:"Tagged process",aliases:["tagged-rectangle","tag-proc","tagged-process"],handler:Ls},{semanticName:"Paper Tape",name:"Flag",shortName:"flag",description:"Paper tape",aliases:["paper-tape"],handler:qs},{semanticName:"Odd",name:"Odd",shortName:"odd",description:"Odd shape",internalAliases:["rect_left_inv_arrow"],handler:$s},{semanticName:"Lined Document",name:"Lined Document",shortName:"lin-doc",description:"Lined document",aliases:["lined-document"],handler:Ns}],Er=x(()=>{let t=[...Object.entries({state:Es,choice:Ze,note:Ms,rectWithTitle:Ps,labelRect:ys,iconSquare:gs,iconCircle:ms,icon:ps,iconRounded:fs,imageSquare:ds,anchor:ze,kanbanItem:Qs,classBox:Zs,erBox:ue,requirementBox:Us}),...Gr.flatMap(i=>[i.shortName,..."aliases"in i?i.aliases:[],..."internalAliases"in i?i.internalAliases:[]].map(e=>[e,i.handler]))];return Object.fromEntries(t)},"generateShapeMap"),ye=Er();function Zf(c){return c in ye}x(Zf,"isValidShape");var Zt=new Map;async function tg(c,t,i){let r,e;t.shape==="rect"&&(t.rx&&t.ry?t.shape="roundedRect":t.shape="squareRect");let a=t.shape?ye[t.shape]:void 0;if(!a)throw new Error(`No such shape: ${t.shape}. Please check your syntax.`);if(t.link){let h;i.config.securityLevel==="sandbox"?h="_top":t.linkTarget&&(h=t.linkTarget||"_blank"),r=c.insert("svg:a").attr("xlink:href",t.link).attr("target",h??null),e=await a(r,t,i)}else e=await a(c,t,i),r=e;return t.tooltip&&e.attr("title",t.tooltip),Zt.set(t.id,r),t.haveCallback&&r.attr("class",r.attr("class")+" clickable"),r}x(tg,"insertNode");var eg=x((c,t)=>{Zt.set(t.id,c)},"setNodeElem"),sg=x(()=>{Zt.clear()},"clear"),rg=x(c=>{let t=Zt.get(c.id);z.trace("Transforming node",c.diff,c,"translate("+(c.x-c.width/2-5)+", "+c.width/2+")");let i=8,r=c.diff||0;return c.clusterNode?t.attr("transform","translate("+(c.x+r-c.width/2)+", "+(c.y-c.height/2-i)+")"):t.attr("transform","translate("+c.x+", "+c.y+")"),r},"positionNode");export{T as a,$ as b,v as c,Rt as d,Zf as e,ga as f,da as g,tg as h,eg as i,sg as j,rg as k};
