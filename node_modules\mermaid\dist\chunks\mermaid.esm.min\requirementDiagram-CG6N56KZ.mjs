import{a as Ze}from"./chunk-7VIK3F2G.mjs";import{a as it}from"./chunk-EJZ3NKMF.mjs";import{b as et,c as tt}from"./chunk-YKNY556I.mjs";import"./chunk-JOFIKEML.mjs";import"./chunk-S67DUUA5.mjs";import"./chunk-DPMNACAB.mjs";import"./chunk-LM6QDVU5.mjs";import"./chunk-Z2NOIGJN.mjs";import"./chunk-IXVBHSNP.mjs";import{m as Je}from"./chunk-3R3PQ5PD.mjs";import"./chunk-TI4EEUUG.mjs";import{Q as He,R as Ke,S as je,T as We,U as Ge,V as ze,W as Xe,Y as de,c as fe}from"./chunk-F632ZYSZ.mjs";import"./chunk-6BY5RJGC.mjs";import{a as h,c as ht}from"./chunk-GTKDMUJJ.mjs";var Ae=function(){var e=h(function(P,s,n,c){for(n=n||{},c=P.length;c--;n[P[c]]=s);return n},"o"),l=[1,3],p=[1,4],u=[1,5],r=[1,6],o=[5,6,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,77,89,90],m=[1,22],R=[2,7],k=[1,26],S=[1,27],N=[1,28],q=[1,29],C=[1,33],A=[1,34],V=[1,35],v=[1,36],L=[1,37],x=[1,38],D=[1,24],O=[1,31],w=[1,32],M=[1,30],b=[1,39],E=[1,40],d=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,77,89,90],$=[1,61],X=[89,90],ve=[5,8,9,11,13,21,22,23,24,27,29,41,42,43,44,45,46,54,61,63,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],be=[27,29],Le=[1,70],xe=[1,71],De=[1,72],Oe=[1,73],we=[1,74],Me=[1,75],Fe=[1,76],Z=[1,83],U=[1,80],ee=[1,84],te=[1,85],ie=[1,86],se=[1,87],re=[1,88],ne=[1,89],ae=[1,90],le=[1,91],ce=[1,92],Ee=[5,8,9,11,13,21,22,23,24,27,41,42,43,44,45,46,54,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],Y=[63,64],Pe=[1,101],$e=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,76,77,89,90],T=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],B=[1,110],Q=[1,106],H=[1,107],K=[1,108],j=[1,109],W=[1,111],oe=[1,116],he=[1,117],ue=[1,114],pe=[1,115],_e={trace:h(function(){},"trace"),yy:{},symbols_:{error:2,start:3,directive:4,NEWLINE:5,RD:6,diagram:7,EOF:8,acc_title:9,acc_title_value:10,acc_descr:11,acc_descr_value:12,acc_descr_multiline_value:13,requirementDef:14,elementDef:15,relationshipDef:16,direction:17,styleStatement:18,classDefStatement:19,classStatement:20,direction_tb:21,direction_bt:22,direction_rl:23,direction_lr:24,requirementType:25,requirementName:26,STRUCT_START:27,requirementBody:28,STYLE_SEPARATOR:29,idList:30,ID:31,COLONSEP:32,id:33,TEXT:34,text:35,RISK:36,riskLevel:37,VERIFYMTHD:38,verifyType:39,STRUCT_STOP:40,REQUIREMENT:41,FUNCTIONAL_REQUIREMENT:42,INTERFACE_REQUIREMENT:43,PERFORMANCE_REQUIREMENT:44,PHYSICAL_REQUIREMENT:45,DESIGN_CONSTRAINT:46,LOW_RISK:47,MED_RISK:48,HIGH_RISK:49,VERIFY_ANALYSIS:50,VERIFY_DEMONSTRATION:51,VERIFY_INSPECTION:52,VERIFY_TEST:53,ELEMENT:54,elementName:55,elementBody:56,TYPE:57,type:58,DOCREF:59,ref:60,END_ARROW_L:61,relationship:62,LINE:63,END_ARROW_R:64,CONTAINS:65,COPIES:66,DERIVES:67,SATISFIES:68,VERIFIES:69,REFINES:70,TRACES:71,CLASSDEF:72,stylesOpt:73,CLASS:74,ALPHA:75,COMMA:76,STYLE:77,style:78,styleComponent:79,NUM:80,COLON:81,UNIT:82,SPACE:83,BRKT:84,PCT:85,MINUS:86,LABEL:87,SEMICOLON:88,unqString:89,qString:90,$accept:0,$end:1},terminals_:{2:"error",5:"NEWLINE",6:"RD",8:"EOF",9:"acc_title",10:"acc_title_value",11:"acc_descr",12:"acc_descr_value",13:"acc_descr_multiline_value",21:"direction_tb",22:"direction_bt",23:"direction_rl",24:"direction_lr",27:"STRUCT_START",29:"STYLE_SEPARATOR",31:"ID",32:"COLONSEP",34:"TEXT",36:"RISK",38:"VERIFYMTHD",40:"STRUCT_STOP",41:"REQUIREMENT",42:"FUNCTIONAL_REQUIREMENT",43:"INTERFACE_REQUIREMENT",44:"PERFORMANCE_REQUIREMENT",45:"PHYSICAL_REQUIREMENT",46:"DESIGN_CONSTRAINT",47:"LOW_RISK",48:"MED_RISK",49:"HIGH_RISK",50:"VERIFY_ANALYSIS",51:"VERIFY_DEMONSTRATION",52:"VERIFY_INSPECTION",53:"VERIFY_TEST",54:"ELEMENT",57:"TYPE",59:"DOCREF",61:"END_ARROW_L",63:"LINE",64:"END_ARROW_R",65:"CONTAINS",66:"COPIES",67:"DERIVES",68:"SATISFIES",69:"VERIFIES",70:"REFINES",71:"TRACES",72:"CLASSDEF",74:"CLASS",75:"ALPHA",76:"COMMA",77:"STYLE",80:"NUM",81:"COLON",82:"UNIT",83:"SPACE",84:"BRKT",85:"PCT",86:"MINUS",87:"LABEL",88:"SEMICOLON",89:"unqString",90:"qString"},productions_:[0,[3,3],[3,2],[3,4],[4,2],[4,2],[4,1],[7,0],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[17,1],[17,1],[17,1],[17,1],[14,5],[14,7],[28,5],[28,5],[28,5],[28,5],[28,2],[28,1],[25,1],[25,1],[25,1],[25,1],[25,1],[25,1],[37,1],[37,1],[37,1],[39,1],[39,1],[39,1],[39,1],[15,5],[15,7],[56,5],[56,5],[56,2],[56,1],[16,5],[16,5],[62,1],[62,1],[62,1],[62,1],[62,1],[62,1],[62,1],[19,3],[20,3],[20,3],[30,1],[30,3],[30,1],[30,3],[18,3],[73,1],[73,3],[78,1],[78,2],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[26,1],[26,1],[33,1],[33,1],[35,1],[35,1],[55,1],[55,1],[58,1],[58,1],[60,1],[60,1]],performAction:h(function(s,n,c,i,f,t,me){var a=t.length-1;switch(f){case 4:this.$=t[a].trim(),i.setAccTitle(this.$);break;case 5:case 6:this.$=t[a].trim(),i.setAccDescription(this.$);break;case 7:this.$=[];break;case 17:i.setDirection("TB");break;case 18:i.setDirection("BT");break;case 19:i.setDirection("RL");break;case 20:i.setDirection("LR");break;case 21:i.addRequirement(t[a-3],t[a-4]);break;case 22:i.addRequirement(t[a-5],t[a-6]),i.setClass([t[a-5]],t[a-3]);break;case 23:i.setNewReqId(t[a-2]);break;case 24:i.setNewReqText(t[a-2]);break;case 25:i.setNewReqRisk(t[a-2]);break;case 26:i.setNewReqVerifyMethod(t[a-2]);break;case 29:this.$=i.RequirementType.REQUIREMENT;break;case 30:this.$=i.RequirementType.FUNCTIONAL_REQUIREMENT;break;case 31:this.$=i.RequirementType.INTERFACE_REQUIREMENT;break;case 32:this.$=i.RequirementType.PERFORMANCE_REQUIREMENT;break;case 33:this.$=i.RequirementType.PHYSICAL_REQUIREMENT;break;case 34:this.$=i.RequirementType.DESIGN_CONSTRAINT;break;case 35:this.$=i.RiskLevel.LOW_RISK;break;case 36:this.$=i.RiskLevel.MED_RISK;break;case 37:this.$=i.RiskLevel.HIGH_RISK;break;case 38:this.$=i.VerifyType.VERIFY_ANALYSIS;break;case 39:this.$=i.VerifyType.VERIFY_DEMONSTRATION;break;case 40:this.$=i.VerifyType.VERIFY_INSPECTION;break;case 41:this.$=i.VerifyType.VERIFY_TEST;break;case 42:i.addElement(t[a-3]);break;case 43:i.addElement(t[a-5]),i.setClass([t[a-5]],t[a-3]);break;case 44:i.setNewElementType(t[a-2]);break;case 45:i.setNewElementDocRef(t[a-2]);break;case 48:i.addRelationship(t[a-2],t[a],t[a-4]);break;case 49:i.addRelationship(t[a-2],t[a-4],t[a]);break;case 50:this.$=i.Relationships.CONTAINS;break;case 51:this.$=i.Relationships.COPIES;break;case 52:this.$=i.Relationships.DERIVES;break;case 53:this.$=i.Relationships.SATISFIES;break;case 54:this.$=i.Relationships.VERIFIES;break;case 55:this.$=i.Relationships.REFINES;break;case 56:this.$=i.Relationships.TRACES;break;case 57:this.$=t[a-2],i.defineClass(t[a-1],t[a]);break;case 58:i.setClass(t[a-1],t[a]);break;case 59:i.setClass([t[a-2]],t[a]);break;case 60:case 62:this.$=[t[a]];break;case 61:case 63:this.$=t[a-2].concat([t[a]]);break;case 64:this.$=t[a-2],i.setCssStyle(t[a-1],t[a]);break;case 65:this.$=[t[a]];break;case 66:t[a-2].push(t[a]),this.$=t[a-2];break;case 68:this.$=t[a-1]+t[a];break}},"anonymous"),table:[{3:1,4:2,6:l,9:p,11:u,13:r},{1:[3]},{3:8,4:2,5:[1,7],6:l,9:p,11:u,13:r},{5:[1,9]},{10:[1,10]},{12:[1,11]},e(o,[2,6]),{3:12,4:2,6:l,9:p,11:u,13:r},{1:[2,2]},{4:17,5:m,7:13,8:R,9:p,11:u,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:k,22:S,23:N,24:q,25:23,33:25,41:C,42:A,43:V,44:v,45:L,46:x,54:D,72:O,74:w,77:M,89:b,90:E},e(o,[2,4]),e(o,[2,5]),{1:[2,1]},{8:[1,41]},{4:17,5:m,7:42,8:R,9:p,11:u,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:k,22:S,23:N,24:q,25:23,33:25,41:C,42:A,43:V,44:v,45:L,46:x,54:D,72:O,74:w,77:M,89:b,90:E},{4:17,5:m,7:43,8:R,9:p,11:u,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:k,22:S,23:N,24:q,25:23,33:25,41:C,42:A,43:V,44:v,45:L,46:x,54:D,72:O,74:w,77:M,89:b,90:E},{4:17,5:m,7:44,8:R,9:p,11:u,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:k,22:S,23:N,24:q,25:23,33:25,41:C,42:A,43:V,44:v,45:L,46:x,54:D,72:O,74:w,77:M,89:b,90:E},{4:17,5:m,7:45,8:R,9:p,11:u,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:k,22:S,23:N,24:q,25:23,33:25,41:C,42:A,43:V,44:v,45:L,46:x,54:D,72:O,74:w,77:M,89:b,90:E},{4:17,5:m,7:46,8:R,9:p,11:u,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:k,22:S,23:N,24:q,25:23,33:25,41:C,42:A,43:V,44:v,45:L,46:x,54:D,72:O,74:w,77:M,89:b,90:E},{4:17,5:m,7:47,8:R,9:p,11:u,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:k,22:S,23:N,24:q,25:23,33:25,41:C,42:A,43:V,44:v,45:L,46:x,54:D,72:O,74:w,77:M,89:b,90:E},{4:17,5:m,7:48,8:R,9:p,11:u,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:k,22:S,23:N,24:q,25:23,33:25,41:C,42:A,43:V,44:v,45:L,46:x,54:D,72:O,74:w,77:M,89:b,90:E},{4:17,5:m,7:49,8:R,9:p,11:u,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:k,22:S,23:N,24:q,25:23,33:25,41:C,42:A,43:V,44:v,45:L,46:x,54:D,72:O,74:w,77:M,89:b,90:E},{4:17,5:m,7:50,8:R,9:p,11:u,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:k,22:S,23:N,24:q,25:23,33:25,41:C,42:A,43:V,44:v,45:L,46:x,54:D,72:O,74:w,77:M,89:b,90:E},{26:51,89:[1,52],90:[1,53]},{55:54,89:[1,55],90:[1,56]},{29:[1,59],61:[1,57],63:[1,58]},e(d,[2,17]),e(d,[2,18]),e(d,[2,19]),e(d,[2,20]),{30:60,33:62,75:$,89:b,90:E},{30:63,33:62,75:$,89:b,90:E},{30:64,33:62,75:$,89:b,90:E},e(X,[2,29]),e(X,[2,30]),e(X,[2,31]),e(X,[2,32]),e(X,[2,33]),e(X,[2,34]),e(ve,[2,81]),e(ve,[2,82]),{1:[2,3]},{8:[2,8]},{8:[2,9]},{8:[2,10]},{8:[2,11]},{8:[2,12]},{8:[2,13]},{8:[2,14]},{8:[2,15]},{8:[2,16]},{27:[1,65],29:[1,66]},e(be,[2,79]),e(be,[2,80]),{27:[1,67],29:[1,68]},e(be,[2,85]),e(be,[2,86]),{62:69,65:Le,66:xe,67:De,68:Oe,69:we,70:Me,71:Fe},{62:77,65:Le,66:xe,67:De,68:Oe,69:we,70:Me,71:Fe},{30:78,33:62,75:$,89:b,90:E},{73:79,75:Z,76:U,78:81,79:82,80:ee,81:te,82:ie,83:se,84:re,85:ne,86:ae,87:le,88:ce},e(Ee,[2,60]),e(Ee,[2,62]),{73:93,75:Z,76:U,78:81,79:82,80:ee,81:te,82:ie,83:se,84:re,85:ne,86:ae,87:le,88:ce},{30:94,33:62,75:$,76:U,89:b,90:E},{5:[1,95]},{30:96,33:62,75:$,89:b,90:E},{5:[1,97]},{30:98,33:62,75:$,89:b,90:E},{63:[1,99]},e(Y,[2,50]),e(Y,[2,51]),e(Y,[2,52]),e(Y,[2,53]),e(Y,[2,54]),e(Y,[2,55]),e(Y,[2,56]),{64:[1,100]},e(d,[2,59],{76:U}),e(d,[2,64],{76:Pe}),{33:103,75:[1,102],89:b,90:E},e($e,[2,65],{79:104,75:Z,80:ee,81:te,82:ie,83:se,84:re,85:ne,86:ae,87:le,88:ce}),e(T,[2,67]),e(T,[2,69]),e(T,[2,70]),e(T,[2,71]),e(T,[2,72]),e(T,[2,73]),e(T,[2,74]),e(T,[2,75]),e(T,[2,76]),e(T,[2,77]),e(T,[2,78]),e(d,[2,57],{76:Pe}),e(d,[2,58],{76:U}),{5:B,28:105,31:Q,34:H,36:K,38:j,40:W},{27:[1,112],76:U},{5:oe,40:he,56:113,57:ue,59:pe},{27:[1,118],76:U},{33:119,89:b,90:E},{33:120,89:b,90:E},{75:Z,78:121,79:82,80:ee,81:te,82:ie,83:se,84:re,85:ne,86:ae,87:le,88:ce},e(Ee,[2,61]),e(Ee,[2,63]),e(T,[2,68]),e(d,[2,21]),{32:[1,122]},{32:[1,123]},{32:[1,124]},{32:[1,125]},{5:B,28:126,31:Q,34:H,36:K,38:j,40:W},e(d,[2,28]),{5:[1,127]},e(d,[2,42]),{32:[1,128]},{32:[1,129]},{5:oe,40:he,56:130,57:ue,59:pe},e(d,[2,47]),{5:[1,131]},e(d,[2,48]),e(d,[2,49]),e($e,[2,66],{79:104,75:Z,80:ee,81:te,82:ie,83:se,84:re,85:ne,86:ae,87:le,88:ce}),{33:132,89:b,90:E},{35:133,89:[1,134],90:[1,135]},{37:136,47:[1,137],48:[1,138],49:[1,139]},{39:140,50:[1,141],51:[1,142],52:[1,143],53:[1,144]},e(d,[2,27]),{5:B,28:145,31:Q,34:H,36:K,38:j,40:W},{58:146,89:[1,147],90:[1,148]},{60:149,89:[1,150],90:[1,151]},e(d,[2,46]),{5:oe,40:he,56:152,57:ue,59:pe},{5:[1,153]},{5:[1,154]},{5:[2,83]},{5:[2,84]},{5:[1,155]},{5:[2,35]},{5:[2,36]},{5:[2,37]},{5:[1,156]},{5:[2,38]},{5:[2,39]},{5:[2,40]},{5:[2,41]},e(d,[2,22]),{5:[1,157]},{5:[2,87]},{5:[2,88]},{5:[1,158]},{5:[2,89]},{5:[2,90]},e(d,[2,43]),{5:B,28:159,31:Q,34:H,36:K,38:j,40:W},{5:B,28:160,31:Q,34:H,36:K,38:j,40:W},{5:B,28:161,31:Q,34:H,36:K,38:j,40:W},{5:B,28:162,31:Q,34:H,36:K,38:j,40:W},{5:oe,40:he,56:163,57:ue,59:pe},{5:oe,40:he,56:164,57:ue,59:pe},e(d,[2,23]),e(d,[2,24]),e(d,[2,25]),e(d,[2,26]),e(d,[2,44]),e(d,[2,45])],defaultActions:{8:[2,2],12:[2,1],41:[2,3],42:[2,8],43:[2,9],44:[2,10],45:[2,11],46:[2,12],47:[2,13],48:[2,14],49:[2,15],50:[2,16],134:[2,83],135:[2,84],137:[2,35],138:[2,36],139:[2,37],141:[2,38],142:[2,39],143:[2,40],144:[2,41],147:[2,87],148:[2,88],150:[2,89],151:[2,90]},parseError:h(function(s,n){if(n.recoverable)this.trace(s);else{var c=new Error(s);throw c.hash=n,c}},"parseError"),parse:h(function(s){var n=this,c=[0],i=[],f=[null],t=[],me=this.table,a="",Re=0,Ue=0,Ye=0,at=2,Be=1,lt=t.slice.call(arguments,1),y=Object.create(this.lexer),G={yy:{}};for(var Te in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Te)&&(G.yy[Te]=this.yy[Te]);y.setInput(s,G.yy),G.yy.lexer=y,G.yy.parser=this,typeof y.yylloc>"u"&&(y.yylloc={});var Ie=y.yylloc;t.push(Ie);var ct=y.options&&y.options.ranges;typeof G.yy.parseError=="function"?this.parseError=G.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function mt(_){c.length=c.length-2*_,f.length=f.length-_,t.length=t.length-_}h(mt,"popStack");function ot(){var _;return _=i.pop()||y.lex()||Be,typeof _!="number"&&(_ instanceof Array&&(i=_,_=i.pop()),_=n.symbols_[_]||_),_}h(ot,"lex");for(var g,Ne,z,I,ft,qe,J={},ye,F,Qe,ge;;){if(z=c[c.length-1],this.defaultActions[z]?I=this.defaultActions[z]:((g===null||typeof g>"u")&&(g=ot()),I=me[z]&&me[z][g]),typeof I>"u"||!I.length||!I[0]){var Ce="";ge=[];for(ye in me[z])this.terminals_[ye]&&ye>at&&ge.push("'"+this.terminals_[ye]+"'");y.showPosition?Ce="Parse error on line "+(Re+1)+`:
`+y.showPosition()+`
Expecting `+ge.join(", ")+", got '"+(this.terminals_[g]||g)+"'":Ce="Parse error on line "+(Re+1)+": Unexpected "+(g==Be?"end of input":"'"+(this.terminals_[g]||g)+"'"),this.parseError(Ce,{text:y.match,token:this.terminals_[g]||g,line:y.yylineno,loc:Ie,expected:ge})}if(I[0]instanceof Array&&I.length>1)throw new Error("Parse Error: multiple actions possible at state: "+z+", token: "+g);switch(I[0]){case 1:c.push(g),f.push(y.yytext),t.push(y.yylloc),c.push(I[1]),g=null,Ne?(g=Ne,Ne=null):(Ue=y.yyleng,a=y.yytext,Re=y.yylineno,Ie=y.yylloc,Ye>0&&Ye--);break;case 2:if(F=this.productions_[I[1]][1],J.$=f[f.length-F],J._$={first_line:t[t.length-(F||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-(F||1)].first_column,last_column:t[t.length-1].last_column},ct&&(J._$.range=[t[t.length-(F||1)].range[0],t[t.length-1].range[1]]),qe=this.performAction.apply(J,[a,Ue,Re,G.yy,I[1],f,t].concat(lt)),typeof qe<"u")return qe;F&&(c=c.slice(0,-1*F*2),f=f.slice(0,-1*F),t=t.slice(0,-1*F)),c.push(this.productions_[I[1]][0]),f.push(J.$),t.push(J._$),Qe=me[c[c.length-2]][c[c.length-1]],c.push(Qe);break;case 3:return!0}}return!0},"parse")},nt=function(){var P={EOF:1,parseError:h(function(n,c){if(this.yy.parser)this.yy.parser.parseError(n,c);else throw new Error(n)},"parseError"),setInput:h(function(s,n){return this.yy=n||this.yy||{},this._input=s,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:h(function(){var s=this._input[0];this.yytext+=s,this.yyleng++,this.offset++,this.match+=s,this.matched+=s;var n=s.match(/(?:\r\n?|\n).*/g);return n?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),s},"input"),unput:h(function(s){var n=s.length,c=s.split(/(?:\r\n?|\n)/g);this._input=s+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-n),this.offset-=n;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),c.length-1&&(this.yylineno-=c.length-1);var f=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:c?(c.length===i.length?this.yylloc.first_column:0)+i[i.length-c.length].length-c[0].length:this.yylloc.first_column-n},this.options.ranges&&(this.yylloc.range=[f[0],f[0]+this.yyleng-n]),this.yyleng=this.yytext.length,this},"unput"),more:h(function(){return this._more=!0,this},"more"),reject:h(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:h(function(s){this.unput(this.match.slice(s))},"less"),pastInput:h(function(){var s=this.matched.substr(0,this.matched.length-this.match.length);return(s.length>20?"...":"")+s.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:h(function(){var s=this.match;return s.length<20&&(s+=this._input.substr(0,20-s.length)),(s.substr(0,20)+(s.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:h(function(){var s=this.pastInput(),n=new Array(s.length+1).join("-");return s+this.upcomingInput()+`
`+n+"^"},"showPosition"),test_match:h(function(s,n){var c,i,f;if(this.options.backtrack_lexer&&(f={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(f.yylloc.range=this.yylloc.range.slice(0))),i=s[0].match(/(?:\r\n?|\n).*/g),i&&(this.yylineno+=i.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+s[0].length},this.yytext+=s[0],this.match+=s[0],this.matches=s,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(s[0].length),this.matched+=s[0],c=this.performAction.call(this,this.yy,this,n,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),c)return c;if(this._backtrack){for(var t in f)this[t]=f[t];return!1}return!1},"test_match"),next:h(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var s,n,c,i;this._more||(this.yytext="",this.match="");for(var f=this._currentRules(),t=0;t<f.length;t++)if(c=this._input.match(this.rules[f[t]]),c&&(!n||c[0].length>n[0].length)){if(n=c,i=t,this.options.backtrack_lexer){if(s=this.test_match(c,f[t]),s!==!1)return s;if(this._backtrack){n=!1;continue}else return!1}else if(!this.options.flex)break}return n?(s=this.test_match(n,f[i]),s!==!1?s:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:h(function(){var n=this.next();return n||this.lex()},"lex"),begin:h(function(n){this.conditionStack.push(n)},"begin"),popState:h(function(){var n=this.conditionStack.length-1;return n>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:h(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:h(function(n){return n=this.conditionStack.length-1-Math.abs(n||0),n>=0?this.conditionStack[n]:"INITIAL"},"topState"),pushState:h(function(n){this.begin(n)},"pushState"),stateStackSize:h(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:h(function(n,c,i,f){var t=f;switch(i){case 0:return"title";case 1:return this.begin("acc_title"),9;break;case 2:return this.popState(),"acc_title_value";break;case 3:return this.begin("acc_descr"),11;break;case 4:return this.popState(),"acc_descr_value";break;case 5:this.begin("acc_descr_multiline");break;case 6:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:return 21;case 9:return 22;case 10:return 23;case 11:return 24;case 12:return 5;case 13:break;case 14:break;case 15:break;case 16:return 8;case 17:return 6;case 18:return 27;case 19:return 40;case 20:return 29;case 21:return 32;case 22:return 31;case 23:return 34;case 24:return 36;case 25:return 38;case 26:return 41;case 27:return 42;case 28:return 43;case 29:return 44;case 30:return 45;case 31:return 46;case 32:return 47;case 33:return 48;case 34:return 49;case 35:return 50;case 36:return 51;case 37:return 52;case 38:return 53;case 39:return 54;case 40:return 65;case 41:return 66;case 42:return 67;case 43:return 68;case 44:return 69;case 45:return 70;case 46:return 71;case 47:return 57;case 48:return 59;case 49:return this.begin("style"),77;break;case 50:return 75;case 51:return 81;case 52:return 88;case 53:return"PERCENT";case 54:return 86;case 55:return 84;case 56:break;case 57:this.begin("string");break;case 58:this.popState();break;case 59:return this.begin("style"),72;break;case 60:return this.begin("style"),74;break;case 61:return 61;case 62:return 64;case 63:return 63;case 64:this.begin("string");break;case 65:this.popState();break;case 66:return"qString";case 67:return c.yytext=c.yytext.trim(),89;break;case 68:return 75;case 69:return 80;case 70:return 76}},"anonymous"),rules:[/^(?:title\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:(\r?\n)+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:%[^\n]*)/i,/^(?:$)/i,/^(?:requirementDiagram\b)/i,/^(?:\{)/i,/^(?:\})/i,/^(?::{3})/i,/^(?::)/i,/^(?:id\b)/i,/^(?:text\b)/i,/^(?:risk\b)/i,/^(?:verifyMethod\b)/i,/^(?:requirement\b)/i,/^(?:functionalRequirement\b)/i,/^(?:interfaceRequirement\b)/i,/^(?:performanceRequirement\b)/i,/^(?:physicalRequirement\b)/i,/^(?:designConstraint\b)/i,/^(?:low\b)/i,/^(?:medium\b)/i,/^(?:high\b)/i,/^(?:analysis\b)/i,/^(?:demonstration\b)/i,/^(?:inspection\b)/i,/^(?:test\b)/i,/^(?:element\b)/i,/^(?:contains\b)/i,/^(?:copies\b)/i,/^(?:derives\b)/i,/^(?:satisfies\b)/i,/^(?:verifies\b)/i,/^(?:refines\b)/i,/^(?:traces\b)/i,/^(?:type\b)/i,/^(?:docref\b)/i,/^(?:style\b)/i,/^(?:\w+)/i,/^(?::)/i,/^(?:;)/i,/^(?:%)/i,/^(?:-)/i,/^(?:#)/i,/^(?: )/i,/^(?:["])/i,/^(?:\n)/i,/^(?:classDef\b)/i,/^(?:class\b)/i,/^(?:<-)/i,/^(?:->)/i,/^(?:-)/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[\w][^:,\r\n\{\<\>\-\=]*)/i,/^(?:\w+)/i,/^(?:[0-9]+)/i,/^(?:,)/i],conditions:{acc_descr_multiline:{rules:[6,7,68,69,70],inclusive:!1},acc_descr:{rules:[4,68,69,70],inclusive:!1},acc_title:{rules:[2,68,69,70],inclusive:!1},style:{rules:[50,51,52,53,54,55,56,57,58,68,69,70],inclusive:!1},unqString:{rules:[68,69,70],inclusive:!1},token:{rules:[68,69,70],inclusive:!1},string:{rules:[65,66,68,69,70],inclusive:!1},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,67,68,69,70],inclusive:!0}}};return P}();_e.lexer=nt;function Se(){this.yy={}}return h(Se,"Parser"),Se.prototype=_e,_e.Parser=Se,new Se}();Ae.parser=Ae;var st=Ae;var ke=class{constructor(){this.relations=[];this.latestRequirement=this.getInitialRequirement();this.requirements=new Map;this.latestElement=this.getInitialElement();this.elements=new Map;this.classes=new Map;this.direction="TB";this.RequirementType={REQUIREMENT:"Requirement",FUNCTIONAL_REQUIREMENT:"Functional Requirement",INTERFACE_REQUIREMENT:"Interface Requirement",PERFORMANCE_REQUIREMENT:"Performance Requirement",PHYSICAL_REQUIREMENT:"Physical Requirement",DESIGN_CONSTRAINT:"Design Constraint"};this.RiskLevel={LOW_RISK:"Low",MED_RISK:"Medium",HIGH_RISK:"High"};this.VerifyType={VERIFY_ANALYSIS:"Analysis",VERIFY_DEMONSTRATION:"Demonstration",VERIFY_INSPECTION:"Inspection",VERIFY_TEST:"Test"};this.Relationships={CONTAINS:"contains",COPIES:"copies",DERIVES:"derives",SATISFIES:"satisfies",VERIFIES:"verifies",REFINES:"refines",TRACES:"traces"};this.setAccTitle=Ke;this.getAccTitle=je;this.setAccDescription=We;this.getAccDescription=Ge;this.setDiagramTitle=ze;this.getDiagramTitle=Xe;this.getConfig=h(()=>de().requirement,"getConfig");this.clear(),this.setDirection=this.setDirection.bind(this),this.addRequirement=this.addRequirement.bind(this),this.setNewReqId=this.setNewReqId.bind(this),this.setNewReqRisk=this.setNewReqRisk.bind(this),this.setNewReqText=this.setNewReqText.bind(this),this.setNewReqVerifyMethod=this.setNewReqVerifyMethod.bind(this),this.addElement=this.addElement.bind(this),this.setNewElementType=this.setNewElementType.bind(this),this.setNewElementDocRef=this.setNewElementDocRef.bind(this),this.addRelationship=this.addRelationship.bind(this),this.setCssStyle=this.setCssStyle.bind(this),this.setClass=this.setClass.bind(this),this.defineClass=this.defineClass.bind(this),this.setAccTitle=this.setAccTitle.bind(this),this.setAccDescription=this.setAccDescription.bind(this)}static{h(this,"RequirementDB")}getDirection(){return this.direction}setDirection(l){this.direction=l}resetLatestRequirement(){this.latestRequirement=this.getInitialRequirement()}resetLatestElement(){this.latestElement=this.getInitialElement()}getInitialRequirement(){return{requirementId:"",text:"",risk:"",verifyMethod:"",name:"",type:"",cssStyles:[],classes:["default"]}}getInitialElement(){return{name:"",type:"",docRef:"",cssStyles:[],classes:["default"]}}addRequirement(l,p){return this.requirements.has(l)||this.requirements.set(l,{name:l,type:p,requirementId:this.latestRequirement.requirementId,text:this.latestRequirement.text,risk:this.latestRequirement.risk,verifyMethod:this.latestRequirement.verifyMethod,cssStyles:[],classes:["default"]}),this.resetLatestRequirement(),this.requirements.get(l)}getRequirements(){return this.requirements}setNewReqId(l){this.latestRequirement!==void 0&&(this.latestRequirement.requirementId=l)}setNewReqText(l){this.latestRequirement!==void 0&&(this.latestRequirement.text=l)}setNewReqRisk(l){this.latestRequirement!==void 0&&(this.latestRequirement.risk=l)}setNewReqVerifyMethod(l){this.latestRequirement!==void 0&&(this.latestRequirement.verifyMethod=l)}addElement(l){return this.elements.has(l)||(this.elements.set(l,{name:l,type:this.latestElement.type,docRef:this.latestElement.docRef,cssStyles:[],classes:["default"]}),fe.info("Added new element: ",l)),this.resetLatestElement(),this.elements.get(l)}getElements(){return this.elements}setNewElementType(l){this.latestElement!==void 0&&(this.latestElement.type=l)}setNewElementDocRef(l){this.latestElement!==void 0&&(this.latestElement.docRef=l)}addRelationship(l,p,u){this.relations.push({type:l,src:p,dst:u})}getRelationships(){return this.relations}clear(){this.relations=[],this.resetLatestRequirement(),this.requirements=new Map,this.resetLatestElement(),this.elements=new Map,this.classes=new Map,He()}setCssStyle(l,p){for(let u of l){let r=this.requirements.get(u)??this.elements.get(u);if(!p||!r)return;for(let o of p)o.includes(",")?r.cssStyles.push(...o.split(",")):r.cssStyles.push(o)}}setClass(l,p){for(let u of l){let r=this.requirements.get(u)??this.elements.get(u);if(r)for(let o of p){r.classes.push(o);let m=this.classes.get(o)?.styles;m&&r.cssStyles.push(...m)}}}defineClass(l,p){for(let u of l){let r=this.classes.get(u);r===void 0&&(r={id:u,styles:[],textStyles:[]},this.classes.set(u,r)),p&&p.forEach(function(o){if(/color/.exec(o)){let m=o.replace("fill","bgFill");r.textStyles.push(m)}r.styles.push(o)}),this.requirements.forEach(o=>{o.classes.includes(u)&&o.cssStyles.push(...p.flatMap(m=>m.split(",")))}),this.elements.forEach(o=>{o.classes.includes(u)&&o.cssStyles.push(...p.flatMap(m=>m.split(",")))})}}getClasses(){return this.classes}getData(){let l=de(),p=[],u=[];for(let r of this.requirements.values()){let o=r;o.id=r.name,o.cssStyles=r.cssStyles,o.cssClasses=r.classes.join(" "),o.shape="requirementBox",o.look=l.look,p.push(o)}for(let r of this.elements.values()){let o=r;o.shape="requirementBox",o.look=l.look,o.id=r.name,o.cssStyles=r.cssStyles,o.cssClasses=r.classes.join(" "),p.push(o)}for(let r of this.relations){let o=0,m=r.type===this.Relationships.CONTAINS,R={id:`${r.src}-${r.dst}-${o}`,start:this.requirements.get(r.src)?.name??this.elements.get(r.src)?.name,end:this.requirements.get(r.dst)?.name??this.elements.get(r.dst)?.name,label:`&lt;&lt;${r.type}&gt;&gt;`,classes:"relationshipLine",style:["fill:none",m?"":"stroke-dasharray: 10,7"],labelpos:"c",thickness:"normal",type:"normal",pattern:m?"normal":"dashed",arrowTypeStart:m?"requirement_contains":"",arrowTypeEnd:m?"":"requirement_arrow",look:l.look};u.push(R),o++}return{nodes:p,edges:u,other:{},config:l,direction:this.getDirection()}}};var ut=h(e=>`

  marker {
    fill: ${e.relationColor};
    stroke: ${e.relationColor};
  }

  marker.cross {
    stroke: ${e.lineColor};
  }

  svg {
    font-family: ${e.fontFamily};
    font-size: ${e.fontSize};
  }

  .reqBox {
    fill: ${e.requirementBackground};
    fill-opacity: 1.0;
    stroke: ${e.requirementBorderColor};
    stroke-width: ${e.requirementBorderSize};
  }
  
  .reqTitle, .reqLabel{
    fill:  ${e.requirementTextColor};
  }
  .reqLabelBox {
    fill: ${e.relationLabelBackground};
    fill-opacity: 1.0;
  }

  .req-title-line {
    stroke: ${e.requirementBorderColor};
    stroke-width: ${e.requirementBorderSize};
  }
  .relationshipLine {
    stroke: ${e.relationColor};
    stroke-width: 1;
  }
  .relationshipLabel {
    fill: ${e.relationLabelColor};
  }
  .divider {
    stroke: ${e.nodeBorder};
    stroke-width: 1;
  }
  .label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }
  .label text,span {
    fill: ${e.nodeTextColor||e.textColor};
    color: ${e.nodeTextColor||e.textColor};
  }
  .labelBkg {
    background-color: ${e.edgeLabelBackground};
  }

`,"getStyles"),rt=ut;var Ve={};ht(Ve,{draw:()=>pt});var pt=h(async function(e,l,p,u){fe.info("REF0:"),fe.info("Drawing requirement diagram (unified)",l);let{securityLevel:r,state:o,layout:m}=de(),R=u.db.getData(),k=Ze(l,r);R.type=u.type,R.layoutAlgorithm=tt(m),R.nodeSpacing=o?.nodeSpacing??50,R.rankSpacing=o?.rankSpacing??50,R.markers=["requirement_contains","requirement_arrow"],R.diagramId=l,await et(R,k);let S=8;Je.insertTitle(k,"requirementDiagramTitleText",o?.titleTopMargin??25,u.db.getDiagramTitle()),it(k,S,"requirementDiagram",o?.useMaxWidth??!0)},"draw");var Dt={parser:st,get db(){return new ke},renderer:Ve,styles:rt};export{Dt as diagram};
