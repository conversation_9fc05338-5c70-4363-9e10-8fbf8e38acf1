import{N as c,c as h}from"./chunk-F632ZYSZ.mjs";import{a as i}from"./chunk-GTKDMUJJ.mjs";var y=i((t,e,o,n)=>{t.attr("class",o);let{width:r,height:m,x:s,y:b}=w(t,e);c(t,m,r,n);let u=x(s,b,r,m,e);t.attr("viewBox",u),h.debug(`viewBox configured: ${u} with padding: ${e}`)},"setupViewPortForSVG"),w=i((t,e)=>{let o=t.node()?.getBBox()||{width:0,height:0,x:0,y:0};return{width:o.width+e*2,height:o.height+e*2,x:o.x,y:o.y}},"calculateDimensionsWithPadding"),x=i((t,e,o,n,r)=>`${t-r} ${e-r} ${o} ${n}`,"createViewBox");export{y as a};
