import{a as X,c as N,d as J}from"./chunk-S3YGE35Q.mjs";import{a as O}from"./chunk-FASC7IG4.mjs";import{a as F}from"./chunk-ZN7TASNU.mjs";import"./chunk-7VIK3F2G.mjs";import"./chunk-EJZ3NKMF.mjs";import"./chunk-YKNY556I.mjs";import"./chunk-JOFIKEML.mjs";import"./chunk-S67DUUA5.mjs";import"./chunk-DPMNACAB.mjs";import"./chunk-LM6QDVU5.mjs";import"./chunk-Z2NOIGJN.mjs";import"./chunk-IXVBHSNP.mjs";import{m as C}from"./chunk-3R3PQ5PD.mjs";import"./chunk-TI4EEUUG.mjs";import{F as z,Ha as W,La as U,N as R,Y as t,c as b,ha as L,z as P}from"./chunk-F632ZYSZ.mjs";import"./chunk-5ZJXQJOJ.mjs";import"./chunk-YPUTD6PB.mjs";import"./chunk-6BY5RJGC.mjs";import{a as f}from"./chunk-GTKDMUJJ.mjs";var j=f(e=>e.append("circle").attr("class","start-state").attr("r",t().state.sizeUnit).attr("cx",t().state.padding+t().state.sizeUnit).attr("cy",t().state.padding+t().state.sizeUnit),"drawStartState"),q=f(e=>e.append("line").style("stroke","grey").style("stroke-dasharray","3").attr("x1",t().state.textHeight).attr("class","divider").attr("x2",t().state.textHeight*2).attr("y1",0).attr("y2",0),"drawDivider"),Z=f((e,n)=>{let s=e.append("text").attr("x",2*t().state.padding).attr("y",t().state.textHeight+2*t().state.padding).attr("font-size",t().state.fontSize).attr("class","state-title").text(n.id),c=s.node().getBBox();return e.insert("rect",":first-child").attr("x",t().state.padding).attr("y",t().state.padding).attr("width",c.width+2*t().state.padding).attr("height",c.height+2*t().state.padding).attr("rx",t().state.radius),s},"drawSimpleState"),K=f((e,n)=>{let s=f(function(p,y,w){let k=p.append("tspan").attr("x",2*t().state.padding).text(y);w||k.attr("dy",t().state.textHeight)},"addTspan"),r=e.append("text").attr("x",2*t().state.padding).attr("y",t().state.textHeight+1.3*t().state.padding).attr("font-size",t().state.fontSize).attr("class","state-title").text(n.descriptions[0]).node().getBBox(),g=r.height,x=e.append("text").attr("x",t().state.padding).attr("y",g+t().state.padding*.4+t().state.dividerMargin+t().state.textHeight).attr("class","state-description"),i=!0,o=!0;n.descriptions.forEach(function(p){i||(s(x,p,o),o=!1),i=!1});let m=e.append("line").attr("x1",t().state.padding).attr("y1",t().state.padding+g+t().state.dividerMargin/2).attr("y2",t().state.padding+g+t().state.dividerMargin/2).attr("class","descr-divider"),h=x.node().getBBox(),d=Math.max(h.width,r.width);return m.attr("x2",d+3*t().state.padding),e.insert("rect",":first-child").attr("x",t().state.padding).attr("y",t().state.padding).attr("width",d+2*t().state.padding).attr("height",h.height+g+2*t().state.padding).attr("rx",t().state.radius),e},"drawDescrState"),v=f((e,n,s)=>{let c=t().state.padding,r=2*t().state.padding,g=e.node().getBBox(),x=g.width,i=g.x,o=e.append("text").attr("x",0).attr("y",t().state.titleShift).attr("font-size",t().state.fontSize).attr("class","state-title").text(n.id),h=o.node().getBBox().width+r,d=Math.max(h,x);d===x&&(d=d+r);let p,y=e.node().getBBox();n.doc,p=i-c,h>x&&(p=(x-d)/2+c),Math.abs(i-y.x)<c&&h>x&&(p=i-(h-x)/2);let w=1-t().state.textHeight;return e.insert("rect",":first-child").attr("x",p).attr("y",w).attr("class",s?"alt-composit":"composit").attr("width",d).attr("height",y.height+t().state.textHeight+t().state.titleShift+1).attr("rx","0"),o.attr("x",p+c),h<=x&&o.attr("x",i+(d-r)/2-h/2+c),e.insert("rect",":first-child").attr("x",p).attr("y",t().state.titleShift-t().state.textHeight-t().state.padding).attr("width",d).attr("height",t().state.textHeight*3).attr("rx",t().state.radius),e.insert("rect",":first-child").attr("x",p).attr("y",t().state.titleShift-t().state.textHeight-t().state.padding).attr("width",d).attr("height",y.height+3+2*t().state.textHeight).attr("rx",t().state.radius),e},"addTitleAndBox"),Q=f(e=>(e.append("circle").attr("class","end-state-outer").attr("r",t().state.sizeUnit+t().state.miniPadding).attr("cx",t().state.padding+t().state.sizeUnit+t().state.miniPadding).attr("cy",t().state.padding+t().state.sizeUnit+t().state.miniPadding),e.append("circle").attr("class","end-state-inner").attr("r",t().state.sizeUnit).attr("cx",t().state.padding+t().state.sizeUnit+2).attr("cy",t().state.padding+t().state.sizeUnit+2)),"drawEndState"),V=f((e,n)=>{let s=t().state.forkWidth,c=t().state.forkHeight;if(n.parentId){let r=s;s=c,c=r}return e.append("rect").style("stroke","black").style("fill","black").attr("width",s).attr("height",c).attr("x",t().state.padding).attr("y",t().state.padding)},"drawForkJoinState");var D=f((e,n,s,c)=>{let r=0,g=c.append("text");g.style("text-anchor","start"),g.attr("class","noteText");let x=e.replace(/\r\n/g,"<br/>");x=x.replace(/\n/g,"<br/>");let i=x.split(z.lineBreakRegex),o=1.25*t().state.noteMargin;for(let m of i){let h=m.trim();if(h.length>0){let d=g.append("tspan");if(d.text(h),o===0){let p=d.node().getBBox();o+=p.height}r+=o,d.attr("x",n+t().state.noteMargin),d.attr("y",s+r+1.25*t().state.noteMargin)}}return{textWidth:g.node().getBBox().width,textHeight:r}},"_drawLongText"),tt=f((e,n)=>{n.attr("class","state-note");let s=n.append("rect").attr("x",0).attr("y",t().state.padding),c=n.append("g"),{textWidth:r,textHeight:g}=D(e,0,0,c);return s.attr("height",g+2*t().state.noteMargin),s.attr("width",r+t().state.noteMargin*2),s},"drawNote"),A=f(function(e,n){let s=n.id,c={id:s,label:n.id,width:0,height:0},r=e.append("g").attr("id",s).attr("class","stateGroup");n.type==="start"&&j(r),n.type==="end"&&Q(r),(n.type==="fork"||n.type==="join")&&V(r,n),n.type==="note"&&tt(n.note.text,r),n.type==="divider"&&q(r),n.type==="default"&&n.descriptions.length===0&&Z(r,n),n.type==="default"&&n.descriptions.length>0&&K(r,n);let g=r.node().getBBox();return c.width=g.width+2*t().state.padding,c.height=g.height+2*t().state.padding,c},"drawState"),Y=0,I=f(function(e,n,s){let c=f(function(o){switch(o){case N.relationType.AGGREGATION:return"aggregation";case N.relationType.EXTENSION:return"extension";case N.relationType.COMPOSITION:return"composition";case N.relationType.DEPENDENCY:return"dependency"}},"getRelationType");n.points=n.points.filter(o=>!Number.isNaN(o.y));let r=n.points,g=W().x(function(o){return o.x}).y(function(o){return o.y}).curve(U),x=e.append("path").attr("d",g(r)).attr("id","edge"+Y).attr("class","transition"),i="";if(t().state.arrowMarkerAbsolute&&(i=P(!0)),x.attr("marker-end","url("+i+"#"+c(N.relationType.DEPENDENCY)+"End)"),s.title!==void 0){let o=e.append("g").attr("class","stateLabel"),{x:m,y:h}=C.calcLabelPosition(n.points),d=z.getRows(s.title),p=0,y=[],w=0,k=0;for(let a=0;a<=d.length;a++){let u=o.append("text").attr("text-anchor","middle").text(d[a]).attr("x",m).attr("y",h+p),l=u.node().getBBox();w=Math.max(w,l.width),k=Math.min(k,l.x),b.info(l.x,m,h+p),p===0&&(p=u.node().getBBox().height,b.info("Title height",p,h)),y.push(u)}let M=p*d.length;if(d.length>1){let a=(d.length-1)*p*.5;y.forEach((u,l)=>u.attr("y",h+l*p-a)),M=p*d.length}let H=o.node().getBBox();o.insert("rect",":first-child").attr("class","box").attr("x",m-w/2-t().state.padding/2).attr("y",h-M/2-t().state.padding/2-3.5).attr("width",w+t().state.padding).attr("height",M+t().state.padding),b.info(H)}Y++},"drawEdge");var S,G={},et=f(function(){},"setConf"),it=f(function(e){e.append("defs").append("marker").attr("id","dependencyEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"insertMarkers"),nt=f(function(e,n,s,c){S=t().state;let r=t().securityLevel,g;r==="sandbox"&&(g=L("#i"+n));let x=r==="sandbox"?L(g.nodes()[0].contentDocument.body):L("body"),i=r==="sandbox"?g.nodes()[0].contentDocument:document;b.debug("Rendering diagram "+e);let o=x.select(`[id='${n}']`);it(o);let m=c.db.getRootDoc();$(m,o,void 0,!1,x,i,c);let h=S.padding,d=o.node().getBBox(),p=d.width+h*2,y=d.height+h*2,w=p*1.75;R(o,y,w,S.useMaxWidth),o.attr("viewBox",`${d.x-S.padding}  ${d.y-S.padding} `+p+" "+y)},"draw"),at=f(e=>e?e.length*S.fontSizeFactor:1,"getLabelWidth"),$=f((e,n,s,c,r,g,x)=>{let i=new F({compound:!0,multigraph:!0}),o,m=!0;for(o=0;o<e.length;o++)if(e[o].stmt==="relation"){m=!1;break}s?i.setGraph({rankdir:"LR",multigraph:!0,compound:!0,ranker:"tight-tree",ranksep:m?1:S.edgeLengthFactor,nodeSep:m?1:50,isMultiGraph:!0}):i.setGraph({rankdir:"TB",multigraph:!0,compound:!0,ranksep:m?1:S.edgeLengthFactor,nodeSep:m?1:50,ranker:"tight-tree",isMultiGraph:!0}),i.setDefaultEdgeLabel(function(){return{}});let h=x.db.getStates(),d=x.db.getRelations(),p=Object.keys(h),y=!0;for(let a of p){let u=h[a];s&&(u.parentId=s);let l;if(u.doc){let B=n.append("g").attr("id",u.id).attr("class","stateGroup");if(l=$(u.doc,B,u.id,!c,r,g,x),y){B=v(B,u,c);let E=B.node().getBBox();l.width=E.width,l.height=E.height+S.padding/2,G[u.id]={y:S.compositTitleSize}}else{let E=B.node().getBBox();l.width=E.width,l.height=E.height}}else l=A(n,u,i);if(u.note){let B={descriptions:[],id:u.id+"-note",note:u.note,type:"note"},E=A(n,B,i);u.note.position==="left of"?(i.setNode(l.id+"-note",E),i.setNode(l.id,l)):(i.setNode(l.id,l),i.setNode(l.id+"-note",E)),i.setParent(l.id,l.id+"-group"),i.setParent(l.id+"-note",l.id+"-group")}else i.setNode(l.id,l)}b.debug("Count=",i.nodeCount(),i);let w=0;d.forEach(function(a){w++,b.debug("Setting edge",a),i.setEdge(a.id1,a.id2,{relation:a,width:at(a.title),height:S.labelHeight*z.getRows(a.title).length,labelpos:"c"},"id"+w)}),O(i),b.debug("Graph after layout",i.nodes());let k=n.node();i.nodes().forEach(function(a){a!==void 0&&i.node(a)!==void 0?(b.warn("Node "+a+": "+JSON.stringify(i.node(a))),r.select("#"+k.id+" #"+a).attr("transform","translate("+(i.node(a).x-i.node(a).width/2)+","+(i.node(a).y+(G[a]?G[a].y:0)-i.node(a).height/2)+" )"),r.select("#"+k.id+" #"+a).attr("data-x-shift",i.node(a).x-i.node(a).width/2),g.querySelectorAll("#"+k.id+" #"+a+" .divider").forEach(l=>{let B=l.parentElement,E=0,T=0;B&&(B.parentElement&&(E=B.parentElement.getBBox().width),T=parseInt(B.getAttribute("data-x-shift"),10),Number.isNaN(T)&&(T=0)),l.setAttribute("x1",0-T+8),l.setAttribute("x2",E-T-8)})):b.debug("No Node "+a+": "+JSON.stringify(i.node(a)))});let M=k.getBBox();i.edges().forEach(function(a){a!==void 0&&i.edge(a)!==void 0&&(b.debug("Edge "+a.v+" -> "+a.w+": "+JSON.stringify(i.edge(a))),I(n,i.edge(a),i.edge(a).relation))}),M=k.getBBox();let H={id:s||"root",label:s||"root",width:0,height:0};return H.width=M.width+2*S.padding,H.height=M.height+2*S.padding,b.debug("Doc rendered",H,i),H},"renderDoc"),_={setConf:et,draw:nt};var Ht={parser:X,get db(){return new N(1)},renderer:_,styles:J,init:f(e=>{e.state||(e.state={}),e.state.arrowMarkerAbsolute=e.arrowMarkerAbsolute},"init")};export{Ht as diagram};
