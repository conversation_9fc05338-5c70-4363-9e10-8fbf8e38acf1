(()=>{var a={};a.id=974,a.ids=[974],a.modules={26:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,1170,23)),Promise.resolve().then(c.t.bind(c,3597,23)),Promise.resolve().then(c.t.bind(c,6893,23)),Promise.resolve().then(c.t.bind(c,9748,23)),Promise.resolve().then(c.t.bind(c,6060,23)),Promise.resolve().then(c.t.bind(c,7184,23)),Promise.resolve().then(c.t.bind(c,9576,23)),Promise.resolve().then(c.t.bind(c,3041,23)),Promise.resolve().then(c.t.bind(c,1384,23))},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},339:(a,b,c)=>{Promise.resolve().then(c.bind(c,6451))},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},967:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(7954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Desktop\\\\ellchan\\\\src\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Desktop\\ellchan\\src\\src\\app\\page.tsx","default")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1063:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(9754),e=c(9117),f=c(6595),g=c(2324),h=c(9326),i=c(8928),j=c(175),k=c(12),l=c(4290),m=c(2696),n=c(2802),o=c(7533),p=c(5229),q=c(2822),r=c(261),s=c(6453),t=c(2474),u=c(6713),v=c(1356),w=c(2685),x=c(6225),y=c(3446),z=c(2762),A=c(5742),B=c(6439),C=c(1170),D=c.n(C),E=c(2506),F=c(1203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,967)),"D:\\Desktop\\ellchan\\src\\src\\app\\page.tsx"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.json"}}]},{layout:[()=>Promise.resolve().then(c.bind(c,1472)),"D:\\Desktop\\ellchan\\src\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,1170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.bind(c,9732)),"D:\\Desktop\\ellchan\\src\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,2768,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.json"}}],I=["D:\\Desktop\\ellchan\\src\\src\\app\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(9902).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},1135:()=>{},1162:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(1124),e=c(3991),f=c.n(e);let g=function(){return(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{children:"404 Not Found"}),(0,d.jsx)("p",{children:"There is nothing here..."}),(0,d.jsx)(f(),{href:"/",children:"Return Home"})]})}},1348:(a,b)=>{"use strict";function c(a){return a.split("/").map(a=>encodeURIComponent(a)).join("/")}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"encodeURIPath",{enumerable:!0,get:function(){return c}})},1472:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m,metadata:()=>k,viewport:()=>l});var d=c(5338),e=c(4105);!function(){var a=Error("Cannot find module '@/components/Provider/Theme'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/components/Provider/I18n'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/components/Internal/Debugger'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/components/ui/sonner'");throw a.code="MODULE_NOT_FOUND",a}(),c(1135);let f=process.env.HEAD_SCRIPTS,g="Deep Research",h="Deep Research",i="%s - PWA App",j="Use any LLMs (Large Language Models) for Deep Research.",k={applicationName:g,title:{default:h,template:i},icons:{icon:{type:"image/svg+xml",url:"./logo.svg"}},description:j,appleWebApp:{capable:!0,statusBarStyle:"default",title:h},formatDetection:{telephone:!1},openGraph:{type:"website",siteName:g,title:{default:h,template:i},description:j},twitter:{card:"summary",title:{default:h,template:i},description:j}},l={width:"device-width",initialScale:1,minimumScale:1,maximumScale:1,viewportFit:"cover",userScalable:!1,themeColor:"#FFFFFF"};function m({children:a}){return(0,d.jsxs)("html",{lang:"en",dir:"auto",suppressHydrationWarning:!0,children:[(0,d.jsxs)("head",{children:[f?(0,d.jsx)(e.default,{id:"headscript",children:f}):null,(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/Internal/Debugger'");throw a.code="MODULE_NOT_FOUND",a}()),{})]}),(0,d.jsxs)("body",{className:"antialiased",children:[(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/Provider/Theme'");throw a.code="MODULE_NOT_FOUND",a}()),{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/Provider/I18n'");throw a.code="MODULE_NOT_FOUND",a}()),{children:a})}),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/sonner'");throw a.code="MODULE_NOT_FOUND",a}()),{richColors:!0,toastOptions:{duration:3e3}})]})]})}},2402:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"PreloadChunks",{enumerable:!0,get:function(){return h}});let d=c(1124),e=c(3312),f=c(9294),g=c(1348);function h(a){let{moduleIds:b}=a,c=f.workAsyncStorage.getStore();if(void 0===c)return null;let h=[];if(c.reactLoadableManifest&&b){let a=c.reactLoadableManifest;for(let c of b){if(!a[c])continue;let b=a[c].files;h.push(...b)}}return 0===h.length?null:(0,d.jsx)(d.Fragment,{children:h.map(a=>{let b=c.assetPrefix+"/_next/"+(0,g.encodeURIPath)(a);return a.endsWith(".css")?(0,d.jsx)("link",{precedence:"dynamic",href:b,rel:"stylesheet",as:"style"},a):((0,e.preload)(b,{as:"script",fetchPriority:"low"}),null)})})}},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3197:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4702,23))},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3447:a=>{a.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},3540:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return e}});let d=c(5288)._(c(3972));function e(a,b){var c;let e={};"function"==typeof a&&(e.loader=a);let f={...e,...b};return(0,d.default)({...f,modules:null==(c=f.loadableGenerated)?void 0:c.modules})}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},3972:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return j}});let d=c(1124),e=c(8301),f=c(4207),g=c(2402);function h(a){return{default:a&&"default"in a?a.default:a}}let i={loader:()=>Promise.resolve(h(()=>null)),loading:null,ssr:!0},j=function(a){let b={...i,...a},c=(0,e.lazy)(()=>b.loader().then(h)),j=b.loading;function k(a){let h=j?(0,d.jsx)(j,{isLoading:!0,pastDelay:!0,error:null}):null,i=!b.ssr||!!b.loading,k=i?e.Suspense:e.Fragment,l=b.ssr?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.PreloadChunks,{moduleIds:b.modules}),(0,d.jsx)(c,{...a})]}):(0,d.jsx)(f.BailoutToCSR,{reason:"next/dynamic",children:(0,d.jsx)(c,{...a})});return(0,d.jsx)(k,{...i?{fallback:h}:{},children:l})}return k.displayName="LoadableComponent",k}},4207:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"BailoutToCSR",{enumerable:!0,get:function(){return e}});let d=c(4339);function e(a){let{reason:b,children:c}=a;throw Object.defineProperty(new d.BailoutToCSRError(b),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},5610:(a,b,c)=>{Promise.resolve().then(c.bind(c,1162))},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6451:(a,b,c)=>{"use strict";let d;c.r(b),c.d(b,{default:()=>G});var e=c(1124),f=c(3540),g=c.n(f),h=c(8301);c(3447),Object.create(null);let i={},j=(a,b,c,d)=>{n(c)&&i[c]||(n(c)&&(i[c]=new Date),((a,b,c,d)=>{let e=[c,{code:b,...d||{}}];if(a?.services?.logger?.forward)return a.services.logger.forward(e,"warn","react-i18next::",!0);n(e[0])&&(e[0]=`react-i18next:: ${e[0]}`),a?.services?.logger?.warn?a.services.logger.warn(...e):console?.warn&&console.warn(...e)})(a,b,c,d))},k=(a,b)=>()=>{if(a.isInitialized)b();else{let c=()=>{setTimeout(()=>{a.off("initialized",c)},0),b()};a.on("initialized",c)}},l=(a,b,c)=>{a.loadNamespaces(b,k(a,c))},m=(a,b,c,d)=>{if(n(c)&&(c=[c]),a.options.preload&&a.options.preload.indexOf(b)>-1)return l(a,c,d);c.forEach(b=>{0>a.options.ns.indexOf(b)&&a.options.ns.push(b)}),a.loadLanguages(b,k(a,d))},n=a=>"string"==typeof a,o=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,p={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},q=a=>p[a],r={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:a=>a.replace(o,q)},s=(0,h.createContext)();class t{constructor(){this.usedNamespaces={}}addUsedNamespaces(a){a.forEach(a=>{this.usedNamespaces[a]||(this.usedNamespaces[a]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}let u=(a,b,c,d)=>a.getFixedT(b,c,d);var v=h.createContext(void 0),w={setTheme:a=>{},themes:[]};!function(){var a=Error("Cannot find module '@/store/global'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/store/setting'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/store/task'");throw a.code="MODULE_NOT_FOUND",a}();let x=g()(()=>Promise.resolve().then(function(){var a=Error("Cannot find module '@/components/Internal/Header'");throw a.code="MODULE_NOT_FOUND",a}),{loadableGenerated:{modules:["app\\page.tsx -> @/components/Internal/Header"]}}),y=g()(()=>Promise.resolve().then(function(){var a=Error("Cannot find module '@/components/Setting'");throw a.code="MODULE_NOT_FOUND",a}),{loadableGenerated:{modules:["app\\page.tsx -> @/components/Setting"]}}),z=g()(()=>Promise.resolve().then(function(){var a=Error("Cannot find module '@/components/Research/Topic'");throw a.code="MODULE_NOT_FOUND",a}),{loadableGenerated:{modules:["app\\page.tsx -> @/components/Research/Topic"]}}),A=g()(()=>Promise.resolve().then(function(){var a=Error("Cannot find module '@/components/Research/Feedback'");throw a.code="MODULE_NOT_FOUND",a}),{loadableGenerated:{modules:["app\\page.tsx -> @/components/Research/Feedback"]}}),B=g()(()=>Promise.resolve().then(function(){var a=Error("Cannot find module '@/components/Research/SearchResult'");throw a.code="MODULE_NOT_FOUND",a}),{loadableGenerated:{modules:["app\\page.tsx -> @/components/Research/SearchResult"]}}),C=g()(()=>Promise.resolve().then(function(){var a=Error("Cannot find module '@/components/Research/FinalReport'");throw a.code="MODULE_NOT_FOUND",a}),{loadableGenerated:{modules:["app\\page.tsx -> @/components/Research/FinalReport"]}}),D=g()(()=>Promise.resolve().then(function(){var a=Error("Cannot find module '@/components/History'");throw a.code="MODULE_NOT_FOUND",a}),{loadableGenerated:{modules:["app\\page.tsx -> @/components/History"]}}),E=g()(()=>Promise.resolve().then(function(){var a=Error("Cannot find module '@/components/Knowledge'");throw a.code="MODULE_NOT_FOUND",a}),{loadableGenerated:{modules:["app\\page.tsx -> @/components/Knowledge"]}}),F=g()(async()=>{!function(){var a=Error("Cannot find module '@/components/Research/FinalReport/HtmlPreview'");throw a.code="MODULE_NOT_FOUND",a}()},{loadableGenerated:{modules:["app\\page.tsx -> @/components/Research/FinalReport/HtmlPreview"]},ssr:!1}),G=function(){var a;let{t:b}=((a,b={})=>{let c,e,f,g,{i18n:i}=b,{i18n:k,defaultNS:o}=(0,h.useContext)(s)||{},p=i||k||d;if(p&&!p.reportNamespaces&&(p.reportNamespaces=new t),!p){j(p,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");let a=(a,b)=>{let c;return n(b)?b:"object"==typeof(c=b)&&null!==c&&n(b.defaultValue)?b.defaultValue:Array.isArray(a)?a[a.length-1]:a},b=[a,{},!1];return b.t=a,b.i18n={},b.ready=!1,b}p.options.react?.wait&&j(p,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");let q={...r,...p.options.react,...b},{useSuspense:v,keyPrefix:w}=q,x=o||p.options?.defaultNS;x=n(x)?[x]:x||["translation"],p.reportNamespaces.addUsedNamespaces?.(x);let y=(p.isInitialized||p.initializedStoreOnce)&&x.every(a=>((a,b,c={})=>b.languages&&b.languages.length?b.hasLoadedNamespace(a,{lng:c.lng,precheck:(b,d)=>{if(c.bindI18n&&c.bindI18n.indexOf("languageChanging")>-1&&b.services.backendConnector.backend&&b.isLanguageChangingTo&&!d(b.isLanguageChangingTo,a))return!1}}):(j(b,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:b.languages}),!0))(a,p,q)),z=(c=p,e=b.lng||null,f="fallback"===q.nsMode?x:x[0],g=w,(0,h.useCallback)(u(c,e,f,g),[c,e,f,g])),A=()=>z,B=()=>u(p,b.lng||null,"fallback"===q.nsMode?x:x[0],w),[C,D]=(0,h.useState)(A),E=x.join();b.lng&&(E=`${b.lng}${E}`);let F=((a,b)=>{let c=(0,h.useRef)();return(0,h.useEffect)(()=>{c.current=a},[a,void 0]),c.current})(E),G=(0,h.useRef)(!0);(0,h.useEffect)(()=>{let{bindI18n:a,bindI18nStore:c}=q;G.current=!0,y||v||(b.lng?m(p,b.lng,x,()=>{G.current&&D(B)}):l(p,x,()=>{G.current&&D(B)})),y&&F&&F!==E&&G.current&&D(B);let d=()=>{G.current&&D(B)};return a&&p?.on(a,d),c&&p?.store.on(c,d),()=>{G.current=!1,p&&a&&a?.split(" ").forEach(a=>p.off(a,d)),c&&p&&c.split(" ").forEach(a=>p.store.off(a,d))}},[p,E]),(0,h.useEffect)(()=>{G.current&&y&&D(A)},[p,w,y]);let H=[C,p,y];if(H.t=C,H.i18n=p,H.ready=y,y||!y&&!v)return H;throw new Promise(a=>{b.lng?m(p,b.lng,x,()=>a()):l(p,x,()=>a())})})(),{openSetting:c,setOpenSetting:f,openHistory:g,setOpenHistory:i,openKnowledge:k,setOpenKnowledge:o}=Object(function(){var a=Error("Cannot find module '@/store/global'");throw a.code="MODULE_NOT_FOUND",a}())(),{theme:p}=Object(function(){var a=Error("Cannot find module '@/store/setting'");throw a.code="MODULE_NOT_FOUND",a}())(),{setTheme:q}=null!=(a=h.useContext(v))?a:w,G=Object(function(){var a=Error("Cannot find module '@/store/task'");throw a.code="MODULE_NOT_FOUND",a}())(a=>a.htmlContent);return(0,e.jsxs)("div",{className:"flex",children:[(0,e.jsxs)("div",{className:"main-box max-lg:max-w-screen-md max-w-screen-lg px-4 mx-auto flex-1",children:[(0,e.jsx)(x,{}),(0,e.jsxs)("main",{children:[(0,e.jsx)(z,{}),(0,e.jsx)(A,{}),(0,e.jsx)(B,{}),(0,e.jsx)(C,{})]}),(0,e.jsx)("footer",{className:"my-4 text-center text-sm text-gray-600 print:hidden",children:(0,e.jsx)("a",{href:"https://github.com/u14app/",target:"_blank",children:b("copyright",{name:"Deep Research"})})}),(0,e.jsxs)("aside",{className:"print:hidden",children:[(0,e.jsx)(y,{open:c,onClose:()=>f(!1)}),(0,e.jsx)(D,{open:g,onClose:()=>i(!1)}),(0,e.jsx)(E,{open:k,onClose:()=>o(!1)})]})]}),G&&(0,e.jsx)(F,{})]})}},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},7195:(a,b,c)=>{Promise.resolve().then(c.bind(c,967))},7962:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,7532,23))},8354:a=>{"use strict";a.exports=require("util")},8754:(a,b,c)=>{Promise.resolve().then(c.bind(c,9732))},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9732:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(7954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Desktop\\\\ellchan\\\\src\\\\src\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Desktop\\ellchan\\src\\src\\app\\not-found.tsx","default")},9778:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4160,23)),Promise.resolve().then(c.t.bind(c,1603,23)),Promise.resolve().then(c.t.bind(c,8495,23)),Promise.resolve().then(c.t.bind(c,5170,23)),Promise.resolve().then(c.t.bind(c,7526,23)),Promise.resolve().then(c.t.bind(c,8922,23)),Promise.resolve().then(c.t.bind(c,9234,23)),Promise.resolve().then(c.t.bind(c,2263,23)),Promise.resolve().then(c.bind(c,2146))},9902:a=>{"use strict";a.exports=require("path")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[586,400],()=>b(b.s=1063));module.exports=c})();