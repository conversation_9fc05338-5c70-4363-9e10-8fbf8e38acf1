// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
}


// Validate ..\..\src\app\page.tsx
{
  const handler = {} as typeof import("..\\..\\src\\app\\page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ..\..\src\app\api\ai\anthropic\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\ai\\anthropic\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/ai/anthropic/[...slug]">
}

// Validate ..\..\src\app\api\ai\azure\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\ai\\azure\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/ai/azure/[...slug]">
}

// Validate ..\..\src\app\api\ai\deepseek\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\ai\\deepseek\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/ai/deepseek/[...slug]">
}

// Validate ..\..\src\app\api\ai\google\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\ai\\google\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/ai/google/[...slug]">
}

// Validate ..\..\src\app\api\ai\mistral\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\ai\\mistral\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/ai/mistral/[...slug]">
}

// Validate ..\..\src\app\api\ai\ollama\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\ai\\ollama\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/ai/ollama/[...slug]">
}

// Validate ..\..\src\app\api\ai\openai\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\ai\\openai\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/ai/openai/[...slug]">
}

// Validate ..\..\src\app\api\ai\openaicompatible\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\ai\\openaicompatible\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/ai/openaicompatible/[...slug]">
}

// Validate ..\..\src\app\api\ai\openrouter\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\ai\\openrouter\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/ai/openrouter/[...slug]">
}

// Validate ..\..\src\app\api\ai\pollinations\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\ai\\pollinations\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/ai/pollinations/[...slug]">
}

// Validate ..\..\src\app\api\ai\xai\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\ai\\xai\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/ai/xai/[...slug]">
}

// Validate ..\..\src\app\api\crawler\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\crawler\\route.js")
  handler satisfies RouteHandlerConfig<"/api/crawler">
}

// Validate ..\..\src\app\api\mcp\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\mcp\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/mcp/[...slug]">
}

// Validate ..\..\src\app\api\mcp\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\mcp\\route.js")
  handler satisfies RouteHandlerConfig<"/api/mcp">
}

// Validate ..\..\src\app\api\search\bocha\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\search\\bocha\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/search/bocha/[...slug]">
}

// Validate ..\..\src\app\api\search\exa\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\search\\exa\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/search/exa/[...slug]">
}

// Validate ..\..\src\app\api\search\firecrawl\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\search\\firecrawl\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/search/firecrawl/[...slug]">
}

// Validate ..\..\src\app\api\search\searxng\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\search\\searxng\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/search/searxng/[...slug]">
}

// Validate ..\..\src\app\api\search\tavily\[...slug]\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\search\\tavily\\[...slug]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/search/tavily/[...slug]">
}

// Validate ..\..\src\app\api\sse\live\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\sse\\live\\route.js")
  handler satisfies RouteHandlerConfig<"/api/sse/live">
}

// Validate ..\..\src\app\api\sse\route.ts
{
  const handler = {} as typeof import("..\\..\\src\\app\\api\\sse\\route.js")
  handler satisfies RouteHandlerConfig<"/api/sse">
}





// Validate ..\..\src\app\layout.tsx
{
  const handler = {} as typeof import("..\\..\\src\\app\\layout.js")
  handler satisfies LayoutConfig<"/">
}
