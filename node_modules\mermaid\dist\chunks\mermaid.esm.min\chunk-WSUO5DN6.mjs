import{a,b as i,c as n,d as u,e as m,f as e,g as d,h as s,p as l,q as c}from"./chunk-L6MQJ2ZU.mjs";import{a as o}from"./chunk-GTKDMUJJ.mjs";var v=class extends c{static{o(this,"InfoTokenBuilder")}static{e(this,"InfoTokenBuilder")}constructor(){super(["info","showInfo"])}},I={parser:{TokenBuilder:e(()=>new v,"TokenBuilder"),ValueConverter:e(()=>new l,"ValueConverter")}};function M(f=u){let r=n(i(f),d),t=n(a({shared:r}),s,I);return r.ServiceRegistry.register(t),{shared:r,Info:t}}o(M,"createInfoServices");e(M,"createInfoServices");export{I as a,M as b};
