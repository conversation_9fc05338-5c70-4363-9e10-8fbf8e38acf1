import{Y as c}from"./chunk-F632ZYSZ.mjs";import{a as i}from"./chunk-GTKDMUJJ.mjs";var u=i(t=>{let{handDrawnSeed:s}=c();return{fill:t,hachureAngle:120,hachureGap:4,fillWeight:2,roughness:.7,stroke:t,seed:s}},"solidStateFill"),p=i(t=>{let s=h([...t.cssCompiledStyles||[],...t.cssStyles||[]]);return{stylesMap:s,stylesArray:[...s]}},"compileStyles"),h=i(t=>{let s=new Map;return t.forEach(o=>{let[n,r]=o.split(":");s.set(n.trim(),r?.trim())}),s},"styles2Map"),g=i(t=>t==="color"||t==="font-size"||t==="font-family"||t==="font-weight"||t==="font-style"||t==="text-decoration"||t==="text-align"||t==="text-transform"||t==="line-height"||t==="letter-spacing"||t==="word-spacing"||t==="text-shadow"||t==="text-overflow"||t==="white-space"||t==="word-wrap"||t==="word-break"||t==="overflow-wrap"||t==="hyphens","isLabelStyle"),m=i(t=>{let{stylesArray:s}=p(t),o=[],n=[],r=[],l=[];return s.forEach(e=>{let a=e[0];g(a)?o.push(e.join(":")+" !important"):(n.push(e.join(":")+" !important"),a.includes("stroke")&&r.push(e.join(":")+" !important"),a==="fill"&&l.push(e.join(":")+" !important"))}),{labelStyles:o.join(";"),nodeStyles:n.join(";"),stylesArray:s,borderStyles:r,backgroundStyles:l}},"styles2String"),S=i((t,s)=>{let{themeVariables:o,handDrawnSeed:n}=c(),{nodeBorder:r,mainBkg:l}=o,{stylesMap:e}=p(t);return Object.assign({roughness:.7,fill:e.get("fill")||l,fillStyle:"hachure",fillWeight:4,hachureGap:5.2,stroke:e.get("stroke")||r,seed:n,strokeWidth:e.get("stroke-width")?.replace("px","")||1.3,fillLineDash:[0,0],strokeLineDash:d(e.get("stroke-dasharray"))},s)},"userNodeOverrides"),d=i(t=>{if(!t)return[0,0];let s=t.trim().split(/\s+/).map(Number);if(s.length===1){let r=isNaN(s[0])?0:s[0];return[r,r]}let o=isNaN(s[0])?0:s[0],n=isNaN(s[1])?0:s[1];return[o,n]},"getStrokeDashArray");export{u as a,p as b,g as c,m as d,S as e};
