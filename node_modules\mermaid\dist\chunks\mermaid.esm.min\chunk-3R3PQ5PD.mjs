import{a as at}from"./chunk-TI4EEUUG.mjs";import{$a as K,F as v,Ga as R,H as m,Ja as F,K as L,Ka as O,La as _,Ma as k,Na as N,Oa as j,Pa as A,Qa as H,Ra as z,Sa as I,Ta as U,Ua as X,Va as Y,Wa as J,Xa as V,Ya as q,Za as G,_a as Z,a as w,c as d,ha as D,m as B}from"./chunk-F632ZYSZ.mjs";import{T as Q,k as b}from"./chunk-6BY5RJGC.mjs";import{a,e as st}from"./chunk-GTKDMUJJ.mjs";var nt=st(at(),1);var ct="\u200B",ut={curveBasis:_,curveBasisClosed:k,curveBasisOpen:N,curveBumpX:F,curveBumpY:O,curveBundle:j,curveCardinalClosed:H,curveCardinalOpen:z,curveCardinal:A,curveCatmullRomClosed:U,curveCatmullRomOpen:X,curveCatmullRom:I,curveLinear:R,curveLinearClosed:Y,curveMonotoneX:J,curveMonotoneY:V,curveNatural:q,curveStep:G,curveStepAfter:K,curveStepBefore:Z},lt=/\s*(?:(\w+)(?=:):|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,ft=a(function(e,t){let r=rt(e,/(?:init\b)|(?:initialize\b)/),n={};if(Array.isArray(r)){let s=r.map(u=>u.args);B(s),n=w(n,[...s])}else n=r.args;if(!n)return;let i=L(e,t),o="config";return n[o]!==void 0&&(i==="flowchart-v2"&&(i="flowchart"),n[i]=n[o],delete n[o]),n},"detectInit"),rt=a(function(e,t=null){try{let r=new RegExp(`[%]{2}(?![{]${lt.source})(?=[}][%]{2}).*
`,"ig");e=e.trim().replace(r,"").replace(/'/gm,'"'),d.debug(`Detecting diagram directive${t!==null?" type:"+t:""} based on the text:${e}`);let n,i=[];for(;(n=m.exec(e))!==null;)if(n.index===m.lastIndex&&m.lastIndex++,n&&!t||t&&n[1]?.match(t)||t&&n[2]?.match(t)){let o=n[1]?n[1]:n[2],s=n[3]?n[3].trim():n[4]?JSON.parse(n[4].trim()):null;i.push({type:o,args:s})}return i.length===0?{type:e,args:null}:i.length===1?i[0]:i}catch(r){return d.error(`ERROR: ${r.message} - Unable to parse directive type: '${t}' based on the text: '${e}'`),{type:void 0,args:null}}},"detectDirective"),zt=a(function(e){return e.replace(m,"")},"removeDirectives"),gt=a(function(e,t){for(let[r,n]of t.entries())if(n.match(e))return r;return-1},"isSubstringInArray");function dt(e,t){if(!e)return t;let r=`curve${e.charAt(0).toUpperCase()+e.slice(1)}`;return ut[r]??t}a(dt,"interpolateToCurve");function ht(e,t){let r=e.trim();if(r)return t.securityLevel!=="loose"?(0,nt.sanitizeUrl)(r):r}a(ht,"formatUrl");var mt=a((e,...t)=>{let r=e.split("."),n=r.length-1,i=r[n],o=window;for(let s=0;s<n;s++)if(o=o[r[s]],!o){d.error(`Function name: ${e} not found in window`);return}o[i](...t)},"runFunc");function it(e,t){return!e||!t?0:Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}a(it,"distance");function pt(e){let t,r=0;e.forEach(i=>{r+=it(i,t),t=i});let n=r/2;return T(e,n)}a(pt,"traverseEdge");function xt(e){return e.length===1?e[0]:pt(e)}a(xt,"calcLabelPosition");var tt=a((e,t=2)=>{let r=Math.pow(10,t);return Math.round(e*r)/r},"roundNumber"),T=a((e,t)=>{let r,n=t;for(let i of e){if(r){let o=it(i,r);if(o===0)return r;if(o<n)n-=o;else{let s=n/o;if(s<=0)return r;if(s>=1)return{x:i.x,y:i.y};if(s>0&&s<1)return{x:tt((1-s)*r.x+s*i.x,5),y:tt((1-s)*r.y+s*i.y,5)}}}r=i}throw new Error("Could not find a suitable point for the given distance")},"calculatePoint"),yt=a((e,t,r)=>{d.info(`our points ${JSON.stringify(t)}`),t[0]!==r&&(t=t.reverse());let i=T(t,25),o=e?10:5,s=Math.atan2(t[0].y-i.y,t[0].x-i.x),u={x:0,y:0};return u.x=Math.sin(s)*o+(t[0].x+i.x)/2,u.y=-Math.cos(s)*o+(t[0].y+i.y)/2,u},"calcCardinalityPosition");function vt(e,t,r){let n=structuredClone(r);d.info("our points",n),t!=="start_left"&&t!=="start_right"&&n.reverse();let i=25+e,o=T(n,i),s=10+e*.5,u=Math.atan2(n[0].y-o.y,n[0].x-o.x),c={x:0,y:0};return t==="start_left"?(c.x=Math.sin(u+Math.PI)*s+(n[0].x+o.x)/2,c.y=-Math.cos(u+Math.PI)*s+(n[0].y+o.y)/2):t==="end_right"?(c.x=Math.sin(u-Math.PI)*s+(n[0].x+o.x)/2-5,c.y=-Math.cos(u-Math.PI)*s+(n[0].y+o.y)/2-5):t==="end_left"?(c.x=Math.sin(u)*s+(n[0].x+o.x)/2-5,c.y=-Math.cos(u)*s+(n[0].y+o.y)/2-5):(c.x=Math.sin(u)*s+(n[0].x+o.x)/2,c.y=-Math.cos(u)*s+(n[0].y+o.y)/2),c}a(vt,"calcTerminalLabelPosition");function bt(e){let t="",r="";for(let n of e)n!==void 0&&(n.startsWith("color:")||n.startsWith("text-align:")?r=r+n+";":t=t+n+";");return{style:t,labelStyle:r}}a(bt,"getStylesFromArray");var et=0,Ct=a(()=>(et++,"id-"+Math.random().toString(36).substr(2,12)+"-"+et),"generateId");function Mt(e){let t="",r="0123456789abcdef",n=r.length;for(let i=0;i<e;i++)t+=r.charAt(Math.floor(Math.random()*n));return t}a(Mt,"makeRandomHex");var wt=a(e=>Mt(e.length),"random"),Pt=a(function(){return{x:0,y:0,fill:void 0,anchor:"start",style:"#666",width:100,height:100,textMargin:0,rx:0,ry:0,valign:void 0,text:""}},"getTextObj"),Tt=a(function(e,t){let r=t.text.replace(v.lineBreakRegex," "),[,n]=$(t.fontSize),i=e.append("text");i.attr("x",t.x),i.attr("y",t.y),i.style("text-anchor",t.anchor),i.style("font-family",t.fontFamily),i.style("font-size",n),i.style("font-weight",t.fontWeight),i.attr("fill",t.fill),t.class!==void 0&&i.attr("class",t.class);let o=i.append("tspan");return o.attr("x",t.x+t.textMargin*2),o.attr("fill",t.fill),o.text(r),i},"drawSimpleText"),St=b((e,t,r)=>{if(!e||(r=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",joinWith:"<br/>"},r),v.lineBreakRegex.test(e)))return e;let n=e.split(" ").filter(Boolean),i=[],o="";return n.forEach((s,u)=>{let c=M(`${s} `,r),l=M(o,r);if(c>t){let{hyphenatedStrings:h,remainingWord:f}=$t(s,t,"-",r);i.push(o,...h),o=f}else l+c>=t?(i.push(o),o=s):o=[o,s].filter(Boolean).join(" ");u+1===n.length&&i.push(o)}),i.filter(s=>s!=="").join(r.joinWith)},(e,t,r)=>`${e}${t}${r.fontSize}${r.fontWeight}${r.fontFamily}${r.joinWith}`),$t=b((e,t,r="-",n)=>{n=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",margin:0},n);let i=[...e],o=[],s="";return i.forEach((u,c)=>{let l=`${s}${u}`;if(M(l,n)>=t){let x=c+1,h=i.length===x,f=`${l}${r}`;o.push(h?l:f),s=""}else s=l}),{hyphenatedStrings:o,remainingWord:s}},(e,t,r="-",n)=>`${e}${t}${r}${n.fontSize}${n.fontWeight}${n.fontFamily}`);function Wt(e,t){return S(e,t).height}a(Wt,"calculateTextHeight");function M(e,t){return S(e,t).width}a(M,"calculateTextWidth");var S=b((e,t)=>{let{fontSize:r=12,fontFamily:n="Arial",fontWeight:i=400}=t;if(!e)return{width:0,height:0};let[,o]=$(r),s=["sans-serif",n],u=e.split(v.lineBreakRegex),c=[],l=D("body");if(!l.remove)return{width:0,height:0,lineHeight:0};let p=l.append("svg");for(let h of s){let f=0,g={width:0,height:0,lineHeight:0};for(let ot of u){let W=Pt();W.text=ot||ct;let E=Tt(p,W).style("font-size",o).style("font-weight",i).style("font-family",h),y=(E._groups||E)[0][0].getBBox();if(y.width===0&&y.height===0)throw new Error("svg element not in render tree");g.width=Math.round(Math.max(g.width,y.width)),f=Math.round(y.height),g.height+=f,g.lineHeight=Math.round(Math.max(g.lineHeight,f))}c.push(g)}p.remove();let x=isNaN(c[1].height)||isNaN(c[1].width)||isNaN(c[1].lineHeight)||c[0].height>c[1].height&&c[0].width>c[1].width&&c[0].lineHeight>c[1].lineHeight?0:1;return c[x]},(e,t)=>`${e}${t.fontSize}${t.fontWeight}${t.fontFamily}`),P=class{constructor(t=!1,r){this.count=0;this.count=r?r.length:0,this.next=t?()=>this.count++:()=>Date.now()}static{a(this,"InitIDGenerator")}},C,Et=a(function(e){return C=C||document.createElement("div"),e=escape(e).replace(/%26/g,"&").replace(/%23/g,"#").replace(/%3B/g,";"),C.innerHTML=e,unescape(C.textContent)},"entityDecode");function It(e){return"str"in e}a(It,"isDetailedError");var Bt=a((e,t,r,n)=>{if(!n)return;let i=e.node()?.getBBox();i&&e.append("text").text(n).attr("text-anchor","middle").attr("x",i.x+i.width/2).attr("y",-r).attr("class",t)},"insertTitle"),$=a(e=>{if(typeof e=="number")return[e,e+"px"];let t=parseInt(e??"",10);return Number.isNaN(t)?[void 0,void 0]:e===String(t)?[t,e+"px"]:[t,e]},"parseFontSize");function Lt(e,t){return Q({},e,t)}a(Lt,"cleanAndMerge");var Ut={assignWithDepth:w,wrapLabel:St,calculateTextHeight:Wt,calculateTextWidth:M,calculateTextDimensions:S,cleanAndMerge:Lt,detectInit:ft,detectDirective:rt,isSubstringInArray:gt,interpolateToCurve:dt,calcLabelPosition:xt,calcCardinalityPosition:yt,calcTerminalLabelPosition:vt,formatUrl:ht,getStylesFromArray:bt,generateId:Ct,random:wt,runFunc:mt,entityDecode:Et,insertTitle:Bt,isLabelCoordinateInPath:Dt,parseFontSize:$,InitIDGenerator:P},Xt=a(function(e){let t=e;return t=t.replace(/style.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/classDef.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/#\w+;/g,function(r){let n=r.substring(1,r.length-1);return/^\+?\d+$/.test(n)?"\uFB02\xB0\xB0"+n+"\xB6\xDF":"\uFB02\xB0"+n+"\xB6\xDF"}),t},"encodeEntities"),Yt=a(function(e){return e.replace(/ﬂ°°/g,"&#").replace(/ﬂ°/g,"&").replace(/¶ß/g,";")},"decodeEntities");var Jt=a((e,t,{counter:r=0,prefix:n,suffix:i},o)=>o||`${n?`${n}_`:""}${e}_${t}_${r}${i?`_${i}`:""}`,"getEdgeId");function Vt(e){return e??null}a(Vt,"handleUndefinedAttr");function Dt(e,t){let r=Math.round(e.x),n=Math.round(e.y),i=t.replace(/(\d+\.\d+)/g,o=>Math.round(parseFloat(o)).toString());return i.includes(r.toString())||i.includes(n.toString())}a(Dt,"isLabelCoordinateInPath");export{ct as a,zt as b,dt as c,bt as d,Ct as e,wt as f,St as g,Wt as h,M as i,It as j,$ as k,Lt as l,Ut as m,Xt as n,Yt as o,Jt as p,Vt as q};
