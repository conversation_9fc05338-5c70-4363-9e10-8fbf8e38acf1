import{a as Ht}from"./chunk-ZN7TASNU.mjs";import{a as jt}from"./chunk-ZZTKAOFA.mjs";import{a as Pt}from"./chunk-S67DUUA5.mjs";import{a as Wt}from"./chunk-LM6QDVU5.mjs";import{f as Ft,g as lt}from"./chunk-IXVBHSNP.mjs";import{d as Mt,m as tt,o as rt}from"./chunk-3R3PQ5PD.mjs";import"./chunk-TI4EEUUG.mjs";import{A as P,F as It,Ha as zt,La as At,N as Ot,Q as Rt,Y as B,c as w,e as Tt,f as Ct,ha as C,t as q,y as et,z as Nt}from"./chunk-F632ZYSZ.mjs";import{e as Yt}from"./chunk-5ZJXQJOJ.mjs";import"./chunk-YPUTD6PB.mjs";import"./chunk-6BY5RJGC.mjs";import{a as c}from"./chunk-GTKDMUJJ.mjs";var yt=function(){var e=c(function(T,y,d,f){for(d=d||{},f=T.length;f--;d[T[f]]=y);return d},"o"),t=[1,15],a=[1,7],i=[1,13],l=[1,14],s=[1,19],r=[1,16],n=[1,17],o=[1,18],u=[8,30],h=[8,10,21,28,29,30,31,39,43,46],x=[1,23],m=[1,24],b=[8,10,15,16,21,28,29,30,31,39,43,46],L=[8,10,15,16,21,27,28,29,30,31,39,43,46],_=[1,49],S={trace:c(function(){},"trace"),yy:{},symbols_:{error:2,spaceLines:3,SPACELINE:4,NL:5,separator:6,SPACE:7,EOF:8,start:9,BLOCK_DIAGRAM_KEY:10,document:11,stop:12,statement:13,link:14,LINK:15,START_LINK:16,LINK_LABEL:17,STR:18,nodeStatement:19,columnsStatement:20,SPACE_BLOCK:21,blockStatement:22,classDefStatement:23,cssClassStatement:24,styleStatement:25,node:26,SIZE:27,COLUMNS:28,"id-block":29,end:30,NODE_ID:31,nodeShapeNLabel:32,dirList:33,DIR:34,NODE_DSTART:35,NODE_DEND:36,BLOCK_ARROW_START:37,BLOCK_ARROW_END:38,classDef:39,CLASSDEF_ID:40,CLASSDEF_STYLEOPTS:41,DEFAULT:42,class:43,CLASSENTITY_IDS:44,STYLECLASS:45,style:46,STYLE_ENTITY_IDS:47,STYLE_DEFINITION_DATA:48,$accept:0,$end:1},terminals_:{2:"error",4:"SPACELINE",5:"NL",7:"SPACE",8:"EOF",10:"BLOCK_DIAGRAM_KEY",15:"LINK",16:"START_LINK",17:"LINK_LABEL",18:"STR",21:"SPACE_BLOCK",27:"SIZE",28:"COLUMNS",29:"id-block",30:"end",31:"NODE_ID",34:"DIR",35:"NODE_DSTART",36:"NODE_DEND",37:"BLOCK_ARROW_START",38:"BLOCK_ARROW_END",39:"classDef",40:"CLASSDEF_ID",41:"CLASSDEF_STYLEOPTS",42:"DEFAULT",43:"class",44:"CLASSENTITY_IDS",45:"STYLECLASS",46:"style",47:"STYLE_ENTITY_IDS",48:"STYLE_DEFINITION_DATA"},productions_:[0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[33,1],[33,2],[32,3],[32,4],[23,3],[23,3],[24,3],[25,3]],performAction:c(function(y,d,f,k,E,g,Y){var p=g.length-1;switch(E){case 4:k.getLogger().debug("Rule: separator (NL) ");break;case 5:k.getLogger().debug("Rule: separator (Space) ");break;case 6:k.getLogger().debug("Rule: separator (EOF) ");break;case 7:k.getLogger().debug("Rule: hierarchy: ",g[p-1]),k.setHierarchy(g[p-1]);break;case 8:k.getLogger().debug("Stop NL ");break;case 9:k.getLogger().debug("Stop EOF ");break;case 10:k.getLogger().debug("Stop NL2 ");break;case 11:k.getLogger().debug("Stop EOF2 ");break;case 12:k.getLogger().debug("Rule: statement: ",g[p]),typeof g[p].length=="number"?this.$=g[p]:this.$=[g[p]];break;case 13:k.getLogger().debug("Rule: statement #2: ",g[p-1]),this.$=[g[p-1]].concat(g[p]);break;case 14:k.getLogger().debug("Rule: link: ",g[p],y),this.$={edgeTypeStr:g[p],label:""};break;case 15:k.getLogger().debug("Rule: LABEL link: ",g[p-3],g[p-1],g[p]),this.$={edgeTypeStr:g[p],label:g[p-1]};break;case 18:let R=parseInt(g[p]),Z=k.generateId();this.$={id:Z,type:"space",label:"",width:R,children:[]};break;case 23:k.getLogger().debug("Rule: (nodeStatement link node) ",g[p-2],g[p-1],g[p]," typestr: ",g[p-1].edgeTypeStr);let V=k.edgeStrToEdgeData(g[p-1].edgeTypeStr);this.$=[{id:g[p-2].id,label:g[p-2].label,type:g[p-2].type,directions:g[p-2].directions},{id:g[p-2].id+"-"+g[p].id,start:g[p-2].id,end:g[p].id,label:g[p-1].label,type:"edge",directions:g[p].directions,arrowTypeEnd:V,arrowTypeStart:"arrow_open"},{id:g[p].id,label:g[p].label,type:k.typeStr2Type(g[p].typeStr),directions:g[p].directions}];break;case 24:k.getLogger().debug("Rule: nodeStatement (abc88 node size) ",g[p-1],g[p]),this.$={id:g[p-1].id,label:g[p-1].label,type:k.typeStr2Type(g[p-1].typeStr),directions:g[p-1].directions,widthInColumns:parseInt(g[p],10)};break;case 25:k.getLogger().debug("Rule: nodeStatement (node) ",g[p]),this.$={id:g[p].id,label:g[p].label,type:k.typeStr2Type(g[p].typeStr),directions:g[p].directions,widthInColumns:1};break;case 26:k.getLogger().debug("APA123",this?this:"na"),k.getLogger().debug("COLUMNS: ",g[p]),this.$={type:"column-setting",columns:g[p]==="auto"?-1:parseInt(g[p])};break;case 27:k.getLogger().debug("Rule: id-block statement : ",g[p-2],g[p-1]);let Bt=k.generateId();this.$={...g[p-2],type:"composite",children:g[p-1]};break;case 28:k.getLogger().debug("Rule: blockStatement : ",g[p-2],g[p-1],g[p]);let st=k.generateId();this.$={id:st,type:"composite",label:"",children:g[p-1]};break;case 29:k.getLogger().debug("Rule: node (NODE_ID separator): ",g[p]),this.$={id:g[p]};break;case 30:k.getLogger().debug("Rule: node (NODE_ID nodeShapeNLabel separator): ",g[p-1],g[p]),this.$={id:g[p-1],label:g[p].label,typeStr:g[p].typeStr,directions:g[p].directions};break;case 31:k.getLogger().debug("Rule: dirList: ",g[p]),this.$=[g[p]];break;case 32:k.getLogger().debug("Rule: dirList: ",g[p-1],g[p]),this.$=[g[p-1]].concat(g[p]);break;case 33:k.getLogger().debug("Rule: nodeShapeNLabel: ",g[p-2],g[p-1],g[p]),this.$={typeStr:g[p-2]+g[p],label:g[p-1]};break;case 34:k.getLogger().debug("Rule: BLOCK_ARROW nodeShapeNLabel: ",g[p-3],g[p-2]," #3:",g[p-1],g[p]),this.$={typeStr:g[p-3]+g[p],label:g[p-2],directions:g[p-1]};break;case 35:case 36:this.$={type:"classDef",id:g[p-1].trim(),css:g[p].trim()};break;case 37:this.$={type:"applyClass",id:g[p-1].trim(),styleClass:g[p].trim()};break;case 38:this.$={type:"applyStyles",id:g[p-1].trim(),stylesStr:g[p].trim()};break}},"anonymous"),table:[{9:1,10:[1,2]},{1:[3]},{10:t,11:3,13:4,19:5,20:6,21:a,22:8,23:9,24:10,25:11,26:12,28:i,29:l,31:s,39:r,43:n,46:o},{8:[1,20]},e(u,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,10:t,21:a,28:i,29:l,31:s,39:r,43:n,46:o}),e(h,[2,16],{14:22,15:x,16:m}),e(h,[2,17]),e(h,[2,18]),e(h,[2,19]),e(h,[2,20]),e(h,[2,21]),e(h,[2,22]),e(b,[2,25],{27:[1,25]}),e(h,[2,26]),{19:26,26:12,31:s},{10:t,11:27,13:4,19:5,20:6,21:a,22:8,23:9,24:10,25:11,26:12,28:i,29:l,31:s,39:r,43:n,46:o},{40:[1,28],42:[1,29]},{44:[1,30]},{47:[1,31]},e(L,[2,29],{32:32,35:[1,33],37:[1,34]}),{1:[2,7]},e(u,[2,13]),{26:35,31:s},{31:[2,14]},{17:[1,36]},e(b,[2,24]),{10:t,11:37,13:4,14:22,15:x,16:m,19:5,20:6,21:a,22:8,23:9,24:10,25:11,26:12,28:i,29:l,31:s,39:r,43:n,46:o},{30:[1,38]},{41:[1,39]},{41:[1,40]},{45:[1,41]},{48:[1,42]},e(L,[2,30]),{18:[1,43]},{18:[1,44]},e(b,[2,23]),{18:[1,45]},{30:[1,46]},e(h,[2,28]),e(h,[2,35]),e(h,[2,36]),e(h,[2,37]),e(h,[2,38]),{36:[1,47]},{33:48,34:_},{15:[1,50]},e(h,[2,27]),e(L,[2,33]),{38:[1,51]},{33:52,34:_,38:[2,31]},{31:[2,15]},e(L,[2,34]),{38:[2,32]}],defaultActions:{20:[2,7],23:[2,14],50:[2,15],52:[2,32]},parseError:c(function(y,d){if(d.recoverable)this.trace(y);else{var f=new Error(y);throw f.hash=d,f}},"parseError"),parse:c(function(y){var d=this,f=[0],k=[],E=[null],g=[],Y=this.table,p="",R=0,Z=0,V=0,Bt=2,st=1,Be=g.slice.call(arguments,1),A=Object.create(this.lexer),J={yy:{}};for(var ut in this.yy)Object.prototype.hasOwnProperty.call(this.yy,ut)&&(J.yy[ut]=this.yy[ut]);A.setInput(y,J.yy),J.yy.lexer=A,J.yy.parser=this,typeof A.yylloc>"u"&&(A.yylloc={});var pt=A.yylloc;g.push(pt);var ve=A.options&&A.options.ranges;typeof J.yy.parseError=="function"?this.parseError=J.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function qr(H){f.length=f.length-2*H,E.length=E.length-H,g.length=g.length-H}c(qr,"popStack");function Te(){var H;return H=k.pop()||A.lex()||st,typeof H!="number"&&(H instanceof Array&&(k=H,H=k.pop()),H=d.symbols_[H]||H),H}c(Te,"lex");for(var F,ft,Q,K,Jr,xt,$={},it,G,vt,nt;;){if(Q=f[f.length-1],this.defaultActions[Q]?K=this.defaultActions[Q]:((F===null||typeof F>"u")&&(F=Te()),K=Y[Q]&&Y[Q][F]),typeof K>"u"||!K.length||!K[0]){var bt="";nt=[];for(it in Y[Q])this.terminals_[it]&&it>Bt&&nt.push("'"+this.terminals_[it]+"'");A.showPosition?bt="Parse error on line "+(R+1)+`:
`+A.showPosition()+`
Expecting `+nt.join(", ")+", got '"+(this.terminals_[F]||F)+"'":bt="Parse error on line "+(R+1)+": Unexpected "+(F==st?"end of input":"'"+(this.terminals_[F]||F)+"'"),this.parseError(bt,{text:A.match,token:this.terminals_[F]||F,line:A.yylineno,loc:pt,expected:nt})}if(K[0]instanceof Array&&K.length>1)throw new Error("Parse Error: multiple actions possible at state: "+Q+", token: "+F);switch(K[0]){case 1:f.push(F),E.push(A.yytext),g.push(A.yylloc),f.push(K[1]),F=null,ft?(F=ft,ft=null):(Z=A.yyleng,p=A.yytext,R=A.yylineno,pt=A.yylloc,V>0&&V--);break;case 2:if(G=this.productions_[K[1]][1],$.$=E[E.length-G],$._$={first_line:g[g.length-(G||1)].first_line,last_line:g[g.length-1].last_line,first_column:g[g.length-(G||1)].first_column,last_column:g[g.length-1].last_column},ve&&($._$.range=[g[g.length-(G||1)].range[0],g[g.length-1].range[1]]),xt=this.performAction.apply($,[p,Z,R,J.yy,K[1],E,g].concat(Be)),typeof xt<"u")return xt;G&&(f=f.slice(0,-1*G*2),E=E.slice(0,-1*G),g=g.slice(0,-1*G)),f.push(this.productions_[K[1]][0]),E.push($.$),g.push($._$),vt=Y[f[f.length-2]][f[f.length-1]],f.push(vt);break;case 3:return!0}}return!0},"parse")},I=function(){var T={EOF:1,parseError:c(function(d,f){if(this.yy.parser)this.yy.parser.parseError(d,f);else throw new Error(d)},"parseError"),setInput:c(function(y,d){return this.yy=d||this.yy||{},this._input=y,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:c(function(){var y=this._input[0];this.yytext+=y,this.yyleng++,this.offset++,this.match+=y,this.matched+=y;var d=y.match(/(?:\r\n?|\n).*/g);return d?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),y},"input"),unput:c(function(y){var d=y.length,f=y.split(/(?:\r\n?|\n)/g);this._input=y+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-d),this.offset-=d;var k=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),f.length-1&&(this.yylineno-=f.length-1);var E=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:f?(f.length===k.length?this.yylloc.first_column:0)+k[k.length-f.length].length-f[0].length:this.yylloc.first_column-d},this.options.ranges&&(this.yylloc.range=[E[0],E[0]+this.yyleng-d]),this.yyleng=this.yytext.length,this},"unput"),more:c(function(){return this._more=!0,this},"more"),reject:c(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:c(function(y){this.unput(this.match.slice(y))},"less"),pastInput:c(function(){var y=this.matched.substr(0,this.matched.length-this.match.length);return(y.length>20?"...":"")+y.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:c(function(){var y=this.match;return y.length<20&&(y+=this._input.substr(0,20-y.length)),(y.substr(0,20)+(y.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:c(function(){var y=this.pastInput(),d=new Array(y.length+1).join("-");return y+this.upcomingInput()+`
`+d+"^"},"showPosition"),test_match:c(function(y,d){var f,k,E;if(this.options.backtrack_lexer&&(E={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(E.yylloc.range=this.yylloc.range.slice(0))),k=y[0].match(/(?:\r\n?|\n).*/g),k&&(this.yylineno+=k.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:k?k[k.length-1].length-k[k.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+y[0].length},this.yytext+=y[0],this.match+=y[0],this.matches=y,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(y[0].length),this.matched+=y[0],f=this.performAction.call(this,this.yy,this,d,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),f)return f;if(this._backtrack){for(var g in E)this[g]=E[g];return!1}return!1},"test_match"),next:c(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var y,d,f,k;this._more||(this.yytext="",this.match="");for(var E=this._currentRules(),g=0;g<E.length;g++)if(f=this._input.match(this.rules[E[g]]),f&&(!d||f[0].length>d[0].length)){if(d=f,k=g,this.options.backtrack_lexer){if(y=this.test_match(f,E[g]),y!==!1)return y;if(this._backtrack){d=!1;continue}else return!1}else if(!this.options.flex)break}return d?(y=this.test_match(d,E[k]),y!==!1?y:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:c(function(){var d=this.next();return d||this.lex()},"lex"),begin:c(function(d){this.conditionStack.push(d)},"begin"),popState:c(function(){var d=this.conditionStack.length-1;return d>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:c(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:c(function(d){return d=this.conditionStack.length-1-Math.abs(d||0),d>=0?this.conditionStack[d]:"INITIAL"},"topState"),pushState:c(function(d){this.begin(d)},"pushState"),stateStackSize:c(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:c(function(d,f,k,E){var g=E;switch(k){case 0:return d.getLogger().debug("Found block-beta"),10;break;case 1:return d.getLogger().debug("Found id-block"),29;break;case 2:return d.getLogger().debug("Found block"),10;break;case 3:d.getLogger().debug(".",f.yytext);break;case 4:d.getLogger().debug("_",f.yytext);break;case 5:return 5;case 6:return f.yytext=-1,28;break;case 7:return f.yytext=f.yytext.replace(/columns\s+/,""),d.getLogger().debug("COLUMNS (LEX)",f.yytext),28;break;case 8:this.pushState("md_string");break;case 9:return"MD_STR";case 10:this.popState();break;case 11:this.pushState("string");break;case 12:d.getLogger().debug("LEX: POPPING STR:",f.yytext),this.popState();break;case 13:return d.getLogger().debug("LEX: STR end:",f.yytext),"STR";break;case 14:return f.yytext=f.yytext.replace(/space\:/,""),d.getLogger().debug("SPACE NUM (LEX)",f.yytext),21;break;case 15:return f.yytext="1",d.getLogger().debug("COLUMNS (LEX)",f.yytext),21;break;case 16:return 42;case 17:return"LINKSTYLE";case 18:return"INTERPOLATE";case 19:return this.pushState("CLASSDEF"),39;break;case 20:return this.popState(),this.pushState("CLASSDEFID"),"DEFAULT_CLASSDEF_ID";break;case 21:return this.popState(),this.pushState("CLASSDEFID"),40;break;case 22:return this.popState(),41;break;case 23:return this.pushState("CLASS"),43;break;case 24:return this.popState(),this.pushState("CLASS_STYLE"),44;break;case 25:return this.popState(),45;break;case 26:return this.pushState("STYLE_STMNT"),46;break;case 27:return this.popState(),this.pushState("STYLE_DEFINITION"),47;break;case 28:return this.popState(),48;break;case 29:return this.pushState("acc_title"),"acc_title";break;case 30:return this.popState(),"acc_title_value";break;case 31:return this.pushState("acc_descr"),"acc_descr";break;case 32:return this.popState(),"acc_descr_value";break;case 33:this.pushState("acc_descr_multiline");break;case 34:this.popState();break;case 35:return"acc_descr_multiline_value";case 36:return 30;case 37:return this.popState(),d.getLogger().debug("Lex: (("),"NODE_DEND";break;case 38:return this.popState(),d.getLogger().debug("Lex: (("),"NODE_DEND";break;case 39:return this.popState(),d.getLogger().debug("Lex: ))"),"NODE_DEND";break;case 40:return this.popState(),d.getLogger().debug("Lex: (("),"NODE_DEND";break;case 41:return this.popState(),d.getLogger().debug("Lex: (("),"NODE_DEND";break;case 42:return this.popState(),d.getLogger().debug("Lex: (-"),"NODE_DEND";break;case 43:return this.popState(),d.getLogger().debug("Lex: -)"),"NODE_DEND";break;case 44:return this.popState(),d.getLogger().debug("Lex: (("),"NODE_DEND";break;case 45:return this.popState(),d.getLogger().debug("Lex: ]]"),"NODE_DEND";break;case 46:return this.popState(),d.getLogger().debug("Lex: ("),"NODE_DEND";break;case 47:return this.popState(),d.getLogger().debug("Lex: ])"),"NODE_DEND";break;case 48:return this.popState(),d.getLogger().debug("Lex: /]"),"NODE_DEND";break;case 49:return this.popState(),d.getLogger().debug("Lex: /]"),"NODE_DEND";break;case 50:return this.popState(),d.getLogger().debug("Lex: )]"),"NODE_DEND";break;case 51:return this.popState(),d.getLogger().debug("Lex: )"),"NODE_DEND";break;case 52:return this.popState(),d.getLogger().debug("Lex: ]>"),"NODE_DEND";break;case 53:return this.popState(),d.getLogger().debug("Lex: ]"),"NODE_DEND";break;case 54:return d.getLogger().debug("Lexa: -)"),this.pushState("NODE"),35;break;case 55:return d.getLogger().debug("Lexa: (-"),this.pushState("NODE"),35;break;case 56:return d.getLogger().debug("Lexa: ))"),this.pushState("NODE"),35;break;case 57:return d.getLogger().debug("Lexa: )"),this.pushState("NODE"),35;break;case 58:return d.getLogger().debug("Lex: ((("),this.pushState("NODE"),35;break;case 59:return d.getLogger().debug("Lexa: )"),this.pushState("NODE"),35;break;case 60:return d.getLogger().debug("Lexa: )"),this.pushState("NODE"),35;break;case 61:return d.getLogger().debug("Lexa: )"),this.pushState("NODE"),35;break;case 62:return d.getLogger().debug("Lexc: >"),this.pushState("NODE"),35;break;case 63:return d.getLogger().debug("Lexa: (["),this.pushState("NODE"),35;break;case 64:return d.getLogger().debug("Lexa: )"),this.pushState("NODE"),35;break;case 65:return this.pushState("NODE"),35;break;case 66:return this.pushState("NODE"),35;break;case 67:return this.pushState("NODE"),35;break;case 68:return this.pushState("NODE"),35;break;case 69:return this.pushState("NODE"),35;break;case 70:return this.pushState("NODE"),35;break;case 71:return this.pushState("NODE"),35;break;case 72:return d.getLogger().debug("Lexa: ["),this.pushState("NODE"),35;break;case 73:return this.pushState("BLOCK_ARROW"),d.getLogger().debug("LEX ARR START"),37;break;case 74:return d.getLogger().debug("Lex: NODE_ID",f.yytext),31;break;case 75:return d.getLogger().debug("Lex: EOF",f.yytext),8;break;case 76:this.pushState("md_string");break;case 77:this.pushState("md_string");break;case 78:return"NODE_DESCR";case 79:this.popState();break;case 80:d.getLogger().debug("Lex: Starting string"),this.pushState("string");break;case 81:d.getLogger().debug("LEX ARR: Starting string"),this.pushState("string");break;case 82:return d.getLogger().debug("LEX: NODE_DESCR:",f.yytext),"NODE_DESCR";break;case 83:d.getLogger().debug("LEX POPPING"),this.popState();break;case 84:d.getLogger().debug("Lex: =>BAE"),this.pushState("ARROW_DIR");break;case 85:return f.yytext=f.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (right): dir:",f.yytext),"DIR";break;case 86:return f.yytext=f.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (left):",f.yytext),"DIR";break;case 87:return f.yytext=f.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (x):",f.yytext),"DIR";break;case 88:return f.yytext=f.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (y):",f.yytext),"DIR";break;case 89:return f.yytext=f.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (up):",f.yytext),"DIR";break;case 90:return f.yytext=f.yytext.replace(/^,\s*/,""),d.getLogger().debug("Lex (down):",f.yytext),"DIR";break;case 91:return f.yytext="]>",d.getLogger().debug("Lex (ARROW_DIR end):",f.yytext),this.popState(),this.popState(),"BLOCK_ARROW_END";break;case 92:return d.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;break;case 93:return d.getLogger().debug("Lex: LINK",f.yytext),15;break;case 94:return d.getLogger().debug("Lex: LINK",f.yytext),15;break;case 95:return d.getLogger().debug("Lex: LINK",f.yytext),15;break;case 96:return d.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;break;case 97:return d.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;break;case 98:return d.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;break;case 99:this.pushState("md_string");break;case 100:return d.getLogger().debug("Lex: Starting string"),this.pushState("string"),"LINK_LABEL";break;case 101:return this.popState(),d.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;break;case 102:return this.popState(),d.getLogger().debug("Lex: LINK",f.yytext),15;break;case 103:return this.popState(),d.getLogger().debug("Lex: LINK",f.yytext),15;break;case 104:return d.getLogger().debug("Lex: COLON",f.yytext),f.yytext=f.yytext.slice(1),27;break}},"anonymous"),rules:[/^(?:block-beta\b)/,/^(?:block:)/,/^(?:block\b)/,/^(?:[\s]+)/,/^(?:[\n]+)/,/^(?:((\u000D\u000A)|(\u000A)))/,/^(?:columns\s+auto\b)/,/^(?:columns\s+[\d]+)/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:space[:]\d+)/,/^(?:space\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\s+)/,/^(?:DEFAULT\s+)/,/^(?:\w+\s+)/,/^(?:[^\n]*)/,/^(?:class\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:style\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:end\b\s*)/,/^(?:\(\(\()/,/^(?:\)\)\))/,/^(?:[\)]\))/,/^(?:\}\})/,/^(?:\})/,/^(?:\(-)/,/^(?:-\))/,/^(?:\(\()/,/^(?:\]\])/,/^(?:\()/,/^(?:\]\))/,/^(?:\\\])/,/^(?:\/\])/,/^(?:\)\])/,/^(?:[\)])/,/^(?:\]>)/,/^(?:[\]])/,/^(?:-\))/,/^(?:\(-)/,/^(?:\)\))/,/^(?:\))/,/^(?:\(\(\()/,/^(?:\(\()/,/^(?:\{\{)/,/^(?:\{)/,/^(?:>)/,/^(?:\(\[)/,/^(?:\()/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\[\\)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:\[)/,/^(?:<\[)/,/^(?:[^\(\[\n\-\)\{\}\s\<\>:]+)/,/^(?:$)/,/^(?:["][`])/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:\]>\s*\()/,/^(?:,?\s*right\s*)/,/^(?:,?\s*left\s*)/,/^(?:,?\s*x\s*)/,/^(?:,?\s*y\s*)/,/^(?:,?\s*up\s*)/,/^(?:,?\s*down\s*)/,/^(?:\)\s*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*~~[\~]+\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:["][`])/,/^(?:["])/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?::\d+)/],conditions:{STYLE_DEFINITION:{rules:[28],inclusive:!1},STYLE_STMNT:{rules:[27],inclusive:!1},CLASSDEFID:{rules:[22],inclusive:!1},CLASSDEF:{rules:[20,21],inclusive:!1},CLASS_STYLE:{rules:[25],inclusive:!1},CLASS:{rules:[24],inclusive:!1},LLABEL:{rules:[99,100,101,102,103],inclusive:!1},ARROW_DIR:{rules:[85,86,87,88,89,90,91],inclusive:!1},BLOCK_ARROW:{rules:[76,81,84],inclusive:!1},NODE:{rules:[37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,77,80],inclusive:!1},md_string:{rules:[9,10,78,79],inclusive:!1},space:{rules:[],inclusive:!1},string:{rules:[12,13,82,83],inclusive:!1},acc_descr_multiline:{rules:[34,35],inclusive:!1},acc_descr:{rules:[32],inclusive:!1},acc_title:{rules:[30],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,11,14,15,16,17,18,19,23,26,29,31,33,36,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,92,93,94,95,96,97,98,104],inclusive:!0}}};return T}();S.lexer=I;function D(){this.yy={}}return c(D,"Parser"),D.prototype=S,S.Parser=D,new D}();yt.parser=yt;var Kt=yt;var X=new Map,wt=[],mt=new Map,Xt="color",Ut="fill",Ne="bgFill",Gt=",",Ie=B(),ot=new Map,Oe=c(e=>It.sanitizeText(e,Ie),"sanitizeText"),Re=c(function(e,t=""){let a=ot.get(e);a||(a={id:e,styles:[],textStyles:[]},ot.set(e,a)),t?.split(Gt).forEach(i=>{let l=i.replace(/([^;]*);/,"$1").trim();if(RegExp(Xt).exec(i)){let r=l.replace(Ut,Ne).replace(Xt,Ut);a.textStyles.push(r)}a.styles.push(l)})},"addStyleClass"),ze=c(function(e,t=""){let a=X.get(e);t!=null&&(a.styles=t.split(Gt))},"addStyle2Node"),Ae=c(function(e,t){e.split(",").forEach(function(a){let i=X.get(a);if(i===void 0){let l=a.trim();i={id:l,type:"na",children:[]},X.set(l,i)}i.classes||(i.classes=[]),i.classes.push(t)})},"setCssClass"),Zt=c((e,t)=>{let a=e.flat(),i=[],s=a.find(r=>r?.type==="column-setting")?.columns??-1;for(let r of a){if(typeof s=="number"&&s>0&&r.type!=="column-setting"&&typeof r.widthInColumns=="number"&&r.widthInColumns>s&&w.warn(`Block ${r.id} width ${r.widthInColumns} exceeds configured column width ${s}`),r.label&&(r.label=Oe(r.label)),r.type==="classDef"){Re(r.id,r.css);continue}if(r.type==="applyClass"){Ae(r.id,r?.styleClass??"");continue}if(r.type==="applyStyles"){r?.stylesStr&&ze(r.id,r?.stylesStr);continue}if(r.type==="column-setting")t.columns=r.columns??-1;else if(r.type==="edge"){let n=(mt.get(r.id)??0)+1;mt.set(r.id,n),r.id=n+"-"+r.id,wt.push(r)}else{r.label||(r.type==="composite"?r.label="":r.label=r.id);let n=X.get(r.id);if(n===void 0?X.set(r.id,r):(r.type!=="na"&&(n.type=r.type),r.label!==r.id&&(n.label=r.label)),r.children&&Zt(r.children,r),r.type==="space"){let o=r.width??1;for(let u=0;u<o;u++){let h=Yt(r);h.id=h.id+"-"+u,X.set(h.id,h),i.push(h)}}else n===void 0&&i.push(r)}}t.children=i},"populateBlockDatabase"),kt=[],at={id:"root",type:"composite",children:[],columns:-1},Me=c(()=>{w.debug("Clear called"),Rt(),at={id:"root",type:"composite",children:[],columns:-1},X=new Map([["root",at]]),kt=[],ot=new Map,wt=[],mt=new Map},"clear");function Fe(e){switch(w.debug("typeStr2Type",e),e){case"[]":return"square";case"()":return w.debug("we have a round"),"round";case"(())":return"circle";case">]":return"rect_left_inv_arrow";case"{}":return"diamond";case"{{}}":return"hexagon";case"([])":return"stadium";case"[[]]":return"subroutine";case"[()]":return"cylinder";case"((()))":return"doublecircle";case"[//]":return"lean_right";case"[\\\\]":return"lean_left";case"[/\\]":return"trapezoid";case"[\\/]":return"inv_trapezoid";case"<[]>":return"block_arrow";default:return"na"}}c(Fe,"typeStr2Type");function We(e){switch(w.debug("typeStr2Type",e),e){case"==":return"thick";default:return"normal"}}c(We,"edgeTypeStr2Type");function Pe(e){switch(e.replace(/^[\s-]+|[\s-]+$/g,"")){case"x":return"arrow_cross";case"o":return"arrow_circle";case">":return"arrow_point";default:return""}}c(Pe,"edgeStrToEdgeData");var Vt=0,Ye=c(()=>(Vt++,"id-"+Math.random().toString(36).substr(2,12)+"-"+Vt),"generateId"),He=c(e=>{at.children=e,Zt(e,at),kt=at.children},"setHierarchy"),je=c(e=>{let t=X.get(e);return t?t.columns?t.columns:t.children?t.children.length:-1:-1},"getColumns"),Ke=c(()=>[...X.values()],"getBlocksFlat"),Xe=c(()=>kt||[],"getBlocks"),Ue=c(()=>wt,"getEdges"),Ve=c(e=>X.get(e),"getBlock"),Ge=c(e=>{X.set(e.id,e)},"setBlock"),Ze=c(()=>w,"getLogger"),qe=c(function(){return ot},"getClasses"),Je={getConfig:c(()=>q().block,"getConfig"),typeStr2Type:Fe,edgeTypeStr2Type:We,edgeStrToEdgeData:Pe,getLogger:Ze,getBlocksFlat:Ke,getBlocks:Xe,getEdges:Ue,setHierarchy:He,getBlock:Ve,setBlock:Ge,getColumns:je,getClasses:qe,clear:Me,generateId:Ye},qt=Je;var ct=c((e,t)=>{let a=Ct,i=a(e,"r"),l=a(e,"g"),s=a(e,"b");return Tt(i,l,s,t)},"fade"),Qe=c(e=>`.label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }
  .cluster-label text {
    fill: ${e.titleColor};
  }
  .cluster-label span,p {
    color: ${e.titleColor};
  }



  .label text,span,p {
    fill: ${e.nodeTextColor||e.textColor};
    color: ${e.nodeTextColor||e.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }
  .flowchart-label text {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }

  .arrowheadPath {
    fill: ${e.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${e.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${e.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${e.edgeLabelBackground};
    rect {
      opacity: 0.5;
      background-color: ${e.edgeLabelBackground};
      fill: ${e.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${ct(e.edgeLabelBackground,.5)};
    // background-color:
  }

  .node .cluster {
    // fill: ${ct(e.mainBkg,.5)};
    fill: ${ct(e.clusterBkg,.5)};
    stroke: ${ct(e.clusterBorder,.2)};
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${e.titleColor};
  }

  .cluster span,p {
    color: ${e.titleColor};
  }
  /* .cluster div {
    color: ${e.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${e.fontFamily};
    font-size: 12px;
    background: ${e.tertiaryColor};
    border: 1px solid ${e.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${e.textColor};
  }
  ${jt()}
`,"getStyles"),Jt=Qe;var $e=c((e,t,a,i)=>{t.forEach(l=>{cr[l](e,a,i)})},"insertMarkers"),tr=c((e,t,a)=>{w.trace("Making markers for ",a),e.append("defs").append("marker").attr("id",a+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),er=c((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),rr=c((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),ar=c((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),sr=c((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",a+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),ir=c((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",6).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),nr=c((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),lr=c((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),or=c((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","strokeWidth").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),cr={extension:tr,composition:er,aggregation:rr,dependency:ar,lollipop:sr,point:ir,circle:nr,cross:lr,barb:or},Qt=$e;var O=B()?.block?.padding??8;function hr(e,t){if(e===0||!Number.isInteger(e))throw new Error("Columns must be an integer !== 0.");if(t<0||!Number.isInteger(t))throw new Error("Position must be a non-negative integer."+t);if(e<0)return{px:t,py:0};if(e===1)return{px:0,py:t};let a=t%e,i=Math.floor(t/e);return{px:a,py:i}}c(hr,"calculateBlockPosition");var gr=c(e=>{let t=0,a=0;for(let i of e.children){let{width:l,height:s,x:r,y:n}=i.size??{width:0,height:0,x:0,y:0};w.debug("getMaxChildSize abc95 child:",i.id,"width:",l,"height:",s,"x:",r,"y:",n,i.type),i.type!=="space"&&(l>t&&(t=l/(e.widthInColumns??1)),s>a&&(a=s))}return{width:t,height:a}},"getMaxChildSize");function Lt(e,t,a=0,i=0){w.debug("setBlockSizes abc95 (start)",e.id,e?.size?.x,"block width =",e?.size,"siblingWidth",a),e?.size?.width||(e.size={width:a,height:i,x:0,y:0});let l=0,s=0;if(e.children?.length>0){for(let b of e.children)Lt(b,t);let r=gr(e);l=r.width,s=r.height,w.debug("setBlockSizes abc95 maxWidth of",e.id,":s children is ",l,s);for(let b of e.children)b.size&&(w.debug(`abc95 Setting size of children of ${e.id} id=${b.id} ${l} ${s} ${JSON.stringify(b.size)}`),b.size.width=l*(b.widthInColumns??1)+O*((b.widthInColumns??1)-1),b.size.height=s,b.size.x=0,b.size.y=0,w.debug(`abc95 updating size of ${e.id} children child:${b.id} maxWidth:${l} maxHeight:${s}`));for(let b of e.children)Lt(b,t,l,s);let n=e.columns??-1,o=0;for(let b of e.children)o+=b.widthInColumns??1;let u=e.children.length;n>0&&n<o&&(u=n);let h=Math.ceil(o/u),x=u*(l+O)+O,m=h*(s+O)+O;if(x<a){w.debug(`Detected to small sibling: abc95 ${e.id} siblingWidth ${a} siblingHeight ${i} width ${x}`),x=a,m=i;let b=(a-u*O-O)/u,L=(i-h*O-O)/h;w.debug("Size indata abc88",e.id,"childWidth",b,"maxWidth",l),w.debug("Size indata abc88",e.id,"childHeight",L,"maxHeight",s),w.debug("Size indata abc88 xSize",u,"padding",O);for(let _ of e.children)_.size&&(_.size.width=b,_.size.height=L,_.size.x=0,_.size.y=0)}if(w.debug(`abc95 (finale calc) ${e.id} xSize ${u} ySize ${h} columns ${n}${e.children.length} width=${Math.max(x,e.size?.width||0)}`),x<(e?.size?.width||0)){x=e?.size?.width||0;let b=n>0?Math.min(e.children.length,n):e.children.length;if(b>0){let L=(x-b*O-O)/b;w.debug("abc95 (growing to fit) width",e.id,x,e.size?.width,L);for(let _ of e.children)_.size&&(_.size.width=L)}}e.size={width:x,height:m,x:0,y:0}}w.debug("setBlockSizes abc94 (done)",e.id,e?.size?.x,e?.size?.width,e?.size?.y,e?.size?.height)}c(Lt,"setBlockSizes");function $t(e,t){w.debug(`abc85 layout blocks (=>layoutBlocks) ${e.id} x: ${e?.size?.x} y: ${e?.size?.y} width: ${e?.size?.width}`);let a=e.columns??-1;if(w.debug("layoutBlocks columns abc95",e.id,"=>",a,e),e.children&&e.children.length>0){let i=e?.children[0]?.size?.width??0,l=e.children.length*i+(e.children.length-1)*O;w.debug("widthOfChildren 88",l,"posX");let s=0;w.debug("abc91 block?.size?.x",e.id,e?.size?.x);let r=e?.size?.x?e?.size?.x+(-e?.size?.width/2||0):-O,n=0;for(let o of e.children){let u=e;if(!o.size)continue;let{width:h,height:x}=o.size,{px:m,py:b}=hr(a,s);if(b!=n&&(n=b,r=e?.size?.x?e?.size?.x+(-e?.size?.width/2||0):-O,w.debug("New row in layout for block",e.id," and child ",o.id,n)),w.debug(`abc89 layout blocks (child) id: ${o.id} Pos: ${s} (px, py) ${m},${b} (${u?.size?.x},${u?.size?.y}) parent: ${u.id} width: ${h}${O}`),u.size){let _=h/2;o.size.x=r+O+_,w.debug(`abc91 layout blocks (calc) px, pyid:${o.id} startingPos=X${r} new startingPosX${o.size.x} ${_} padding=${O} width=${h} halfWidth=${_} => x:${o.size.x} y:${o.size.y} ${o.widthInColumns} (width * (child?.w || 1)) / 2 ${h*(o?.widthInColumns??1)/2}`),r=o.size.x+_,o.size.y=u.size.y-u.size.height/2+b*(x+O)+x/2+O,w.debug(`abc88 layout blocks (calc) px, pyid:${o.id}startingPosX${r}${O}${_}=>x:${o.size.x}y:${o.size.y}${o.widthInColumns}(width * (child?.w || 1)) / 2${h*(o?.widthInColumns??1)/2}`)}o.children&&$t(o,t);let L=o?.widthInColumns??1;a>0&&(L=Math.min(L,a-s%a)),s+=L,w.debug("abc88 columnsPos",o,s)}}w.debug(`layout blocks (<==layoutBlocks) ${e.id} x: ${e?.size?.x} y: ${e?.size?.y} width: ${e?.size?.width}`)}c($t,"layoutBlocks");function te(e,{minX:t,minY:a,maxX:i,maxY:l}={minX:0,minY:0,maxX:0,maxY:0}){if(e.size&&e.id!=="root"){let{x:s,y:r,width:n,height:o}=e.size;s-n/2<t&&(t=s-n/2),r-o/2<a&&(a=r-o/2),s+n/2>i&&(i=s+n/2),r+o/2>l&&(l=r+o/2)}if(e.children)for(let s of e.children)({minX:t,minY:a,maxX:i,maxY:l}=te(s,{minX:t,minY:a,maxX:i,maxY:l}));return{minX:t,minY:a,maxX:i,maxY:l}}c(te,"findBounds");function ee(e){let t=e.getBlock("root");if(!t)return;Lt(t,e,0,0),$t(t,e),w.debug("getBlocks",JSON.stringify(t,null,2));let{minX:a,minY:i,maxX:l,maxY:s}=te(t),r=s-i,n=l-a;return{x:a,y:i,width:n,height:r}}c(ee,"layout");function re(e,t){t&&e.attr("style",t)}c(re,"applyStyle");function dr(e,t){let a=C(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),i=a.append("xhtml:div"),l=e.label,s=e.isNode?"nodeLabel":"edgeLabel",r=i.append("span");return r.html(et(l,t)),re(r,e.labelStyle),r.attr("class",s),re(i,e.labelStyle),i.style("display","inline-block"),i.style("white-space","nowrap"),i.attr("xmlns","http://www.w3.org/1999/xhtml"),a.node()}c(dr,"addHtmlLabel");var ur=c(async(e,t,a,i)=>{let l=e||"";typeof l=="object"&&(l=l[0]);let s=B();if(P(s.flowchart.htmlLabels)){l=l.replace(/\\n|\n/g,"<br />"),w.debug("vertexText"+l);let r=await Ft(rt(l)),n={isNode:i,label:r,labelStyle:t.replace("fill:","color:")};return dr(n,s)}else{let r=document.createElementNS("http://www.w3.org/2000/svg","text");r.setAttribute("style",t.replace("color:","fill:"));let n=[];typeof l=="string"?n=l.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(l)?n=l:n=[];for(let o of n){let u=document.createElementNS("http://www.w3.org/2000/svg","tspan");u.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),u.setAttribute("dy","1em"),u.setAttribute("x","0"),a?u.setAttribute("class","title-row"):u.setAttribute("class","row"),u.textContent=o.trim(),r.appendChild(u)}return r}},"createLabel"),W=ur;var se=c((e,t,a,i,l)=>{t.arrowTypeStart&&ae(e,"start",t.arrowTypeStart,a,i,l),t.arrowTypeEnd&&ae(e,"end",t.arrowTypeEnd,a,i,l)},"addEdgeMarkers"),pr={arrow_cross:"cross",arrow_point:"point",arrow_barb:"barb",arrow_circle:"circle",aggregation:"aggregation",extension:"extension",composition:"composition",dependency:"dependency",lollipop:"lollipop"},ae=c((e,t,a,i,l,s)=>{let r=pr[a];if(!r){w.warn(`Unknown arrow type: ${a}`);return}let n=t==="start"?"Start":"End";e.attr(`marker-${t}`,`url(${i}#${l}_${s}-${r}${n})`)},"addEdgeMarker");var St={},M={};var ne=c(async(e,t)=>{let a=B(),i=P(a.flowchart.htmlLabels),l=t.labelType==="markdown"?lt(e,t.label,{style:t.labelStyle,useHtmlLabels:i,addSvgBackground:!0},a):await W(t.label,t.labelStyle),s=e.insert("g").attr("class","edgeLabel"),r=s.insert("g").attr("class","label");r.node().appendChild(l);let n=l.getBBox();if(i){let u=l.children[0],h=C(l);n=u.getBoundingClientRect(),h.attr("width",n.width),h.attr("height",n.height)}r.attr("transform","translate("+-n.width/2+", "+-n.height/2+")"),St[t.id]=s,t.width=n.width,t.height=n.height;let o;if(t.startLabelLeft){let u=await W(t.startLabelLeft,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");o=x.node().appendChild(u);let m=u.getBBox();x.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),M[t.id]||(M[t.id]={}),M[t.id].startLeft=h,ht(o,t.startLabelLeft)}if(t.startLabelRight){let u=await W(t.startLabelRight,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");o=h.node().appendChild(u),x.node().appendChild(u);let m=u.getBBox();x.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),M[t.id]||(M[t.id]={}),M[t.id].startRight=h,ht(o,t.startLabelRight)}if(t.endLabelLeft){let u=await W(t.endLabelLeft,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");o=x.node().appendChild(u);let m=u.getBBox();x.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),h.node().appendChild(u),M[t.id]||(M[t.id]={}),M[t.id].endLeft=h,ht(o,t.endLabelLeft)}if(t.endLabelRight){let u=await W(t.endLabelRight,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");o=x.node().appendChild(u);let m=u.getBBox();x.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),h.node().appendChild(u),M[t.id]||(M[t.id]={}),M[t.id].endRight=h,ht(o,t.endLabelRight)}return l},"insertEdgeLabel");function ht(e,t){B().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}c(ht,"setTerminalWidth");var le=c((e,t)=>{w.debug("Moving label abc88 ",e.id,e.label,St[e.id],t);let a=t.updatedPath?t.updatedPath:t.originalPath,i=B(),{subGraphTitleTotalMargin:l}=Wt(i);if(e.label){let s=St[e.id],r=e.x,n=e.y;if(a){let o=tt.calcLabelPosition(a);w.debug("Moving label "+e.label+" from (",r,",",n,") to (",o.x,",",o.y,") abc88"),t.updatedPath&&(r=o.x,n=o.y)}s.attr("transform",`translate(${r}, ${n+l/2})`)}if(e.startLabelLeft){let s=M[e.id].startLeft,r=e.x,n=e.y;if(a){let o=tt.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",a);r=o.x,n=o.y}s.attr("transform",`translate(${r}, ${n})`)}if(e.startLabelRight){let s=M[e.id].startRight,r=e.x,n=e.y;if(a){let o=tt.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",a);r=o.x,n=o.y}s.attr("transform",`translate(${r}, ${n})`)}if(e.endLabelLeft){let s=M[e.id].endLeft,r=e.x,n=e.y;if(a){let o=tt.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",a);r=o.x,n=o.y}s.attr("transform",`translate(${r}, ${n})`)}if(e.endLabelRight){let s=M[e.id].endRight,r=e.x,n=e.y;if(a){let o=tt.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",a);r=o.x,n=o.y}s.attr("transform",`translate(${r}, ${n})`)}},"positionEdgeLabel"),fr=c((e,t)=>{let a=e.x,i=e.y,l=Math.abs(t.x-a),s=Math.abs(t.y-i),r=e.width/2,n=e.height/2;return l>=r||s>=n},"outsideNode"),xr=c((e,t,a)=>{w.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(a)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);let i=e.x,l=e.y,s=Math.abs(i-a.x),r=e.width/2,n=a.x<t.x?r-s:r+s,o=e.height/2,u=Math.abs(t.y-a.y),h=Math.abs(t.x-a.x);if(Math.abs(l-t.y)*r>Math.abs(i-t.x)*o){let x=a.y<t.y?t.y-o-l:l-o-t.y;n=h*x/u;let m={x:a.x<t.x?a.x+n:a.x-h+n,y:a.y<t.y?a.y+u-x:a.y-u+x};return n===0&&(m.x=t.x,m.y=t.y),h===0&&(m.x=t.x),u===0&&(m.y=t.y),w.debug(`abc89 topp/bott calc, Q ${u}, q ${x}, R ${h}, r ${n}`,m),m}else{a.x<t.x?n=t.x-r-i:n=i-r-t.x;let x=u*n/h,m=a.x<t.x?a.x+h-n:a.x-h+n,b=a.y<t.y?a.y+x:a.y-x;return w.debug(`sides calc abc89, Q ${u}, q ${x}, R ${h}, r ${n}`,{_x:m,_y:b}),n===0&&(m=t.x,b=t.y),h===0&&(m=t.x),u===0&&(b=t.y),{x:m,y:b}}},"intersection"),ie=c((e,t)=>{w.debug("abc88 cutPathAtIntersect",e,t);let a=[],i=e[0],l=!1;return e.forEach(s=>{if(!fr(t,s)&&!l){let r=xr(t,i,s),n=!1;a.forEach(o=>{n=n||o.x===r.x&&o.y===r.y}),a.some(o=>o.x===r.x&&o.y===r.y)||a.push(r),l=!0}else i=s,l||a.push(s)}),a},"cutPathAtIntersect"),oe=c(function(e,t,a,i,l,s,r){let n=a.points;w.debug("abc88 InsertEdge: edge=",a,"e=",t);let o=!1,u=s.node(t.v);var h=s.node(t.w);h?.intersect&&u?.intersect&&(n=n.slice(1,a.points.length-1),n.unshift(u.intersect(n[0])),n.push(h.intersect(n[n.length-1]))),a.toCluster&&(w.debug("to cluster abc88",i[a.toCluster]),n=ie(a.points,i[a.toCluster].node),o=!0),a.fromCluster&&(w.debug("from cluster abc88",i[a.fromCluster]),n=ie(n.reverse(),i[a.fromCluster].node).reverse(),o=!0);let x=n.filter(y=>!Number.isNaN(y.y)),m=At;a.curve&&(l==="graph"||l==="flowchart")&&(m=a.curve);let{x:b,y:L}=Pt(a),_=zt().x(b).y(L).curve(m),S;switch(a.thickness){case"normal":S="edge-thickness-normal";break;case"thick":S="edge-thickness-thick";break;case"invisible":S="edge-thickness-thick";break;default:S=""}switch(a.pattern){case"solid":S+=" edge-pattern-solid";break;case"dotted":S+=" edge-pattern-dotted";break;case"dashed":S+=" edge-pattern-dashed";break}let I=e.append("path").attr("d",_(x)).attr("id",a.id).attr("class"," "+S+(a.classes?" "+a.classes:"")).attr("style",a.style),D="";(B().flowchart.arrowMarkerAbsolute||B().state.arrowMarkerAbsolute)&&(D=Nt(!0)),se(I,a,D,r,l);let T={};return o&&(T.updatedPath=n),T.originalPath=a.points,T},"insertEdge");var br=c(e=>{let t=new Set;for(let a of e)switch(a){case"x":t.add("right"),t.add("left");break;case"y":t.add("up"),t.add("down");break;default:t.add(a);break}return t},"expandAndDeduplicateDirections"),ce=c((e,t,a)=>{let i=br(e),l=2,s=t.height+2*a.padding,r=s/l,n=t.width+2*r+a.padding,o=a.padding/2;return i.has("right")&&i.has("left")&&i.has("up")&&i.has("down")?[{x:0,y:0},{x:r,y:0},{x:n/2,y:2*o},{x:n-r,y:0},{x:n,y:0},{x:n,y:-s/3},{x:n+2*o,y:-s/2},{x:n,y:-2*s/3},{x:n,y:-s},{x:n-r,y:-s},{x:n/2,y:-s-2*o},{x:r,y:-s},{x:0,y:-s},{x:0,y:-2*s/3},{x:-2*o,y:-s/2},{x:0,y:-s/3}]:i.has("right")&&i.has("left")&&i.has("up")?[{x:r,y:0},{x:n-r,y:0},{x:n,y:-s/2},{x:n-r,y:-s},{x:r,y:-s},{x:0,y:-s/2}]:i.has("right")&&i.has("left")&&i.has("down")?[{x:0,y:0},{x:r,y:-s},{x:n-r,y:-s},{x:n,y:0}]:i.has("right")&&i.has("up")&&i.has("down")?[{x:0,y:0},{x:n,y:-r},{x:n,y:-s+r},{x:0,y:-s}]:i.has("left")&&i.has("up")&&i.has("down")?[{x:n,y:0},{x:0,y:-r},{x:0,y:-s+r},{x:n,y:-s}]:i.has("right")&&i.has("left")?[{x:r,y:0},{x:r,y:-o},{x:n-r,y:-o},{x:n-r,y:0},{x:n,y:-s/2},{x:n-r,y:-s},{x:n-r,y:-s+o},{x:r,y:-s+o},{x:r,y:-s},{x:0,y:-s/2}]:i.has("up")&&i.has("down")?[{x:n/2,y:0},{x:0,y:-o},{x:r,y:-o},{x:r,y:-s+o},{x:0,y:-s+o},{x:n/2,y:-s},{x:n,y:-s+o},{x:n-r,y:-s+o},{x:n-r,y:-o},{x:n,y:-o}]:i.has("right")&&i.has("up")?[{x:0,y:0},{x:n,y:-r},{x:0,y:-s}]:i.has("right")&&i.has("down")?[{x:0,y:0},{x:n,y:0},{x:0,y:-s}]:i.has("left")&&i.has("up")?[{x:n,y:0},{x:0,y:-r},{x:n,y:-s}]:i.has("left")&&i.has("down")?[{x:n,y:0},{x:0,y:0},{x:n,y:-s}]:i.has("right")?[{x:r,y:-o},{x:r,y:-o},{x:n-r,y:-o},{x:n-r,y:0},{x:n,y:-s/2},{x:n-r,y:-s},{x:n-r,y:-s+o},{x:r,y:-s+o},{x:r,y:-s+o}]:i.has("left")?[{x:r,y:0},{x:r,y:-o},{x:n-r,y:-o},{x:n-r,y:-s+o},{x:r,y:-s+o},{x:r,y:-s},{x:0,y:-s/2}]:i.has("up")?[{x:r,y:-o},{x:r,y:-s+o},{x:0,y:-s+o},{x:n/2,y:-s},{x:n,y:-s+o},{x:n-r,y:-s+o},{x:n-r,y:-o}]:i.has("down")?[{x:n/2,y:0},{x:0,y:-o},{x:r,y:-o},{x:r,y:-s+o},{x:n-r,y:-s+o},{x:n-r,y:-o},{x:n,y:-o}]:[{x:0,y:0}]},"getArrowPoints");function yr(e,t){return e.intersect(t)}c(yr,"intersectNode");var he=yr;function mr(e,t,a,i){var l=e.x,s=e.y,r=l-i.x,n=s-i.y,o=Math.sqrt(t*t*n*n+a*a*r*r),u=Math.abs(t*a*r/o);i.x<l&&(u=-u);var h=Math.abs(t*a*n/o);return i.y<s&&(h=-h),{x:l+u,y:s+h}}c(mr,"intersectEllipse");var gt=mr;function wr(e,t,a){return gt(e,t,t,a)}c(wr,"intersectCircle");var ge=wr;function kr(e,t,a,i){var l,s,r,n,o,u,h,x,m,b,L,_,S,I,D;if(l=t.y-e.y,r=e.x-t.x,o=t.x*e.y-e.x*t.y,m=l*a.x+r*a.y+o,b=l*i.x+r*i.y+o,!(m!==0&&b!==0&&de(m,b))&&(s=i.y-a.y,n=a.x-i.x,u=i.x*a.y-a.x*i.y,h=s*e.x+n*e.y+u,x=s*t.x+n*t.y+u,!(h!==0&&x!==0&&de(h,x))&&(L=l*n-s*r,L!==0)))return _=Math.abs(L/2),S=r*u-n*o,I=S<0?(S-_)/L:(S+_)/L,S=s*o-l*u,D=S<0?(S-_)/L:(S+_)/L,{x:I,y:D}}c(kr,"intersectLine");function de(e,t){return e*t>0}c(de,"sameSign");var ue=kr;var pe=Lr;function Lr(e,t,a){var i=e.x,l=e.y,s=[],r=Number.POSITIVE_INFINITY,n=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(L){r=Math.min(r,L.x),n=Math.min(n,L.y)}):(r=Math.min(r,t.x),n=Math.min(n,t.y));for(var o=i-e.width/2-r,u=l-e.height/2-n,h=0;h<t.length;h++){var x=t[h],m=t[h<t.length-1?h+1:0],b=ue(e,a,{x:o+x.x,y:u+x.y},{x:o+m.x,y:u+m.y});b&&s.push(b)}return s.length?(s.length>1&&s.sort(function(L,_){var S=L.x-a.x,I=L.y-a.y,D=Math.sqrt(S*S+I*I),T=_.x-a.x,y=_.y-a.y,d=Math.sqrt(T*T+y*y);return D<d?-1:D===d?0:1}),s[0]):e}c(Lr,"intersectPolygon");var Sr=c((e,t)=>{var a=e.x,i=e.y,l=t.x-a,s=t.y-i,r=e.width/2,n=e.height/2,o,u;return Math.abs(s)*r>Math.abs(l)*n?(s<0&&(n=-n),o=s===0?0:n*l/s,u=n):(l<0&&(r=-r),o=r,u=l===0?0:r*s/l),{x:a+o,y:i+u}},"intersectRect"),fe=Sr;var v={node:he,circle:ge,ellipse:gt,polygon:pe,rect:fe};var z=c(async(e,t,a,i)=>{let l=B(),s,r=t.useHtmlLabels||P(l.flowchart.htmlLabels);a?s=a:s="node default";let n=e.insert("g").attr("class",s).attr("id",t.domId||t.id),o=n.insert("g").attr("class","label").attr("style",t.labelStyle),u;t.labelText===void 0?u="":u=typeof t.labelText=="string"?t.labelText:t.labelText[0];let h=o.node(),x;t.labelType==="markdown"?x=lt(o,et(rt(u),l),{useHtmlLabels:r,width:t.width||l.flowchart.wrappingWidth,classes:"markdown-node-label"},l):x=h.appendChild(await W(et(rt(u),l),t.labelStyle,!1,i));let m=x.getBBox(),b=t.padding/2;if(P(l.flowchart.htmlLabels)){let L=x.children[0],_=C(x),S=L.getElementsByTagName("img");if(S){let I=u.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...S].map(D=>new Promise(T=>{function y(){if(D.style.display="flex",D.style.flexDirection="column",I){let d=l.fontSize?l.fontSize:window.getComputedStyle(document.body).fontSize,k=parseInt(d,10)*5+"px";D.style.minWidth=k,D.style.maxWidth=k}else D.style.width="100%";T(D)}c(y,"setupImage"),setTimeout(()=>{D.complete&&y()}),D.addEventListener("error",y),D.addEventListener("load",y)})))}m=L.getBoundingClientRect(),_.attr("width",m.width),_.attr("height",m.height)}return r?o.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"):o.attr("transform","translate(0, "+-m.height/2+")"),t.centerLabel&&o.attr("transform","translate("+-m.width/2+", "+-m.height/2+")"),o.insert("rect",":first-child"),{shapeSvg:n,bbox:m,halfPadding:b,label:o}},"labelHelper"),N=c((e,t)=>{let a=t.node().getBBox();e.width=a.width,e.height=a.height},"updateNodeBounds");function U(e,t,a,i){return e.insert("polygon",":first-child").attr("points",i.map(function(l){return l.x+","+l.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+a/2+")")}c(U,"insertPolygonShape");var Er=c(async(e,t)=>{t.useHtmlLabels||B().flowchart.htmlLabels||(t.centerLabel=!0);let{shapeSvg:i,bbox:l,halfPadding:s}=await z(e,t,"node "+t.classes,!0);w.info("Classes = ",t.classes);let r=i.insert("rect",":first-child");return r.attr("rx",t.rx).attr("ry",t.ry).attr("x",-l.width/2-s).attr("y",-l.height/2-s).attr("width",l.width+t.padding).attr("height",l.height+t.padding),N(t,r),t.intersect=function(n){return v.rect(t,n)},i},"note"),xe=Er;var be=c(e=>e?" "+e:"","formatClass"),j=c((e,t)=>`${t||"node default"}${be(e.classes)} ${be(e.class)}`,"getClassesFromNode"),ye=c(async(e,t)=>{let{shapeSvg:a,bbox:i}=await z(e,t,j(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=l+s,n=[{x:r/2,y:0},{x:r,y:-r/2},{x:r/2,y:-r},{x:0,y:-r/2}];w.info("Question main (Circle)");let o=U(a,r,r,n);return o.attr("style",t.style),N(t,o),t.intersect=function(u){return w.warn("Intersect called"),v.polygon(t,n,u)},a},"question"),_r=c((e,t)=>{let a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=28,l=[{x:0,y:i/2},{x:i/2,y:0},{x:0,y:-i/2},{x:-i/2,y:0}];return a.insert("polygon",":first-child").attr("points",l.map(function(r){return r.x+","+r.y}).join(" ")).attr("class","state-start").attr("r",7).attr("width",28).attr("height",28),t.width=28,t.height=28,t.intersect=function(r){return v.circle(t,14,r)},a},"choice"),Dr=c(async(e,t)=>{let{shapeSvg:a,bbox:i}=await z(e,t,j(t,void 0),!0),l=4,s=i.height+t.padding,r=s/l,n=i.width+2*r+t.padding,o=[{x:r,y:0},{x:n-r,y:0},{x:n,y:-s/2},{x:n-r,y:-s},{x:r,y:-s},{x:0,y:-s/2}],u=U(a,n,s,o);return u.attr("style",t.style),N(t,u),t.intersect=function(h){return v.polygon(t,o,h)},a},"hexagon"),Br=c(async(e,t)=>{let{shapeSvg:a,bbox:i}=await z(e,t,void 0,!0),l=2,s=i.height+2*t.padding,r=s/l,n=i.width+2*r+t.padding,o=ce(t.directions,i,t),u=U(a,n,s,o);return u.attr("style",t.style),N(t,u),t.intersect=function(h){return v.polygon(t,o,h)},a},"block_arrow"),vr=c(async(e,t)=>{let{shapeSvg:a,bbox:i}=await z(e,t,j(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:-s/2,y:0},{x:l,y:0},{x:l,y:-s},{x:-s/2,y:-s},{x:0,y:-s/2}];return U(a,l,s,r).attr("style",t.style),t.width=l+s,t.height=s,t.intersect=function(o){return v.polygon(t,r,o)},a},"rect_left_inv_arrow"),Tr=c(async(e,t)=>{let{shapeSvg:a,bbox:i}=await z(e,t,j(t),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:-2*s/6,y:0},{x:l-s/6,y:0},{x:l+2*s/6,y:-s},{x:s/6,y:-s}],n=U(a,l,s,r);return n.attr("style",t.style),N(t,n),t.intersect=function(o){return v.polygon(t,r,o)},a},"lean_right"),Cr=c(async(e,t)=>{let{shapeSvg:a,bbox:i}=await z(e,t,j(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:2*s/6,y:0},{x:l+s/6,y:0},{x:l-2*s/6,y:-s},{x:-s/6,y:-s}],n=U(a,l,s,r);return n.attr("style",t.style),N(t,n),t.intersect=function(o){return v.polygon(t,r,o)},a},"lean_left"),Nr=c(async(e,t)=>{let{shapeSvg:a,bbox:i}=await z(e,t,j(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:-2*s/6,y:0},{x:l+2*s/6,y:0},{x:l-s/6,y:-s},{x:s/6,y:-s}],n=U(a,l,s,r);return n.attr("style",t.style),N(t,n),t.intersect=function(o){return v.polygon(t,r,o)},a},"trapezoid"),Ir=c(async(e,t)=>{let{shapeSvg:a,bbox:i}=await z(e,t,j(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:s/6,y:0},{x:l-s/6,y:0},{x:l+2*s/6,y:-s},{x:-2*s/6,y:-s}],n=U(a,l,s,r);return n.attr("style",t.style),N(t,n),t.intersect=function(o){return v.polygon(t,r,o)},a},"inv_trapezoid"),Or=c(async(e,t)=>{let{shapeSvg:a,bbox:i}=await z(e,t,j(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:0,y:0},{x:l+s/2,y:0},{x:l,y:-s/2},{x:l+s/2,y:-s},{x:0,y:-s}],n=U(a,l,s,r);return n.attr("style",t.style),N(t,n),t.intersect=function(o){return v.polygon(t,r,o)},a},"rect_right_inv_arrow"),Rr=c(async(e,t)=>{let{shapeSvg:a,bbox:i}=await z(e,t,j(t,void 0),!0),l=i.width+t.padding,s=l/2,r=s/(2.5+l/50),n=i.height+r+t.padding,o="M 0,"+r+" a "+s+","+r+" 0,0,0 "+l+" 0 a "+s+","+r+" 0,0,0 "+-l+" 0 l 0,"+n+" a "+s+","+r+" 0,0,0 "+l+" 0 l 0,"+-n,u=a.attr("label-offset-y",r).insert("path",":first-child").attr("style",t.style).attr("d",o).attr("transform","translate("+-l/2+","+-(n/2+r)+")");return N(t,u),t.intersect=function(h){let x=v.rect(t,h),m=x.x-t.x;if(s!=0&&(Math.abs(m)<t.width/2||Math.abs(m)==t.width/2&&Math.abs(x.y-t.y)>t.height/2-r)){let b=r*r*(1-m*m/(s*s));b!=0&&(b=Math.sqrt(b)),b=r-b,h.y-t.y>0&&(b=-b),x.y+=b}return x},a},"cylinder"),zr=c(async(e,t)=>{let{shapeSvg:a,bbox:i,halfPadding:l}=await z(e,t,"node "+t.classes+" "+t.class,!0),s=a.insert("rect",":first-child"),r=t.positioned?t.width:i.width+t.padding,n=t.positioned?t.height:i.height+t.padding,o=t.positioned?-r/2:-i.width/2-l,u=t.positioned?-n/2:-i.height/2-l;if(s.attr("class","basic label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",o).attr("y",u).attr("width",r).attr("height",n),t.props){let h=new Set(Object.keys(t.props));t.props.borders&&(Et(s,t.props.borders,r,n),h.delete("borders")),h.forEach(x=>{w.warn(`Unknown node property ${x}`)})}return N(t,s),t.intersect=function(h){return v.rect(t,h)},a},"rect"),Ar=c(async(e,t)=>{let{shapeSvg:a,bbox:i,halfPadding:l}=await z(e,t,"node "+t.classes,!0),s=a.insert("rect",":first-child"),r=t.positioned?t.width:i.width+t.padding,n=t.positioned?t.height:i.height+t.padding,o=t.positioned?-r/2:-i.width/2-l,u=t.positioned?-n/2:-i.height/2-l;if(s.attr("class","basic cluster composite label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",o).attr("y",u).attr("width",r).attr("height",n),t.props){let h=new Set(Object.keys(t.props));t.props.borders&&(Et(s,t.props.borders,r,n),h.delete("borders")),h.forEach(x=>{w.warn(`Unknown node property ${x}`)})}return N(t,s),t.intersect=function(h){return v.rect(t,h)},a},"composite"),Mr=c(async(e,t)=>{let{shapeSvg:a}=await z(e,t,"label",!0);w.trace("Classes = ",t.class);let i=a.insert("rect",":first-child"),l=0,s=0;if(i.attr("width",l).attr("height",s),a.attr("class","label edgeLabel"),t.props){let r=new Set(Object.keys(t.props));t.props.borders&&(Et(i,t.props.borders,l,s),r.delete("borders")),r.forEach(n=>{w.warn(`Unknown node property ${n}`)})}return N(t,i),t.intersect=function(r){return v.rect(t,r)},a},"labelRect");function Et(e,t,a,i){let l=[],s=c(n=>{l.push(n,0)},"addBorder"),r=c(n=>{l.push(0,n)},"skipBorder");t.includes("t")?(w.debug("add top border"),s(a)):r(a),t.includes("r")?(w.debug("add right border"),s(i)):r(i),t.includes("b")?(w.debug("add bottom border"),s(a)):r(a),t.includes("l")?(w.debug("add left border"),s(i)):r(i),e.attr("stroke-dasharray",l.join(" "))}c(Et,"applyNodePropertyBorders");var Fr=c(async(e,t)=>{let a;t.classes?a="node "+t.classes:a="node default";let i=e.insert("g").attr("class",a).attr("id",t.domId||t.id),l=i.insert("rect",":first-child"),s=i.insert("line"),r=i.insert("g").attr("class","label"),n=t.labelText.flat?t.labelText.flat():t.labelText,o="";typeof n=="object"?o=n[0]:o=n,w.info("Label text abc79",o,n,typeof n=="object");let u=r.node().appendChild(await W(o,t.labelStyle,!0,!0)),h={width:0,height:0};if(P(B().flowchart.htmlLabels)){let _=u.children[0],S=C(u);h=_.getBoundingClientRect(),S.attr("width",h.width),S.attr("height",h.height)}w.info("Text 2",n);let x=n.slice(1,n.length),m=u.getBBox(),b=r.node().appendChild(await W(x.join?x.join("<br/>"):x,t.labelStyle,!0,!0));if(P(B().flowchart.htmlLabels)){let _=b.children[0],S=C(b);h=_.getBoundingClientRect(),S.attr("width",h.width),S.attr("height",h.height)}let L=t.padding/2;return C(b).attr("transform","translate( "+(h.width>m.width?0:(m.width-h.width)/2)+", "+(m.height+L+5)+")"),C(u).attr("transform","translate( "+(h.width<m.width?0:-(m.width-h.width)/2)+", 0)"),h=r.node().getBBox(),r.attr("transform","translate("+-h.width/2+", "+(-h.height/2-L+3)+")"),l.attr("class","outer title-state").attr("x",-h.width/2-L).attr("y",-h.height/2-L).attr("width",h.width+t.padding).attr("height",h.height+t.padding),s.attr("class","divider").attr("x1",-h.width/2-L).attr("x2",h.width/2+L).attr("y1",-h.height/2-L+m.height+L).attr("y2",-h.height/2-L+m.height+L),N(t,l),t.intersect=function(_){return v.rect(t,_)},i},"rectWithTitle"),Wr=c(async(e,t)=>{let{shapeSvg:a,bbox:i}=await z(e,t,j(t,void 0),!0),l=i.height+t.padding,s=i.width+l/4+t.padding,r=a.insert("rect",":first-child").attr("style",t.style).attr("rx",l/2).attr("ry",l/2).attr("x",-s/2).attr("y",-l/2).attr("width",s).attr("height",l);return N(t,r),t.intersect=function(n){return v.rect(t,n)},a},"stadium"),Pr=c(async(e,t)=>{let{shapeSvg:a,bbox:i,halfPadding:l}=await z(e,t,j(t,void 0),!0),s=a.insert("circle",":first-child");return s.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+l).attr("width",i.width+t.padding).attr("height",i.height+t.padding),w.info("Circle main"),N(t,s),t.intersect=function(r){return w.info("Circle intersect",t,i.width/2+l,r),v.circle(t,i.width/2+l,r)},a},"circle"),Yr=c(async(e,t)=>{let{shapeSvg:a,bbox:i,halfPadding:l}=await z(e,t,j(t,void 0),!0),s=5,r=a.insert("g",":first-child"),n=r.insert("circle"),o=r.insert("circle");return r.attr("class",t.class),n.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+l+s).attr("width",i.width+t.padding+s*2).attr("height",i.height+t.padding+s*2),o.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+l).attr("width",i.width+t.padding).attr("height",i.height+t.padding),w.info("DoubleCircle main"),N(t,n),t.intersect=function(u){return w.info("DoubleCircle intersect",t,i.width/2+l+s,u),v.circle(t,i.width/2+l+s,u)},a},"doublecircle"),Hr=c(async(e,t)=>{let{shapeSvg:a,bbox:i}=await z(e,t,j(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:0,y:0},{x:l,y:0},{x:l,y:-s},{x:0,y:-s},{x:0,y:0},{x:-8,y:0},{x:l+8,y:0},{x:l+8,y:-s},{x:-8,y:-s},{x:-8,y:0}],n=U(a,l,s,r);return n.attr("style",t.style),N(t,n),t.intersect=function(o){return v.polygon(t,r,o)},a},"subroutine"),jr=c((e,t)=>{let a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=a.insert("circle",":first-child");return i.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),N(t,i),t.intersect=function(l){return v.circle(t,7,l)},a},"start"),me=c((e,t,a)=>{let i=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),l=70,s=10;a==="LR"&&(l=10,s=70);let r=i.append("rect").attr("x",-1*l/2).attr("y",-1*s/2).attr("width",l).attr("height",s).attr("class","fork-join");return N(t,r),t.height=t.height+t.padding/2,t.width=t.width+t.padding/2,t.intersect=function(n){return v.rect(t,n)},i},"forkJoin"),Kr=c((e,t)=>{let a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=a.insert("circle",":first-child"),l=a.insert("circle",":first-child");return l.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),i.attr("class","state-end").attr("r",5).attr("width",10).attr("height",10),N(t,l),t.intersect=function(s){return v.circle(t,7,s)},a},"end"),Xr=c(async(e,t)=>{let a=t.padding/2,i=4,l=8,s;t.classes?s="node "+t.classes:s="node default";let r=e.insert("g").attr("class",s).attr("id",t.domId||t.id),n=r.insert("rect",":first-child"),o=r.insert("line"),u=r.insert("line"),h=0,x=i,m=r.insert("g").attr("class","label"),b=0,L=t.classData.annotations?.[0],_=t.classData.annotations[0]?"\xAB"+t.classData.annotations[0]+"\xBB":"",S=m.node().appendChild(await W(_,t.labelStyle,!0,!0)),I=S.getBBox();if(P(B().flowchart.htmlLabels)){let E=S.children[0],g=C(S);I=E.getBoundingClientRect(),g.attr("width",I.width),g.attr("height",I.height)}t.classData.annotations[0]&&(x+=I.height+i,h+=I.width);let D=t.classData.label;t.classData.type!==void 0&&t.classData.type!==""&&(B().flowchart.htmlLabels?D+="&lt;"+t.classData.type+"&gt;":D+="<"+t.classData.type+">");let T=m.node().appendChild(await W(D,t.labelStyle,!0,!0));C(T).attr("class","classTitle");let y=T.getBBox();if(P(B().flowchart.htmlLabels)){let E=T.children[0],g=C(T);y=E.getBoundingClientRect(),g.attr("width",y.width),g.attr("height",y.height)}x+=y.height+i,y.width>h&&(h=y.width);let d=[];t.classData.members.forEach(async E=>{let g=E.getDisplayDetails(),Y=g.displayText;B().flowchart.htmlLabels&&(Y=Y.replace(/</g,"&lt;").replace(/>/g,"&gt;"));let p=m.node().appendChild(await W(Y,g.cssStyle?g.cssStyle:t.labelStyle,!0,!0)),R=p.getBBox();if(P(B().flowchart.htmlLabels)){let Z=p.children[0],V=C(p);R=Z.getBoundingClientRect(),V.attr("width",R.width),V.attr("height",R.height)}R.width>h&&(h=R.width),x+=R.height+i,d.push(p)}),x+=l;let f=[];if(t.classData.methods.forEach(async E=>{let g=E.getDisplayDetails(),Y=g.displayText;B().flowchart.htmlLabels&&(Y=Y.replace(/</g,"&lt;").replace(/>/g,"&gt;"));let p=m.node().appendChild(await W(Y,g.cssStyle?g.cssStyle:t.labelStyle,!0,!0)),R=p.getBBox();if(P(B().flowchart.htmlLabels)){let Z=p.children[0],V=C(p);R=Z.getBoundingClientRect(),V.attr("width",R.width),V.attr("height",R.height)}R.width>h&&(h=R.width),x+=R.height+i,f.push(p)}),x+=l,L){let E=(h-I.width)/2;C(S).attr("transform","translate( "+(-1*h/2+E)+", "+-1*x/2+")"),b=I.height+i}let k=(h-y.width)/2;return C(T).attr("transform","translate( "+(-1*h/2+k)+", "+(-1*x/2+b)+")"),b+=y.height+i,o.attr("class","divider").attr("x1",-h/2-a).attr("x2",h/2+a).attr("y1",-x/2-a+l+b).attr("y2",-x/2-a+l+b),b+=l,d.forEach(E=>{C(E).attr("transform","translate( "+-h/2+", "+(-1*x/2+b+l/2)+")");let g=E?.getBBox();b+=(g?.height??0)+i}),b+=l,u.attr("class","divider").attr("x1",-h/2-a).attr("x2",h/2+a).attr("y1",-x/2-a+l+b).attr("y2",-x/2-a+l+b),b+=l,f.forEach(E=>{C(E).attr("transform","translate( "+-h/2+", "+(-1*x/2+b)+")");let g=E?.getBBox();b+=(g?.height??0)+i}),n.attr("style",t.style).attr("class","outer title-state").attr("x",-h/2-a).attr("y",-(x/2)-a).attr("width",h+t.padding).attr("height",x+t.padding),N(t,n),t.intersect=function(E){return v.rect(t,E)},r},"class_box"),we={rhombus:ye,composite:Ar,question:ye,rect:zr,labelRect:Mr,rectWithTitle:Fr,choice:_r,circle:Pr,doublecircle:Yr,stadium:Wr,hexagon:Dr,block_arrow:Br,rect_left_inv_arrow:vr,lean_right:Tr,lean_left:Cr,trapezoid:Nr,inv_trapezoid:Ir,rect_right_inv_arrow:Or,cylinder:Rr,start:jr,end:Kr,note:xe,subroutine:Hr,fork:me,join:me,class_box:Xr},dt={},_t=c(async(e,t,a)=>{let i,l;if(t.link){let s;B().securityLevel==="sandbox"?s="_top":t.linkTarget&&(s=t.linkTarget||"_blank"),i=e.insert("svg:a").attr("xlink:href",t.link).attr("target",s),l=await we[t.shape](i,t,a)}else l=await we[t.shape](e,t,a),i=l;return t.tooltip&&l.attr("title",t.tooltip),t.class&&l.attr("class","node default "+t.class),dt[t.id]=i,t.haveCallback&&dt[t.id].attr("class",dt[t.id].attr("class")+" clickable"),i},"insertNode");var ke=c(e=>{let t=dt[e.id];w.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");let a=8,i=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+i-e.width/2)+", "+(e.y-e.height/2-a)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),i},"positionNode");function Le(e,t,a=!1){let i=e,l="default";(i?.classes?.length||0)>0&&(l=(i?.classes??[]).join(" ")),l=l+" flowchart-label";let s=0,r="",n;switch(i.type){case"round":s=5,r="rect";break;case"composite":s=0,r="composite",n=0;break;case"square":r="rect";break;case"diamond":r="question";break;case"hexagon":r="hexagon";break;case"block_arrow":r="block_arrow";break;case"odd":r="rect_left_inv_arrow";break;case"lean_right":r="lean_right";break;case"lean_left":r="lean_left";break;case"trapezoid":r="trapezoid";break;case"inv_trapezoid":r="inv_trapezoid";break;case"rect_left_inv_arrow":r="rect_left_inv_arrow";break;case"circle":r="circle";break;case"ellipse":r="ellipse";break;case"stadium":r="stadium";break;case"subroutine":r="subroutine";break;case"cylinder":r="cylinder";break;case"group":r="rect";break;case"doublecircle":r="doublecircle";break;default:r="rect"}let o=Mt(i?.styles??[]),u=i.label,h=i.size??{width:0,height:0,x:0,y:0};return{labelStyle:o.labelStyle,shape:r,labelText:u,rx:s,ry:s,class:l,style:o.style,id:i.id,directions:i.directions,width:h.width,height:h.height,x:h.x,y:h.y,positioned:a,intersect:void 0,type:i.type,padding:n??q()?.block?.padding??0}}c(Le,"getNodeFromBlock");async function Ur(e,t,a){let i=Le(t,a,!1);if(i.type==="group")return;let l=q(),s=await _t(e,i,{config:l}),r=s.node().getBBox(),n=a.getBlock(i.id);n.size={width:r.width,height:r.height,x:0,y:0,node:s},a.setBlock(n),s.remove()}c(Ur,"calculateBlockSize");async function Vr(e,t,a){let i=Le(t,a,!0);if(a.getBlock(i.id).type!=="space"){let s=q();await _t(e,i,{config:s}),t.intersect=i?.intersect,ke(i)}}c(Vr,"insertBlockPositioned");async function Dt(e,t,a,i){for(let l of t)await i(e,l,a),l.children&&await Dt(e,l.children,a,i)}c(Dt,"performOperations");async function Se(e,t,a){await Dt(e,t,a,Ur)}c(Se,"calculateBlockSizes");async function Ee(e,t,a){await Dt(e,t,a,Vr)}c(Ee,"insertBlocks");async function _e(e,t,a,i,l){let s=new Ht({multigraph:!0,compound:!0});s.setGraph({rankdir:"TB",nodesep:10,ranksep:10,marginx:8,marginy:8});for(let r of a)r.size&&s.setNode(r.id,{width:r.size.width,height:r.size.height,intersect:r.intersect});for(let r of t)if(r.start&&r.end){let n=i.getBlock(r.start),o=i.getBlock(r.end);if(n?.size&&o?.size){let u=n.size,h=o.size,x=[{x:u.x,y:u.y},{x:u.x+(h.x-u.x)/2,y:u.y+(h.y-u.y)/2},{x:h.x,y:h.y}];oe(e,{v:r.start,w:r.end,name:r.id},{...r,arrowTypeEnd:r.arrowTypeEnd,arrowTypeStart:r.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"},void 0,"block",s,l),r.label&&(await ne(e,{...r,label:r.label,labelStyle:"stroke: #333; stroke-width: 1.5px;fill:none;",arrowTypeEnd:r.arrowTypeEnd,arrowTypeStart:r.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"}),le({...r,x:x[1].x,y:x[1].y},{originalPath:x}))}}}c(_e,"insertEdges");var Gr=c(function(e,t){return t.db.getClasses()},"getClasses"),Zr=c(async function(e,t,a,i){let{securityLevel:l,block:s}=q(),r=i.db,n;l==="sandbox"&&(n=C("#i"+t));let o=l==="sandbox"?C(n.nodes()[0].contentDocument.body):C("body"),u=l==="sandbox"?o.select(`[id="${t}"]`):C(`[id="${t}"]`);Qt(u,["point","circle","cross"],i.type,t);let x=r.getBlocks(),m=r.getBlocksFlat(),b=r.getEdges(),L=u.insert("g").attr("class","block");await Se(L,x,r);let _=ee(r);if(await Ee(L,x,r),await _e(L,b,m,r,t),_){let S=_,I=Math.max(1,Math.round(.125*(S.width/S.height))),D=S.height+I+10,T=S.width+10,{useMaxWidth:y}=s;Ot(u,D,T,!!y),w.debug("Here Bounds",_,S),u.attr("viewBox",`${S.x-5} ${S.y-5} ${S.width+10} ${S.height+10}`)}},"draw"),De={draw:Zr,getClasses:Gr};var $s={parser:Kt,db:qt,renderer:De,styles:Jt};export{$s as diagram};
