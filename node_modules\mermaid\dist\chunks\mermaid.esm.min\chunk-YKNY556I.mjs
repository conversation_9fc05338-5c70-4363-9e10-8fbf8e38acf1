import{b as g,c as y,d,e as f}from"./chunk-JOFIKEML.mjs";import{a as p,f as l,h as u}from"./chunk-DPMNACAB.mjs";import{c as m}from"./chunk-3R3PQ5PD.mjs";import{F as s,c as i,t as a}from"./chunk-F632ZYSZ.mjs";import{a as o}from"./chunk-GTKDMUJJ.mjs";var L={common:s,getConfig:a,insertCluster:l,insertEdge:d,insertEdgeLabel:g,insertMarkers:f,insertNode:u,interpolateToCurve:m,labelHelper:p,log:i,positionEdgeLabel:y};var t={},h=o(r=>{for(let e of r)t[e.name]=e},"registerLayoutLoaders"),c=o(()=>{h([{name:"dagre",loader:o(async()=>await import("./dagre-4KEWV3BN.mjs"),"loader")}])},"registerDefaultLayoutLoaders");c();var S=o(async(r,e)=>{if(!(r.layoutAlgorithm in t))throw new Error(`Unknown layout algorithm: ${r.layoutAlgorithm}`);let n=t[r.layoutAlgorithm];return(await n.loader()).render(r,e,L,{algorithm:n.algorithm})},"render"),V=o((r="",{fallback:e="dagre"}={})=>{if(r in t)return r;if(e in t)return i.warn(`Layout algorithm ${r} is not registered. Using ${e} as fallback.`),e;throw new Error(`Both layout algorithms ${r} and ${e} are not registered.`)},"getRegisteredLayoutAlgorithm");export{h as a,S as b,V as c};
